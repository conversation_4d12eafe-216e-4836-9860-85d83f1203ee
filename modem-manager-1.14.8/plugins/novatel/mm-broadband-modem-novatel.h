/* -*- Mode: C; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/*
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details:
 *
 * Copyright (C) 2008 - 2009 Novell, Inc.
 * Copyright (C) 2009 - 2012 Red Hat, Inc.
 * Copyright (C) 2012 Aleksander Morgado <<EMAIL>>
 */

#ifndef MM_BROADBAND_MODEM_NOVATEL_H
#define MM_BROADBAND_MODEM_NOVATEL_H

#include "mm-broadband-modem.h"

#define MM_TYPE_BROADBAND_MODEM_NOVATEL            (mm_broadband_modem_novatel_get_type ())
#define MM_BROADBAND_MODEM_NOVATEL(obj)            (G_TYPE_CHECK_INSTANCE_CAST ((obj), MM_TYPE_BROADBAND_MODEM_NOVATEL, MMBroadbandModemNovatel))
#define MM_BROADBAND_MODEM_NOVATEL_CLASS(klass)    (G_TYPE_CHECK_CLASS_CAST ((klass),  MM_TYPE_BROADBAND_MODEM_NOVATEL, MMBroadbandModemNovatelClass))
#define MM_IS_BROADBAND_MODEM_NOVATEL(obj)         (G_TYPE_CHECK_INSTANCE_TYPE ((obj), MM_TYPE_BROADBAND_MODEM_NOVATEL))
#define MM_IS_BROADBAND_MODEM_NOVATEL_CLASS(klass) (G_TYPE_CHECK_CLASS_TYPE ((klass),  MM_TYPE_BROADBAND_MODEM_NOVATEL))
#define MM_BROADBAND_MODEM_NOVATEL_GET_CLASS(obj)  (G_TYPE_INSTANCE_GET_CLASS ((obj),  MM_TYPE_BROADBAND_MODEM_NOVATEL, MMBroadbandModemNovatelClass))

typedef struct _MMBroadbandModemNovatel MMBroadbandModemNovatel;
typedef struct _MMBroadbandModemNovatelClass MMBroadbandModemNovatelClass;
typedef struct _MMBroadbandModemNovatelPrivate MMBroadbandModemNovatelPrivate;

struct _MMBroadbandModemNovatel {
    MMBroadbandModem parent;
    MMBroadbandModemNovatelPrivate *priv;
};

struct _MMBroadbandModemNovatelClass{
    MMBroadbandModemClass parent;
};

GType mm_broadband_modem_novatel_get_type (void);

MMBroadbandModemNovatel *mm_broadband_modem_novatel_new (const gchar *device,
                                                         const gchar **drivers,
                                                         const gchar *plugin,
                                                         guint16 vendor_id,
                                                         guint16 product_id);

#endif /* MM_BROADBAND_MODEM_NOVATEL_H */

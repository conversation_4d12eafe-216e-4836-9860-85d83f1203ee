

AT                   \r\nOK\r\n
ATE0                 \r\nOK\r\n
ATV1                 \r\nOK\r\n
AT+CMEE=1            \r\nOK\r\n
ATX4                 \r\nOK\r\n
AT&C1                \r\nOK\r\n
AT+IFC=1,1           \r\nOK\r\n
AT+GCAP              \r\n+GCAP: +CGSM +DS +ES\r\n\r\nOK\r\n
ATI                  \r\nManufacturer: Dummy vendor\r\nModel: Dummy model\r\nRevision: Dummy revision\r\nIMEI: 001100110011002<CR><LF>+GCAP: +CGSM,+DS,+ES\r\n\r\nOK\r\n
AT+WS46=?            \r\n+WS46: (12,22)\r\n\r\nOK\r\n
AT+CGMI              \r\nDummy vendor\r\n\r\nOK\r\n
AT+CGMM              \r\nDummy model\r\n\r\nOK\r\n
AT+CGMR              \r\nDummy revision\r\n\r\nOK\r\n
AT+CGSN              \r\n123456789012345\r\n\r\nOK\r\n
AT+CGDCONT=?         \r\n+CGDCONT: (1-11),"IP",,,(0-2),(0-3)\r\n+CGDCONT: (1-11),"IPV6",,,(0-2),(0-3)\r\n+CGDCONT: (1-11),"IPV4V6",,,(0-2),(0-3)\r\n+CGDCONT: (1-11),"PPP",,,(0-2),(0-3)\r\n\r\nOK\r\n
AT+CIMI              \r\n998899889988997\r\n\r\nOK\r\n
AT+CLCK=?            \r\n+CLCK: ("SC","AO","OI","OX","AI","IR","AB","AG","AC","PS","FD")\r\n\r\nOK\r\n
AT+CLCK="SC",2       \r\n+CLCK: 1\r\n\r\nOK\r\n
AT+CLCK="FD",2       \r\n+CLCK: 1\r\n\r\nOK\r\n
AT+CLCK="PS",2       \r\n+CLCK: 1\r\n\r\nOK\r\n
AT+CFUN?             \r\n+CFUN: 1\r\n\r\nOK\r\n
AT+CSCS=?            \r\n+CSCS: ("IRA","UCS2","GSM")\r\n\r\nOK\r\n
AT+CSCS="UCS2"       \r\nOK\r\n
AT+CSCS?             \r\n+CSCS: "UCS2"\r\n\r\nOK\r\n
AT+CREG=2            \r\nOK\r\n
AT+CGREG=2           \r\nOK\r\n
AT+CREG=0            \r\nOK\r\n
AT+CGREG=0           \r\nOK\r\n
AT+CREG?             \r\n+CREG: 2,1,"1234","001122BB"\r\n\r\nOK\r\n
AT+CGREG?            \r\n+CGREG: 2,1,"31C5","0083F7CD"\r\n\r\nOK\r\n
AT+COPS=3,2;+COPS?   \r\n+COPS: 0,2,"21401",2\r\n\r\nOK\r\n
AT+COPS=3,0;+COPS?   \r\n+COPS: 0,0,"vodafone ES"\r\n\r\nOK\r\n
AT+CMGF=?            \r\n+CMGF: (0,1)\r\n\r\nOK\r\n
AT+CMGF=0            \r\nOK\r\n
AT+CSQ               \r\n+CSQ: 17,99\r\n\r\nOK\r\n

# By default, no PIN required
AT+CPIN?             \r\n+CPIN: READY\r\n\r\nOK\r\n

# By default, no messaging support
AT+CNMI=?            \r\nERROR\r\n

# By default, no USSD support
AT+CUSD=?            \r\nERROR\r\n

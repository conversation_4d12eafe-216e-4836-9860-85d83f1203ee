/* -*- Mode: C; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/*
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details:
 *
 * Copyright (C) 2013 Aleksander Morgado <<EMAIL>>
 * Copyright (C) 2014 <PERSON> <<EMAIL>>
 */

#include <glib.h>
#include <glib-object.h>
#include <locale.h>
#include <netinet/in.h>
#include <arpa/inet.h>

#include <ModemManager.h>
#define _LIBMM_INSIDE_MM
#include <libmm-glib.h>

#include "mm-log-test.h"
#include "mm-modem-helpers.h"
#include "mm-modem-helpers-icera.h"

/*****************************************************************************/
/* Test %IPDPADDR responses */

typedef struct {
    const gchar *str;
    const guint expected_cid;

    /* IPv4 */
    const gchar *ipv4_addr;
    const guint ipv4_prefix;
    const gchar *ipv4_gw;
    const gchar *ipv4_dns1;
    const gchar *ipv4_dns2;

    /* IPv6 */
    const gchar *ipv6_addr;
    const gchar *ipv6_dns1;
} IpdpaddrTest;

static const IpdpaddrTest ipdpaddr_tests[] = {
    /* Sierra USB305 */
    { "%IPDPADDR: 2, ************, ************, ***********, **************, 0.0.0.0, 0.0.0.0\r\n",
        2, "************", 32, "************", "***********", "**************",
        NULL, NULL },

    /* ZTE/Vodafone K3805-Z */
    { "%IPDPADDR: 5, ************, ************, ***********, **************, 0.0.0.0, 0.0.0.0, *********, *************, ************,\r\n",
        5, "************", 24, "************", "***********", "**************",
        NULL, NULL },

    /* Secondary gateway check */
    { "%IPDPADDR: 5, ************, 0.0.0.0, ***********, **************, 0.0.0.0, 0.0.0.0, *********, *************, ************,\r\n",
        5, "************", 24, "************", "***********", "**************",
        NULL, NULL },

    /* Secondary gateway check #2 */
    { "%IPDPADDR: 5, *************, 0.0.0.0, ***************, ***************, 0.0.0.0, 0.0.0.0, ***************, *************\r\n",
        5, "*************", 31, "*************", "***************", "***************",
        NULL, NULL },

    /* Nokia 21M */
    { "%IPDPADDR: 1, ************, ************, ***********, **************, 0.0.0.0, 0.0.0.0, *********, ************, fe80::f:9135:5901, ::, fd00:976a::9, ::, ::, ::, ::, ::\r\n",
        1, "************", 32, "************", "***********", "**************",
        "fe80::f:9135:5901", "fd00:976a::9" },

    { "%IPDPADDR: 3, 0.0.0.0, 0.0.0.0, 0.0.0.0, 0.0.0.0, 0.0.0.0, 0.0.0.0, 0.0.0.0, 0.0.0.0, fe80::2e:437b:7901, ::, fd00:976a::9, ::, ::, ::, ::, ::\r\n",
        3, NULL, 0, NULL, NULL, NULL,
        "fe80::2e:437b:7901", "fd00:976a::9" },

    /* Some development chip (cnsbg.p1001.rev2, CL477342) */
    { "%IPDPADDR: 5, *************, *************, ***************, ***************, 0.0.0.0, 0.0.0.0, ***************, *************\r\n",
        5, "*************", 31, "*************", "***************", "***************",
        NULL, NULL },

    /* 21M with newer firmware */
    { "%IPDPADDR: 2, **************, **************, **************, 0.0.0.0, 0.0.0.0, 0.0.0.0, ***********, **************, fe80::1:e414:eb01, ::, 2a00:e18:0:3::6, ::, ::, ::, ::, ::\r\n",
        2, "**************", 16, "**************", "**************", NULL,
        "fe80::1:e414:eb01", "2a00:e18:0:3::6" },

    { "%IPDPADDR: 1, 0.0.0.0, 0.0.0.0, 0.0.0.0, 0.0.0.0, 0.0.0.0, 0.0.0.0, 0.0.0.0, 0.0.0.0, fe80::1f:fad1:4c01, ::, 2001:4600:4:fff::54, 2001:4600:4:1fff::54, ::, ::, ::, ::\r\n",
      1, NULL, 0, NULL, NULL, NULL,
      "fe80::1f:fad1:4c01", "2001:4600:4:fff::54" },

    { "%IPDPADDR: 1, *************, 46.157.76.180, 193.213.112.4, 130.67.15.198, 0.0.0.0, 0.0.0.0, *********, 46.157.76.180, ::, ::, ::, ::, ::, ::, ::, ::\r\n",
      1, "*************", 32, "46.157.76.180", "193.213.112.4", "130.67.15.198",
      NULL, NULL },

    { "%IPDPADDR: 1, 0.0.0.0, 0.0.0.0, 193.213.112.4, 130.67.15.198, 0.0.0.0, 0.0.0.0, 0.0.0.0, 0.0.0.0, ::, ::, 2001:4600:4:fff::52, 2001:4600:4:1fff::52, ::, ::, ::, ::",
      1, NULL, 0, NULL, NULL, NULL,
      NULL, "2001:4600:4:fff::52" },

    { NULL }
};

static void
test_ipdpaddr (void)
{
    guint i;

    for (i = 0; ipdpaddr_tests[i].str; i++) {
        gboolean success;
        GError *error = NULL;
        MMBearerIpConfig *ipv4 = NULL;
        MMBearerIpConfig *ipv6 = NULL;
        const gchar **dns;
        guint dnslen;

        success = mm_icera_parse_ipdpaddr_response (
                        ipdpaddr_tests[i].str,
                        ipdpaddr_tests[i].expected_cid,
                        &ipv4,
                        &ipv6,
                        &error);
        g_assert_no_error (error);
        g_assert (success);

        /* IPv4 */
        if (ipdpaddr_tests[i].ipv4_addr) {
            g_assert (ipv4);
            g_assert_cmpint (mm_bearer_ip_config_get_method (ipv4), ==, MM_BEARER_IP_METHOD_STATIC);
            g_assert_cmpstr (mm_bearer_ip_config_get_address (ipv4), ==, ipdpaddr_tests[i].ipv4_addr);
            g_assert_cmpint (mm_bearer_ip_config_get_prefix (ipv4), ==, ipdpaddr_tests[i].ipv4_prefix);
            g_assert_cmpstr (mm_bearer_ip_config_get_gateway (ipv4), ==, ipdpaddr_tests[i].ipv4_gw);

            dns = mm_bearer_ip_config_get_dns (ipv4);
            g_assert (dns);
            dnslen = g_strv_length ((gchar **) dns);
            if (ipdpaddr_tests[i].ipv4_dns2 != NULL)
                g_assert_cmpint (dnslen, ==, 2);
            else
                g_assert_cmpint (dnslen, ==, 1);
            g_assert_cmpstr (dns[0], ==, ipdpaddr_tests[i].ipv4_dns1);
            g_assert_cmpstr (dns[1], ==, ipdpaddr_tests[i].ipv4_dns2);
            g_object_unref (ipv4);
        } else
            g_assert (ipv4 == NULL);

        /* IPv6 */
        if (ipdpaddr_tests[i].ipv6_addr || ipdpaddr_tests[i].ipv6_dns1) {
            struct in6_addr a6;
            g_assert (ipv6);

            if (ipdpaddr_tests[i].ipv6_addr) {
                g_assert_cmpstr (mm_bearer_ip_config_get_address (ipv6), ==, ipdpaddr_tests[i].ipv6_addr);
                g_assert_cmpint (mm_bearer_ip_config_get_prefix (ipv6), ==, 64);

                g_assert (inet_pton (AF_INET6, mm_bearer_ip_config_get_address (ipv6), &a6));
                if (IN6_IS_ADDR_LINKLOCAL (&a6))
                    g_assert_cmpint (mm_bearer_ip_config_get_method (ipv6), ==, MM_BEARER_IP_METHOD_DHCP);
                else
                    g_assert_cmpint (mm_bearer_ip_config_get_method (ipv6), ==, MM_BEARER_IP_METHOD_STATIC);
            } else
                g_assert_cmpint (mm_bearer_ip_config_get_method (ipv6), ==, MM_BEARER_IP_METHOD_DHCP);

            dns = mm_bearer_ip_config_get_dns (ipv6);
            g_assert (dns);
            dnslen = g_strv_length ((gchar **) dns);
            g_assert_cmpint (dnslen, ==, 1);
            g_assert_cmpstr (dns[0], ==, ipdpaddr_tests[i].ipv6_dns1);
            g_object_unref (ipv6);
        } else
            g_assert (ipv6 == NULL);
    }
}

/*****************************************************************************/

int main (int argc, char **argv)
{
    setlocale (LC_ALL, "");

    g_test_init (&argc, &argv, NULL);

    g_test_add_func ("/MM/icera/ipdpaddr", test_ipdpaddr);

    return g_test_run ();
}

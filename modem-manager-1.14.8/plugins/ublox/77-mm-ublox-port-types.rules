# do not edit this file, it will be overwritten on update
ACTION!="add|change|move|bind", GOTO="mm_ublox_port_types_end"
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1546", GOTO="mm_ublox_port_types"
GOTO="mm_ublox_port_types_end"

LABEL="mm_ublox_port_types"

SUBSYSTEMS=="usb", ATTRS{bInterfaceNumber}=="?*", ENV{.MM_USBIFNUM}="$attr{bInterfaceNumber}"

# Fully ignore u-blox GPS devices
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="01a5", ENV{ID_MM_DEVICE_IGNORE}="1"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="01a6", ENV{ID_MM_DEVICE_IGNORE}="1"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="01a7", ENV{ID_MM_DEVICE_IGNORE}="1"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="01a8", ENV{ID_MM_DEVICE_IGNORE}="1"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="01a9", ENV{ID_MM_DEVICE_IGNORE}="1"

# Toby-L4 port types
#  ttyACM0 (if #2): secondary (ignore)
#  ttyACM1 (if #4): debug port (ignore)
#  ttyACM2 (if #6): primary
#      Wait up to 20s for the +READY URC
#  ttyACM3 (if #8): AT port for FOTA (ignore)
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1010", ENV{.MM_USBIFNUM}=="02", ENV{ID_MM_PORT_IGNORE}="1"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1010", ENV{.MM_USBIFNUM}=="04", ENV{ID_MM_PORT_IGNORE}="1"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1010", ENV{.MM_USBIFNUM}=="06", ENV{ID_MM_UBLOX_PORT_READY_DELAY}="20"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1010", ENV{.MM_USBIFNUM}=="08", ENV{ID_MM_PORT_IGNORE}="1"

# TOBY-L200
#  Wait up to 20s before probing AT ports
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1141", ENV{ID_MM_UBLOX_PORT_READY_DELAY}="20"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1143", ENV{ID_MM_UBLOX_PORT_READY_DELAY}="20"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1146", ENV{ID_MM_UBLOX_PORT_READY_DELAY}="20"

# TOBY-R2 port types
#  ttyACM0 (if #0): primary
#  ttyACM1 (if #2): secondary
#  ttyACM2 (if #4): tertiary
#  ttyACM3 (if #6): GNSS Tunneling (ignore)
#  ttyACM4 (if #8): SIM Access Profile (ignore)
#  ttyACM5 (if #10): Primary Log for diagnostics (ignore)
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1107", ENV{.MM_USBIFNUM}=="06", ENV{ID_MM_PORT_IGNORE}="1"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1107", ENV{.MM_USBIFNUM}=="08", ENV{ID_MM_PORT_IGNORE}="1"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1107", ENV{.MM_USBIFNUM}=="0a", ENV{ID_MM_PORT_IGNORE}="1"

# LARA-R2 port types
#  ttyACM0 (if #0): primary
#  ttyACM1 (if #2): secondary
#  ttyACM2 (if #4): tertiary
#  ttyACM3 (if #6): GNSS Tunneling (ignore)
#  ttyACM4 (if #8): SIM Access Profile (ignore)
#  ttyACM5 (if #10): Primary Log for diagnostics (ignore)
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="110a", ENV{.MM_USBIFNUM}=="06", ENV{ID_MM_PORT_IGNORE}="1"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="110a", ENV{.MM_USBIFNUM}=="08", ENV{ID_MM_PORT_IGNORE}="1"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="110a", ENV{.MM_USBIFNUM}=="0a", ENV{ID_MM_PORT_IGNORE}="1"

# LISA-U2 / SARA-U2 port types
#  ttyACM0 (if #0): primary
#  ttyACM1 (if #2): secondary
#  ttyACM2 (if #4): tertiary
#  ttyACM3 (if #6): GNSS Tunneling (ignore)
#  ttyACM4 (if #8): Primary Log for diagnostics (ignore)
#  ttyACM5 (if #10): Secondary Log for diagnostics (ignore)
#  ttyACM6 (if #12): SAP (SIM Access Profile) (ignore)
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1102", ENV{.MM_USBIFNUM}=="06", ENV{ID_MM_PORT_IGNORE}="1"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1102", ENV{.MM_USBIFNUM}=="08", ENV{ID_MM_PORT_IGNORE}="1"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1102", ENV{.MM_USBIFNUM}=="0a", ENV{ID_MM_PORT_IGNORE}="1"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1102", ENV{.MM_USBIFNUM}=="0c", ENV{ID_MM_PORT_IGNORE}="1"

# LISA-U2 / SARA-U2 (alternative configuration) port types
#  ttyACM0 (if #0): primary
#  ttyACM1 (if #2): GNSS Tunneling (ignore)
#  ttyACM2 (if #4): Primary Log for diagnostics (ignore)
#  ttyACM3 (if #6): SAP (SIM Access Profile) (ignore)
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1104", ENV{.MM_USBIFNUM}=="02", ENV{ID_MM_PORT_IGNORE}="1"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1104", ENV{.MM_USBIFNUM}=="04", ENV{ID_MM_PORT_IGNORE}="1"
ATTRS{idVendor}=="1546", ATTRS{idProduct}=="1104", ENV{.MM_USBIFNUM}=="06", ENV{ID_MM_PORT_IGNORE}="1"

LABEL="mm_ublox_port_types_end"

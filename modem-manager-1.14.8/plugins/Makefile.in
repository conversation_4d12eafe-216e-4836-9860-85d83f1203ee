# Makefile.in generated by automake 1.16.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@



VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
@WITH_QMI_TRUE@am__append_1 = $(QMI_CFLAGS)
@WITH_QMI_TRUE@am__append_2 = $(QMI_LIBS)
@WITH_MBIM_TRUE@am__append_3 = $(MBIM_CFLAGS)
@WITH_MBIM_TRUE@am__append_4 = $(MBIM_LIBS)
noinst_PROGRAMS = $(am__EXEEXT_1) $(am__EXEEXT_2) $(am__EXEEXT_3) \
	$(am__EXEEXT_4) $(am__EXEEXT_5) $(am__EXEEXT_6) \
	$(am__EXEEXT_7) $(am__EXEEXT_8) $(am__EXEEXT_9) \
	$(am__EXEEXT_10) $(am__EXEEXT_11) $(am__EXEEXT_12) \
	$(am__EXEEXT_13) test-udev-rules$(EXEEXT) \
	test-keyfiles$(EXEEXT)

################################################################################
# common icera support
################################################################################
@WITH_SHARED_ICERA_TRUE@am__append_5 = libhelpers-icera.la
@WITH_SHARED_ICERA_TRUE@am__append_6 = test-modem-helpers-icera
@WITH_SHARED_ICERA_TRUE@am__append_7 = libmm-shared-icera.la

################################################################################
# common sierra support
################################################################################
@WITH_SHARED_SIERRA_TRUE@am__append_8 = libhelpers-sierra.la
@WITH_SHARED_SIERRA_TRUE@am__append_9 = test-modem-helpers-sierra
@WITH_SHARED_SIERRA_TRUE@am__append_10 = libmm-shared-sierra.la

################################################################################
# common option support
################################################################################
@WITH_SHARED_OPTION_TRUE@am__append_11 = libmm-shared-option.la

################################################################################
# common novatel support
################################################################################

# Common Novatel modem support library
@WITH_SHARED_NOVATEL_TRUE@am__append_12 = libmm-shared-novatel.la

################################################################################
# common xmm support
################################################################################
@WITH_SHARED_XMM_TRUE@am__append_13 = libhelpers-xmm.la
@WITH_SHARED_XMM_TRUE@am__append_14 = test-modem-helpers-xmm
@WITH_SHARED_XMM_TRUE@am__append_15 = libmm-shared-xmm.la
@WITH_MBIM_TRUE@@WITH_SHARED_XMM_TRUE@am__append_16 = \
@WITH_MBIM_TRUE@@WITH_SHARED_XMM_TRUE@	xmm/mm-broadband-modem-mbim-xmm.h \
@WITH_MBIM_TRUE@@WITH_SHARED_XMM_TRUE@	xmm/mm-broadband-modem-mbim-xmm.c \
@WITH_MBIM_TRUE@@WITH_SHARED_XMM_TRUE@	$(NULL)

@WITH_SHARED_TELIT_TRUE@am__append_17 = $(TELIT_ENUMS_GENERATED)
@WITH_SHARED_TELIT_TRUE@am__append_18 = $(TELIT_ENUMS_GENERATED)
@WITH_SHARED_TELIT_TRUE@am__append_19 = libhelpers-telit.la
@WITH_SHARED_TELIT_TRUE@am__append_20 = test-modem-helpers-telit

# Common telit modem support library
@WITH_SHARED_TELIT_TRUE@am__append_21 = libmm-shared-telit.la
@WITH_MBIM_TRUE@@WITH_SHARED_TELIT_TRUE@am__append_22 = \
@WITH_MBIM_TRUE@@WITH_SHARED_TELIT_TRUE@	telit/mm-broadband-modem-mbim-telit.h \
@WITH_MBIM_TRUE@@WITH_SHARED_TELIT_TRUE@	telit/mm-broadband-modem-mbim-telit.c \
@WITH_MBIM_TRUE@@WITH_SHARED_TELIT_TRUE@	$(NULL)


################################################################################
# common foxconn support
################################################################################

# Common Foxconn modem support library (MBIM only)
@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@am__append_23 = libmm-shared-foxconn.la

################################################################################
# plugin: generic
################################################################################
@ENABLE_PLUGIN_GENERIC_TRUE@am__append_24 = libmm-plugin-generic.la
@ENABLE_PLUGIN_GENERIC_TRUE@am__append_25 = test-service-generic

################################################################################
# plugin: altair lte
################################################################################
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@am__append_26 = libhelpers-altair-lte.la
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@am__append_27 = test-modem-helpers-altair-lte
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@am__append_28 = libmm-plugin-altair-lte.la

################################################################################
# plugin: anydata
################################################################################
@ENABLE_PLUGIN_ANYDATA_TRUE@am__append_29 = libmm-plugin-anydata.la

################################################################################
# plugin: broadmobi
################################################################################
@ENABLE_PLUGIN_BROADMOBI_TRUE@am__append_30 = libmm-plugin-broadmobi.la
@ENABLE_PLUGIN_BROADMOBI_TRUE@am__append_31 = broadmobi/77-mm-broadmobi-port-types.rules
@ENABLE_PLUGIN_BROADMOBI_TRUE@am__append_32 = -DTESTUDEVRULESDIR_BROADMOBI=\"${srcdir}/broadmobi\"

################################################################################
# plugin: cinterion (previously siemens)
################################################################################
@ENABLE_PLUGIN_CINTERION_TRUE@am__append_33 = libhelpers-cinterion.la
@ENABLE_PLUGIN_CINTERION_TRUE@am__append_34 = test-modem-helpers-cinterion
@ENABLE_PLUGIN_CINTERION_TRUE@am__append_35 = libmm-plugin-cinterion.la
@ENABLE_PLUGIN_CINTERION_TRUE@@WITH_QMI_TRUE@am__append_36 = \
@ENABLE_PLUGIN_CINTERION_TRUE@@WITH_QMI_TRUE@	cinterion/mm-broadband-modem-qmi-cinterion.c \
@ENABLE_PLUGIN_CINTERION_TRUE@@WITH_QMI_TRUE@	cinterion/mm-broadband-modem-qmi-cinterion.h \
@ENABLE_PLUGIN_CINTERION_TRUE@@WITH_QMI_TRUE@	$(NULL)

@ENABLE_PLUGIN_CINTERION_TRUE@am__append_37 = cinterion/77-mm-cinterion-port-types.rules
@ENABLE_PLUGIN_CINTERION_TRUE@am__append_38 = -DTESTUDEVRULESDIR_CINTERION=\"${srcdir}/cinterion\"

################################################################################
# plugin: dell (novatel, sierra, telit or foxconn)
################################################################################
@ENABLE_PLUGIN_DELL_TRUE@am__append_39 = libmm-plugin-dell.la
@ENABLE_PLUGIN_DELL_TRUE@am__append_40 = dell/77-mm-dell-port-types.rules
@ENABLE_PLUGIN_DELL_TRUE@am__append_41 = \
@ENABLE_PLUGIN_DELL_TRUE@	-DTESTUDEVRULESDIR_DELL=\"${srcdir}/dell\" \
@ENABLE_PLUGIN_DELL_TRUE@	$(NULL)


################################################################################
# plugin: dlink
################################################################################
@ENABLE_PLUGIN_DLINK_TRUE@am__append_42 = libmm-plugin-dlink.la
@ENABLE_PLUGIN_DLINK_TRUE@am__append_43 = dlink/77-mm-dlink-port-types.rules
@ENABLE_PLUGIN_DLINK_TRUE@am__append_44 = -DTESTUDEVRULESDIR_DLINK=\"${srcdir}/dlink\"

################################################################################
# plugin: fibocom
################################################################################

#if 0

#pkglib_LTLIBRARIES += libmm-plugin-fibocom.la
#libmm_plugin_fibocom_la_SOURCES = \
#	fibocom/mm-plugin-fibocom.c \
#	fibocom/mm-plugin-fibocom.h \
#	$(NULL)
#libmm_plugin_fibocom_la_CPPFLAGS = \
#	$(PLUGIN_COMMON_COMPILER_FLAGS) \
#	$(XMM_COMMON_COMPILER_FLAGS) \
#	-DMM_MODULE_NAME=\"fibocom\" \
#	$(NULL)
#libmm_plugin_fibocom_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)

#dist_udevrules_DATA += fibocom/77-mm-fibocom-port-types.rules

#AM_CFLAGS += -DTESTUDEVRULESDIR_FIBOCOM=\"${srcdir}/fibocom\"

#endif

################################################################################
# plugin: foxconn
################################################################################
@ENABLE_PLUGIN_FOXCONN_TRUE@am__append_45 = libmm-plugin-foxconn.la
@ENABLE_PLUGIN_FOXCONN_TRUE@am__append_46 = foxconn/77-mm-foxconn-port-types.rules
@ENABLE_PLUGIN_FOXCONN_TRUE@am__append_47 = foxconn/mm-foxconn-t77w968-carrier-mapping.conf
@ENABLE_PLUGIN_FOXCONN_TRUE@am__append_48 = \
@ENABLE_PLUGIN_FOXCONN_TRUE@	-DTESTUDEVRULESDIR_FOXCONN=\"${srcdir}/foxconn\" \
@ENABLE_PLUGIN_FOXCONN_TRUE@	-DTESTKEYFILE_FOXCONN_T77W968=\"${srcdir}/foxconn/mm-foxconn-t77w968-carrier-mapping.conf\" \
@ENABLE_PLUGIN_FOXCONN_TRUE@	$(NULL)


################################################################################
# plugin: haier
################################################################################
@ENABLE_PLUGIN_HAIER_TRUE@am__append_49 = libmm-plugin-haier.la
@ENABLE_PLUGIN_HAIER_TRUE@am__append_50 = haier/77-mm-haier-port-types.rules
@ENABLE_PLUGIN_HAIER_TRUE@am__append_51 = -DTESTUDEVRULESDIR_HAIER=\"${srcdir}/haier\"

################################################################################
# plugin: huawei
################################################################################
@ENABLE_PLUGIN_HUAWEI_TRUE@am__append_52 = libhelpers-huawei.la
@ENABLE_PLUGIN_HUAWEI_TRUE@am__append_53 = test-modem-helpers-huawei
@ENABLE_PLUGIN_HUAWEI_TRUE@am__append_54 = libmm-plugin-huawei.la
@ENABLE_PLUGIN_HUAWEI_TRUE@am__append_55 = huawei/77-mm-huawei-net-port-types.rules
@ENABLE_PLUGIN_HUAWEI_TRUE@am__append_56 = -DTESTUDEVRULESDIR_HUAWEI=\"${srcdir}/huawei\"

################################################################################
# plugin: iridium
################################################################################
@ENABLE_PLUGIN_IRIDIUM_TRUE@am__append_57 = libmm-plugin-iridium.la

################################################################################
# plugin: linktop
################################################################################
@ENABLE_PLUGIN_LINKTOP_TRUE@am__append_58 = libhelpers-linktop.la
@ENABLE_PLUGIN_LINKTOP_TRUE@am__append_59 = test-modem-helpers-linktop
@ENABLE_PLUGIN_LINKTOP_TRUE@am__append_60 = libmm-plugin-linktop.la

################################################################################
# plugin: longcheer (and rebranded dongles)
################################################################################
@ENABLE_PLUGIN_LONGCHEER_TRUE@am__append_61 = libmm-plugin-longcheer.la
@ENABLE_PLUGIN_LONGCHEER_TRUE@am__append_62 = longcheer/77-mm-longcheer-port-types.rules
@ENABLE_PLUGIN_LONGCHEER_TRUE@am__append_63 = -DTESTUDEVRULESDIR_LONGCHEER=\"${srcdir}/longcheer\"

################################################################################
# plugin: ericsson mbm
################################################################################
@ENABLE_PLUGIN_MBM_TRUE@am__append_64 = libhelpers-mbm.la
@ENABLE_PLUGIN_MBM_TRUE@am__append_65 = test-modem-helpers-mbm
@ENABLE_PLUGIN_MBM_TRUE@am__append_66 = libmm-plugin-ericsson-mbm.la
@ENABLE_PLUGIN_MBM_TRUE@am__append_67 = mbm/77-mm-ericsson-mbm.rules
@ENABLE_PLUGIN_MBM_TRUE@am__append_68 = -DTESTUDEVRULESDIR_MBM=\"${srcdir}/mbm\"

################################################################################
# plugin: motorola
################################################################################
@ENABLE_PLUGIN_MOTOROLA_TRUE@am__append_69 = libmm-plugin-motorola.la

################################################################################
# plugin: mtk
################################################################################
@ENABLE_PLUGIN_MTK_TRUE@am__append_70 = libmm-plugin-mtk.la
@ENABLE_PLUGIN_MTK_TRUE@am__append_71 = mtk/77-mm-mtk-port-types.rules
@ENABLE_PLUGIN_MTK_TRUE@am__append_72 = -DTESTUDEVRULESDIR_MTK=\"${srcdir}/mtk\"

################################################################################
# plugin: nokia
################################################################################
@ENABLE_PLUGIN_NOKIA_TRUE@am__append_73 = libmm-plugin-nokia.la

################################################################################
# plugin: nokia (icera)
################################################################################
@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@am__append_74 = libmm-plugin-nokia-icera.la
@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@am__append_75 = nokia/77-mm-nokia-port-types.rules
@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@am__append_76 = -DTESTUDEVRULESDIR_NOKIA_ICERA=\"${srcdir}/nokia\"

################################################################################
# plugin: novatel non-lte
################################################################################
@ENABLE_PLUGIN_NOVATEL_TRUE@am__append_77 = libmm-plugin-novatel.la

################################################################################
# plugin: novatel lte
################################################################################
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@am__append_78 = libmm-plugin-novatel-lte.la

################################################################################
# plugin: option
################################################################################
@ENABLE_PLUGIN_OPTION_TRUE@am__append_79 = libmm-plugin-option.la

################################################################################
# plugin: option hso
################################################################################
@ENABLE_PLUGIN_OPTION_HSO_TRUE@am__append_80 = libmm-plugin-option-hso.la

################################################################################
# plugin: pantech
################################################################################
@ENABLE_PLUGIN_PANTECH_TRUE@am__append_81 = libmm-plugin-pantech.la

################################################################################
# plugin: quectel
################################################################################
@ENABLE_PLUGIN_QUECTEL_TRUE@am__append_82 = libmm-plugin-quectel.la
@ENABLE_PLUGIN_QUECTEL_TRUE@@WITH_QMI_TRUE@am__append_83 = \
@ENABLE_PLUGIN_QUECTEL_TRUE@@WITH_QMI_TRUE@	quectel/mm-broadband-modem-qmi-quectel.c \
@ENABLE_PLUGIN_QUECTEL_TRUE@@WITH_QMI_TRUE@	quectel/mm-broadband-modem-qmi-quectel.h \
@ENABLE_PLUGIN_QUECTEL_TRUE@@WITH_QMI_TRUE@	$(NULL)

@ENABLE_PLUGIN_QUECTEL_TRUE@am__append_84 = quectel/77-mm-quectel-port-types.rules
@ENABLE_PLUGIN_QUECTEL_TRUE@am__append_85 = -DTESTUDEVRULESDIR_QUECTEL=\"${srcdir}/quectel\"

################################################################################
# plugin: samsung
################################################################################
@ENABLE_PLUGIN_SAMSUNG_TRUE@am__append_86 = libmm-plugin-samsung.la

################################################################################
# plugin: sierra (legacy)
################################################################################
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@am__append_87 = libmm-plugin-sierra-legacy.la

################################################################################
# plugin: sierra (new QMI or MBIM modems)
################################################################################
@ENABLE_PLUGIN_SIERRA_TRUE@am__append_88 = sierra/77-mm-sierra.rules
@ENABLE_PLUGIN_SIERRA_TRUE@am__append_89 = libmm-plugin-sierra.la

################################################################################
# plugin: simtech
################################################################################
@ENABLE_PLUGIN_SIMTECH_TRUE@am__append_90 = libhelpers-simtech.la
@ENABLE_PLUGIN_SIMTECH_TRUE@am__append_91 = test-modem-helpers-simtech
@ENABLE_PLUGIN_SIMTECH_TRUE@am__append_92 = libmm-plugin-simtech.la
@ENABLE_PLUGIN_SIMTECH_TRUE@@WITH_QMI_TRUE@am__append_93 = \
@ENABLE_PLUGIN_SIMTECH_TRUE@@WITH_QMI_TRUE@	simtech/mm-broadband-modem-qmi-simtech.c \
@ENABLE_PLUGIN_SIMTECH_TRUE@@WITH_QMI_TRUE@	simtech/mm-broadband-modem-qmi-simtech.h \
@ENABLE_PLUGIN_SIMTECH_TRUE@@WITH_QMI_TRUE@	$(NULL)

@ENABLE_PLUGIN_SIMTECH_TRUE@am__append_94 = simtech/77-mm-simtech-port-types.rules
@ENABLE_PLUGIN_SIMTECH_TRUE@am__append_95 = -DTESTUDEVRULESDIR_SIMTECH=\"${srcdir}/simtech\"

################################################################################
# plugin: telit
################################################################################
@ENABLE_PLUGIN_TELIT_TRUE@am__append_96 = libmm-plugin-telit.la
@ENABLE_PLUGIN_TELIT_TRUE@am__append_97 = telit/77-mm-telit-port-types.rules
@ENABLE_PLUGIN_TELIT_TRUE@am__append_98 = -DTESTUDEVRULESDIR_TELIT=\"${srcdir}/telit\"

################################################################################
# plugin: thuraya xt
################################################################################
@ENABLE_PLUGIN_THURAYA_TRUE@am__append_99 = libhelpers-thuraya.la
@ENABLE_PLUGIN_THURAYA_TRUE@am__append_100 = test-modem-helpers-thuraya
@ENABLE_PLUGIN_THURAYA_TRUE@am__append_101 = libmm-plugin-thuraya.la

################################################################################
# plugin: tplink
################################################################################
@ENABLE_PLUGIN_TPLINK_TRUE@am__append_102 = libmm-plugin-tplink.la
@ENABLE_PLUGIN_TPLINK_TRUE@am__append_103 = tplink/77-mm-tplink-port-types.rules
@ENABLE_PLUGIN_TPLINK_TRUE@am__append_104 = -DTESTUDEVRULESDIR_TPLINK=\"${srcdir}/tplink\"

################################################################################
# plugin: u-blox
################################################################################
@ENABLE_PLUGIN_UBLOX_TRUE@am__append_105 = ublox/77-mm-ublox-port-types.rules
@ENABLE_PLUGIN_UBLOX_TRUE@am__append_106 = libhelpers-ublox.la
@ENABLE_PLUGIN_UBLOX_TRUE@am__append_107 = $(UBLOX_ENUMS_GENERATED)
@ENABLE_PLUGIN_UBLOX_TRUE@am__append_108 = $(UBLOX_ENUMS_GENERATED)
@ENABLE_PLUGIN_UBLOX_TRUE@am__append_109 = test-modem-helpers-ublox
@ENABLE_PLUGIN_UBLOX_TRUE@am__append_110 = libmm-plugin-ublox.la

################################################################################
# plugin: via
################################################################################
@ENABLE_PLUGIN_VIA_TRUE@am__append_111 = libmm-plugin-via.la

################################################################################
# plugin: wavecom (now sierra airlink)
################################################################################
@ENABLE_PLUGIN_WAVECOM_TRUE@am__append_112 = libmm-plugin-wavecom.la

################################################################################
# plugin: alcatel/TCT/JRD x220D and possibly others
################################################################################
@ENABLE_PLUGIN_X22X_TRUE@am__append_113 = libmm-plugin-x22x.la
@ENABLE_PLUGIN_X22X_TRUE@am__append_114 = x22x/77-mm-x22x-port-types.rules
@ENABLE_PLUGIN_X22X_TRUE@am__append_115 = -DTESTUDEVRULESDIR_X22X=\"${srcdir}/x22x\"

################################################################################
# plugin: zte
################################################################################
@ENABLE_PLUGIN_ZTE_TRUE@am__append_116 = libmm-plugin-zte.la
@ENABLE_PLUGIN_ZTE_TRUE@am__append_117 = zte/77-mm-zte-port-types.rules
@ENABLE_PLUGIN_ZTE_TRUE@am__append_118 = -DTESTUDEVRULESDIR_ZTE=\"${srcdir}/zte\"
@WITH_MBIM_TRUE@am__append_119 = \
@WITH_MBIM_TRUE@	NL668/mm-broadband-modem-mbim-xmm-fibocom.c \
@WITH_MBIM_TRUE@	NL668/mm-broadband-modem-mbim-xmm-fibocom.h \
@WITH_MBIM_TRUE@	NL668/mm-broadband-modem-mbim-fibocom.c \
@WITH_MBIM_TRUE@	NL668/mm-broadband-modem-mbim-fibocom.h \
@WITH_MBIM_TRUE@	$(NULL)

subdir = plugins
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_code_coverage.m4 \
	$(top_srcdir)/m4/gettext.m4 $(top_srcdir)/m4/gtk-doc.m4 \
	$(top_srcdir)/m4/iconv.m4 $(top_srcdir)/m4/intlmacosx.m4 \
	$(top_srcdir)/m4/introspection.m4 $(top_srcdir)/m4/lib-ld.m4 \
	$(top_srcdir)/m4/lib-link.m4 $(top_srcdir)/m4/lib-prefix.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/mm-enable-plugin.m4 $(top_srcdir)/m4/nls.m4 \
	$(top_srcdir)/m4/po.m4 $(top_srcdir)/m4/progtest.m4 \
	$(top_srcdir)/m4/vapigen.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__dist_pkgdata_DATA_DIST) \
	$(am__dist_udevrules_DATA_DIST) $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
@WITH_SHARED_ICERA_TRUE@am__EXEEXT_1 =  \
@WITH_SHARED_ICERA_TRUE@	test-modem-helpers-icera$(EXEEXT)
@WITH_SHARED_SIERRA_TRUE@am__EXEEXT_2 =  \
@WITH_SHARED_SIERRA_TRUE@	test-modem-helpers-sierra$(EXEEXT)
@WITH_SHARED_XMM_TRUE@am__EXEEXT_3 = test-modem-helpers-xmm$(EXEEXT)
@WITH_SHARED_TELIT_TRUE@am__EXEEXT_4 =  \
@WITH_SHARED_TELIT_TRUE@	test-modem-helpers-telit$(EXEEXT)
@ENABLE_PLUGIN_GENERIC_TRUE@am__EXEEXT_5 =  \
@ENABLE_PLUGIN_GENERIC_TRUE@	test-service-generic$(EXEEXT)
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@am__EXEEXT_6 = test-modem-helpers-altair-lte$(EXEEXT)
@ENABLE_PLUGIN_CINTERION_TRUE@am__EXEEXT_7 = test-modem-helpers-cinterion$(EXEEXT)
@ENABLE_PLUGIN_HUAWEI_TRUE@am__EXEEXT_8 =  \
@ENABLE_PLUGIN_HUAWEI_TRUE@	test-modem-helpers-huawei$(EXEEXT)
@ENABLE_PLUGIN_LINKTOP_TRUE@am__EXEEXT_9 = test-modem-helpers-linktop$(EXEEXT)
@ENABLE_PLUGIN_MBM_TRUE@am__EXEEXT_10 =  \
@ENABLE_PLUGIN_MBM_TRUE@	test-modem-helpers-mbm$(EXEEXT)
@ENABLE_PLUGIN_SIMTECH_TRUE@am__EXEEXT_11 = test-modem-helpers-simtech$(EXEEXT)
@ENABLE_PLUGIN_THURAYA_TRUE@am__EXEEXT_12 = test-modem-helpers-thuraya$(EXEEXT)
@ENABLE_PLUGIN_UBLOX_TRUE@am__EXEEXT_13 =  \
@ENABLE_PLUGIN_UBLOX_TRUE@	test-modem-helpers-ublox$(EXEEXT)
PROGRAMS = $(noinst_PROGRAMS)
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(pkglibdir)" "$(DESTDIR)$(pkgdatadir)" \
	"$(DESTDIR)$(udevrulesdir)"
LTLIBRARIES = $(noinst_LTLIBRARIES) $(pkglib_LTLIBRARIES)
libhelpers_altair_lte_la_LIBADD =
am__libhelpers_altair_lte_la_SOURCES_DIST =  \
	altair/mm-modem-helpers-altair-lte.c \
	altair/mm-modem-helpers-altair-lte.h
am__dirstamp = $(am__leading_dot)dirstamp
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@am_libhelpers_altair_lte_la_OBJECTS = altair/libhelpers_altair_lte_la-mm-modem-helpers-altair-lte.lo
libhelpers_altair_lte_la_OBJECTS =  \
	$(am_libhelpers_altair_lte_la_OBJECTS)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@am_libhelpers_altair_lte_la_rpath =
libhelpers_cinterion_la_LIBADD =
am__libhelpers_cinterion_la_SOURCES_DIST =  \
	cinterion/mm-modem-helpers-cinterion.c \
	cinterion/mm-modem-helpers-cinterion.h
@ENABLE_PLUGIN_CINTERION_TRUE@am_libhelpers_cinterion_la_OBJECTS = cinterion/libhelpers_cinterion_la-mm-modem-helpers-cinterion.lo
libhelpers_cinterion_la_OBJECTS =  \
	$(am_libhelpers_cinterion_la_OBJECTS)
@ENABLE_PLUGIN_CINTERION_TRUE@am_libhelpers_cinterion_la_rpath =
libhelpers_huawei_la_LIBADD =
am__libhelpers_huawei_la_SOURCES_DIST =  \
	huawei/mm-modem-helpers-huawei.c \
	huawei/mm-modem-helpers-huawei.h
@ENABLE_PLUGIN_HUAWEI_TRUE@am_libhelpers_huawei_la_OBJECTS = huawei/libhelpers_huawei_la-mm-modem-helpers-huawei.lo
libhelpers_huawei_la_OBJECTS = $(am_libhelpers_huawei_la_OBJECTS)
@ENABLE_PLUGIN_HUAWEI_TRUE@am_libhelpers_huawei_la_rpath =
libhelpers_icera_la_LIBADD =
am__libhelpers_icera_la_SOURCES_DIST = icera/mm-modem-helpers-icera.c \
	icera/mm-modem-helpers-icera.h
@WITH_SHARED_ICERA_TRUE@am_libhelpers_icera_la_OBJECTS = icera/libhelpers_icera_la-mm-modem-helpers-icera.lo
libhelpers_icera_la_OBJECTS = $(am_libhelpers_icera_la_OBJECTS)
@WITH_SHARED_ICERA_TRUE@am_libhelpers_icera_la_rpath =
libhelpers_linktop_la_LIBADD =
am__libhelpers_linktop_la_SOURCES_DIST =  \
	linktop/mm-modem-helpers-linktop.c \
	linktop/mm-modem-helpers-linktop.h
@ENABLE_PLUGIN_LINKTOP_TRUE@am_libhelpers_linktop_la_OBJECTS = linktop/libhelpers_linktop_la-mm-modem-helpers-linktop.lo
libhelpers_linktop_la_OBJECTS = $(am_libhelpers_linktop_la_OBJECTS)
@ENABLE_PLUGIN_LINKTOP_TRUE@am_libhelpers_linktop_la_rpath =
libhelpers_mbm_la_LIBADD =
am__libhelpers_mbm_la_SOURCES_DIST = mbm/mm-modem-helpers-mbm.c \
	mbm/mm-modem-helpers-mbm.h
@ENABLE_PLUGIN_MBM_TRUE@am_libhelpers_mbm_la_OBJECTS = mbm/libhelpers_mbm_la-mm-modem-helpers-mbm.lo
libhelpers_mbm_la_OBJECTS = $(am_libhelpers_mbm_la_OBJECTS)
@ENABLE_PLUGIN_MBM_TRUE@am_libhelpers_mbm_la_rpath =
libhelpers_sierra_la_LIBADD =
am__libhelpers_sierra_la_SOURCES_DIST =  \
	sierra/mm-modem-helpers-sierra.c \
	sierra/mm-modem-helpers-sierra.h
@WITH_SHARED_SIERRA_TRUE@am_libhelpers_sierra_la_OBJECTS = sierra/libhelpers_sierra_la-mm-modem-helpers-sierra.lo
libhelpers_sierra_la_OBJECTS = $(am_libhelpers_sierra_la_OBJECTS)
@WITH_SHARED_SIERRA_TRUE@am_libhelpers_sierra_la_rpath =
libhelpers_simtech_la_LIBADD =
am__libhelpers_simtech_la_SOURCES_DIST =  \
	simtech/mm-modem-helpers-simtech.c \
	simtech/mm-modem-helpers-simtech.h
@ENABLE_PLUGIN_SIMTECH_TRUE@am_libhelpers_simtech_la_OBJECTS = simtech/libhelpers_simtech_la-mm-modem-helpers-simtech.lo
libhelpers_simtech_la_OBJECTS = $(am_libhelpers_simtech_la_OBJECTS)
@ENABLE_PLUGIN_SIMTECH_TRUE@am_libhelpers_simtech_la_rpath =
libhelpers_telit_la_LIBADD =
am__libhelpers_telit_la_SOURCES_DIST = telit/mm-modem-helpers-telit.c \
	telit/mm-modem-helpers-telit.h
@WITH_SHARED_TELIT_TRUE@am_libhelpers_telit_la_OBJECTS = telit/libhelpers_telit_la-mm-modem-helpers-telit.lo
@WITH_SHARED_TELIT_TRUE@am__objects_1 = telit/libhelpers_telit_la-mm-telit-enums-types.lo
@WITH_SHARED_TELIT_TRUE@nodist_libhelpers_telit_la_OBJECTS =  \
@WITH_SHARED_TELIT_TRUE@	$(am__objects_1)
libhelpers_telit_la_OBJECTS = $(am_libhelpers_telit_la_OBJECTS) \
	$(nodist_libhelpers_telit_la_OBJECTS)
@WITH_SHARED_TELIT_TRUE@am_libhelpers_telit_la_rpath =
libhelpers_thuraya_la_LIBADD =
am__libhelpers_thuraya_la_SOURCES_DIST =  \
	thuraya/mm-modem-helpers-thuraya.c \
	thuraya/mm-modem-helpers-thuraya.h
@ENABLE_PLUGIN_THURAYA_TRUE@am_libhelpers_thuraya_la_OBJECTS = thuraya/libhelpers_thuraya_la-mm-modem-helpers-thuraya.lo
libhelpers_thuraya_la_OBJECTS = $(am_libhelpers_thuraya_la_OBJECTS)
@ENABLE_PLUGIN_THURAYA_TRUE@am_libhelpers_thuraya_la_rpath =
libhelpers_ublox_la_LIBADD =
am__libhelpers_ublox_la_SOURCES_DIST = ublox/mm-modem-helpers-ublox.c \
	ublox/mm-modem-helpers-ublox.h
@ENABLE_PLUGIN_UBLOX_TRUE@am_libhelpers_ublox_la_OBJECTS = ublox/libhelpers_ublox_la-mm-modem-helpers-ublox.lo
@ENABLE_PLUGIN_UBLOX_TRUE@am__objects_2 = ublox/libhelpers_ublox_la-mm-ublox-enums-types.lo
@ENABLE_PLUGIN_UBLOX_TRUE@nodist_libhelpers_ublox_la_OBJECTS =  \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(am__objects_2)
libhelpers_ublox_la_OBJECTS = $(am_libhelpers_ublox_la_OBJECTS) \
	$(nodist_libhelpers_ublox_la_OBJECTS)
@ENABLE_PLUGIN_UBLOX_TRUE@am_libhelpers_ublox_la_rpath =
libhelpers_xmm_la_LIBADD =
am__libhelpers_xmm_la_SOURCES_DIST = xmm/mm-modem-helpers-xmm.c \
	xmm/mm-modem-helpers-xmm.h
@WITH_SHARED_XMM_TRUE@am_libhelpers_xmm_la_OBJECTS = xmm/libhelpers_xmm_la-mm-modem-helpers-xmm.lo
libhelpers_xmm_la_OBJECTS = $(am_libhelpers_xmm_la_OBJECTS)
@WITH_SHARED_XMM_TRUE@am_libhelpers_xmm_la_rpath =
libmm_plugin_NL668_la_LIBADD =
am__libmm_plugin_NL668_la_SOURCES_DIST =  \
	NL668/mm-broadband-bearer-fibocom-ecm.c \
	NL668/mm-broadband-bearer-fibocom-ecm.h \
	NL668/mm-broadband-modem-fibocom.c \
	NL668/mm-broadband-modem-fibocom.h NL668/mm-plugin-fibocom.c \
	NL668/mm-plugin-fibocom.h \
	NL668/mm-broadband-modem-mbim-xmm-fibocom.c \
	NL668/mm-broadband-modem-mbim-xmm-fibocom.h \
	NL668/mm-broadband-modem-mbim-fibocom.c \
	NL668/mm-broadband-modem-mbim-fibocom.h
@WITH_MBIM_TRUE@am__objects_3 = NL668/libmm_plugin_NL668_la-mm-broadband-modem-mbim-xmm-fibocom.lo \
@WITH_MBIM_TRUE@	NL668/libmm_plugin_NL668_la-mm-broadband-modem-mbim-fibocom.lo
am_libmm_plugin_NL668_la_OBJECTS = NL668/libmm_plugin_NL668_la-mm-broadband-bearer-fibocom-ecm.lo \
	NL668/libmm_plugin_NL668_la-mm-broadband-modem-fibocom.lo \
	NL668/libmm_plugin_NL668_la-mm-plugin-fibocom.lo \
	$(am__objects_3)
libmm_plugin_NL668_la_OBJECTS = $(am_libmm_plugin_NL668_la_OBJECTS)
libmm_plugin_NL668_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_NL668_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@libmm_plugin_altair_lte_la_DEPENDENCIES = $(builddir)/libhelpers-altair-lte.la
am__libmm_plugin_altair_lte_la_SOURCES_DIST =  \
	altair/mm-plugin-altair-lte.c altair/mm-plugin-altair-lte.h \
	altair/mm-broadband-modem-altair-lte.c \
	altair/mm-broadband-modem-altair-lte.h \
	altair/mm-broadband-bearer-altair-lte.c \
	altair/mm-broadband-bearer-altair-lte.h
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@am_libmm_plugin_altair_lte_la_OBJECTS = altair/libmm_plugin_altair_lte_la-mm-plugin-altair-lte.lo \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	altair/libmm_plugin_altair_lte_la-mm-broadband-modem-altair-lte.lo \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	altair/libmm_plugin_altair_lte_la-mm-broadband-bearer-altair-lte.lo
libmm_plugin_altair_lte_la_OBJECTS =  \
	$(am_libmm_plugin_altair_lte_la_OBJECTS)
libmm_plugin_altair_lte_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_altair_lte_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@am_libmm_plugin_altair_lte_la_rpath =  \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	-rpath $(pkglibdir)
libmm_plugin_anydata_la_LIBADD =
am__libmm_plugin_anydata_la_SOURCES_DIST =  \
	anydata/mm-plugin-anydata.c anydata/mm-plugin-anydata.h \
	anydata/mm-broadband-modem-anydata.h \
	anydata/mm-broadband-modem-anydata.c
@ENABLE_PLUGIN_ANYDATA_TRUE@am_libmm_plugin_anydata_la_OBJECTS = anydata/libmm_plugin_anydata_la-mm-plugin-anydata.lo \
@ENABLE_PLUGIN_ANYDATA_TRUE@	anydata/libmm_plugin_anydata_la-mm-broadband-modem-anydata.lo
libmm_plugin_anydata_la_OBJECTS =  \
	$(am_libmm_plugin_anydata_la_OBJECTS)
libmm_plugin_anydata_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_anydata_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_ANYDATA_TRUE@am_libmm_plugin_anydata_la_rpath = -rpath \
@ENABLE_PLUGIN_ANYDATA_TRUE@	$(pkglibdir)
libmm_plugin_broadmobi_la_LIBADD =
am__libmm_plugin_broadmobi_la_SOURCES_DIST =  \
	broadmobi/mm-plugin-broadmobi.c \
	broadmobi/mm-plugin-broadmobi.h
@ENABLE_PLUGIN_BROADMOBI_TRUE@am_libmm_plugin_broadmobi_la_OBJECTS = broadmobi/libmm_plugin_broadmobi_la-mm-plugin-broadmobi.lo
libmm_plugin_broadmobi_la_OBJECTS =  \
	$(am_libmm_plugin_broadmobi_la_OBJECTS)
libmm_plugin_broadmobi_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_broadmobi_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_BROADMOBI_TRUE@am_libmm_plugin_broadmobi_la_rpath =  \
@ENABLE_PLUGIN_BROADMOBI_TRUE@	-rpath $(pkglibdir)
@ENABLE_PLUGIN_CINTERION_TRUE@libmm_plugin_cinterion_la_DEPENDENCIES = $(builddir)/libhelpers-cinterion.la
am__libmm_plugin_cinterion_la_SOURCES_DIST =  \
	cinterion/mm-plugin-cinterion.c \
	cinterion/mm-plugin-cinterion.h \
	cinterion/mm-shared-cinterion.c \
	cinterion/mm-shared-cinterion.h \
	cinterion/mm-broadband-modem-cinterion.c \
	cinterion/mm-broadband-modem-cinterion.h \
	cinterion/mm-broadband-bearer-cinterion.c \
	cinterion/mm-broadband-bearer-cinterion.h \
	cinterion/mm-broadband-modem-qmi-cinterion.c \
	cinterion/mm-broadband-modem-qmi-cinterion.h
@ENABLE_PLUGIN_CINTERION_TRUE@@WITH_QMI_TRUE@am__objects_4 = cinterion/libmm_plugin_cinterion_la-mm-broadband-modem-qmi-cinterion.lo
@ENABLE_PLUGIN_CINTERION_TRUE@am_libmm_plugin_cinterion_la_OBJECTS = cinterion/libmm_plugin_cinterion_la-mm-plugin-cinterion.lo \
@ENABLE_PLUGIN_CINTERION_TRUE@	cinterion/libmm_plugin_cinterion_la-mm-shared-cinterion.lo \
@ENABLE_PLUGIN_CINTERION_TRUE@	cinterion/libmm_plugin_cinterion_la-mm-broadband-modem-cinterion.lo \
@ENABLE_PLUGIN_CINTERION_TRUE@	cinterion/libmm_plugin_cinterion_la-mm-broadband-bearer-cinterion.lo \
@ENABLE_PLUGIN_CINTERION_TRUE@	$(am__objects_4)
libmm_plugin_cinterion_la_OBJECTS =  \
	$(am_libmm_plugin_cinterion_la_OBJECTS)
libmm_plugin_cinterion_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_cinterion_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_CINTERION_TRUE@am_libmm_plugin_cinterion_la_rpath =  \
@ENABLE_PLUGIN_CINTERION_TRUE@	-rpath $(pkglibdir)
libmm_plugin_dell_la_LIBADD =
am__libmm_plugin_dell_la_SOURCES_DIST = dell/mm-plugin-dell.c \
	dell/mm-plugin-dell.h
@ENABLE_PLUGIN_DELL_TRUE@am_libmm_plugin_dell_la_OBJECTS = dell/libmm_plugin_dell_la-mm-plugin-dell.lo
libmm_plugin_dell_la_OBJECTS = $(am_libmm_plugin_dell_la_OBJECTS)
libmm_plugin_dell_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_dell_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_DELL_TRUE@am_libmm_plugin_dell_la_rpath = -rpath \
@ENABLE_PLUGIN_DELL_TRUE@	$(pkglibdir)
libmm_plugin_dlink_la_LIBADD =
am__libmm_plugin_dlink_la_SOURCES_DIST = dlink/mm-plugin-dlink.c \
	dlink/mm-plugin-dlink.h
@ENABLE_PLUGIN_DLINK_TRUE@am_libmm_plugin_dlink_la_OBJECTS = dlink/libmm_plugin_dlink_la-mm-plugin-dlink.lo
libmm_plugin_dlink_la_OBJECTS = $(am_libmm_plugin_dlink_la_OBJECTS)
libmm_plugin_dlink_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_dlink_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_DLINK_TRUE@am_libmm_plugin_dlink_la_rpath = -rpath \
@ENABLE_PLUGIN_DLINK_TRUE@	$(pkglibdir)
@ENABLE_PLUGIN_MBM_TRUE@libmm_plugin_ericsson_mbm_la_DEPENDENCIES =  \
@ENABLE_PLUGIN_MBM_TRUE@	$(builddir)/libhelpers-mbm.la
am__libmm_plugin_ericsson_mbm_la_SOURCES_DIST =  \
	mbm/mm-broadband-modem-mbm.c mbm/mm-broadband-modem-mbm.h \
	mbm/mm-broadband-bearer-mbm.c mbm/mm-broadband-bearer-mbm.h \
	mbm/mm-sim-mbm.c mbm/mm-sim-mbm.h mbm/mm-plugin-mbm.c \
	mbm/mm-plugin-mbm.h
@ENABLE_PLUGIN_MBM_TRUE@am_libmm_plugin_ericsson_mbm_la_OBJECTS = mbm/libmm_plugin_ericsson_mbm_la-mm-broadband-modem-mbm.lo \
@ENABLE_PLUGIN_MBM_TRUE@	mbm/libmm_plugin_ericsson_mbm_la-mm-broadband-bearer-mbm.lo \
@ENABLE_PLUGIN_MBM_TRUE@	mbm/libmm_plugin_ericsson_mbm_la-mm-sim-mbm.lo \
@ENABLE_PLUGIN_MBM_TRUE@	mbm/libmm_plugin_ericsson_mbm_la-mm-plugin-mbm.lo
libmm_plugin_ericsson_mbm_la_OBJECTS =  \
	$(am_libmm_plugin_ericsson_mbm_la_OBJECTS)
libmm_plugin_ericsson_mbm_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_ericsson_mbm_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_MBM_TRUE@am_libmm_plugin_ericsson_mbm_la_rpath =  \
@ENABLE_PLUGIN_MBM_TRUE@	-rpath $(pkglibdir)
libmm_plugin_foxconn_la_LIBADD =
am__libmm_plugin_foxconn_la_SOURCES_DIST =  \
	foxconn/mm-plugin-foxconn.c foxconn/mm-plugin-foxconn.h
@ENABLE_PLUGIN_FOXCONN_TRUE@am_libmm_plugin_foxconn_la_OBJECTS = foxconn/libmm_plugin_foxconn_la-mm-plugin-foxconn.lo
libmm_plugin_foxconn_la_OBJECTS =  \
	$(am_libmm_plugin_foxconn_la_OBJECTS)
libmm_plugin_foxconn_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_foxconn_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_FOXCONN_TRUE@am_libmm_plugin_foxconn_la_rpath = -rpath \
@ENABLE_PLUGIN_FOXCONN_TRUE@	$(pkglibdir)
libmm_plugin_generic_la_LIBADD =
am__libmm_plugin_generic_la_SOURCES_DIST =  \
	generic/mm-plugin-generic.c generic/mm-plugin-generic.h
@ENABLE_PLUGIN_GENERIC_TRUE@am_libmm_plugin_generic_la_OBJECTS = generic/libmm_plugin_generic_la-mm-plugin-generic.lo
libmm_plugin_generic_la_OBJECTS =  \
	$(am_libmm_plugin_generic_la_OBJECTS)
libmm_plugin_generic_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_generic_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_GENERIC_TRUE@am_libmm_plugin_generic_la_rpath = -rpath \
@ENABLE_PLUGIN_GENERIC_TRUE@	$(pkglibdir)
libmm_plugin_haier_la_LIBADD =
am__libmm_plugin_haier_la_SOURCES_DIST = haier/mm-plugin-haier.c \
	haier/mm-plugin-haier.h
@ENABLE_PLUGIN_HAIER_TRUE@am_libmm_plugin_haier_la_OBJECTS = haier/libmm_plugin_haier_la-mm-plugin-haier.lo
libmm_plugin_haier_la_OBJECTS = $(am_libmm_plugin_haier_la_OBJECTS)
libmm_plugin_haier_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_haier_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_HAIER_TRUE@am_libmm_plugin_haier_la_rpath = -rpath \
@ENABLE_PLUGIN_HAIER_TRUE@	$(pkglibdir)
@ENABLE_PLUGIN_HUAWEI_TRUE@libmm_plugin_huawei_la_DEPENDENCIES =  \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(builddir)/libhelpers-huawei.la
am__libmm_plugin_huawei_la_SOURCES_DIST = huawei/mm-plugin-huawei.c \
	huawei/mm-plugin-huawei.h huawei/mm-sim-huawei.c \
	huawei/mm-sim-huawei.h huawei/mm-broadband-modem-huawei.c \
	huawei/mm-broadband-modem-huawei.h \
	huawei/mm-broadband-bearer-huawei.c \
	huawei/mm-broadband-bearer-huawei.h
@ENABLE_PLUGIN_HUAWEI_TRUE@am_libmm_plugin_huawei_la_OBJECTS = huawei/libmm_plugin_huawei_la-mm-plugin-huawei.lo \
@ENABLE_PLUGIN_HUAWEI_TRUE@	huawei/libmm_plugin_huawei_la-mm-sim-huawei.lo \
@ENABLE_PLUGIN_HUAWEI_TRUE@	huawei/libmm_plugin_huawei_la-mm-broadband-modem-huawei.lo \
@ENABLE_PLUGIN_HUAWEI_TRUE@	huawei/libmm_plugin_huawei_la-mm-broadband-bearer-huawei.lo
libmm_plugin_huawei_la_OBJECTS = $(am_libmm_plugin_huawei_la_OBJECTS)
libmm_plugin_huawei_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_huawei_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_HUAWEI_TRUE@am_libmm_plugin_huawei_la_rpath = -rpath \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(pkglibdir)
libmm_plugin_iridium_la_LIBADD =
am__libmm_plugin_iridium_la_SOURCES_DIST =  \
	iridium/mm-plugin-iridium.c iridium/mm-plugin-iridium.h \
	iridium/mm-broadband-modem-iridium.c \
	iridium/mm-broadband-modem-iridium.h \
	iridium/mm-bearer-iridium.c iridium/mm-bearer-iridium.h \
	iridium/mm-sim-iridium.c iridium/mm-sim-iridium.h
@ENABLE_PLUGIN_IRIDIUM_TRUE@am_libmm_plugin_iridium_la_OBJECTS = iridium/libmm_plugin_iridium_la-mm-plugin-iridium.lo \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	iridium/libmm_plugin_iridium_la-mm-broadband-modem-iridium.lo \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	iridium/libmm_plugin_iridium_la-mm-bearer-iridium.lo \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	iridium/libmm_plugin_iridium_la-mm-sim-iridium.lo
libmm_plugin_iridium_la_OBJECTS =  \
	$(am_libmm_plugin_iridium_la_OBJECTS)
libmm_plugin_iridium_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_iridium_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_IRIDIUM_TRUE@am_libmm_plugin_iridium_la_rpath = -rpath \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	$(pkglibdir)
@ENABLE_PLUGIN_LINKTOP_TRUE@libmm_plugin_linktop_la_DEPENDENCIES =  \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(builddir)/libhelpers-linktop.la
am__libmm_plugin_linktop_la_SOURCES_DIST =  \
	linktop/mm-plugin-linktop.c linktop/mm-plugin-linktop.h \
	linktop/mm-broadband-modem-linktop.h \
	linktop/mm-broadband-modem-linktop.c
@ENABLE_PLUGIN_LINKTOP_TRUE@am_libmm_plugin_linktop_la_OBJECTS = linktop/libmm_plugin_linktop_la-mm-plugin-linktop.lo \
@ENABLE_PLUGIN_LINKTOP_TRUE@	linktop/libmm_plugin_linktop_la-mm-broadband-modem-linktop.lo
libmm_plugin_linktop_la_OBJECTS =  \
	$(am_libmm_plugin_linktop_la_OBJECTS)
libmm_plugin_linktop_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_linktop_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_LINKTOP_TRUE@am_libmm_plugin_linktop_la_rpath = -rpath \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(pkglibdir)
libmm_plugin_longcheer_la_LIBADD =
am__libmm_plugin_longcheer_la_SOURCES_DIST =  \
	longcheer/mm-plugin-longcheer.c \
	longcheer/mm-plugin-longcheer.h \
	longcheer/mm-broadband-modem-longcheer.h \
	longcheer/mm-broadband-modem-longcheer.c
@ENABLE_PLUGIN_LONGCHEER_TRUE@am_libmm_plugin_longcheer_la_OBJECTS = longcheer/libmm_plugin_longcheer_la-mm-plugin-longcheer.lo \
@ENABLE_PLUGIN_LONGCHEER_TRUE@	longcheer/libmm_plugin_longcheer_la-mm-broadband-modem-longcheer.lo
libmm_plugin_longcheer_la_OBJECTS =  \
	$(am_libmm_plugin_longcheer_la_OBJECTS)
libmm_plugin_longcheer_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_longcheer_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_LONGCHEER_TRUE@am_libmm_plugin_longcheer_la_rpath =  \
@ENABLE_PLUGIN_LONGCHEER_TRUE@	-rpath $(pkglibdir)
libmm_plugin_me3630_la_LIBADD =
am_libmm_plugin_me3630_la_OBJECTS =  \
	me3630/libmm_plugin_me3630_la-mm-plugin-me3630.lo \
	me3630/libmm_plugin_me3630_la-mm-common-me3630.lo \
	me3630/libmm_plugin_me3630_la-mm-broadband-modem-me3630.lo \
	me3630/libmm_plugin_me3630_la-mm-broadband-bearer-me3630.lo
libmm_plugin_me3630_la_OBJECTS = $(am_libmm_plugin_me3630_la_OBJECTS)
libmm_plugin_me3630_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_me3630_la_LDFLAGS) \
	$(LDFLAGS) -o $@
libmm_plugin_motorola_la_LIBADD =
am__libmm_plugin_motorola_la_SOURCES_DIST =  \
	motorola/mm-plugin-motorola.c motorola/mm-plugin-motorola.h \
	motorola/mm-broadband-modem-motorola.c \
	motorola/mm-broadband-modem-motorola.h
@ENABLE_PLUGIN_MOTOROLA_TRUE@am_libmm_plugin_motorola_la_OBJECTS = motorola/libmm_plugin_motorola_la-mm-plugin-motorola.lo \
@ENABLE_PLUGIN_MOTOROLA_TRUE@	motorola/libmm_plugin_motorola_la-mm-broadband-modem-motorola.lo
libmm_plugin_motorola_la_OBJECTS =  \
	$(am_libmm_plugin_motorola_la_OBJECTS)
libmm_plugin_motorola_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_motorola_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_MOTOROLA_TRUE@am_libmm_plugin_motorola_la_rpath =  \
@ENABLE_PLUGIN_MOTOROLA_TRUE@	-rpath $(pkglibdir)
libmm_plugin_mtk_la_LIBADD =
am__libmm_plugin_mtk_la_SOURCES_DIST = mtk/mm-plugin-mtk.c \
	mtk/mm-plugin-mtk.h mtk/mm-broadband-modem-mtk.h \
	mtk/mm-broadband-modem-mtk.c
@ENABLE_PLUGIN_MTK_TRUE@am_libmm_plugin_mtk_la_OBJECTS = mtk/libmm_plugin_mtk_la-mm-plugin-mtk.lo \
@ENABLE_PLUGIN_MTK_TRUE@	mtk/libmm_plugin_mtk_la-mm-broadband-modem-mtk.lo
libmm_plugin_mtk_la_OBJECTS = $(am_libmm_plugin_mtk_la_OBJECTS)
libmm_plugin_mtk_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_mtk_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_MTK_TRUE@am_libmm_plugin_mtk_la_rpath = -rpath \
@ENABLE_PLUGIN_MTK_TRUE@	$(pkglibdir)
libmm_plugin_nokia_icera_la_LIBADD =
am__libmm_plugin_nokia_icera_la_SOURCES_DIST =  \
	nokia/mm-plugin-nokia-icera.c nokia/mm-plugin-nokia-icera.h
@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@am_libmm_plugin_nokia_icera_la_OBJECTS = nokia/libmm_plugin_nokia_icera_la-mm-plugin-nokia-icera.lo
libmm_plugin_nokia_icera_la_OBJECTS =  \
	$(am_libmm_plugin_nokia_icera_la_OBJECTS)
libmm_plugin_nokia_icera_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_nokia_icera_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@am_libmm_plugin_nokia_icera_la_rpath =  \
@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@	-rpath $(pkglibdir)
libmm_plugin_nokia_la_LIBADD =
am__libmm_plugin_nokia_la_SOURCES_DIST = nokia/mm-plugin-nokia.c \
	nokia/mm-plugin-nokia.h nokia/mm-sim-nokia.c \
	nokia/mm-sim-nokia.h nokia/mm-broadband-modem-nokia.c \
	nokia/mm-broadband-modem-nokia.h
@ENABLE_PLUGIN_NOKIA_TRUE@am_libmm_plugin_nokia_la_OBJECTS = nokia/libmm_plugin_nokia_la-mm-plugin-nokia.lo \
@ENABLE_PLUGIN_NOKIA_TRUE@	nokia/libmm_plugin_nokia_la-mm-sim-nokia.lo \
@ENABLE_PLUGIN_NOKIA_TRUE@	nokia/libmm_plugin_nokia_la-mm-broadband-modem-nokia.lo
libmm_plugin_nokia_la_OBJECTS = $(am_libmm_plugin_nokia_la_OBJECTS)
libmm_plugin_nokia_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_nokia_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_NOKIA_TRUE@am_libmm_plugin_nokia_la_rpath = -rpath \
@ENABLE_PLUGIN_NOKIA_TRUE@	$(pkglibdir)
libmm_plugin_novatel_lte_la_LIBADD =
am__libmm_plugin_novatel_lte_la_SOURCES_DIST =  \
	novatel/mm-plugin-novatel-lte.c \
	novatel/mm-plugin-novatel-lte.h \
	novatel/mm-broadband-modem-novatel-lte.c \
	novatel/mm-broadband-modem-novatel-lte.h \
	novatel/mm-broadband-bearer-novatel-lte.c \
	novatel/mm-broadband-bearer-novatel-lte.h \
	novatel/mm-sim-novatel-lte.c novatel/mm-sim-novatel-lte.h
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@am_libmm_plugin_novatel_lte_la_OBJECTS = novatel/libmm_plugin_novatel_lte_la-mm-plugin-novatel-lte.lo \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	novatel/libmm_plugin_novatel_lte_la-mm-broadband-modem-novatel-lte.lo \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	novatel/libmm_plugin_novatel_lte_la-mm-broadband-bearer-novatel-lte.lo \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	novatel/libmm_plugin_novatel_lte_la-mm-sim-novatel-lte.lo
libmm_plugin_novatel_lte_la_OBJECTS =  \
	$(am_libmm_plugin_novatel_lte_la_OBJECTS)
libmm_plugin_novatel_lte_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_novatel_lte_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@am_libmm_plugin_novatel_lte_la_rpath =  \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	-rpath $(pkglibdir)
libmm_plugin_novatel_la_LIBADD =
am__libmm_plugin_novatel_la_SOURCES_DIST =  \
	novatel/mm-plugin-novatel.c novatel/mm-plugin-novatel.h
@ENABLE_PLUGIN_NOVATEL_TRUE@am_libmm_plugin_novatel_la_OBJECTS = novatel/libmm_plugin_novatel_la-mm-plugin-novatel.lo
libmm_plugin_novatel_la_OBJECTS =  \
	$(am_libmm_plugin_novatel_la_OBJECTS)
libmm_plugin_novatel_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_novatel_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_NOVATEL_TRUE@am_libmm_plugin_novatel_la_rpath = -rpath \
@ENABLE_PLUGIN_NOVATEL_TRUE@	$(pkglibdir)
libmm_plugin_option_hso_la_LIBADD =
am__libmm_plugin_option_hso_la_SOURCES_DIST = option/mm-plugin-hso.c \
	option/mm-plugin-hso.h option/mm-broadband-bearer-hso.c \
	option/mm-broadband-bearer-hso.h \
	option/mm-broadband-modem-hso.c \
	option/mm-broadband-modem-hso.h
@ENABLE_PLUGIN_OPTION_HSO_TRUE@am_libmm_plugin_option_hso_la_OBJECTS = option/libmm_plugin_option_hso_la-mm-plugin-hso.lo \
@ENABLE_PLUGIN_OPTION_HSO_TRUE@	option/libmm_plugin_option_hso_la-mm-broadband-bearer-hso.lo \
@ENABLE_PLUGIN_OPTION_HSO_TRUE@	option/libmm_plugin_option_hso_la-mm-broadband-modem-hso.lo
libmm_plugin_option_hso_la_OBJECTS =  \
	$(am_libmm_plugin_option_hso_la_OBJECTS)
libmm_plugin_option_hso_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_option_hso_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_OPTION_HSO_TRUE@am_libmm_plugin_option_hso_la_rpath =  \
@ENABLE_PLUGIN_OPTION_HSO_TRUE@	-rpath $(pkglibdir)
libmm_plugin_option_la_LIBADD =
am__libmm_plugin_option_la_SOURCES_DIST = option/mm-plugin-option.c \
	option/mm-plugin-option.h
@ENABLE_PLUGIN_OPTION_TRUE@am_libmm_plugin_option_la_OBJECTS = option/libmm_plugin_option_la-mm-plugin-option.lo
libmm_plugin_option_la_OBJECTS = $(am_libmm_plugin_option_la_OBJECTS)
libmm_plugin_option_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_option_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_OPTION_TRUE@am_libmm_plugin_option_la_rpath = -rpath \
@ENABLE_PLUGIN_OPTION_TRUE@	$(pkglibdir)
libmm_plugin_pantech_la_LIBADD =
am__libmm_plugin_pantech_la_SOURCES_DIST =  \
	pantech/mm-plugin-pantech.c pantech/mm-plugin-pantech.h \
	pantech/mm-sim-pantech.c pantech/mm-sim-pantech.h \
	pantech/mm-broadband-modem-pantech.c \
	pantech/mm-broadband-modem-pantech.h
@ENABLE_PLUGIN_PANTECH_TRUE@am_libmm_plugin_pantech_la_OBJECTS = pantech/libmm_plugin_pantech_la-mm-plugin-pantech.lo \
@ENABLE_PLUGIN_PANTECH_TRUE@	pantech/libmm_plugin_pantech_la-mm-sim-pantech.lo \
@ENABLE_PLUGIN_PANTECH_TRUE@	pantech/libmm_plugin_pantech_la-mm-broadband-modem-pantech.lo
libmm_plugin_pantech_la_OBJECTS =  \
	$(am_libmm_plugin_pantech_la_OBJECTS)
libmm_plugin_pantech_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_pantech_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_PANTECH_TRUE@am_libmm_plugin_pantech_la_rpath = -rpath \
@ENABLE_PLUGIN_PANTECH_TRUE@	$(pkglibdir)
libmm_plugin_quectel_la_LIBADD =
am__libmm_plugin_quectel_la_SOURCES_DIST =  \
	quectel/mm-plugin-quectel.c quectel/mm-plugin-quectel.h \
	quectel/mm-shared-quectel.c quectel/mm-shared-quectel.h \
	quectel/mm-broadband-modem-quectel.c \
	quectel/mm-broadband-modem-quectel.h \
	quectel/mm-broadband-modem-qmi-quectel.c \
	quectel/mm-broadband-modem-qmi-quectel.h
@ENABLE_PLUGIN_QUECTEL_TRUE@@WITH_QMI_TRUE@am__objects_5 = quectel/libmm_plugin_quectel_la-mm-broadband-modem-qmi-quectel.lo
@ENABLE_PLUGIN_QUECTEL_TRUE@am_libmm_plugin_quectel_la_OBJECTS = quectel/libmm_plugin_quectel_la-mm-plugin-quectel.lo \
@ENABLE_PLUGIN_QUECTEL_TRUE@	quectel/libmm_plugin_quectel_la-mm-shared-quectel.lo \
@ENABLE_PLUGIN_QUECTEL_TRUE@	quectel/libmm_plugin_quectel_la-mm-broadband-modem-quectel.lo \
@ENABLE_PLUGIN_QUECTEL_TRUE@	$(am__objects_5)
libmm_plugin_quectel_la_OBJECTS =  \
	$(am_libmm_plugin_quectel_la_OBJECTS)
libmm_plugin_quectel_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_quectel_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_QUECTEL_TRUE@am_libmm_plugin_quectel_la_rpath = -rpath \
@ENABLE_PLUGIN_QUECTEL_TRUE@	$(pkglibdir)
libmm_plugin_samsung_la_LIBADD =
am__libmm_plugin_samsung_la_SOURCES_DIST =  \
	samsung/mm-plugin-samsung.c samsung/mm-plugin-samsung.h \
	samsung/mm-broadband-modem-samsung.c \
	samsung/mm-broadband-modem-samsung.h
@ENABLE_PLUGIN_SAMSUNG_TRUE@am_libmm_plugin_samsung_la_OBJECTS = samsung/libmm_plugin_samsung_la-mm-plugin-samsung.lo \
@ENABLE_PLUGIN_SAMSUNG_TRUE@	samsung/libmm_plugin_samsung_la-mm-broadband-modem-samsung.lo
libmm_plugin_samsung_la_OBJECTS =  \
	$(am_libmm_plugin_samsung_la_OBJECTS)
libmm_plugin_samsung_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_samsung_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_SAMSUNG_TRUE@am_libmm_plugin_samsung_la_rpath = -rpath \
@ENABLE_PLUGIN_SAMSUNG_TRUE@	$(pkglibdir)
libmm_plugin_sierra_legacy_la_LIBADD =
am__libmm_plugin_sierra_legacy_la_SOURCES_DIST =  \
	sierra/mm-plugin-sierra-legacy.c \
	sierra/mm-plugin-sierra-legacy.h \
	sierra/mm-broadband-modem-sierra-icera.c \
	sierra/mm-broadband-modem-sierra-icera.h
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@am_libmm_plugin_sierra_legacy_la_OBJECTS = sierra/libmm_plugin_sierra_legacy_la-mm-plugin-sierra-legacy.lo \
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@	sierra/libmm_plugin_sierra_legacy_la-mm-broadband-modem-sierra-icera.lo
libmm_plugin_sierra_legacy_la_OBJECTS =  \
	$(am_libmm_plugin_sierra_legacy_la_OBJECTS)
libmm_plugin_sierra_legacy_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) \
	$(libmm_plugin_sierra_legacy_la_LDFLAGS) $(LDFLAGS) -o $@
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@am_libmm_plugin_sierra_legacy_la_rpath =  \
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@	-rpath $(pkglibdir)
libmm_plugin_sierra_la_LIBADD =
am__libmm_plugin_sierra_la_SOURCES_DIST = sierra/mm-plugin-sierra.c \
	sierra/mm-plugin-sierra.h
@ENABLE_PLUGIN_SIERRA_TRUE@am_libmm_plugin_sierra_la_OBJECTS = sierra/libmm_plugin_sierra_la-mm-plugin-sierra.lo
libmm_plugin_sierra_la_OBJECTS = $(am_libmm_plugin_sierra_la_OBJECTS)
libmm_plugin_sierra_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_sierra_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_SIERRA_TRUE@am_libmm_plugin_sierra_la_rpath = -rpath \
@ENABLE_PLUGIN_SIERRA_TRUE@	$(pkglibdir)
@ENABLE_PLUGIN_SIMTECH_TRUE@libmm_plugin_simtech_la_DEPENDENCIES =  \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(builddir)/libhelpers-simtech.la
am__libmm_plugin_simtech_la_SOURCES_DIST =  \
	simtech/mm-plugin-simtech.c simtech/mm-plugin-simtech.h \
	simtech/mm-shared-simtech.c simtech/mm-shared-simtech.h \
	simtech/mm-broadband-modem-simtech.h \
	simtech/mm-broadband-modem-simtech.c \
	simtech/mm-broadband-modem-qmi-simtech.c \
	simtech/mm-broadband-modem-qmi-simtech.h
@ENABLE_PLUGIN_SIMTECH_TRUE@@WITH_QMI_TRUE@am__objects_6 = simtech/libmm_plugin_simtech_la-mm-broadband-modem-qmi-simtech.lo
@ENABLE_PLUGIN_SIMTECH_TRUE@am_libmm_plugin_simtech_la_OBJECTS = simtech/libmm_plugin_simtech_la-mm-plugin-simtech.lo \
@ENABLE_PLUGIN_SIMTECH_TRUE@	simtech/libmm_plugin_simtech_la-mm-shared-simtech.lo \
@ENABLE_PLUGIN_SIMTECH_TRUE@	simtech/libmm_plugin_simtech_la-mm-broadband-modem-simtech.lo \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(am__objects_6)
libmm_plugin_simtech_la_OBJECTS =  \
	$(am_libmm_plugin_simtech_la_OBJECTS)
libmm_plugin_simtech_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_simtech_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_SIMTECH_TRUE@am_libmm_plugin_simtech_la_rpath = -rpath \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(pkglibdir)
libmm_plugin_telit_la_LIBADD =
am__libmm_plugin_telit_la_SOURCES_DIST = telit/mm-plugin-telit.c \
	telit/mm-plugin-telit.h
@ENABLE_PLUGIN_TELIT_TRUE@am_libmm_plugin_telit_la_OBJECTS = telit/libmm_plugin_telit_la-mm-plugin-telit.lo
libmm_plugin_telit_la_OBJECTS = $(am_libmm_plugin_telit_la_OBJECTS)
libmm_plugin_telit_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_telit_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_TELIT_TRUE@am_libmm_plugin_telit_la_rpath = -rpath \
@ENABLE_PLUGIN_TELIT_TRUE@	$(pkglibdir)
@ENABLE_PLUGIN_THURAYA_TRUE@libmm_plugin_thuraya_la_DEPENDENCIES =  \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(builddir)/libhelpers-thuraya.la
am__libmm_plugin_thuraya_la_SOURCES_DIST =  \
	thuraya/mm-plugin-thuraya.c thuraya/mm-plugin-thuraya.h \
	thuraya/mm-broadband-modem-thuraya.c \
	thuraya/mm-broadband-modem-thuraya.h
@ENABLE_PLUGIN_THURAYA_TRUE@am_libmm_plugin_thuraya_la_OBJECTS = thuraya/libmm_plugin_thuraya_la-mm-plugin-thuraya.lo \
@ENABLE_PLUGIN_THURAYA_TRUE@	thuraya/libmm_plugin_thuraya_la-mm-broadband-modem-thuraya.lo
libmm_plugin_thuraya_la_OBJECTS =  \
	$(am_libmm_plugin_thuraya_la_OBJECTS)
libmm_plugin_thuraya_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_thuraya_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_THURAYA_TRUE@am_libmm_plugin_thuraya_la_rpath = -rpath \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(pkglibdir)
libmm_plugin_tplink_la_LIBADD =
am__libmm_plugin_tplink_la_SOURCES_DIST = tplink/mm-plugin-tplink.c \
	tplink/mm-plugin-tplink.h
@ENABLE_PLUGIN_TPLINK_TRUE@am_libmm_plugin_tplink_la_OBJECTS = tplink/libmm_plugin_tplink_la-mm-plugin-tplink.lo
libmm_plugin_tplink_la_OBJECTS = $(am_libmm_plugin_tplink_la_OBJECTS)
libmm_plugin_tplink_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_tplink_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_TPLINK_TRUE@am_libmm_plugin_tplink_la_rpath = -rpath \
@ENABLE_PLUGIN_TPLINK_TRUE@	$(pkglibdir)
@ENABLE_PLUGIN_UBLOX_TRUE@libmm_plugin_ublox_la_DEPENDENCIES =  \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(builddir)/libhelpers-ublox.la
am__libmm_plugin_ublox_la_SOURCES_DIST = ublox/mm-plugin-ublox.c \
	ublox/mm-plugin-ublox.h ublox/mm-broadband-bearer-ublox.h \
	ublox/mm-broadband-bearer-ublox.c \
	ublox/mm-broadband-modem-ublox.h \
	ublox/mm-broadband-modem-ublox.c ublox/mm-sim-ublox.c \
	ublox/mm-sim-ublox.h
@ENABLE_PLUGIN_UBLOX_TRUE@am_libmm_plugin_ublox_la_OBJECTS = ublox/libmm_plugin_ublox_la-mm-plugin-ublox.lo \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/libmm_plugin_ublox_la-mm-broadband-bearer-ublox.lo \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/libmm_plugin_ublox_la-mm-broadband-modem-ublox.lo \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/libmm_plugin_ublox_la-mm-sim-ublox.lo
libmm_plugin_ublox_la_OBJECTS = $(am_libmm_plugin_ublox_la_OBJECTS)
libmm_plugin_ublox_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_ublox_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_UBLOX_TRUE@am_libmm_plugin_ublox_la_rpath = -rpath \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(pkglibdir)
libmm_plugin_via_la_LIBADD =
am__libmm_plugin_via_la_SOURCES_DIST = via/mm-plugin-via.c \
	via/mm-plugin-via.h via/mm-broadband-modem-via.c \
	via/mm-broadband-modem-via.h
@ENABLE_PLUGIN_VIA_TRUE@am_libmm_plugin_via_la_OBJECTS = via/libmm_plugin_via_la-mm-plugin-via.lo \
@ENABLE_PLUGIN_VIA_TRUE@	via/libmm_plugin_via_la-mm-broadband-modem-via.lo
libmm_plugin_via_la_OBJECTS = $(am_libmm_plugin_via_la_OBJECTS)
libmm_plugin_via_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_via_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_VIA_TRUE@am_libmm_plugin_via_la_rpath = -rpath \
@ENABLE_PLUGIN_VIA_TRUE@	$(pkglibdir)
libmm_plugin_wavecom_la_LIBADD =
am__libmm_plugin_wavecom_la_SOURCES_DIST =  \
	wavecom/mm-plugin-wavecom.c wavecom/mm-plugin-wavecom.h \
	wavecom/mm-broadband-modem-wavecom.c \
	wavecom/mm-broadband-modem-wavecom.h
@ENABLE_PLUGIN_WAVECOM_TRUE@am_libmm_plugin_wavecom_la_OBJECTS = wavecom/libmm_plugin_wavecom_la-mm-plugin-wavecom.lo \
@ENABLE_PLUGIN_WAVECOM_TRUE@	wavecom/libmm_plugin_wavecom_la-mm-broadband-modem-wavecom.lo
libmm_plugin_wavecom_la_OBJECTS =  \
	$(am_libmm_plugin_wavecom_la_OBJECTS)
libmm_plugin_wavecom_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_wavecom_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_WAVECOM_TRUE@am_libmm_plugin_wavecom_la_rpath = -rpath \
@ENABLE_PLUGIN_WAVECOM_TRUE@	$(pkglibdir)
libmm_plugin_x22x_la_LIBADD =
am__libmm_plugin_x22x_la_SOURCES_DIST = x22x/mm-plugin-x22x.c \
	x22x/mm-plugin-x22x.h x22x/mm-broadband-modem-x22x.h \
	x22x/mm-broadband-modem-x22x.c
@ENABLE_PLUGIN_X22X_TRUE@am_libmm_plugin_x22x_la_OBJECTS = x22x/libmm_plugin_x22x_la-mm-plugin-x22x.lo \
@ENABLE_PLUGIN_X22X_TRUE@	x22x/libmm_plugin_x22x_la-mm-broadband-modem-x22x.lo
libmm_plugin_x22x_la_OBJECTS = $(am_libmm_plugin_x22x_la_OBJECTS)
libmm_plugin_x22x_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_x22x_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_X22X_TRUE@am_libmm_plugin_x22x_la_rpath = -rpath \
@ENABLE_PLUGIN_X22X_TRUE@	$(pkglibdir)
libmm_plugin_zte_la_LIBADD =
am__libmm_plugin_zte_la_SOURCES_DIST = zte/mm-plugin-zte.c \
	zte/mm-plugin-zte.h zte/mm-common-zte.h zte/mm-common-zte.c \
	zte/mm-broadband-modem-zte.h zte/mm-broadband-modem-zte.c \
	zte/mm-broadband-modem-zte-icera.h \
	zte/mm-broadband-modem-zte-icera.c
@ENABLE_PLUGIN_ZTE_TRUE@am_libmm_plugin_zte_la_OBJECTS = zte/libmm_plugin_zte_la-mm-plugin-zte.lo \
@ENABLE_PLUGIN_ZTE_TRUE@	zte/libmm_plugin_zte_la-mm-common-zte.lo \
@ENABLE_PLUGIN_ZTE_TRUE@	zte/libmm_plugin_zte_la-mm-broadband-modem-zte.lo \
@ENABLE_PLUGIN_ZTE_TRUE@	zte/libmm_plugin_zte_la-mm-broadband-modem-zte-icera.lo
libmm_plugin_zte_la_OBJECTS = $(am_libmm_plugin_zte_la_OBJECTS)
libmm_plugin_zte_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_plugin_zte_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@ENABLE_PLUGIN_ZTE_TRUE@am_libmm_plugin_zte_la_rpath = -rpath \
@ENABLE_PLUGIN_ZTE_TRUE@	$(pkglibdir)
libmm_shared_foxconn_la_LIBADD =
am__libmm_shared_foxconn_la_SOURCES_DIST = foxconn/mm-shared.c \
	foxconn/mm-broadband-modem-foxconn-t77w968.c \
	foxconn/mm-broadband-modem-foxconn-t77w968.h
@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@am_libmm_shared_foxconn_la_OBJECTS = foxconn/libmm_shared_foxconn_la-mm-shared.lo \
@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@	foxconn/libmm_shared_foxconn_la-mm-broadband-modem-foxconn-t77w968.lo
libmm_shared_foxconn_la_OBJECTS =  \
	$(am_libmm_shared_foxconn_la_OBJECTS)
libmm_shared_foxconn_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_shared_foxconn_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@am_libmm_shared_foxconn_la_rpath =  \
@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@	-rpath $(pkglibdir)
@WITH_SHARED_ICERA_TRUE@libmm_shared_icera_la_DEPENDENCIES =  \
@WITH_SHARED_ICERA_TRUE@	$(builddir)/libhelpers-icera.la
am__libmm_shared_icera_la_SOURCES_DIST = icera/mm-shared.c \
	icera/mm-broadband-modem-icera.h \
	icera/mm-broadband-modem-icera.c \
	icera/mm-broadband-bearer-icera.h \
	icera/mm-broadband-bearer-icera.c
@WITH_SHARED_ICERA_TRUE@am_libmm_shared_icera_la_OBJECTS = icera/libmm_shared_icera_la-mm-shared.lo \
@WITH_SHARED_ICERA_TRUE@	icera/libmm_shared_icera_la-mm-broadband-modem-icera.lo \
@WITH_SHARED_ICERA_TRUE@	icera/libmm_shared_icera_la-mm-broadband-bearer-icera.lo
libmm_shared_icera_la_OBJECTS = $(am_libmm_shared_icera_la_OBJECTS)
libmm_shared_icera_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_shared_icera_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@WITH_SHARED_ICERA_TRUE@am_libmm_shared_icera_la_rpath = -rpath \
@WITH_SHARED_ICERA_TRUE@	$(pkglibdir)
libmm_shared_novatel_la_LIBADD =
am__libmm_shared_novatel_la_SOURCES_DIST = novatel/mm-shared.c \
	novatel/mm-common-novatel.c novatel/mm-common-novatel.h \
	novatel/mm-broadband-modem-novatel.c \
	novatel/mm-broadband-modem-novatel.h
@WITH_SHARED_NOVATEL_TRUE@am_libmm_shared_novatel_la_OBJECTS = novatel/libmm_shared_novatel_la-mm-shared.lo \
@WITH_SHARED_NOVATEL_TRUE@	novatel/libmm_shared_novatel_la-mm-common-novatel.lo \
@WITH_SHARED_NOVATEL_TRUE@	novatel/libmm_shared_novatel_la-mm-broadband-modem-novatel.lo
libmm_shared_novatel_la_OBJECTS =  \
	$(am_libmm_shared_novatel_la_OBJECTS)
libmm_shared_novatel_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_shared_novatel_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@WITH_SHARED_NOVATEL_TRUE@am_libmm_shared_novatel_la_rpath = -rpath \
@WITH_SHARED_NOVATEL_TRUE@	$(pkglibdir)
libmm_shared_option_la_LIBADD =
am__libmm_shared_option_la_SOURCES_DIST = option/mm-shared.c \
	option/mm-broadband-modem-option.c \
	option/mm-broadband-modem-option.h
@WITH_SHARED_OPTION_TRUE@am_libmm_shared_option_la_OBJECTS = option/libmm_shared_option_la-mm-shared.lo \
@WITH_SHARED_OPTION_TRUE@	option/libmm_shared_option_la-mm-broadband-modem-option.lo
libmm_shared_option_la_OBJECTS = $(am_libmm_shared_option_la_OBJECTS)
libmm_shared_option_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_shared_option_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@WITH_SHARED_OPTION_TRUE@am_libmm_shared_option_la_rpath = -rpath \
@WITH_SHARED_OPTION_TRUE@	$(pkglibdir)
@WITH_SHARED_SIERRA_TRUE@libmm_shared_sierra_la_DEPENDENCIES =  \
@WITH_SHARED_SIERRA_TRUE@	$(builddir)/libhelpers-sierra.la
am__libmm_shared_sierra_la_SOURCES_DIST = sierra/mm-shared.c \
	sierra/mm-common-sierra.c sierra/mm-common-sierra.h \
	sierra/mm-sim-sierra.c sierra/mm-sim-sierra.h \
	sierra/mm-broadband-bearer-sierra.c \
	sierra/mm-broadband-bearer-sierra.h \
	sierra/mm-broadband-modem-sierra.c \
	sierra/mm-broadband-modem-sierra.h
@WITH_SHARED_SIERRA_TRUE@am_libmm_shared_sierra_la_OBJECTS = sierra/libmm_shared_sierra_la-mm-shared.lo \
@WITH_SHARED_SIERRA_TRUE@	sierra/libmm_shared_sierra_la-mm-common-sierra.lo \
@WITH_SHARED_SIERRA_TRUE@	sierra/libmm_shared_sierra_la-mm-sim-sierra.lo \
@WITH_SHARED_SIERRA_TRUE@	sierra/libmm_shared_sierra_la-mm-broadband-bearer-sierra.lo \
@WITH_SHARED_SIERRA_TRUE@	sierra/libmm_shared_sierra_la-mm-broadband-modem-sierra.lo
libmm_shared_sierra_la_OBJECTS = $(am_libmm_shared_sierra_la_OBJECTS)
libmm_shared_sierra_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_shared_sierra_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@WITH_SHARED_SIERRA_TRUE@am_libmm_shared_sierra_la_rpath = -rpath \
@WITH_SHARED_SIERRA_TRUE@	$(pkglibdir)
@WITH_SHARED_TELIT_TRUE@libmm_shared_telit_la_DEPENDENCIES =  \
@WITH_SHARED_TELIT_TRUE@	$(builddir)/libhelpers-telit.la
am__libmm_shared_telit_la_SOURCES_DIST = telit/mm-shared.c \
	telit/mm-common-telit.c telit/mm-common-telit.h \
	telit/mm-shared-telit.c telit/mm-shared-telit.h \
	telit/mm-broadband-modem-telit.c \
	telit/mm-broadband-modem-telit.h \
	telit/mm-broadband-modem-mbim-telit.h \
	telit/mm-broadband-modem-mbim-telit.c
@WITH_MBIM_TRUE@@WITH_SHARED_TELIT_TRUE@am__objects_7 = telit/libmm_shared_telit_la-mm-broadband-modem-mbim-telit.lo
@WITH_SHARED_TELIT_TRUE@am_libmm_shared_telit_la_OBJECTS = telit/libmm_shared_telit_la-mm-shared.lo \
@WITH_SHARED_TELIT_TRUE@	telit/libmm_shared_telit_la-mm-common-telit.lo \
@WITH_SHARED_TELIT_TRUE@	telit/libmm_shared_telit_la-mm-shared-telit.lo \
@WITH_SHARED_TELIT_TRUE@	telit/libmm_shared_telit_la-mm-broadband-modem-telit.lo \
@WITH_SHARED_TELIT_TRUE@	$(am__objects_7)
libmm_shared_telit_la_OBJECTS = $(am_libmm_shared_telit_la_OBJECTS)
libmm_shared_telit_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_shared_telit_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@WITH_SHARED_TELIT_TRUE@am_libmm_shared_telit_la_rpath = -rpath \
@WITH_SHARED_TELIT_TRUE@	$(pkglibdir)
@WITH_SHARED_XMM_TRUE@libmm_shared_xmm_la_DEPENDENCIES =  \
@WITH_SHARED_XMM_TRUE@	$(builddir)/libhelpers-xmm.la
am__libmm_shared_xmm_la_SOURCES_DIST = xmm/mm-shared.c \
	xmm/mm-shared-xmm.h xmm/mm-shared-xmm.c \
	xmm/mm-broadband-modem-xmm.h xmm/mm-broadband-modem-xmm.c \
	xmm/mm-broadband-modem-mbim-xmm.h \
	xmm/mm-broadband-modem-mbim-xmm.c
@WITH_MBIM_TRUE@@WITH_SHARED_XMM_TRUE@am__objects_8 = xmm/libmm_shared_xmm_la-mm-broadband-modem-mbim-xmm.lo
@WITH_SHARED_XMM_TRUE@am_libmm_shared_xmm_la_OBJECTS =  \
@WITH_SHARED_XMM_TRUE@	xmm/libmm_shared_xmm_la-mm-shared.lo \
@WITH_SHARED_XMM_TRUE@	xmm/libmm_shared_xmm_la-mm-shared-xmm.lo \
@WITH_SHARED_XMM_TRUE@	xmm/libmm_shared_xmm_la-mm-broadband-modem-xmm.lo \
@WITH_SHARED_XMM_TRUE@	$(am__objects_8)
libmm_shared_xmm_la_OBJECTS = $(am_libmm_shared_xmm_la_OBJECTS)
libmm_shared_xmm_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libmm_shared_xmm_la_LDFLAGS) \
	$(LDFLAGS) -o $@
@WITH_SHARED_XMM_TRUE@am_libmm_shared_xmm_la_rpath = -rpath \
@WITH_SHARED_XMM_TRUE@	$(pkglibdir)
libmm_test_common_la_DEPENDENCIES = ${top_builddir}/libmm-glib/generated/tests/libmm-test-generated.la \
	$(top_builddir)/libmm-glib/libmm-glib.la
am_libmm_test_common_la_OBJECTS =  \
	tests/libmm_test_common_la-test-fixture.lo \
	tests/libmm_test_common_la-test-port-context.lo \
	tests/libmm_test_common_la-test-helpers.lo
libmm_test_common_la_OBJECTS = $(am_libmm_test_common_la_OBJECTS)
am_test_keyfiles_OBJECTS = tests/test-keyfiles.$(OBJEXT)
test_keyfiles_OBJECTS = $(am_test_keyfiles_OBJECTS)
test_keyfiles_DEPENDENCIES = $(top_builddir)/libmm-glib/libmm-glib.la
am__test_modem_helpers_altair_lte_SOURCES_DIST =  \
	altair/tests/test-modem-helpers-altair-lte.c
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@am_test_modem_helpers_altair_lte_OBJECTS = altair/tests/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.$(OBJEXT)
test_modem_helpers_altair_lte_OBJECTS =  \
	$(am_test_modem_helpers_altair_lte_OBJECTS)
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@test_modem_helpers_altair_lte_DEPENDENCIES = $(builddir)/libhelpers-altair-lte.la \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la
am__test_modem_helpers_cinterion_SOURCES_DIST =  \
	cinterion/tests/test-modem-helpers-cinterion.c
@ENABLE_PLUGIN_CINTERION_TRUE@am_test_modem_helpers_cinterion_OBJECTS = cinterion/tests/test_modem_helpers_cinterion-test-modem-helpers-cinterion.$(OBJEXT)
test_modem_helpers_cinterion_OBJECTS =  \
	$(am_test_modem_helpers_cinterion_OBJECTS)
@ENABLE_PLUGIN_CINTERION_TRUE@test_modem_helpers_cinterion_DEPENDENCIES = $(builddir)/libhelpers-cinterion.la \
@ENABLE_PLUGIN_CINTERION_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_CINTERION_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la
am__test_modem_helpers_huawei_SOURCES_DIST =  \
	huawei/tests/test-modem-helpers-huawei.c
@ENABLE_PLUGIN_HUAWEI_TRUE@am_test_modem_helpers_huawei_OBJECTS = huawei/tests/test_modem_helpers_huawei-test-modem-helpers-huawei.$(OBJEXT)
test_modem_helpers_huawei_OBJECTS =  \
	$(am_test_modem_helpers_huawei_OBJECTS)
@ENABLE_PLUGIN_HUAWEI_TRUE@test_modem_helpers_huawei_DEPENDENCIES =  \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(builddir)/libhelpers-huawei.la \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la
am__test_modem_helpers_icera_SOURCES_DIST =  \
	icera/tests/test-modem-helpers-icera.c
@WITH_SHARED_ICERA_TRUE@am_test_modem_helpers_icera_OBJECTS = icera/tests/test_modem_helpers_icera-test-modem-helpers-icera.$(OBJEXT)
test_modem_helpers_icera_OBJECTS =  \
	$(am_test_modem_helpers_icera_OBJECTS)
@WITH_SHARED_ICERA_TRUE@test_modem_helpers_icera_DEPENDENCIES =  \
@WITH_SHARED_ICERA_TRUE@	$(builddir)/libhelpers-icera.la \
@WITH_SHARED_ICERA_TRUE@	$(top_builddir)/src/libhelpers.la \
@WITH_SHARED_ICERA_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la
am__test_modem_helpers_linktop_SOURCES_DIST =  \
	linktop/tests/test-modem-helpers-linktop.c
@ENABLE_PLUGIN_LINKTOP_TRUE@am_test_modem_helpers_linktop_OBJECTS = linktop/tests/test_modem_helpers_linktop-test-modem-helpers-linktop.$(OBJEXT)
test_modem_helpers_linktop_OBJECTS =  \
	$(am_test_modem_helpers_linktop_OBJECTS)
@ENABLE_PLUGIN_LINKTOP_TRUE@test_modem_helpers_linktop_DEPENDENCIES =  \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(builddir)/libhelpers-linktop.la \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la
am__test_modem_helpers_mbm_SOURCES_DIST =  \
	mbm/tests/test-modem-helpers-mbm.c
@ENABLE_PLUGIN_MBM_TRUE@am_test_modem_helpers_mbm_OBJECTS = mbm/tests/test_modem_helpers_mbm-test-modem-helpers-mbm.$(OBJEXT)
test_modem_helpers_mbm_OBJECTS = $(am_test_modem_helpers_mbm_OBJECTS)
@ENABLE_PLUGIN_MBM_TRUE@test_modem_helpers_mbm_DEPENDENCIES =  \
@ENABLE_PLUGIN_MBM_TRUE@	$(builddir)/libhelpers-mbm.la \
@ENABLE_PLUGIN_MBM_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_MBM_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la
am__test_modem_helpers_sierra_SOURCES_DIST =  \
	sierra/tests/test-modem-helpers-sierra.c
@WITH_SHARED_SIERRA_TRUE@am_test_modem_helpers_sierra_OBJECTS = sierra/tests/test_modem_helpers_sierra-test-modem-helpers-sierra.$(OBJEXT)
test_modem_helpers_sierra_OBJECTS =  \
	$(am_test_modem_helpers_sierra_OBJECTS)
@WITH_SHARED_SIERRA_TRUE@test_modem_helpers_sierra_DEPENDENCIES =  \
@WITH_SHARED_SIERRA_TRUE@	$(builddir)/libhelpers-sierra.la \
@WITH_SHARED_SIERRA_TRUE@	$(top_builddir)/src/libhelpers.la \
@WITH_SHARED_SIERRA_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la
am__test_modem_helpers_simtech_SOURCES_DIST =  \
	simtech/tests/test-modem-helpers-simtech.c
@ENABLE_PLUGIN_SIMTECH_TRUE@am_test_modem_helpers_simtech_OBJECTS = simtech/tests/test_modem_helpers_simtech-test-modem-helpers-simtech.$(OBJEXT)
test_modem_helpers_simtech_OBJECTS =  \
	$(am_test_modem_helpers_simtech_OBJECTS)
@ENABLE_PLUGIN_SIMTECH_TRUE@test_modem_helpers_simtech_DEPENDENCIES =  \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(builddir)/libhelpers-simtech.la \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la
am__test_modem_helpers_telit_SOURCES_DIST =  \
	telit/tests/test-mm-modem-helpers-telit.c
@WITH_SHARED_TELIT_TRUE@am_test_modem_helpers_telit_OBJECTS = telit/tests/test_modem_helpers_telit-test-mm-modem-helpers-telit.$(OBJEXT)
test_modem_helpers_telit_OBJECTS =  \
	$(am_test_modem_helpers_telit_OBJECTS)
am__DEPENDENCIES_1 = $(builddir)/libmm-test-common.la
@WITH_SHARED_TELIT_TRUE@test_modem_helpers_telit_DEPENDENCIES =  \
@WITH_SHARED_TELIT_TRUE@	$(am__DEPENDENCIES_1) \
@WITH_SHARED_TELIT_TRUE@	$(builddir)/libhelpers-telit.la \
@WITH_SHARED_TELIT_TRUE@	$(top_builddir)/src/libhelpers.la \
@WITH_SHARED_TELIT_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la
am__test_modem_helpers_thuraya_SOURCES_DIST =  \
	thuraya/tests/test-mm-modem-helpers-thuraya.c
@ENABLE_PLUGIN_THURAYA_TRUE@am_test_modem_helpers_thuraya_OBJECTS = thuraya/tests/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.$(OBJEXT)
test_modem_helpers_thuraya_OBJECTS =  \
	$(am_test_modem_helpers_thuraya_OBJECTS)
@ENABLE_PLUGIN_THURAYA_TRUE@test_modem_helpers_thuraya_DEPENDENCIES =  \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(builddir)/libhelpers-thuraya.la \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la
am__test_modem_helpers_ublox_SOURCES_DIST =  \
	ublox/tests/test-modem-helpers-ublox.c
@ENABLE_PLUGIN_UBLOX_TRUE@am_test_modem_helpers_ublox_OBJECTS = ublox/tests/test_modem_helpers_ublox-test-modem-helpers-ublox.$(OBJEXT)
test_modem_helpers_ublox_OBJECTS =  \
	$(am_test_modem_helpers_ublox_OBJECTS)
@ENABLE_PLUGIN_UBLOX_TRUE@test_modem_helpers_ublox_DEPENDENCIES =  \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(am__DEPENDENCIES_1) \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(builddir)/libhelpers-ublox.la \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la
am__test_modem_helpers_xmm_SOURCES_DIST =  \
	xmm/tests/test-modem-helpers-xmm.c
@WITH_SHARED_XMM_TRUE@am_test_modem_helpers_xmm_OBJECTS = xmm/tests/test_modem_helpers_xmm-test-modem-helpers-xmm.$(OBJEXT)
test_modem_helpers_xmm_OBJECTS = $(am_test_modem_helpers_xmm_OBJECTS)
@WITH_SHARED_XMM_TRUE@test_modem_helpers_xmm_DEPENDENCIES =  \
@WITH_SHARED_XMM_TRUE@	$(builddir)/libhelpers-xmm.la \
@WITH_SHARED_XMM_TRUE@	$(top_builddir)/src/libhelpers.la \
@WITH_SHARED_XMM_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la
am__test_service_generic_SOURCES_DIST =  \
	generic/tests/test-service-generic.c
@ENABLE_PLUGIN_GENERIC_TRUE@am_test_service_generic_OBJECTS = generic/tests/test_service_generic-test-service-generic.$(OBJEXT)
test_service_generic_OBJECTS = $(am_test_service_generic_OBJECTS)
@ENABLE_PLUGIN_GENERIC_TRUE@test_service_generic_DEPENDENCIES = $(top_builddir)/libmm-glib/libmm-glib.la \
@ENABLE_PLUGIN_GENERIC_TRUE@	$(am__DEPENDENCIES_1)
am_test_udev_rules_OBJECTS = tests/test-udev-rules.$(OBJEXT)
test_udev_rules_OBJECTS = $(am_test_udev_rules_OBJECTS)
test_udev_rules_DEPENDENCIES = $(top_builddir)/src/libkerneldevice.la \
	$(top_builddir)/libmm-glib/libmm-glib.la
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-bearer-fibocom-ecm.Plo \
	NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-fibocom.Plo \
	NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-mbim-fibocom.Plo \
	NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-mbim-xmm-fibocom.Plo \
	NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-plugin-fibocom.Plo \
	altair/$(DEPDIR)/libhelpers_altair_lte_la-mm-modem-helpers-altair-lte.Plo \
	altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-broadband-bearer-altair-lte.Plo \
	altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-broadband-modem-altair-lte.Plo \
	altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-plugin-altair-lte.Plo \
	altair/tests/$(DEPDIR)/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.Po \
	anydata/$(DEPDIR)/libmm_plugin_anydata_la-mm-broadband-modem-anydata.Plo \
	anydata/$(DEPDIR)/libmm_plugin_anydata_la-mm-plugin-anydata.Plo \
	broadmobi/$(DEPDIR)/libmm_plugin_broadmobi_la-mm-plugin-broadmobi.Plo \
	cinterion/$(DEPDIR)/libhelpers_cinterion_la-mm-modem-helpers-cinterion.Plo \
	cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-bearer-cinterion.Plo \
	cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-modem-cinterion.Plo \
	cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-modem-qmi-cinterion.Plo \
	cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-plugin-cinterion.Plo \
	cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-shared-cinterion.Plo \
	cinterion/tests/$(DEPDIR)/test_modem_helpers_cinterion-test-modem-helpers-cinterion.Po \
	dell/$(DEPDIR)/libmm_plugin_dell_la-mm-plugin-dell.Plo \
	dlink/$(DEPDIR)/libmm_plugin_dlink_la-mm-plugin-dlink.Plo \
	foxconn/$(DEPDIR)/libmm_plugin_foxconn_la-mm-plugin-foxconn.Plo \
	foxconn/$(DEPDIR)/libmm_shared_foxconn_la-mm-broadband-modem-foxconn-t77w968.Plo \
	foxconn/$(DEPDIR)/libmm_shared_foxconn_la-mm-shared.Plo \
	generic/$(DEPDIR)/libmm_plugin_generic_la-mm-plugin-generic.Plo \
	generic/tests/$(DEPDIR)/test_service_generic-test-service-generic.Po \
	haier/$(DEPDIR)/libmm_plugin_haier_la-mm-plugin-haier.Plo \
	huawei/$(DEPDIR)/libhelpers_huawei_la-mm-modem-helpers-huawei.Plo \
	huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-broadband-bearer-huawei.Plo \
	huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-broadband-modem-huawei.Plo \
	huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-plugin-huawei.Plo \
	huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-sim-huawei.Plo \
	huawei/tests/$(DEPDIR)/test_modem_helpers_huawei-test-modem-helpers-huawei.Po \
	icera/$(DEPDIR)/libhelpers_icera_la-mm-modem-helpers-icera.Plo \
	icera/$(DEPDIR)/libmm_shared_icera_la-mm-broadband-bearer-icera.Plo \
	icera/$(DEPDIR)/libmm_shared_icera_la-mm-broadband-modem-icera.Plo \
	icera/$(DEPDIR)/libmm_shared_icera_la-mm-shared.Plo \
	icera/tests/$(DEPDIR)/test_modem_helpers_icera-test-modem-helpers-icera.Po \
	iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-bearer-iridium.Plo \
	iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-broadband-modem-iridium.Plo \
	iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-plugin-iridium.Plo \
	iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-sim-iridium.Plo \
	linktop/$(DEPDIR)/libhelpers_linktop_la-mm-modem-helpers-linktop.Plo \
	linktop/$(DEPDIR)/libmm_plugin_linktop_la-mm-broadband-modem-linktop.Plo \
	linktop/$(DEPDIR)/libmm_plugin_linktop_la-mm-plugin-linktop.Plo \
	linktop/tests/$(DEPDIR)/test_modem_helpers_linktop-test-modem-helpers-linktop.Po \
	longcheer/$(DEPDIR)/libmm_plugin_longcheer_la-mm-broadband-modem-longcheer.Plo \
	longcheer/$(DEPDIR)/libmm_plugin_longcheer_la-mm-plugin-longcheer.Plo \
	mbm/$(DEPDIR)/libhelpers_mbm_la-mm-modem-helpers-mbm.Plo \
	mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-broadband-bearer-mbm.Plo \
	mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-broadband-modem-mbm.Plo \
	mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-plugin-mbm.Plo \
	mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-sim-mbm.Plo \
	mbm/tests/$(DEPDIR)/test_modem_helpers_mbm-test-modem-helpers-mbm.Po \
	me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-broadband-bearer-me3630.Plo \
	me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-broadband-modem-me3630.Plo \
	me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-common-me3630.Plo \
	me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-plugin-me3630.Plo \
	motorola/$(DEPDIR)/libmm_plugin_motorola_la-mm-broadband-modem-motorola.Plo \
	motorola/$(DEPDIR)/libmm_plugin_motorola_la-mm-plugin-motorola.Plo \
	mtk/$(DEPDIR)/libmm_plugin_mtk_la-mm-broadband-modem-mtk.Plo \
	mtk/$(DEPDIR)/libmm_plugin_mtk_la-mm-plugin-mtk.Plo \
	nokia/$(DEPDIR)/libmm_plugin_nokia_icera_la-mm-plugin-nokia-icera.Plo \
	nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-broadband-modem-nokia.Plo \
	nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-plugin-nokia.Plo \
	nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-sim-nokia.Plo \
	novatel/$(DEPDIR)/libmm_plugin_novatel_la-mm-plugin-novatel.Plo \
	novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-broadband-bearer-novatel-lte.Plo \
	novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-broadband-modem-novatel-lte.Plo \
	novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-plugin-novatel-lte.Plo \
	novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-sim-novatel-lte.Plo \
	novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-broadband-modem-novatel.Plo \
	novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-common-novatel.Plo \
	novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-shared.Plo \
	option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-broadband-bearer-hso.Plo \
	option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-broadband-modem-hso.Plo \
	option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-plugin-hso.Plo \
	option/$(DEPDIR)/libmm_plugin_option_la-mm-plugin-option.Plo \
	option/$(DEPDIR)/libmm_shared_option_la-mm-broadband-modem-option.Plo \
	option/$(DEPDIR)/libmm_shared_option_la-mm-shared.Plo \
	pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-broadband-modem-pantech.Plo \
	pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-plugin-pantech.Plo \
	pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-sim-pantech.Plo \
	quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-broadband-modem-qmi-quectel.Plo \
	quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-broadband-modem-quectel.Plo \
	quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-plugin-quectel.Plo \
	quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-shared-quectel.Plo \
	samsung/$(DEPDIR)/libmm_plugin_samsung_la-mm-broadband-modem-samsung.Plo \
	samsung/$(DEPDIR)/libmm_plugin_samsung_la-mm-plugin-samsung.Plo \
	sierra/$(DEPDIR)/libhelpers_sierra_la-mm-modem-helpers-sierra.Plo \
	sierra/$(DEPDIR)/libmm_plugin_sierra_la-mm-plugin-sierra.Plo \
	sierra/$(DEPDIR)/libmm_plugin_sierra_legacy_la-mm-broadband-modem-sierra-icera.Plo \
	sierra/$(DEPDIR)/libmm_plugin_sierra_legacy_la-mm-plugin-sierra-legacy.Plo \
	sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-broadband-bearer-sierra.Plo \
	sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-broadband-modem-sierra.Plo \
	sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-common-sierra.Plo \
	sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-shared.Plo \
	sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-sim-sierra.Plo \
	sierra/tests/$(DEPDIR)/test_modem_helpers_sierra-test-modem-helpers-sierra.Po \
	simtech/$(DEPDIR)/libhelpers_simtech_la-mm-modem-helpers-simtech.Plo \
	simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-broadband-modem-qmi-simtech.Plo \
	simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-broadband-modem-simtech.Plo \
	simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-plugin-simtech.Plo \
	simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-shared-simtech.Plo \
	simtech/tests/$(DEPDIR)/test_modem_helpers_simtech-test-modem-helpers-simtech.Po \
	telit/$(DEPDIR)/libhelpers_telit_la-mm-modem-helpers-telit.Plo \
	telit/$(DEPDIR)/libhelpers_telit_la-mm-telit-enums-types.Plo \
	telit/$(DEPDIR)/libmm_plugin_telit_la-mm-plugin-telit.Plo \
	telit/$(DEPDIR)/libmm_shared_telit_la-mm-broadband-modem-mbim-telit.Plo \
	telit/$(DEPDIR)/libmm_shared_telit_la-mm-broadband-modem-telit.Plo \
	telit/$(DEPDIR)/libmm_shared_telit_la-mm-common-telit.Plo \
	telit/$(DEPDIR)/libmm_shared_telit_la-mm-shared-telit.Plo \
	telit/$(DEPDIR)/libmm_shared_telit_la-mm-shared.Plo \
	telit/tests/$(DEPDIR)/test_modem_helpers_telit-test-mm-modem-helpers-telit.Po \
	tests/$(DEPDIR)/libmm_test_common_la-test-fixture.Plo \
	tests/$(DEPDIR)/libmm_test_common_la-test-helpers.Plo \
	tests/$(DEPDIR)/libmm_test_common_la-test-port-context.Plo \
	tests/$(DEPDIR)/test-keyfiles.Po \
	tests/$(DEPDIR)/test-udev-rules.Po \
	thuraya/$(DEPDIR)/libhelpers_thuraya_la-mm-modem-helpers-thuraya.Plo \
	thuraya/$(DEPDIR)/libmm_plugin_thuraya_la-mm-broadband-modem-thuraya.Plo \
	thuraya/$(DEPDIR)/libmm_plugin_thuraya_la-mm-plugin-thuraya.Plo \
	thuraya/tests/$(DEPDIR)/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.Po \
	tplink/$(DEPDIR)/libmm_plugin_tplink_la-mm-plugin-tplink.Plo \
	ublox/$(DEPDIR)/libhelpers_ublox_la-mm-modem-helpers-ublox.Plo \
	ublox/$(DEPDIR)/libhelpers_ublox_la-mm-ublox-enums-types.Plo \
	ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-broadband-bearer-ublox.Plo \
	ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-broadband-modem-ublox.Plo \
	ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-plugin-ublox.Plo \
	ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-sim-ublox.Plo \
	ublox/tests/$(DEPDIR)/test_modem_helpers_ublox-test-modem-helpers-ublox.Po \
	via/$(DEPDIR)/libmm_plugin_via_la-mm-broadband-modem-via.Plo \
	via/$(DEPDIR)/libmm_plugin_via_la-mm-plugin-via.Plo \
	wavecom/$(DEPDIR)/libmm_plugin_wavecom_la-mm-broadband-modem-wavecom.Plo \
	wavecom/$(DEPDIR)/libmm_plugin_wavecom_la-mm-plugin-wavecom.Plo \
	x22x/$(DEPDIR)/libmm_plugin_x22x_la-mm-broadband-modem-x22x.Plo \
	x22x/$(DEPDIR)/libmm_plugin_x22x_la-mm-plugin-x22x.Plo \
	xmm/$(DEPDIR)/libhelpers_xmm_la-mm-modem-helpers-xmm.Plo \
	xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-broadband-modem-mbim-xmm.Plo \
	xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-broadband-modem-xmm.Plo \
	xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-shared-xmm.Plo \
	xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-shared.Plo \
	xmm/tests/$(DEPDIR)/test_modem_helpers_xmm-test-modem-helpers-xmm.Po \
	zte/$(DEPDIR)/libmm_plugin_zte_la-mm-broadband-modem-zte-icera.Plo \
	zte/$(DEPDIR)/libmm_plugin_zte_la-mm-broadband-modem-zte.Plo \
	zte/$(DEPDIR)/libmm_plugin_zte_la-mm-common-zte.Plo \
	zte/$(DEPDIR)/libmm_plugin_zte_la-mm-plugin-zte.Plo
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(libhelpers_altair_lte_la_SOURCES) \
	$(libhelpers_cinterion_la_SOURCES) \
	$(libhelpers_huawei_la_SOURCES) $(libhelpers_icera_la_SOURCES) \
	$(libhelpers_linktop_la_SOURCES) $(libhelpers_mbm_la_SOURCES) \
	$(libhelpers_sierra_la_SOURCES) \
	$(libhelpers_simtech_la_SOURCES) \
	$(libhelpers_telit_la_SOURCES) \
	$(nodist_libhelpers_telit_la_SOURCES) \
	$(libhelpers_thuraya_la_SOURCES) \
	$(libhelpers_ublox_la_SOURCES) \
	$(nodist_libhelpers_ublox_la_SOURCES) \
	$(libhelpers_xmm_la_SOURCES) $(libmm_plugin_NL668_la_SOURCES) \
	$(libmm_plugin_altair_lte_la_SOURCES) \
	$(libmm_plugin_anydata_la_SOURCES) \
	$(libmm_plugin_broadmobi_la_SOURCES) \
	$(libmm_plugin_cinterion_la_SOURCES) \
	$(libmm_plugin_dell_la_SOURCES) \
	$(libmm_plugin_dlink_la_SOURCES) \
	$(libmm_plugin_ericsson_mbm_la_SOURCES) \
	$(libmm_plugin_foxconn_la_SOURCES) \
	$(libmm_plugin_generic_la_SOURCES) \
	$(libmm_plugin_haier_la_SOURCES) \
	$(libmm_plugin_huawei_la_SOURCES) \
	$(libmm_plugin_iridium_la_SOURCES) \
	$(libmm_plugin_linktop_la_SOURCES) \
	$(libmm_plugin_longcheer_la_SOURCES) \
	$(libmm_plugin_me3630_la_SOURCES) \
	$(libmm_plugin_motorola_la_SOURCES) \
	$(libmm_plugin_mtk_la_SOURCES) \
	$(libmm_plugin_nokia_icera_la_SOURCES) \
	$(libmm_plugin_nokia_la_SOURCES) \
	$(libmm_plugin_novatel_lte_la_SOURCES) \
	$(libmm_plugin_novatel_la_SOURCES) \
	$(libmm_plugin_option_hso_la_SOURCES) \
	$(libmm_plugin_option_la_SOURCES) \
	$(libmm_plugin_pantech_la_SOURCES) \
	$(libmm_plugin_quectel_la_SOURCES) \
	$(libmm_plugin_samsung_la_SOURCES) \
	$(libmm_plugin_sierra_legacy_la_SOURCES) \
	$(libmm_plugin_sierra_la_SOURCES) \
	$(libmm_plugin_simtech_la_SOURCES) \
	$(libmm_plugin_telit_la_SOURCES) \
	$(libmm_plugin_thuraya_la_SOURCES) \
	$(libmm_plugin_tplink_la_SOURCES) \
	$(libmm_plugin_ublox_la_SOURCES) \
	$(libmm_plugin_via_la_SOURCES) \
	$(libmm_plugin_wavecom_la_SOURCES) \
	$(libmm_plugin_x22x_la_SOURCES) $(libmm_plugin_zte_la_SOURCES) \
	$(libmm_shared_foxconn_la_SOURCES) \
	$(libmm_shared_icera_la_SOURCES) \
	$(libmm_shared_novatel_la_SOURCES) \
	$(libmm_shared_option_la_SOURCES) \
	$(libmm_shared_sierra_la_SOURCES) \
	$(libmm_shared_telit_la_SOURCES) \
	$(libmm_shared_xmm_la_SOURCES) $(libmm_test_common_la_SOURCES) \
	$(test_keyfiles_SOURCES) \
	$(test_modem_helpers_altair_lte_SOURCES) \
	$(test_modem_helpers_cinterion_SOURCES) \
	$(test_modem_helpers_huawei_SOURCES) \
	$(test_modem_helpers_icera_SOURCES) \
	$(test_modem_helpers_linktop_SOURCES) \
	$(test_modem_helpers_mbm_SOURCES) \
	$(test_modem_helpers_sierra_SOURCES) \
	$(test_modem_helpers_simtech_SOURCES) \
	$(test_modem_helpers_telit_SOURCES) \
	$(test_modem_helpers_thuraya_SOURCES) \
	$(test_modem_helpers_ublox_SOURCES) \
	$(test_modem_helpers_xmm_SOURCES) \
	$(test_service_generic_SOURCES) $(test_udev_rules_SOURCES)
DIST_SOURCES = $(am__libhelpers_altair_lte_la_SOURCES_DIST) \
	$(am__libhelpers_cinterion_la_SOURCES_DIST) \
	$(am__libhelpers_huawei_la_SOURCES_DIST) \
	$(am__libhelpers_icera_la_SOURCES_DIST) \
	$(am__libhelpers_linktop_la_SOURCES_DIST) \
	$(am__libhelpers_mbm_la_SOURCES_DIST) \
	$(am__libhelpers_sierra_la_SOURCES_DIST) \
	$(am__libhelpers_simtech_la_SOURCES_DIST) \
	$(am__libhelpers_telit_la_SOURCES_DIST) \
	$(am__libhelpers_thuraya_la_SOURCES_DIST) \
	$(am__libhelpers_ublox_la_SOURCES_DIST) \
	$(am__libhelpers_xmm_la_SOURCES_DIST) \
	$(am__libmm_plugin_NL668_la_SOURCES_DIST) \
	$(am__libmm_plugin_altair_lte_la_SOURCES_DIST) \
	$(am__libmm_plugin_anydata_la_SOURCES_DIST) \
	$(am__libmm_plugin_broadmobi_la_SOURCES_DIST) \
	$(am__libmm_plugin_cinterion_la_SOURCES_DIST) \
	$(am__libmm_plugin_dell_la_SOURCES_DIST) \
	$(am__libmm_plugin_dlink_la_SOURCES_DIST) \
	$(am__libmm_plugin_ericsson_mbm_la_SOURCES_DIST) \
	$(am__libmm_plugin_foxconn_la_SOURCES_DIST) \
	$(am__libmm_plugin_generic_la_SOURCES_DIST) \
	$(am__libmm_plugin_haier_la_SOURCES_DIST) \
	$(am__libmm_plugin_huawei_la_SOURCES_DIST) \
	$(am__libmm_plugin_iridium_la_SOURCES_DIST) \
	$(am__libmm_plugin_linktop_la_SOURCES_DIST) \
	$(am__libmm_plugin_longcheer_la_SOURCES_DIST) \
	$(libmm_plugin_me3630_la_SOURCES) \
	$(am__libmm_plugin_motorola_la_SOURCES_DIST) \
	$(am__libmm_plugin_mtk_la_SOURCES_DIST) \
	$(am__libmm_plugin_nokia_icera_la_SOURCES_DIST) \
	$(am__libmm_plugin_nokia_la_SOURCES_DIST) \
	$(am__libmm_plugin_novatel_lte_la_SOURCES_DIST) \
	$(am__libmm_plugin_novatel_la_SOURCES_DIST) \
	$(am__libmm_plugin_option_hso_la_SOURCES_DIST) \
	$(am__libmm_plugin_option_la_SOURCES_DIST) \
	$(am__libmm_plugin_pantech_la_SOURCES_DIST) \
	$(am__libmm_plugin_quectel_la_SOURCES_DIST) \
	$(am__libmm_plugin_samsung_la_SOURCES_DIST) \
	$(am__libmm_plugin_sierra_legacy_la_SOURCES_DIST) \
	$(am__libmm_plugin_sierra_la_SOURCES_DIST) \
	$(am__libmm_plugin_simtech_la_SOURCES_DIST) \
	$(am__libmm_plugin_telit_la_SOURCES_DIST) \
	$(am__libmm_plugin_thuraya_la_SOURCES_DIST) \
	$(am__libmm_plugin_tplink_la_SOURCES_DIST) \
	$(am__libmm_plugin_ublox_la_SOURCES_DIST) \
	$(am__libmm_plugin_via_la_SOURCES_DIST) \
	$(am__libmm_plugin_wavecom_la_SOURCES_DIST) \
	$(am__libmm_plugin_x22x_la_SOURCES_DIST) \
	$(am__libmm_plugin_zte_la_SOURCES_DIST) \
	$(am__libmm_shared_foxconn_la_SOURCES_DIST) \
	$(am__libmm_shared_icera_la_SOURCES_DIST) \
	$(am__libmm_shared_novatel_la_SOURCES_DIST) \
	$(am__libmm_shared_option_la_SOURCES_DIST) \
	$(am__libmm_shared_sierra_la_SOURCES_DIST) \
	$(am__libmm_shared_telit_la_SOURCES_DIST) \
	$(am__libmm_shared_xmm_la_SOURCES_DIST) \
	$(libmm_test_common_la_SOURCES) $(test_keyfiles_SOURCES) \
	$(am__test_modem_helpers_altair_lte_SOURCES_DIST) \
	$(am__test_modem_helpers_cinterion_SOURCES_DIST) \
	$(am__test_modem_helpers_huawei_SOURCES_DIST) \
	$(am__test_modem_helpers_icera_SOURCES_DIST) \
	$(am__test_modem_helpers_linktop_SOURCES_DIST) \
	$(am__test_modem_helpers_mbm_SOURCES_DIST) \
	$(am__test_modem_helpers_sierra_SOURCES_DIST) \
	$(am__test_modem_helpers_simtech_SOURCES_DIST) \
	$(am__test_modem_helpers_telit_SOURCES_DIST) \
	$(am__test_modem_helpers_thuraya_SOURCES_DIST) \
	$(am__test_modem_helpers_ublox_SOURCES_DIST) \
	$(am__test_modem_helpers_xmm_SOURCES_DIST) \
	$(am__test_service_generic_SOURCES_DIST) \
	$(test_udev_rules_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__dist_pkgdata_DATA_DIST =  \
	foxconn/mm-foxconn-t77w968-carrier-mapping.conf
am__dist_udevrules_DATA_DIST =  \
	broadmobi/77-mm-broadmobi-port-types.rules \
	cinterion/77-mm-cinterion-port-types.rules \
	dell/77-mm-dell-port-types.rules \
	dlink/77-mm-dlink-port-types.rules \
	foxconn/77-mm-foxconn-port-types.rules \
	haier/77-mm-haier-port-types.rules \
	huawei/77-mm-huawei-net-port-types.rules \
	longcheer/77-mm-longcheer-port-types.rules \
	mbm/77-mm-ericsson-mbm.rules mtk/77-mm-mtk-port-types.rules \
	nokia/77-mm-nokia-port-types.rules \
	quectel/77-mm-quectel-port-types.rules \
	sierra/77-mm-sierra.rules \
	simtech/77-mm-simtech-port-types.rules \
	telit/77-mm-telit-port-types.rules \
	tplink/77-mm-tplink-port-types.rules \
	ublox/77-mm-ublox-port-types.rules \
	x22x/77-mm-x22x-port-types.rules \
	zte/77-mm-zte-port-types.rules \
	NL668/77-mm-fibocom-port-types.rules
DATA = $(dist_pkgdata_DATA) $(dist_udevrules_DATA)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp \
	$(top_srcdir)/gtester.make
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CODE_COVERAGE_CFLAGS = @CODE_COVERAGE_CFLAGS@
CODE_COVERAGE_ENABLED = @CODE_COVERAGE_ENABLED@
CODE_COVERAGE_LDFLAGS = @CODE_COVERAGE_LDFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CYGPATH_W = @CYGPATH_W@
DBUS_SYS_DIR = @DBUS_SYS_DIR@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
GCOV = @GCOV@
GDBUS_CODEGEN = @GDBUS_CODEGEN@
GENHTML = @GENHTML@
GETTEXT_MACRO_VERSION = @GETTEXT_MACRO_VERSION@
GETTEXT_PACKAGE = @GETTEXT_PACKAGE@
GLIB_MKENUMS = @GLIB_MKENUMS@
GMSGFMT = @GMSGFMT@
GMSGFMT_015 = @GMSGFMT_015@
GREP = @GREP@
GTKDOC_CHECK = @GTKDOC_CHECK@
GTKDOC_CHECK_PATH = @GTKDOC_CHECK_PATH@
GTKDOC_DEPS_CFLAGS = @GTKDOC_DEPS_CFLAGS@
GTKDOC_DEPS_LIBS = @GTKDOC_DEPS_LIBS@
GTKDOC_MKPDF = @GTKDOC_MKPDF@
GTKDOC_REBASE = @GTKDOC_REBASE@
GUDEV_CFLAGS = @GUDEV_CFLAGS@
GUDEV_LIBS = @GUDEV_LIBS@
HTML_DIR = @HTML_DIR@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
INTLLIBS = @INTLLIBS@
INTL_MACOSX_LIBS = @INTL_MACOSX_LIBS@
INTROSPECTION_CFLAGS = @INTROSPECTION_CFLAGS@
INTROSPECTION_COMPILER = @INTROSPECTION_COMPILER@
INTROSPECTION_GENERATE = @INTROSPECTION_GENERATE@
INTROSPECTION_GIRDIR = @INTROSPECTION_GIRDIR@
INTROSPECTION_LIBS = @INTROSPECTION_LIBS@
INTROSPECTION_MAKEFILE = @INTROSPECTION_MAKEFILE@
INTROSPECTION_SCANNER = @INTROSPECTION_SCANNER@
INTROSPECTION_TYPELIBDIR = @INTROSPECTION_TYPELIBDIR@
LCOV = @LCOV@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBICONV = @LIBICONV@
LIBINTL = @LIBINTL@
LIBMM_GLIB_CFLAGS = @LIBMM_GLIB_CFLAGS@
LIBMM_GLIB_LIBS = @LIBMM_GLIB_LIBS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBSYSTEMD_CFLAGS = @LIBSYSTEMD_CFLAGS@
LIBSYSTEMD_LIBS = @LIBSYSTEMD_LIBS@
LIBSYSTEMD_LOGIN_CFLAGS = @LIBSYSTEMD_LOGIN_CFLAGS@
LIBSYSTEMD_LOGIN_LIBS = @LIBSYSTEMD_LOGIN_LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBICONV = @LTLIBICONV@
LTLIBINTL = @LTLIBINTL@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAINT = @MAINT@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MBIM_CFLAGS = @MBIM_CFLAGS@
MBIM_LIBS = @MBIM_LIBS@
MKDIR_P = @MKDIR_P@
MMCLI_CFLAGS = @MMCLI_CFLAGS@
MMCLI_LIBS = @MMCLI_LIBS@
MM_CFLAGS = @MM_CFLAGS@
MM_DEFAULT_USER_POLICY = @MM_DEFAULT_USER_POLICY@
MM_GLIB_LT_AGE = @MM_GLIB_LT_AGE@
MM_GLIB_LT_CURRENT = @MM_GLIB_LT_CURRENT@
MM_GLIB_LT_REVISION = @MM_GLIB_LT_REVISION@
MM_LIBS = @MM_LIBS@
MM_MAJOR_VERSION = @MM_MAJOR_VERSION@
MM_MICRO_VERSION = @MM_MICRO_VERSION@
MM_MINOR_VERSION = @MM_MINOR_VERSION@
MM_POLKIT_SERVICE = @MM_POLKIT_SERVICE@
MM_VERSION = @MM_VERSION@
MSGFMT = @MSGFMT@
MSGFMT_015 = @MSGFMT_015@
MSGMERGE = @MSGMERGE@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PKG_CONFIG = @PKG_CONFIG@
PKG_CONFIG_LIBDIR = @PKG_CONFIG_LIBDIR@
PKG_CONFIG_PATH = @PKG_CONFIG_PATH@
POLKIT_CFLAGS = @POLKIT_CFLAGS@
POLKIT_LIBS = @POLKIT_LIBS@
POSUB = @POSUB@
QMI_CFLAGS = @QMI_CFLAGS@
QMI_LIBS = @QMI_LIBS@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
SYSTEMD_UNIT_DIR = @SYSTEMD_UNIT_DIR@
UDEV_BASE_DIR = @UDEV_BASE_DIR@
USE_NLS = @USE_NLS@
VAPIGEN = @VAPIGEN@
VAPIGEN_MAKEFILE = @VAPIGEN_MAKEFILE@
VAPIGEN_VAPIDIR = @VAPIGEN_VAPIDIR@
VERSION = @VERSION@
WARN_CFLAGS = @WARN_CFLAGS@
WARN_LDFLAGS = @WARN_LDFLAGS@
WARN_SCANNERFLAGS = @WARN_SCANNERFLAGS@
XGETTEXT = @XGETTEXT@
XGETTEXT_015 = @XGETTEXT_015@
XGETTEXT_EXTRA_OPTIONS = @XGETTEXT_EXTRA_OPTIONS@
XSLTPROC_CHECK = @XSLTPROC_CHECK@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
GTESTER = gtester
GTESTER_REPORT = gtester-report

# initialize variables for unconditional += appending
EXTRA_DIST = tests/gsm-port.conf

################################################################################
TEST_PROGS = $(noinst_PROGRAMS)

################################################################################
# common
################################################################################

# dist_udevrules_DATA += me3630/77-mm-me3630-port-types.rules
AM_CFLAGS = $(WARN_CFLAGS) $(MM_CFLAGS) $(CODE_COVERAGE_CFLAGS) \
	$(GUDEV_CFLAGS) -DPKGDATADIR=\"${pkgdatadir}\" -I$(top_srcdir) \
	-I$(top_srcdir)/src -I$(top_builddir)/src \
	-I$(top_srcdir)/src/kerneldevice -I$(top_srcdir)/include \
	-I$(top_builddir)/include -I$(top_srcdir)/libmm-glib \
	-I$(top_srcdir)/libmm-glib/generated \
	-I$(top_builddir)/libmm-glib/generated $(NULL) $(am__append_1) \
	$(am__append_3) $(am__append_32) $(am__append_38) \
	$(am__append_41) $(am__append_44) $(am__append_48) \
	$(am__append_51) $(am__append_56) $(am__append_63) \
	$(am__append_68) $(am__append_72) $(am__append_76) \
	$(am__append_85) $(am__append_95) $(am__append_98) \
	$(am__append_104) $(am__append_115) $(am__append_118) \
	-DTESTUDEVRULESDIR_ME3630=\"${srcdir}/me3630\" \
	-DTESTUDEVRULESDIR_NL668=\"${srcdir}/NL668\"
AM_LDFLAGS = $(WARN_LDFLAGS) $(MM_LIBS) $(CODE_COVERAGE_LDFLAGS) \
	$(GUDEV_LIBS) $(NULL) $(am__append_2) $(am__append_4)

# Common compiler/linker flags for shared utils
SHARED_COMMON_COMPILER_FLAGS = \
	$(NULL)

SHARED_COMMON_LINKER_FLAGS = \
	-module        \
	-avoid-version \
	$(NULL)


# Common compiler/linker flags for plugins
PLUGIN_COMMON_COMPILER_FLAGS = \
	$(NULL)

PLUGIN_COMMON_LINKER_FLAGS = \
	-module        \
	-avoid-version \
	-export-symbols-regex '^mm_plugin_major_version$$|^mm_plugin_minor_version$$|^mm_plugin_create$$' \
	$(NULL)


# UDev rules
udevrulesdir = $(UDEV_BASE_DIR)/rules.d
dist_udevrules_DATA = $(am__append_31) $(am__append_37) \
	$(am__append_40) $(am__append_43) $(am__append_46) \
	$(am__append_50) $(am__append_55) $(am__append_62) \
	$(am__append_67) $(am__append_71) $(am__append_75) \
	$(am__append_84) $(am__append_88) $(am__append_94) \
	$(am__append_97) $(am__append_103) $(am__append_105) \
	$(am__append_114) $(am__append_117) \
	NL668/77-mm-fibocom-port-types.rules

# Helper libs

################################################################################
# common service test support
################################################################################
noinst_LTLIBRARIES = libmm-test-common.la $(am__append_5) \
	$(am__append_8) $(am__append_13) $(am__append_19) \
	$(am__append_26) $(am__append_33) $(am__append_52) \
	$(am__append_58) $(am__append_64) $(am__append_90) \
	$(am__append_99) $(am__append_106)

# Plugins

################################################################################
# plugin: me3630
################################################################################

#if ENABLE_PLUGIN_ME3630

#endif

################################################################################
# plugin: NL668
################################################################################

#if 1
pkglib_LTLIBRARIES = $(am__append_7) $(am__append_10) $(am__append_11) \
	$(am__append_12) $(am__append_15) $(am__append_21) \
	$(am__append_23) $(am__append_24) $(am__append_28) \
	$(am__append_29) $(am__append_30) $(am__append_35) \
	$(am__append_39) $(am__append_42) $(am__append_45) \
	$(am__append_49) $(am__append_54) $(am__append_57) \
	$(am__append_60) $(am__append_61) $(am__append_66) \
	$(am__append_69) $(am__append_70) $(am__append_73) \
	$(am__append_74) $(am__append_77) $(am__append_78) \
	$(am__append_79) $(am__append_80) $(am__append_81) \
	$(am__append_82) $(am__append_86) $(am__append_87) \
	$(am__append_89) $(am__append_92) $(am__append_96) \
	$(am__append_101) $(am__append_102) $(am__append_110) \
	$(am__append_111) $(am__append_112) $(am__append_113) \
	$(am__append_116) libmm-plugin-me3630.la libmm-plugin-NL668.la

# Built sources
BUILT_SOURCES = $(am__append_17) $(am__append_107)

# Clean files
CLEANFILES = $(am__append_18) $(am__append_108)

# Data files
dist_pkgdata_DATA = $(am__append_47)
libmm_test_common_la_SOURCES = \
	tests/test-fixture.h \
	tests/test-fixture.c \
	tests/test-port-context.h \
	tests/test-port-context.c \
	tests/test-helpers.h \
	tests/test-helpers.c \
	$(NULL)

libmm_test_common_la_CPPFLAGS = \
	-I$(top_builddir)/libmm-glib/generated/tests \
	-DTEST_SERVICES=\""$(abs_top_builddir)/data/tests"\" \
	$(NULL)

libmm_test_common_la_LIBADD = \
	${top_builddir}/libmm-glib/generated/tests/libmm-test-generated.la \
	$(top_builddir)/libmm-glib/libmm-glib.la

TEST_COMMON_COMPILER_FLAGS = \
	$(MM_CFLAGS) \
	-I$(top_srcdir)/plugins/tests \
	-I$(top_srcdir)/include \
	-I$(top_builddir)/include \
	-I$(top_srcdir)/libmm-glib \
	-I$(top_srcdir)/libmm-glib/generated \
	-I$(top_builddir)/libmm-glib/generated \
	-I$(top_builddir)/libmm-glib/generated/tests \
	-DCOMMON_GSM_PORT_CONF=\""$(abs_top_srcdir)/plugins/tests/gsm-port.conf"\" \
	$(NULL)

TEST_COMMON_LIBADD_FLAGS = \
	$(builddir)/libmm-test-common.la \
	$(NULL)

@WITH_SHARED_ICERA_TRUE@libhelpers_icera_la_SOURCES = \
@WITH_SHARED_ICERA_TRUE@	icera/mm-modem-helpers-icera.c \
@WITH_SHARED_ICERA_TRUE@	icera/mm-modem-helpers-icera.h \
@WITH_SHARED_ICERA_TRUE@	$(NULL)

@WITH_SHARED_ICERA_TRUE@libhelpers_icera_la_CPPFLAGS = \
@WITH_SHARED_ICERA_TRUE@	-DMM_MODULE_NAME=\"shared-icera\" \
@WITH_SHARED_ICERA_TRUE@	$(NULL)

@WITH_SHARED_ICERA_TRUE@test_modem_helpers_icera_SOURCES = \
@WITH_SHARED_ICERA_TRUE@	icera/tests/test-modem-helpers-icera.c \
@WITH_SHARED_ICERA_TRUE@	$(NULL)

@WITH_SHARED_ICERA_TRUE@test_modem_helpers_icera_CPPFLAGS = \
@WITH_SHARED_ICERA_TRUE@	-I$(top_srcdir)/plugins/icera \
@WITH_SHARED_ICERA_TRUE@	$(NULL)

@WITH_SHARED_ICERA_TRUE@test_modem_helpers_icera_LDADD = \
@WITH_SHARED_ICERA_TRUE@	$(builddir)/libhelpers-icera.la  \
@WITH_SHARED_ICERA_TRUE@	$(top_builddir)/src/libhelpers.la \
@WITH_SHARED_ICERA_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la \
@WITH_SHARED_ICERA_TRUE@	$(NULL)

@WITH_SHARED_ICERA_TRUE@libmm_shared_icera_la_SOURCES = \
@WITH_SHARED_ICERA_TRUE@	icera/mm-shared.c \
@WITH_SHARED_ICERA_TRUE@	icera/mm-broadband-modem-icera.h \
@WITH_SHARED_ICERA_TRUE@	icera/mm-broadband-modem-icera.c \
@WITH_SHARED_ICERA_TRUE@	icera/mm-broadband-bearer-icera.h \
@WITH_SHARED_ICERA_TRUE@	icera/mm-broadband-bearer-icera.c \
@WITH_SHARED_ICERA_TRUE@	$(NULL)

@WITH_SHARED_ICERA_TRUE@libmm_shared_icera_la_CPPFLAGS = \
@WITH_SHARED_ICERA_TRUE@	$(SHARED_COMMON_COMPILER_FLAGS)

@WITH_SHARED_ICERA_TRUE@libmm_shared_icera_la_LDFLAGS = $(SHARED_COMMON_LINKER_FLAGS)
@WITH_SHARED_ICERA_TRUE@libmm_shared_icera_la_LIBADD = \
@WITH_SHARED_ICERA_TRUE@	$(builddir)/libhelpers-icera.la \
@WITH_SHARED_ICERA_TRUE@	$(NULL)

@WITH_SHARED_ICERA_TRUE@ICERA_COMMON_COMPILER_FLAGS = -I$(top_srcdir)/plugins/icera
@WITH_SHARED_SIERRA_TRUE@libhelpers_sierra_la_SOURCES = \
@WITH_SHARED_SIERRA_TRUE@	sierra/mm-modem-helpers-sierra.c \
@WITH_SHARED_SIERRA_TRUE@	sierra/mm-modem-helpers-sierra.h \
@WITH_SHARED_SIERRA_TRUE@	$(NULL)

@WITH_SHARED_SIERRA_TRUE@libhelpers_sierra_la_CPPFLAGS = \
@WITH_SHARED_SIERRA_TRUE@	-DMM_MODULE_NAME=\"shared-sierra\" \
@WITH_SHARED_SIERRA_TRUE@	$(NULL)

@WITH_SHARED_SIERRA_TRUE@test_modem_helpers_sierra_SOURCES = \
@WITH_SHARED_SIERRA_TRUE@	sierra/tests/test-modem-helpers-sierra.c \
@WITH_SHARED_SIERRA_TRUE@	$(NULL)

@WITH_SHARED_SIERRA_TRUE@test_modem_helpers_sierra_CPPFLAGS = \
@WITH_SHARED_SIERRA_TRUE@	-I$(top_srcdir)/plugins/sierra \
@WITH_SHARED_SIERRA_TRUE@	$(NULL)

@WITH_SHARED_SIERRA_TRUE@test_modem_helpers_sierra_LDADD = \
@WITH_SHARED_SIERRA_TRUE@	$(builddir)/libhelpers-sierra.la \
@WITH_SHARED_SIERRA_TRUE@	$(top_builddir)/src/libhelpers.la \
@WITH_SHARED_SIERRA_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la \
@WITH_SHARED_SIERRA_TRUE@	$(NULL)

@WITH_SHARED_SIERRA_TRUE@libmm_shared_sierra_la_SOURCES = \
@WITH_SHARED_SIERRA_TRUE@	sierra/mm-shared.c \
@WITH_SHARED_SIERRA_TRUE@	sierra/mm-common-sierra.c \
@WITH_SHARED_SIERRA_TRUE@	sierra/mm-common-sierra.h \
@WITH_SHARED_SIERRA_TRUE@	sierra/mm-sim-sierra.c \
@WITH_SHARED_SIERRA_TRUE@	sierra/mm-sim-sierra.h \
@WITH_SHARED_SIERRA_TRUE@	sierra/mm-broadband-bearer-sierra.c \
@WITH_SHARED_SIERRA_TRUE@	sierra/mm-broadband-bearer-sierra.h \
@WITH_SHARED_SIERRA_TRUE@	sierra/mm-broadband-modem-sierra.c  \
@WITH_SHARED_SIERRA_TRUE@	sierra/mm-broadband-modem-sierra.h  \
@WITH_SHARED_SIERRA_TRUE@	$(NULL)

@WITH_SHARED_SIERRA_TRUE@libmm_shared_sierra_la_CPPFLAGS = \
@WITH_SHARED_SIERRA_TRUE@	$(SHARED_COMMON_COMPILER_FLAGS) \
@WITH_SHARED_SIERRA_TRUE@	-DMM_MODULE_NAME=\"shared-sierra\" \
@WITH_SHARED_SIERRA_TRUE@	$(NULL)

@WITH_SHARED_SIERRA_TRUE@libmm_shared_sierra_la_LDFLAGS = $(SHARED_COMMON_LINKER_FLAGS)
@WITH_SHARED_SIERRA_TRUE@libmm_shared_sierra_la_LIBADD = \
@WITH_SHARED_SIERRA_TRUE@	$(builddir)/libhelpers-sierra.la \
@WITH_SHARED_SIERRA_TRUE@	$(NULL)

@WITH_SHARED_SIERRA_TRUE@SIERRA_COMMON_COMPILER_FLAGS = -I$(top_srcdir)/plugins/sierra
@WITH_SHARED_OPTION_TRUE@libmm_shared_option_la_SOURCES = \
@WITH_SHARED_OPTION_TRUE@	option/mm-shared.c \
@WITH_SHARED_OPTION_TRUE@	option/mm-broadband-modem-option.c \
@WITH_SHARED_OPTION_TRUE@	option/mm-broadband-modem-option.h \
@WITH_SHARED_OPTION_TRUE@	$(NULL)

@WITH_SHARED_OPTION_TRUE@libmm_shared_option_la_CPPFLAGS = $(SHARED_COMMON_COMPILER_FLAGS)
@WITH_SHARED_OPTION_TRUE@libmm_shared_option_la_LDFLAGS = $(SHARED_COMMON_LINKER_FLAGS)
@WITH_SHARED_OPTION_TRUE@OPTION_COMMON_COMPILER_FLAGS = -I$(top_srcdir)/plugins/option
@WITH_SHARED_NOVATEL_TRUE@libmm_shared_novatel_la_SOURCES = \
@WITH_SHARED_NOVATEL_TRUE@	novatel/mm-shared.c \
@WITH_SHARED_NOVATEL_TRUE@	novatel/mm-common-novatel.c \
@WITH_SHARED_NOVATEL_TRUE@	novatel/mm-common-novatel.h \
@WITH_SHARED_NOVATEL_TRUE@	novatel/mm-broadband-modem-novatel.c \
@WITH_SHARED_NOVATEL_TRUE@	novatel/mm-broadband-modem-novatel.h \
@WITH_SHARED_NOVATEL_TRUE@	$(NULL)

@WITH_SHARED_NOVATEL_TRUE@libmm_shared_novatel_la_CPPFLAGS = \
@WITH_SHARED_NOVATEL_TRUE@	$(SHARED_COMMON_COMPILER_FLAGS) \
@WITH_SHARED_NOVATEL_TRUE@	-DMM_MODULE_NAME=\"shared-novatel\" \
@WITH_SHARED_NOVATEL_TRUE@	$(NULL)

@WITH_SHARED_NOVATEL_TRUE@libmm_shared_novatel_la_LDFLAGS = $(SHARED_COMMON_LINKER_FLAGS)
@WITH_SHARED_NOVATEL_TRUE@NOVATEL_COMMON_COMPILER_FLAGS = -I$(top_srcdir)/plugins/novatel
@WITH_SHARED_XMM_TRUE@libhelpers_xmm_la_SOURCES = \
@WITH_SHARED_XMM_TRUE@	xmm/mm-modem-helpers-xmm.c \
@WITH_SHARED_XMM_TRUE@	xmm/mm-modem-helpers-xmm.h \
@WITH_SHARED_XMM_TRUE@	$(NULL)

@WITH_SHARED_XMM_TRUE@libhelpers_xmm_la_CPPFLAGS = \
@WITH_SHARED_XMM_TRUE@	-DMM_MODULE_NAME=\"shared-xmm\" \
@WITH_SHARED_XMM_TRUE@	$(NULL)

@WITH_SHARED_XMM_TRUE@test_modem_helpers_xmm_SOURCES = \
@WITH_SHARED_XMM_TRUE@	xmm/tests/test-modem-helpers-xmm.c \
@WITH_SHARED_XMM_TRUE@	$(NULL)

@WITH_SHARED_XMM_TRUE@test_modem_helpers_xmm_CPPFLAGS = \
@WITH_SHARED_XMM_TRUE@	-I$(top_srcdir)/plugins/xmm \
@WITH_SHARED_XMM_TRUE@	$(NULL)

@WITH_SHARED_XMM_TRUE@test_modem_helpers_xmm_LDADD = \
@WITH_SHARED_XMM_TRUE@	$(builddir)/libhelpers-xmm.la  \
@WITH_SHARED_XMM_TRUE@	$(top_builddir)/src/libhelpers.la \
@WITH_SHARED_XMM_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la \
@WITH_SHARED_XMM_TRUE@	$(NULL)

@WITH_SHARED_XMM_TRUE@libmm_shared_xmm_la_SOURCES = xmm/mm-shared.c \
@WITH_SHARED_XMM_TRUE@	xmm/mm-shared-xmm.h xmm/mm-shared-xmm.c \
@WITH_SHARED_XMM_TRUE@	xmm/mm-broadband-modem-xmm.h \
@WITH_SHARED_XMM_TRUE@	xmm/mm-broadband-modem-xmm.c $(NULL) \
@WITH_SHARED_XMM_TRUE@	$(am__append_16)
@WITH_SHARED_XMM_TRUE@libmm_shared_xmm_la_CPPFLAGS = \
@WITH_SHARED_XMM_TRUE@	$(SHARED_COMMON_COMPILER_FLAGS) \
@WITH_SHARED_XMM_TRUE@	-DMM_MODULE_NAME=\"shared-xmm\" \
@WITH_SHARED_XMM_TRUE@	$(NULL)

@WITH_SHARED_XMM_TRUE@libmm_shared_xmm_la_LDFLAGS = $(SHARED_COMMON_LINKER_FLAGS)
@WITH_SHARED_XMM_TRUE@libmm_shared_xmm_la_LIBADD = \
@WITH_SHARED_XMM_TRUE@	$(builddir)/libhelpers-xmm.la \
@WITH_SHARED_XMM_TRUE@	$(NULL)

@WITH_SHARED_XMM_TRUE@XMM_COMMON_COMPILER_FLAGS = -I$(top_srcdir)/plugins/xmm

################################################################################
# common telit support
################################################################################
@WITH_SHARED_TELIT_TRUE@TELIT_ENUMS_INPUTS = \
@WITH_SHARED_TELIT_TRUE@	$(top_srcdir)/plugins/telit/mm-modem-helpers-telit.h \
@WITH_SHARED_TELIT_TRUE@	$(NULL)

@WITH_SHARED_TELIT_TRUE@TELIT_ENUMS_GENERATED = \
@WITH_SHARED_TELIT_TRUE@	telit/mm-telit-enums-types.h \
@WITH_SHARED_TELIT_TRUE@	telit/mm-telit-enums-types.c \
@WITH_SHARED_TELIT_TRUE@	$(NULL)

@WITH_SHARED_TELIT_TRUE@libhelpers_telit_la_SOURCES = \
@WITH_SHARED_TELIT_TRUE@	telit/mm-modem-helpers-telit.c \
@WITH_SHARED_TELIT_TRUE@	telit/mm-modem-helpers-telit.h \
@WITH_SHARED_TELIT_TRUE@	$(NULL)

@WITH_SHARED_TELIT_TRUE@libhelpers_telit_la_CPPFLAGS = \
@WITH_SHARED_TELIT_TRUE@	-DMM_MODULE_NAME=\"shared-telit\" \
@WITH_SHARED_TELIT_TRUE@	-I$(top_srcdir)/plugins/telit \
@WITH_SHARED_TELIT_TRUE@	-I$(top_builddir)/plugins/telit \
@WITH_SHARED_TELIT_TRUE@	$(NULL)

@WITH_SHARED_TELIT_TRUE@nodist_libhelpers_telit_la_SOURCES = $(TELIT_ENUMS_GENERATED)
@WITH_SHARED_TELIT_TRUE@test_modem_helpers_telit_SOURCES = \
@WITH_SHARED_TELIT_TRUE@	telit/tests/test-mm-modem-helpers-telit.c \
@WITH_SHARED_TELIT_TRUE@	$(NULL)

@WITH_SHARED_TELIT_TRUE@test_modem_helpers_telit_CPPFLAGS = \
@WITH_SHARED_TELIT_TRUE@	-I$(top_srcdir)/plugins/telit \
@WITH_SHARED_TELIT_TRUE@	-I$(top_builddir)/plugins/telit \
@WITH_SHARED_TELIT_TRUE@	$(TEST_COMMON_COMPILER_FLAGS) \
@WITH_SHARED_TELIT_TRUE@	$(NULL)

@WITH_SHARED_TELIT_TRUE@test_modem_helpers_telit_LDADD = \
@WITH_SHARED_TELIT_TRUE@	$(TEST_COMMON_LIBADD_FLAGS) \
@WITH_SHARED_TELIT_TRUE@	$(builddir)/libhelpers-telit.la \
@WITH_SHARED_TELIT_TRUE@	$(top_builddir)/src/libhelpers.la \
@WITH_SHARED_TELIT_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la \
@WITH_SHARED_TELIT_TRUE@	$(NULL)

@WITH_SHARED_TELIT_TRUE@libmm_shared_telit_la_SOURCES =  \
@WITH_SHARED_TELIT_TRUE@	telit/mm-shared.c \
@WITH_SHARED_TELIT_TRUE@	telit/mm-common-telit.c \
@WITH_SHARED_TELIT_TRUE@	telit/mm-common-telit.h \
@WITH_SHARED_TELIT_TRUE@	telit/mm-shared-telit.c \
@WITH_SHARED_TELIT_TRUE@	telit/mm-shared-telit.h \
@WITH_SHARED_TELIT_TRUE@	telit/mm-broadband-modem-telit.c \
@WITH_SHARED_TELIT_TRUE@	telit/mm-broadband-modem-telit.h \
@WITH_SHARED_TELIT_TRUE@	$(NULL) $(am__append_22)
@WITH_SHARED_TELIT_TRUE@libmm_shared_telit_la_CPPFLAGS = \
@WITH_SHARED_TELIT_TRUE@	-I$(top_srcdir)/plugins/telit \
@WITH_SHARED_TELIT_TRUE@	-I$(top_builddir)/plugins/telit \
@WITH_SHARED_TELIT_TRUE@	$(SHARED_COMMON_COMPILER_FLAGS) \
@WITH_SHARED_TELIT_TRUE@	-DMM_MODULE_NAME=\"shared-telit\" \
@WITH_SHARED_TELIT_TRUE@	$(NULL)

@WITH_SHARED_TELIT_TRUE@libmm_shared_telit_la_LDFLAGS = $(SHARED_COMMON_LINKER_FLAGS)
@WITH_SHARED_TELIT_TRUE@libmm_shared_telit_la_LIBADD = \
@WITH_SHARED_TELIT_TRUE@	$(builddir)/libhelpers-telit.la \
@WITH_SHARED_TELIT_TRUE@	$(NULL)

@WITH_SHARED_TELIT_TRUE@TELIT_COMMON_COMPILER_FLAGS = \
@WITH_SHARED_TELIT_TRUE@	-I$(top_srcdir)/plugins/telit \
@WITH_SHARED_TELIT_TRUE@	-I$(top_builddir)/plugins/telit \
@WITH_SHARED_TELIT_TRUE@	$(NULL)

@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@libmm_shared_foxconn_la_SOURCES = \
@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@	foxconn/mm-shared.c \
@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@	foxconn/mm-broadband-modem-foxconn-t77w968.c \
@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@	foxconn/mm-broadband-modem-foxconn-t77w968.h \
@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@	$(NULL)

@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@libmm_shared_foxconn_la_CPPFLAGS = \
@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@	$(SHARED_COMMON_COMPILER_FLAGS) \
@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@	-DMM_MODULE_NAME=\"shared-foxconn\" \
@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@	$(NULL)

@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@libmm_shared_foxconn_la_LDFLAGS = $(SHARED_COMMON_LINKER_FLAGS)
@WITH_MBIM_TRUE@@WITH_SHARED_FOXCONN_TRUE@FOXCONN_COMMON_COMPILER_FLAGS = -I$(top_srcdir)/plugins/foxconn
@ENABLE_PLUGIN_GENERIC_TRUE@libmm_plugin_generic_la_SOURCES = \
@ENABLE_PLUGIN_GENERIC_TRUE@	generic/mm-plugin-generic.c \
@ENABLE_PLUGIN_GENERIC_TRUE@	generic/mm-plugin-generic.h \
@ENABLE_PLUGIN_GENERIC_TRUE@	$(NULL)

@ENABLE_PLUGIN_GENERIC_TRUE@libmm_plugin_generic_la_CPPFLAGS = \
@ENABLE_PLUGIN_GENERIC_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_GENERIC_TRUE@	-DMM_MODULE_NAME=\"generic\" \
@ENABLE_PLUGIN_GENERIC_TRUE@	$(NULL)

@ENABLE_PLUGIN_GENERIC_TRUE@libmm_plugin_generic_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_GENERIC_TRUE@test_service_generic_SOURCES = generic/tests/test-service-generic.c
@ENABLE_PLUGIN_GENERIC_TRUE@test_service_generic_CPPFLAGS = $(TEST_COMMON_COMPILER_FLAGS)
@ENABLE_PLUGIN_GENERIC_TRUE@test_service_generic_LDADD = \
@ENABLE_PLUGIN_GENERIC_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la \
@ENABLE_PLUGIN_GENERIC_TRUE@	$(TEST_COMMON_LIBADD_FLAGS) \
@ENABLE_PLUGIN_GENERIC_TRUE@	$(NULL)

@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@libhelpers_altair_lte_la_SOURCES = \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	altair/mm-modem-helpers-altair-lte.c \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	altair/mm-modem-helpers-altair-lte.h \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	$(NULL)

@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@libhelpers_altair_lte_la_CPPFLAGS = \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	-DMM_MODULE_NAME=\"altair-lte\" \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	$(NULL)

@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@test_modem_helpers_altair_lte_SOURCES = \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	altair/tests/test-modem-helpers-altair-lte.c \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	$(NULL)

@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@test_modem_helpers_altair_lte_CPPFLAGS = \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	-I$(top_srcdir)/plugins/altair \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	$(NULL)

@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@test_modem_helpers_altair_lte_LDADD = \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	$(builddir)/libhelpers-altair-lte.la \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	$(NULL)

@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@libmm_plugin_altair_lte_la_SOURCES = \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	altair/mm-plugin-altair-lte.c \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	altair/mm-plugin-altair-lte.h \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	altair/mm-broadband-modem-altair-lte.c \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	altair/mm-broadband-modem-altair-lte.h \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	altair/mm-broadband-bearer-altair-lte.c \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	altair/mm-broadband-bearer-altair-lte.h \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	$(NULL)

@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@libmm_plugin_altair_lte_la_CPPFLAGS = \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	-DMM_MODULE_NAME=\"altair-lte\" \
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@	$(NULL)

@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@libmm_plugin_altair_lte_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_ALTAIR_LTE_TRUE@libmm_plugin_altair_lte_la_LIBADD = $(builddir)/libhelpers-altair-lte.la
@ENABLE_PLUGIN_ANYDATA_TRUE@libmm_plugin_anydata_la_SOURCES = \
@ENABLE_PLUGIN_ANYDATA_TRUE@	anydata/mm-plugin-anydata.c \
@ENABLE_PLUGIN_ANYDATA_TRUE@	anydata/mm-plugin-anydata.h \
@ENABLE_PLUGIN_ANYDATA_TRUE@	anydata/mm-broadband-modem-anydata.h \
@ENABLE_PLUGIN_ANYDATA_TRUE@	anydata/mm-broadband-modem-anydata.c \
@ENABLE_PLUGIN_ANYDATA_TRUE@	$(NULL)

@ENABLE_PLUGIN_ANYDATA_TRUE@libmm_plugin_anydata_la_CPPFLAGS = \
@ENABLE_PLUGIN_ANYDATA_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_ANYDATA_TRUE@	-DMM_MODULE_NAME=\"anydata\" \
@ENABLE_PLUGIN_ANYDATA_TRUE@	$(NULL)

@ENABLE_PLUGIN_ANYDATA_TRUE@libmm_plugin_anydata_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_BROADMOBI_TRUE@libmm_plugin_broadmobi_la_SOURCES = \
@ENABLE_PLUGIN_BROADMOBI_TRUE@	broadmobi/mm-plugin-broadmobi.c \
@ENABLE_PLUGIN_BROADMOBI_TRUE@	broadmobi/mm-plugin-broadmobi.h \
@ENABLE_PLUGIN_BROADMOBI_TRUE@	$(NULL)

@ENABLE_PLUGIN_BROADMOBI_TRUE@libmm_plugin_broadmobi_la_CPPFLAGS = \
@ENABLE_PLUGIN_BROADMOBI_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_BROADMOBI_TRUE@	-DMM_MODULE_NAME=\"broadmobi\" \
@ENABLE_PLUGIN_BROADMOBI_TRUE@	$(NULL)

@ENABLE_PLUGIN_BROADMOBI_TRUE@libmm_plugin_broadmobi_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_CINTERION_TRUE@libhelpers_cinterion_la_SOURCES = \
@ENABLE_PLUGIN_CINTERION_TRUE@	cinterion/mm-modem-helpers-cinterion.c \
@ENABLE_PLUGIN_CINTERION_TRUE@	cinterion/mm-modem-helpers-cinterion.h \
@ENABLE_PLUGIN_CINTERION_TRUE@	$(NULL)

@ENABLE_PLUGIN_CINTERION_TRUE@libhelpers_cinterion_la_CPPFLAGS = \
@ENABLE_PLUGIN_CINTERION_TRUE@	-DMM_MODULE_NAME=\"cinterion\" \
@ENABLE_PLUGIN_CINTERION_TRUE@	$(NULL)

@ENABLE_PLUGIN_CINTERION_TRUE@test_modem_helpers_cinterion_SOURCES = \
@ENABLE_PLUGIN_CINTERION_TRUE@	cinterion/tests/test-modem-helpers-cinterion.c \
@ENABLE_PLUGIN_CINTERION_TRUE@	$(NULL)

@ENABLE_PLUGIN_CINTERION_TRUE@test_modem_helpers_cinterion_CPPFLAGS = \
@ENABLE_PLUGIN_CINTERION_TRUE@	-I$(top_srcdir)/plugins/cinterion \
@ENABLE_PLUGIN_CINTERION_TRUE@	$(NULL)

@ENABLE_PLUGIN_CINTERION_TRUE@test_modem_helpers_cinterion_LDADD = \
@ENABLE_PLUGIN_CINTERION_TRUE@	$(builddir)/libhelpers-cinterion.la \
@ENABLE_PLUGIN_CINTERION_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_CINTERION_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la \
@ENABLE_PLUGIN_CINTERION_TRUE@	$(NULL)

@ENABLE_PLUGIN_CINTERION_TRUE@libmm_plugin_cinterion_la_SOURCES =  \
@ENABLE_PLUGIN_CINTERION_TRUE@	cinterion/mm-plugin-cinterion.c \
@ENABLE_PLUGIN_CINTERION_TRUE@	cinterion/mm-plugin-cinterion.h \
@ENABLE_PLUGIN_CINTERION_TRUE@	cinterion/mm-shared-cinterion.c \
@ENABLE_PLUGIN_CINTERION_TRUE@	cinterion/mm-shared-cinterion.h \
@ENABLE_PLUGIN_CINTERION_TRUE@	cinterion/mm-broadband-modem-cinterion.c \
@ENABLE_PLUGIN_CINTERION_TRUE@	cinterion/mm-broadband-modem-cinterion.h \
@ENABLE_PLUGIN_CINTERION_TRUE@	cinterion/mm-broadband-bearer-cinterion.c \
@ENABLE_PLUGIN_CINTERION_TRUE@	cinterion/mm-broadband-bearer-cinterion.h \
@ENABLE_PLUGIN_CINTERION_TRUE@	$(NULL) $(am__append_36)
@ENABLE_PLUGIN_CINTERION_TRUE@libmm_plugin_cinterion_la_CPPFLAGS = \
@ENABLE_PLUGIN_CINTERION_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_CINTERION_TRUE@	-DMM_MODULE_NAME=\"cinterion\" \
@ENABLE_PLUGIN_CINTERION_TRUE@	$(NULL)

@ENABLE_PLUGIN_CINTERION_TRUE@libmm_plugin_cinterion_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_CINTERION_TRUE@libmm_plugin_cinterion_la_LIBADD = $(builddir)/libhelpers-cinterion.la
@ENABLE_PLUGIN_DELL_TRUE@libmm_plugin_dell_la_SOURCES = \
@ENABLE_PLUGIN_DELL_TRUE@	dell/mm-plugin-dell.c \
@ENABLE_PLUGIN_DELL_TRUE@	dell/mm-plugin-dell.h \
@ENABLE_PLUGIN_DELL_TRUE@	$(NULL)

@ENABLE_PLUGIN_DELL_TRUE@libmm_plugin_dell_la_CPPFLAGS = \
@ENABLE_PLUGIN_DELL_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_DELL_TRUE@	$(NOVATEL_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_DELL_TRUE@	$(SIERRA_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_DELL_TRUE@	$(TELIT_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_DELL_TRUE@	$(XMM_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_DELL_TRUE@	$(FOXCONN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_DELL_TRUE@	-DMM_MODULE_NAME=\"dell\" \
@ENABLE_PLUGIN_DELL_TRUE@	$(NULL)

@ENABLE_PLUGIN_DELL_TRUE@libmm_plugin_dell_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_DLINK_TRUE@libmm_plugin_dlink_la_SOURCES = \
@ENABLE_PLUGIN_DLINK_TRUE@	dlink/mm-plugin-dlink.c \
@ENABLE_PLUGIN_DLINK_TRUE@	dlink/mm-plugin-dlink.h \
@ENABLE_PLUGIN_DLINK_TRUE@	$(NULL)

@ENABLE_PLUGIN_DLINK_TRUE@libmm_plugin_dlink_la_CPPFLAGS = \
@ENABLE_PLUGIN_DLINK_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_DLINK_TRUE@	-DMM_MODULE_NAME=\"d-link\" \
@ENABLE_PLUGIN_DLINK_TRUE@	$(NULL)

@ENABLE_PLUGIN_DLINK_TRUE@libmm_plugin_dlink_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_FOXCONN_TRUE@libmm_plugin_foxconn_la_SOURCES = \
@ENABLE_PLUGIN_FOXCONN_TRUE@	foxconn/mm-plugin-foxconn.c \
@ENABLE_PLUGIN_FOXCONN_TRUE@	foxconn/mm-plugin-foxconn.h \
@ENABLE_PLUGIN_FOXCONN_TRUE@	$(NULL)

@ENABLE_PLUGIN_FOXCONN_TRUE@libmm_plugin_foxconn_la_CPPFLAGS = \
@ENABLE_PLUGIN_FOXCONN_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_FOXCONN_TRUE@	$(FOXCONN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_FOXCONN_TRUE@	-DMM_MODULE_NAME=\"foxconn\" \
@ENABLE_PLUGIN_FOXCONN_TRUE@	$(NULL)

@ENABLE_PLUGIN_FOXCONN_TRUE@libmm_plugin_foxconn_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_HAIER_TRUE@libmm_plugin_haier_la_SOURCES = \
@ENABLE_PLUGIN_HAIER_TRUE@	haier/mm-plugin-haier.c \
@ENABLE_PLUGIN_HAIER_TRUE@	haier/mm-plugin-haier.h \
@ENABLE_PLUGIN_HAIER_TRUE@	$(NULL)

@ENABLE_PLUGIN_HAIER_TRUE@libmm_plugin_haier_la_CPPFLAGS = \
@ENABLE_PLUGIN_HAIER_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_HAIER_TRUE@	-DMM_MODULE_NAME=\"haier\" \
@ENABLE_PLUGIN_HAIER_TRUE@	$(NULL)

@ENABLE_PLUGIN_HAIER_TRUE@libmm_plugin_haier_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_HUAWEI_TRUE@libhelpers_huawei_la_SOURCES = \
@ENABLE_PLUGIN_HUAWEI_TRUE@	huawei/mm-modem-helpers-huawei.c \
@ENABLE_PLUGIN_HUAWEI_TRUE@	huawei/mm-modem-helpers-huawei.h \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(NULL)

@ENABLE_PLUGIN_HUAWEI_TRUE@libhelpers_huawei_la_CPPFLAGS = \
@ENABLE_PLUGIN_HUAWEI_TRUE@	-DMM_MODULE_NAME=\"huawei\" \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(NULL)

@ENABLE_PLUGIN_HUAWEI_TRUE@test_modem_helpers_huawei_SOURCES = \
@ENABLE_PLUGIN_HUAWEI_TRUE@	huawei/tests/test-modem-helpers-huawei.c \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(NULL)

@ENABLE_PLUGIN_HUAWEI_TRUE@test_modem_helpers_huawei_CPPFLAGS = \
@ENABLE_PLUGIN_HUAWEI_TRUE@	-I$(top_srcdir)/plugins/huawei \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(NULL)

@ENABLE_PLUGIN_HUAWEI_TRUE@test_modem_helpers_huawei_LDADD = \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(builddir)/libhelpers-huawei.la \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(NULL)

@ENABLE_PLUGIN_HUAWEI_TRUE@libmm_plugin_huawei_la_SOURCES = \
@ENABLE_PLUGIN_HUAWEI_TRUE@	huawei/mm-plugin-huawei.c \
@ENABLE_PLUGIN_HUAWEI_TRUE@	huawei/mm-plugin-huawei.h \
@ENABLE_PLUGIN_HUAWEI_TRUE@	huawei/mm-sim-huawei.c \
@ENABLE_PLUGIN_HUAWEI_TRUE@	huawei/mm-sim-huawei.h \
@ENABLE_PLUGIN_HUAWEI_TRUE@	huawei/mm-broadband-modem-huawei.c \
@ENABLE_PLUGIN_HUAWEI_TRUE@	huawei/mm-broadband-modem-huawei.h \
@ENABLE_PLUGIN_HUAWEI_TRUE@	huawei/mm-broadband-bearer-huawei.c \
@ENABLE_PLUGIN_HUAWEI_TRUE@	huawei/mm-broadband-bearer-huawei.h \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(NULL)

@ENABLE_PLUGIN_HUAWEI_TRUE@libmm_plugin_huawei_la_CPPFLAGS = \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_HUAWEI_TRUE@	-DMM_MODULE_NAME=\"huawei\" \
@ENABLE_PLUGIN_HUAWEI_TRUE@	$(NULL)

@ENABLE_PLUGIN_HUAWEI_TRUE@libmm_plugin_huawei_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_HUAWEI_TRUE@libmm_plugin_huawei_la_LIBADD = $(builddir)/libhelpers-huawei.la
@ENABLE_PLUGIN_IRIDIUM_TRUE@libmm_plugin_iridium_la_SOURCES = \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	iridium/mm-plugin-iridium.c \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	iridium/mm-plugin-iridium.h \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	iridium/mm-broadband-modem-iridium.c \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	iridium/mm-broadband-modem-iridium.h \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	iridium/mm-bearer-iridium.c \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	iridium/mm-bearer-iridium.h \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	iridium/mm-sim-iridium.c \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	iridium/mm-sim-iridium.h \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	$(NULL)

@ENABLE_PLUGIN_IRIDIUM_TRUE@libmm_plugin_iridium_la_CPPFLAGS = \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	-DMM_MODULE_NAME=\"iridium\" \
@ENABLE_PLUGIN_IRIDIUM_TRUE@	$(NULL)

@ENABLE_PLUGIN_IRIDIUM_TRUE@libmm_plugin_iridium_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_LINKTOP_TRUE@libhelpers_linktop_la_SOURCES = \
@ENABLE_PLUGIN_LINKTOP_TRUE@	linktop/mm-modem-helpers-linktop.c \
@ENABLE_PLUGIN_LINKTOP_TRUE@	linktop/mm-modem-helpers-linktop.h \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(NULL)

@ENABLE_PLUGIN_LINKTOP_TRUE@libhelpers_linktop_la_CPPFLAGS = \
@ENABLE_PLUGIN_LINKTOP_TRUE@	-DMM_MODULE_NAME=\"linktop\" \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(NULL)

@ENABLE_PLUGIN_LINKTOP_TRUE@test_modem_helpers_linktop_SOURCES = \
@ENABLE_PLUGIN_LINKTOP_TRUE@	linktop/tests/test-modem-helpers-linktop.c \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(NULL)

@ENABLE_PLUGIN_LINKTOP_TRUE@test_modem_helpers_linktop_CPPFLAGS = \
@ENABLE_PLUGIN_LINKTOP_TRUE@	-I$(top_srcdir)/plugins/linktop \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(NULL)

@ENABLE_PLUGIN_LINKTOP_TRUE@test_modem_helpers_linktop_LDADD = \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(builddir)/libhelpers-linktop.la \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(NULL)

@ENABLE_PLUGIN_LINKTOP_TRUE@libmm_plugin_linktop_la_SOURCES = \
@ENABLE_PLUGIN_LINKTOP_TRUE@	linktop/mm-plugin-linktop.c \
@ENABLE_PLUGIN_LINKTOP_TRUE@	linktop/mm-plugin-linktop.h \
@ENABLE_PLUGIN_LINKTOP_TRUE@	linktop/mm-broadband-modem-linktop.h \
@ENABLE_PLUGIN_LINKTOP_TRUE@	linktop/mm-broadband-modem-linktop.c \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(NULL)

@ENABLE_PLUGIN_LINKTOP_TRUE@libmm_plugin_linktop_la_CPPFLAGS = \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_LINKTOP_TRUE@	-DMM_MODULE_NAME=\"linktop\" \
@ENABLE_PLUGIN_LINKTOP_TRUE@	$(NULL)

@ENABLE_PLUGIN_LINKTOP_TRUE@libmm_plugin_linktop_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_LINKTOP_TRUE@libmm_plugin_linktop_la_LIBADD = $(builddir)/libhelpers-linktop.la
@ENABLE_PLUGIN_LONGCHEER_TRUE@libmm_plugin_longcheer_la_SOURCES = \
@ENABLE_PLUGIN_LONGCHEER_TRUE@	longcheer/mm-plugin-longcheer.c \
@ENABLE_PLUGIN_LONGCHEER_TRUE@	longcheer/mm-plugin-longcheer.h \
@ENABLE_PLUGIN_LONGCHEER_TRUE@	longcheer/mm-broadband-modem-longcheer.h \
@ENABLE_PLUGIN_LONGCHEER_TRUE@	longcheer/mm-broadband-modem-longcheer.c \
@ENABLE_PLUGIN_LONGCHEER_TRUE@	$(NULL)

@ENABLE_PLUGIN_LONGCHEER_TRUE@libmm_plugin_longcheer_la_CPPFLAGS = \
@ENABLE_PLUGIN_LONGCHEER_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_LONGCHEER_TRUE@	-DMM_MODULE_NAME=\"longcheer\" \
@ENABLE_PLUGIN_LONGCHEER_TRUE@	$(NULL)

@ENABLE_PLUGIN_LONGCHEER_TRUE@libmm_plugin_longcheer_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_MBM_TRUE@libhelpers_mbm_la_SOURCES = \
@ENABLE_PLUGIN_MBM_TRUE@	mbm/mm-modem-helpers-mbm.c \
@ENABLE_PLUGIN_MBM_TRUE@	mbm/mm-modem-helpers-mbm.h \
@ENABLE_PLUGIN_MBM_TRUE@	$(NULL)

@ENABLE_PLUGIN_MBM_TRUE@libhelpers_mbm_la_CPPFLAGS = \
@ENABLE_PLUGIN_MBM_TRUE@	-DMM_MODULE_NAME=\"ericsson-mbm\" \
@ENABLE_PLUGIN_MBM_TRUE@	$(NULL)

@ENABLE_PLUGIN_MBM_TRUE@test_modem_helpers_mbm_SOURCES = \
@ENABLE_PLUGIN_MBM_TRUE@	mbm/tests/test-modem-helpers-mbm.c \
@ENABLE_PLUGIN_MBM_TRUE@	$(NULL)

@ENABLE_PLUGIN_MBM_TRUE@test_modem_helpers_mbm_CPPFLAGS = \
@ENABLE_PLUGIN_MBM_TRUE@	-I$(top_srcdir)/plugins/mbm \
@ENABLE_PLUGIN_MBM_TRUE@	$(NULL)

@ENABLE_PLUGIN_MBM_TRUE@test_modem_helpers_mbm_LDADD = \
@ENABLE_PLUGIN_MBM_TRUE@	$(builddir)/libhelpers-mbm.la \
@ENABLE_PLUGIN_MBM_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_MBM_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la \
@ENABLE_PLUGIN_MBM_TRUE@	$(NULL)

@ENABLE_PLUGIN_MBM_TRUE@libmm_plugin_ericsson_mbm_la_SOURCES = \
@ENABLE_PLUGIN_MBM_TRUE@	mbm/mm-broadband-modem-mbm.c \
@ENABLE_PLUGIN_MBM_TRUE@	mbm/mm-broadband-modem-mbm.h \
@ENABLE_PLUGIN_MBM_TRUE@	mbm/mm-broadband-bearer-mbm.c \
@ENABLE_PLUGIN_MBM_TRUE@	mbm/mm-broadband-bearer-mbm.h \
@ENABLE_PLUGIN_MBM_TRUE@	mbm/mm-sim-mbm.c \
@ENABLE_PLUGIN_MBM_TRUE@	mbm/mm-sim-mbm.h \
@ENABLE_PLUGIN_MBM_TRUE@	mbm/mm-plugin-mbm.c \
@ENABLE_PLUGIN_MBM_TRUE@	mbm/mm-plugin-mbm.h \
@ENABLE_PLUGIN_MBM_TRUE@	$(NULL)

@ENABLE_PLUGIN_MBM_TRUE@libmm_plugin_ericsson_mbm_la_CPPFLAGS = \
@ENABLE_PLUGIN_MBM_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_MBM_TRUE@	-DMM_MODULE_NAME=\"ericsson-mbm\" \
@ENABLE_PLUGIN_MBM_TRUE@	$(NULL)

@ENABLE_PLUGIN_MBM_TRUE@libmm_plugin_ericsson_mbm_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_MBM_TRUE@libmm_plugin_ericsson_mbm_la_LIBADD = $(builddir)/libhelpers-mbm.la
@ENABLE_PLUGIN_MOTOROLA_TRUE@libmm_plugin_motorola_la_SOURCES = \
@ENABLE_PLUGIN_MOTOROLA_TRUE@	motorola/mm-plugin-motorola.c \
@ENABLE_PLUGIN_MOTOROLA_TRUE@	motorola/mm-plugin-motorola.h \
@ENABLE_PLUGIN_MOTOROLA_TRUE@	motorola/mm-broadband-modem-motorola.c \
@ENABLE_PLUGIN_MOTOROLA_TRUE@	motorola/mm-broadband-modem-motorola.h \
@ENABLE_PLUGIN_MOTOROLA_TRUE@	$(NULL)

@ENABLE_PLUGIN_MOTOROLA_TRUE@libmm_plugin_motorola_la_CPPFLAGS = \
@ENABLE_PLUGIN_MOTOROLA_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_MOTOROLA_TRUE@	-DMM_MODULE_NAME=\"motorola\" \
@ENABLE_PLUGIN_MOTOROLA_TRUE@	$(NULL)

@ENABLE_PLUGIN_MOTOROLA_TRUE@libmm_plugin_motorola_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_MTK_TRUE@libmm_plugin_mtk_la_SOURCES = \
@ENABLE_PLUGIN_MTK_TRUE@	mtk/mm-plugin-mtk.c \
@ENABLE_PLUGIN_MTK_TRUE@	mtk/mm-plugin-mtk.h \
@ENABLE_PLUGIN_MTK_TRUE@	mtk/mm-broadband-modem-mtk.h \
@ENABLE_PLUGIN_MTK_TRUE@	mtk/mm-broadband-modem-mtk.c \
@ENABLE_PLUGIN_MTK_TRUE@	$(NULL)

@ENABLE_PLUGIN_MTK_TRUE@libmm_plugin_mtk_la_CPPFLAGS = \
@ENABLE_PLUGIN_MTK_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_MTK_TRUE@	-DMM_MODULE_NAME=\"mtk\" \
@ENABLE_PLUGIN_MTK_TRUE@	$(NULL)

@ENABLE_PLUGIN_MTK_TRUE@libmm_plugin_mtk_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_NOKIA_TRUE@libmm_plugin_nokia_la_SOURCES = \
@ENABLE_PLUGIN_NOKIA_TRUE@	nokia/mm-plugin-nokia.c \
@ENABLE_PLUGIN_NOKIA_TRUE@	nokia/mm-plugin-nokia.h \
@ENABLE_PLUGIN_NOKIA_TRUE@	nokia/mm-sim-nokia.c \
@ENABLE_PLUGIN_NOKIA_TRUE@	nokia/mm-sim-nokia.h \
@ENABLE_PLUGIN_NOKIA_TRUE@	nokia/mm-broadband-modem-nokia.c \
@ENABLE_PLUGIN_NOKIA_TRUE@	nokia/mm-broadband-modem-nokia.h \
@ENABLE_PLUGIN_NOKIA_TRUE@	$(NULL)

@ENABLE_PLUGIN_NOKIA_TRUE@libmm_plugin_nokia_la_CPPFLAGS = $(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_NOKIA_TRUE@	-DMM_MODULE_NAME=\"nokia\" \
@ENABLE_PLUGIN_NOKIA_TRUE@	$(NULL)

@ENABLE_PLUGIN_NOKIA_TRUE@libmm_plugin_nokia_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@libmm_plugin_nokia_icera_la_SOURCES = \
@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@	nokia/mm-plugin-nokia-icera.c \
@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@	nokia/mm-plugin-nokia-icera.h \
@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@	$(NULL)

@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@libmm_plugin_nokia_icera_la_CPPFLAGS = \
@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@	$(ICERA_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@	-DMM_MODULE_NAME=\"nokia-icera\" \
@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@	$(NULL)

@ENABLE_PLUGIN_NOKIA_ICERA_TRUE@libmm_plugin_nokia_icera_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_NOVATEL_TRUE@libmm_plugin_novatel_la_SOURCES = \
@ENABLE_PLUGIN_NOVATEL_TRUE@	novatel/mm-plugin-novatel.c \
@ENABLE_PLUGIN_NOVATEL_TRUE@	novatel/mm-plugin-novatel.h \
@ENABLE_PLUGIN_NOVATEL_TRUE@	$(NULL)

@ENABLE_PLUGIN_NOVATEL_TRUE@libmm_plugin_novatel_la_CPPFLAGS = \
@ENABLE_PLUGIN_NOVATEL_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_NOVATEL_TRUE@	$(NOVATEL_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_NOVATEL_TRUE@	-DMM_MODULE_NAME=\"novatel\" \
@ENABLE_PLUGIN_NOVATEL_TRUE@	$(NULL)

@ENABLE_PLUGIN_NOVATEL_TRUE@libmm_plugin_novatel_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@libmm_plugin_novatel_lte_la_SOURCES = \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	novatel/mm-plugin-novatel-lte.c \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	novatel/mm-plugin-novatel-lte.h \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	novatel/mm-broadband-modem-novatel-lte.c \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	novatel/mm-broadband-modem-novatel-lte.h \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	novatel/mm-broadband-bearer-novatel-lte.c \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	novatel/mm-broadband-bearer-novatel-lte.h \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	novatel/mm-sim-novatel-lte.c \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	novatel/mm-sim-novatel-lte.h \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	$(NULL)

@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@libmm_plugin_novatel_lte_la_CPPFLAGS = \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	-DMM_MODULE_NAME=\"novatel-lte\" \
@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@	$(NULL)

@ENABLE_PLUGIN_NOVATEL_LTE_TRUE@libmm_plugin_novatel_lte_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_OPTION_TRUE@libmm_plugin_option_la_SOURCES = \
@ENABLE_PLUGIN_OPTION_TRUE@	option/mm-plugin-option.c \
@ENABLE_PLUGIN_OPTION_TRUE@	option/mm-plugin-option.h \
@ENABLE_PLUGIN_OPTION_TRUE@	$(NULL)

@ENABLE_PLUGIN_OPTION_TRUE@libmm_plugin_option_la_CPPFLAGS = \
@ENABLE_PLUGIN_OPTION_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_OPTION_TRUE@	$(OPTION_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_OPTION_TRUE@	-DMM_MODULE_NAME=\"option\" \
@ENABLE_PLUGIN_OPTION_TRUE@	$(NULL)

@ENABLE_PLUGIN_OPTION_TRUE@libmm_plugin_option_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_OPTION_HSO_TRUE@libmm_plugin_option_hso_la_SOURCES = \
@ENABLE_PLUGIN_OPTION_HSO_TRUE@	option/mm-plugin-hso.c \
@ENABLE_PLUGIN_OPTION_HSO_TRUE@	option/mm-plugin-hso.h \
@ENABLE_PLUGIN_OPTION_HSO_TRUE@	option/mm-broadband-bearer-hso.c \
@ENABLE_PLUGIN_OPTION_HSO_TRUE@	option/mm-broadband-bearer-hso.h \
@ENABLE_PLUGIN_OPTION_HSO_TRUE@	option/mm-broadband-modem-hso.c \
@ENABLE_PLUGIN_OPTION_HSO_TRUE@	option/mm-broadband-modem-hso.h \
@ENABLE_PLUGIN_OPTION_HSO_TRUE@	$(NULL)

@ENABLE_PLUGIN_OPTION_HSO_TRUE@libmm_plugin_option_hso_la_CPPFLAGS = \
@ENABLE_PLUGIN_OPTION_HSO_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_OPTION_HSO_TRUE@	$(OPTION_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_OPTION_HSO_TRUE@	-DMM_MODULE_NAME=\"option-hso\" \
@ENABLE_PLUGIN_OPTION_HSO_TRUE@	$(NULL)

@ENABLE_PLUGIN_OPTION_HSO_TRUE@libmm_plugin_option_hso_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_PANTECH_TRUE@libmm_plugin_pantech_la_SOURCES = \
@ENABLE_PLUGIN_PANTECH_TRUE@	pantech/mm-plugin-pantech.c \
@ENABLE_PLUGIN_PANTECH_TRUE@	pantech/mm-plugin-pantech.h \
@ENABLE_PLUGIN_PANTECH_TRUE@	pantech/mm-sim-pantech.c \
@ENABLE_PLUGIN_PANTECH_TRUE@	pantech/mm-sim-pantech.h \
@ENABLE_PLUGIN_PANTECH_TRUE@	pantech/mm-broadband-modem-pantech.c \
@ENABLE_PLUGIN_PANTECH_TRUE@	pantech/mm-broadband-modem-pantech.h \
@ENABLE_PLUGIN_PANTECH_TRUE@	$(NULL)

@ENABLE_PLUGIN_PANTECH_TRUE@libmm_plugin_pantech_la_CPPFLAGS = \
@ENABLE_PLUGIN_PANTECH_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_PANTECH_TRUE@	-DMM_MODULE_NAME=\"pantech\" \
@ENABLE_PLUGIN_PANTECH_TRUE@	$(NULL)

@ENABLE_PLUGIN_PANTECH_TRUE@libmm_plugin_pantech_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_QUECTEL_TRUE@libmm_plugin_quectel_la_SOURCES =  \
@ENABLE_PLUGIN_QUECTEL_TRUE@	quectel/mm-plugin-quectel.c \
@ENABLE_PLUGIN_QUECTEL_TRUE@	quectel/mm-plugin-quectel.h \
@ENABLE_PLUGIN_QUECTEL_TRUE@	quectel/mm-shared-quectel.c \
@ENABLE_PLUGIN_QUECTEL_TRUE@	quectel/mm-shared-quectel.h \
@ENABLE_PLUGIN_QUECTEL_TRUE@	quectel/mm-broadband-modem-quectel.c \
@ENABLE_PLUGIN_QUECTEL_TRUE@	quectel/mm-broadband-modem-quectel.h \
@ENABLE_PLUGIN_QUECTEL_TRUE@	$(NULL) $(am__append_83)
@ENABLE_PLUGIN_QUECTEL_TRUE@libmm_plugin_quectel_la_CPPFLAGS = \
@ENABLE_PLUGIN_QUECTEL_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_QUECTEL_TRUE@	-DMM_MODULE_NAME=\"quectel\" \
@ENABLE_PLUGIN_QUECTEL_TRUE@	$(NULL)

@ENABLE_PLUGIN_QUECTEL_TRUE@libmm_plugin_quectel_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_SAMSUNG_TRUE@libmm_plugin_samsung_la_SOURCES = \
@ENABLE_PLUGIN_SAMSUNG_TRUE@	samsung/mm-plugin-samsung.c \
@ENABLE_PLUGIN_SAMSUNG_TRUE@	samsung/mm-plugin-samsung.h \
@ENABLE_PLUGIN_SAMSUNG_TRUE@	samsung/mm-broadband-modem-samsung.c \
@ENABLE_PLUGIN_SAMSUNG_TRUE@	samsung/mm-broadband-modem-samsung.h \
@ENABLE_PLUGIN_SAMSUNG_TRUE@	$(NULL)

@ENABLE_PLUGIN_SAMSUNG_TRUE@libmm_plugin_samsung_la_CPPFLAGS = \
@ENABLE_PLUGIN_SAMSUNG_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_SAMSUNG_TRUE@	$(ICERA_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_SAMSUNG_TRUE@	-DMM_MODULE_NAME=\"samsung\" \
@ENABLE_PLUGIN_SAMSUNG_TRUE@	$(NULL)

@ENABLE_PLUGIN_SAMSUNG_TRUE@libmm_plugin_samsung_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@libmm_plugin_sierra_legacy_la_SOURCES = \
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@	sierra/mm-plugin-sierra-legacy.c \
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@	sierra/mm-plugin-sierra-legacy.h \
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@	sierra/mm-broadband-modem-sierra-icera.c \
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@	sierra/mm-broadband-modem-sierra-icera.h \
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@	$(NULL)

@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@libmm_plugin_sierra_legacy_la_CPPFLAGS = \
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@	$(ICERA_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@	$(SIERRA_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@	-DMM_MODULE_NAME=\"sierra-legacy\" \
@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@	$(NULL)

@ENABLE_PLUGIN_SIERRA_LEGACY_TRUE@libmm_plugin_sierra_legacy_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_SIERRA_TRUE@libmm_plugin_sierra_la_SOURCES = \
@ENABLE_PLUGIN_SIERRA_TRUE@	sierra/mm-plugin-sierra.c \
@ENABLE_PLUGIN_SIERRA_TRUE@	sierra/mm-plugin-sierra.h \
@ENABLE_PLUGIN_SIERRA_TRUE@	$(NULL)

@ENABLE_PLUGIN_SIERRA_TRUE@libmm_plugin_sierra_la_CPPFLAGS = \
@ENABLE_PLUGIN_SIERRA_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_SIERRA_TRUE@	-DMM_MODULE_NAME=\"sierra\" \
@ENABLE_PLUGIN_SIERRA_TRUE@	$(NULL)

@ENABLE_PLUGIN_SIERRA_TRUE@libmm_plugin_sierra_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_SIMTECH_TRUE@libhelpers_simtech_la_SOURCES = \
@ENABLE_PLUGIN_SIMTECH_TRUE@	simtech/mm-modem-helpers-simtech.c \
@ENABLE_PLUGIN_SIMTECH_TRUE@	simtech/mm-modem-helpers-simtech.h \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(NULL)

@ENABLE_PLUGIN_SIMTECH_TRUE@libhelpers_simtech_la_CPPFLAGS = \
@ENABLE_PLUGIN_SIMTECH_TRUE@	-DMM_MODULE_NAME=\"simtech\" \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(NULL)

@ENABLE_PLUGIN_SIMTECH_TRUE@test_modem_helpers_simtech_SOURCES = \
@ENABLE_PLUGIN_SIMTECH_TRUE@	simtech/tests/test-modem-helpers-simtech.c \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(NULL)

@ENABLE_PLUGIN_SIMTECH_TRUE@test_modem_helpers_simtech_CPPFLAGS = \
@ENABLE_PLUGIN_SIMTECH_TRUE@	-I$(top_srcdir)/plugins/simtech \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(NULL)

@ENABLE_PLUGIN_SIMTECH_TRUE@test_modem_helpers_simtech_LDADD = \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(builddir)/libhelpers-simtech.la \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(NULL)

@ENABLE_PLUGIN_SIMTECH_TRUE@libmm_plugin_simtech_la_SOURCES =  \
@ENABLE_PLUGIN_SIMTECH_TRUE@	simtech/mm-plugin-simtech.c \
@ENABLE_PLUGIN_SIMTECH_TRUE@	simtech/mm-plugin-simtech.h \
@ENABLE_PLUGIN_SIMTECH_TRUE@	simtech/mm-shared-simtech.c \
@ENABLE_PLUGIN_SIMTECH_TRUE@	simtech/mm-shared-simtech.h \
@ENABLE_PLUGIN_SIMTECH_TRUE@	simtech/mm-broadband-modem-simtech.h \
@ENABLE_PLUGIN_SIMTECH_TRUE@	simtech/mm-broadband-modem-simtech.c \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(NULL) $(am__append_93)
@ENABLE_PLUGIN_SIMTECH_TRUE@libmm_plugin_simtech_la_CPPFLAGS = \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_SIMTECH_TRUE@	-DMM_MODULE_NAME=\"simtech\" \
@ENABLE_PLUGIN_SIMTECH_TRUE@	$(NULL)

@ENABLE_PLUGIN_SIMTECH_TRUE@libmm_plugin_simtech_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_SIMTECH_TRUE@libmm_plugin_simtech_la_LIBADD = $(builddir)/libhelpers-simtech.la
@ENABLE_PLUGIN_TELIT_TRUE@libmm_plugin_telit_la_SOURCES = \
@ENABLE_PLUGIN_TELIT_TRUE@	telit/mm-plugin-telit.c \
@ENABLE_PLUGIN_TELIT_TRUE@	telit/mm-plugin-telit.h \
@ENABLE_PLUGIN_TELIT_TRUE@	$(NULL)

@ENABLE_PLUGIN_TELIT_TRUE@libmm_plugin_telit_la_CPPFLAGS = \
@ENABLE_PLUGIN_TELIT_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_TELIT_TRUE@	$(TELIT_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_TELIT_TRUE@	-DMM_MODULE_NAME=\"telit\" \
@ENABLE_PLUGIN_TELIT_TRUE@	$(NULL)

@ENABLE_PLUGIN_TELIT_TRUE@libmm_plugin_telit_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_THURAYA_TRUE@libhelpers_thuraya_la_SOURCES = \
@ENABLE_PLUGIN_THURAYA_TRUE@	thuraya/mm-modem-helpers-thuraya.c \
@ENABLE_PLUGIN_THURAYA_TRUE@	thuraya/mm-modem-helpers-thuraya.h \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(NULL)

@ENABLE_PLUGIN_THURAYA_TRUE@libhelpers_thuraya_la_CPPFLAGS = \
@ENABLE_PLUGIN_THURAYA_TRUE@	-DMM_MODULE_NAME=\"thuraya\" \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(NULL)

@ENABLE_PLUGIN_THURAYA_TRUE@test_modem_helpers_thuraya_SOURCES = \
@ENABLE_PLUGIN_THURAYA_TRUE@	thuraya/tests/test-mm-modem-helpers-thuraya.c \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(NULL)

@ENABLE_PLUGIN_THURAYA_TRUE@test_modem_helpers_thuraya_CPPFLAGS = \
@ENABLE_PLUGIN_THURAYA_TRUE@	-I$(top_srcdir)/plugins/thuraya \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(NULL)

@ENABLE_PLUGIN_THURAYA_TRUE@test_modem_helpers_thuraya_LDADD = \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(builddir)/libhelpers-thuraya.la \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(NULL)

@ENABLE_PLUGIN_THURAYA_TRUE@libmm_plugin_thuraya_la_SOURCES = \
@ENABLE_PLUGIN_THURAYA_TRUE@	thuraya/mm-plugin-thuraya.c \
@ENABLE_PLUGIN_THURAYA_TRUE@	thuraya/mm-plugin-thuraya.h \
@ENABLE_PLUGIN_THURAYA_TRUE@	thuraya/mm-broadband-modem-thuraya.c \
@ENABLE_PLUGIN_THURAYA_TRUE@	thuraya/mm-broadband-modem-thuraya.h \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(NULL)

@ENABLE_PLUGIN_THURAYA_TRUE@libmm_plugin_thuraya_la_CPPFLAGS = \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_THURAYA_TRUE@	-DMM_MODULE_NAME=\"thuraya\" \
@ENABLE_PLUGIN_THURAYA_TRUE@	$(NULL)

@ENABLE_PLUGIN_THURAYA_TRUE@libmm_plugin_thuraya_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_THURAYA_TRUE@libmm_plugin_thuraya_la_LIBADD = $(builddir)/libhelpers-thuraya.la
@ENABLE_PLUGIN_TPLINK_TRUE@libmm_plugin_tplink_la_SOURCES = \
@ENABLE_PLUGIN_TPLINK_TRUE@	tplink/mm-plugin-tplink.c \
@ENABLE_PLUGIN_TPLINK_TRUE@	tplink/mm-plugin-tplink.h \
@ENABLE_PLUGIN_TPLINK_TRUE@	$(NULL)

@ENABLE_PLUGIN_TPLINK_TRUE@libmm_plugin_tplink_la_CPPFLAGS = \
@ENABLE_PLUGIN_TPLINK_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_TPLINK_TRUE@	-DMM_MODULE_NAME=\"tp-link\" \
@ENABLE_PLUGIN_TPLINK_TRUE@	$(NULL)

@ENABLE_PLUGIN_TPLINK_TRUE@libmm_plugin_tplink_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_UBLOX_TRUE@PLUGIN_UBLOX_COMPILER_FLAGS = \
@ENABLE_PLUGIN_UBLOX_TRUE@	-I$(top_srcdir)/plugins/ublox \
@ENABLE_PLUGIN_UBLOX_TRUE@	-I$(top_builddir)/plugins/ublox \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(NULL)

@ENABLE_PLUGIN_UBLOX_TRUE@UBLOX_ENUMS_INPUTS = \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(top_srcdir)/plugins/ublox/mm-modem-helpers-ublox.h \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(NULL)

@ENABLE_PLUGIN_UBLOX_TRUE@UBLOX_ENUMS_GENERATED = \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/mm-ublox-enums-types.h \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/mm-ublox-enums-types.c \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(NULL)

@ENABLE_PLUGIN_UBLOX_TRUE@libhelpers_ublox_la_SOURCES = \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/mm-modem-helpers-ublox.c \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/mm-modem-helpers-ublox.h \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(NULL)

@ENABLE_PLUGIN_UBLOX_TRUE@nodist_libhelpers_ublox_la_SOURCES = $(UBLOX_ENUMS_GENERATED)
@ENABLE_PLUGIN_UBLOX_TRUE@libhelpers_ublox_la_CPPFLAGS = \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(PLUGIN_UBLOX_COMPILER_FLAGS) \
@ENABLE_PLUGIN_UBLOX_TRUE@	-DMM_MODULE_NAME=\"u-blox\" \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(NULL)

@ENABLE_PLUGIN_UBLOX_TRUE@test_modem_helpers_ublox_SOURCES = \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/tests/test-modem-helpers-ublox.c \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(NULL)

@ENABLE_PLUGIN_UBLOX_TRUE@test_modem_helpers_ublox_CPPFLAGS = \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(PLUGIN_UBLOX_COMPILER_FLAGS) \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(TEST_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(NULL)

@ENABLE_PLUGIN_UBLOX_TRUE@test_modem_helpers_ublox_LDADD = \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(TEST_COMMON_LIBADD_FLAGS) \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(builddir)/libhelpers-ublox.la \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(top_builddir)/src/libhelpers.la \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(top_builddir)/libmm-glib/libmm-glib.la \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(NULL)

@ENABLE_PLUGIN_UBLOX_TRUE@libmm_plugin_ublox_la_SOURCES = \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/mm-plugin-ublox.c \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/mm-plugin-ublox.h \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/mm-broadband-bearer-ublox.h \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/mm-broadband-bearer-ublox.c \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/mm-broadband-modem-ublox.h \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/mm-broadband-modem-ublox.c \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/mm-sim-ublox.c \
@ENABLE_PLUGIN_UBLOX_TRUE@	ublox/mm-sim-ublox.h \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(NULL)

@ENABLE_PLUGIN_UBLOX_TRUE@libmm_plugin_ublox_la_CPPFLAGS = \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(PLUGIN_UBLOX_COMPILER_FLAGS) \
@ENABLE_PLUGIN_UBLOX_TRUE@	-DMM_MODULE_NAME=\"u-blox\" \
@ENABLE_PLUGIN_UBLOX_TRUE@	$(NULL)

@ENABLE_PLUGIN_UBLOX_TRUE@libmm_plugin_ublox_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_UBLOX_TRUE@libmm_plugin_ublox_la_LIBADD = $(builddir)/libhelpers-ublox.la
@ENABLE_PLUGIN_VIA_TRUE@libmm_plugin_via_la_SOURCES = \
@ENABLE_PLUGIN_VIA_TRUE@	via/mm-plugin-via.c \
@ENABLE_PLUGIN_VIA_TRUE@	via/mm-plugin-via.h \
@ENABLE_PLUGIN_VIA_TRUE@	via/mm-broadband-modem-via.c \
@ENABLE_PLUGIN_VIA_TRUE@	via/mm-broadband-modem-via.h \
@ENABLE_PLUGIN_VIA_TRUE@	$(NULL)

@ENABLE_PLUGIN_VIA_TRUE@libmm_plugin_via_la_CPPFLAGS = \
@ENABLE_PLUGIN_VIA_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_VIA_TRUE@	-DMM_MODULE_NAME=\"via\" \
@ENABLE_PLUGIN_VIA_TRUE@	$(NULL)

@ENABLE_PLUGIN_VIA_TRUE@libmm_plugin_via_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_WAVECOM_TRUE@libmm_plugin_wavecom_la_SOURCES = \
@ENABLE_PLUGIN_WAVECOM_TRUE@	wavecom/mm-plugin-wavecom.c \
@ENABLE_PLUGIN_WAVECOM_TRUE@	wavecom/mm-plugin-wavecom.h \
@ENABLE_PLUGIN_WAVECOM_TRUE@	wavecom/mm-broadband-modem-wavecom.c \
@ENABLE_PLUGIN_WAVECOM_TRUE@	wavecom/mm-broadband-modem-wavecom.h \
@ENABLE_PLUGIN_WAVECOM_TRUE@	$(NULL)

@ENABLE_PLUGIN_WAVECOM_TRUE@libmm_plugin_wavecom_la_CPPFLAGS = \
@ENABLE_PLUGIN_WAVECOM_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_WAVECOM_TRUE@	-DMM_MODULE_NAME=\"wavecom\" \
@ENABLE_PLUGIN_WAVECOM_TRUE@	$(NULL)

@ENABLE_PLUGIN_WAVECOM_TRUE@libmm_plugin_wavecom_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_X22X_TRUE@libmm_plugin_x22x_la_SOURCES = \
@ENABLE_PLUGIN_X22X_TRUE@	x22x/mm-plugin-x22x.c \
@ENABLE_PLUGIN_X22X_TRUE@	x22x/mm-plugin-x22x.h \
@ENABLE_PLUGIN_X22X_TRUE@	x22x/mm-broadband-modem-x22x.h \
@ENABLE_PLUGIN_X22X_TRUE@	x22x/mm-broadband-modem-x22x.c \
@ENABLE_PLUGIN_X22X_TRUE@	$(NULL)

@ENABLE_PLUGIN_X22X_TRUE@libmm_plugin_x22x_la_CPPFLAGS = \
@ENABLE_PLUGIN_X22X_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_X22X_TRUE@	-DMM_MODULE_NAME=\"x22x\" \
@ENABLE_PLUGIN_X22X_TRUE@	$(NULL)

@ENABLE_PLUGIN_X22X_TRUE@libmm_plugin_x22x_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
@ENABLE_PLUGIN_ZTE_TRUE@libmm_plugin_zte_la_SOURCES = \
@ENABLE_PLUGIN_ZTE_TRUE@	zte/mm-plugin-zte.c \
@ENABLE_PLUGIN_ZTE_TRUE@	zte/mm-plugin-zte.h \
@ENABLE_PLUGIN_ZTE_TRUE@	zte/mm-common-zte.h \
@ENABLE_PLUGIN_ZTE_TRUE@	zte/mm-common-zte.c \
@ENABLE_PLUGIN_ZTE_TRUE@	zte/mm-broadband-modem-zte.h \
@ENABLE_PLUGIN_ZTE_TRUE@	zte/mm-broadband-modem-zte.c \
@ENABLE_PLUGIN_ZTE_TRUE@	zte/mm-broadband-modem-zte-icera.h \
@ENABLE_PLUGIN_ZTE_TRUE@	zte/mm-broadband-modem-zte-icera.c \
@ENABLE_PLUGIN_ZTE_TRUE@	$(NULL)

@ENABLE_PLUGIN_ZTE_TRUE@libmm_plugin_zte_la_CPPFLAGS = \
@ENABLE_PLUGIN_ZTE_TRUE@	$(PLUGIN_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_ZTE_TRUE@	$(ICERA_COMMON_COMPILER_FLAGS) \
@ENABLE_PLUGIN_ZTE_TRUE@	-DMM_MODULE_NAME=\"zte\" \
@ENABLE_PLUGIN_ZTE_TRUE@	$(NULL)

@ENABLE_PLUGIN_ZTE_TRUE@libmm_plugin_zte_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
libmm_plugin_me3630_la_SOURCES = \
	me3630/mm-plugin-me3630.c \
	me3630/mm-plugin-me3630.h \
	me3630/mm-common-me3630.h \
	me3630/mm-common-me3630.c \
	me3630/mm-broadband-modem-me3630.h \
	me3630/mm-broadband-modem-me3630.c \
	me3630/mm-broadband-bearer-me3630.h \
	me3630/mm-broadband-bearer-me3630.c \
	$(NULL)

libmm_plugin_me3630_la_CPPFLAGS = \
	$(PLUGIN_COMMON_COMPILER_FLAGS) \
	-DMM_MODULE_NAME=\"me3630\" \
	$(NULL)

libmm_plugin_me3630_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
libmm_plugin_NL668_la_SOURCES =  \
	NL668/mm-broadband-bearer-fibocom-ecm.c \
	NL668/mm-broadband-bearer-fibocom-ecm.h \
	NL668/mm-broadband-modem-fibocom.c \
	NL668/mm-broadband-modem-fibocom.h NL668/mm-plugin-fibocom.c \
	NL668/mm-plugin-fibocom.h $(NULL) $(am__append_119)
libmm_plugin_NL668_la_CPPFLAGS = \
	$(PLUGIN_COMMON_COMPILER_FLAGS) \
	$(XMM_COMMON_COMPILER_FLAGS) \
	$(NL668_COMMON_COMPILER_FLAGS) \
	-DMM_MODULE_NAME=\"NL668\" \
	$(NULL)

libmm_plugin_NL668_la_LDFLAGS = $(PLUGIN_COMMON_LINKER_FLAGS)
test_udev_rules_SOURCES = \
	tests/test-udev-rules.c \
	$(NULL)

test_udev_rules_LDADD = \
	$(top_builddir)/src/libkerneldevice.la \
	$(top_builddir)/libmm-glib/libmm-glib.la \
	$(NULL)

test_keyfiles_SOURCES = \
	tests/test-keyfiles.c \
	$(NULL)

test_keyfiles_LDADD = \
	$(top_builddir)/libmm-glib/libmm-glib.la \
	$(NULL)

all: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) all-am

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in: @MAINTAINER_MODE_TRUE@ $(srcdir)/Makefile.am $(top_srcdir)/gtester.make $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu plugins/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu plugins/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;
$(top_srcdir)/gtester.make $(am__empty):

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure: @MAINTAINER_MODE_TRUE@ $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4): @MAINTAINER_MODE_TRUE@ $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

clean-noinstPROGRAMS:
	@list='$(noinst_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

clean-noinstLTLIBRARIES:
	-test -z "$(noinst_LTLIBRARIES)" || rm -f $(noinst_LTLIBRARIES)
	@list='$(noinst_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}

install-pkglibLTLIBRARIES: $(pkglib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(pkglib_LTLIBRARIES)'; test -n "$(pkglibdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkglibdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkglibdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(pkglibdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(pkglibdir)"; \
	}

uninstall-pkglibLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(pkglib_LTLIBRARIES)'; test -n "$(pkglibdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(pkglibdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(pkglibdir)/$$f"; \
	done

clean-pkglibLTLIBRARIES:
	-test -z "$(pkglib_LTLIBRARIES)" || rm -f $(pkglib_LTLIBRARIES)
	@list='$(pkglib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}
altair/$(am__dirstamp):
	@$(MKDIR_P) altair
	@: > altair/$(am__dirstamp)
altair/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) altair/$(DEPDIR)
	@: > altair/$(DEPDIR)/$(am__dirstamp)
altair/libhelpers_altair_lte_la-mm-modem-helpers-altair-lte.lo:  \
	altair/$(am__dirstamp) altair/$(DEPDIR)/$(am__dirstamp)

libhelpers-altair-lte.la: $(libhelpers_altair_lte_la_OBJECTS) $(libhelpers_altair_lte_la_DEPENDENCIES) $(EXTRA_libhelpers_altair_lte_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK) $(am_libhelpers_altair_lte_la_rpath) $(libhelpers_altair_lte_la_OBJECTS) $(libhelpers_altair_lte_la_LIBADD) $(LIBS)
cinterion/$(am__dirstamp):
	@$(MKDIR_P) cinterion
	@: > cinterion/$(am__dirstamp)
cinterion/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) cinterion/$(DEPDIR)
	@: > cinterion/$(DEPDIR)/$(am__dirstamp)
cinterion/libhelpers_cinterion_la-mm-modem-helpers-cinterion.lo:  \
	cinterion/$(am__dirstamp) cinterion/$(DEPDIR)/$(am__dirstamp)

libhelpers-cinterion.la: $(libhelpers_cinterion_la_OBJECTS) $(libhelpers_cinterion_la_DEPENDENCIES) $(EXTRA_libhelpers_cinterion_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK) $(am_libhelpers_cinterion_la_rpath) $(libhelpers_cinterion_la_OBJECTS) $(libhelpers_cinterion_la_LIBADD) $(LIBS)
huawei/$(am__dirstamp):
	@$(MKDIR_P) huawei
	@: > huawei/$(am__dirstamp)
huawei/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) huawei/$(DEPDIR)
	@: > huawei/$(DEPDIR)/$(am__dirstamp)
huawei/libhelpers_huawei_la-mm-modem-helpers-huawei.lo:  \
	huawei/$(am__dirstamp) huawei/$(DEPDIR)/$(am__dirstamp)

libhelpers-huawei.la: $(libhelpers_huawei_la_OBJECTS) $(libhelpers_huawei_la_DEPENDENCIES) $(EXTRA_libhelpers_huawei_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK) $(am_libhelpers_huawei_la_rpath) $(libhelpers_huawei_la_OBJECTS) $(libhelpers_huawei_la_LIBADD) $(LIBS)
icera/$(am__dirstamp):
	@$(MKDIR_P) icera
	@: > icera/$(am__dirstamp)
icera/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) icera/$(DEPDIR)
	@: > icera/$(DEPDIR)/$(am__dirstamp)
icera/libhelpers_icera_la-mm-modem-helpers-icera.lo:  \
	icera/$(am__dirstamp) icera/$(DEPDIR)/$(am__dirstamp)

libhelpers-icera.la: $(libhelpers_icera_la_OBJECTS) $(libhelpers_icera_la_DEPENDENCIES) $(EXTRA_libhelpers_icera_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK) $(am_libhelpers_icera_la_rpath) $(libhelpers_icera_la_OBJECTS) $(libhelpers_icera_la_LIBADD) $(LIBS)
linktop/$(am__dirstamp):
	@$(MKDIR_P) linktop
	@: > linktop/$(am__dirstamp)
linktop/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) linktop/$(DEPDIR)
	@: > linktop/$(DEPDIR)/$(am__dirstamp)
linktop/libhelpers_linktop_la-mm-modem-helpers-linktop.lo:  \
	linktop/$(am__dirstamp) linktop/$(DEPDIR)/$(am__dirstamp)

libhelpers-linktop.la: $(libhelpers_linktop_la_OBJECTS) $(libhelpers_linktop_la_DEPENDENCIES) $(EXTRA_libhelpers_linktop_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK) $(am_libhelpers_linktop_la_rpath) $(libhelpers_linktop_la_OBJECTS) $(libhelpers_linktop_la_LIBADD) $(LIBS)
mbm/$(am__dirstamp):
	@$(MKDIR_P) mbm
	@: > mbm/$(am__dirstamp)
mbm/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) mbm/$(DEPDIR)
	@: > mbm/$(DEPDIR)/$(am__dirstamp)
mbm/libhelpers_mbm_la-mm-modem-helpers-mbm.lo: mbm/$(am__dirstamp) \
	mbm/$(DEPDIR)/$(am__dirstamp)

libhelpers-mbm.la: $(libhelpers_mbm_la_OBJECTS) $(libhelpers_mbm_la_DEPENDENCIES) $(EXTRA_libhelpers_mbm_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK) $(am_libhelpers_mbm_la_rpath) $(libhelpers_mbm_la_OBJECTS) $(libhelpers_mbm_la_LIBADD) $(LIBS)
sierra/$(am__dirstamp):
	@$(MKDIR_P) sierra
	@: > sierra/$(am__dirstamp)
sierra/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) sierra/$(DEPDIR)
	@: > sierra/$(DEPDIR)/$(am__dirstamp)
sierra/libhelpers_sierra_la-mm-modem-helpers-sierra.lo:  \
	sierra/$(am__dirstamp) sierra/$(DEPDIR)/$(am__dirstamp)

libhelpers-sierra.la: $(libhelpers_sierra_la_OBJECTS) $(libhelpers_sierra_la_DEPENDENCIES) $(EXTRA_libhelpers_sierra_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK) $(am_libhelpers_sierra_la_rpath) $(libhelpers_sierra_la_OBJECTS) $(libhelpers_sierra_la_LIBADD) $(LIBS)
simtech/$(am__dirstamp):
	@$(MKDIR_P) simtech
	@: > simtech/$(am__dirstamp)
simtech/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) simtech/$(DEPDIR)
	@: > simtech/$(DEPDIR)/$(am__dirstamp)
simtech/libhelpers_simtech_la-mm-modem-helpers-simtech.lo:  \
	simtech/$(am__dirstamp) simtech/$(DEPDIR)/$(am__dirstamp)

libhelpers-simtech.la: $(libhelpers_simtech_la_OBJECTS) $(libhelpers_simtech_la_DEPENDENCIES) $(EXTRA_libhelpers_simtech_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK) $(am_libhelpers_simtech_la_rpath) $(libhelpers_simtech_la_OBJECTS) $(libhelpers_simtech_la_LIBADD) $(LIBS)
telit/$(am__dirstamp):
	@$(MKDIR_P) telit
	@: > telit/$(am__dirstamp)
telit/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) telit/$(DEPDIR)
	@: > telit/$(DEPDIR)/$(am__dirstamp)
telit/libhelpers_telit_la-mm-modem-helpers-telit.lo:  \
	telit/$(am__dirstamp) telit/$(DEPDIR)/$(am__dirstamp)
telit/libhelpers_telit_la-mm-telit-enums-types.lo:  \
	telit/$(am__dirstamp) telit/$(DEPDIR)/$(am__dirstamp)

libhelpers-telit.la: $(libhelpers_telit_la_OBJECTS) $(libhelpers_telit_la_DEPENDENCIES) $(EXTRA_libhelpers_telit_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK) $(am_libhelpers_telit_la_rpath) $(libhelpers_telit_la_OBJECTS) $(libhelpers_telit_la_LIBADD) $(LIBS)
thuraya/$(am__dirstamp):
	@$(MKDIR_P) thuraya
	@: > thuraya/$(am__dirstamp)
thuraya/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) thuraya/$(DEPDIR)
	@: > thuraya/$(DEPDIR)/$(am__dirstamp)
thuraya/libhelpers_thuraya_la-mm-modem-helpers-thuraya.lo:  \
	thuraya/$(am__dirstamp) thuraya/$(DEPDIR)/$(am__dirstamp)

libhelpers-thuraya.la: $(libhelpers_thuraya_la_OBJECTS) $(libhelpers_thuraya_la_DEPENDENCIES) $(EXTRA_libhelpers_thuraya_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK) $(am_libhelpers_thuraya_la_rpath) $(libhelpers_thuraya_la_OBJECTS) $(libhelpers_thuraya_la_LIBADD) $(LIBS)
ublox/$(am__dirstamp):
	@$(MKDIR_P) ublox
	@: > ublox/$(am__dirstamp)
ublox/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) ublox/$(DEPDIR)
	@: > ublox/$(DEPDIR)/$(am__dirstamp)
ublox/libhelpers_ublox_la-mm-modem-helpers-ublox.lo:  \
	ublox/$(am__dirstamp) ublox/$(DEPDIR)/$(am__dirstamp)
ublox/libhelpers_ublox_la-mm-ublox-enums-types.lo:  \
	ublox/$(am__dirstamp) ublox/$(DEPDIR)/$(am__dirstamp)

libhelpers-ublox.la: $(libhelpers_ublox_la_OBJECTS) $(libhelpers_ublox_la_DEPENDENCIES) $(EXTRA_libhelpers_ublox_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK) $(am_libhelpers_ublox_la_rpath) $(libhelpers_ublox_la_OBJECTS) $(libhelpers_ublox_la_LIBADD) $(LIBS)
xmm/$(am__dirstamp):
	@$(MKDIR_P) xmm
	@: > xmm/$(am__dirstamp)
xmm/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) xmm/$(DEPDIR)
	@: > xmm/$(DEPDIR)/$(am__dirstamp)
xmm/libhelpers_xmm_la-mm-modem-helpers-xmm.lo: xmm/$(am__dirstamp) \
	xmm/$(DEPDIR)/$(am__dirstamp)

libhelpers-xmm.la: $(libhelpers_xmm_la_OBJECTS) $(libhelpers_xmm_la_DEPENDENCIES) $(EXTRA_libhelpers_xmm_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK) $(am_libhelpers_xmm_la_rpath) $(libhelpers_xmm_la_OBJECTS) $(libhelpers_xmm_la_LIBADD) $(LIBS)
NL668/$(am__dirstamp):
	@$(MKDIR_P) NL668
	@: > NL668/$(am__dirstamp)
NL668/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) NL668/$(DEPDIR)
	@: > NL668/$(DEPDIR)/$(am__dirstamp)
NL668/libmm_plugin_NL668_la-mm-broadband-bearer-fibocom-ecm.lo:  \
	NL668/$(am__dirstamp) NL668/$(DEPDIR)/$(am__dirstamp)
NL668/libmm_plugin_NL668_la-mm-broadband-modem-fibocom.lo:  \
	NL668/$(am__dirstamp) NL668/$(DEPDIR)/$(am__dirstamp)
NL668/libmm_plugin_NL668_la-mm-plugin-fibocom.lo:  \
	NL668/$(am__dirstamp) NL668/$(DEPDIR)/$(am__dirstamp)
NL668/libmm_plugin_NL668_la-mm-broadband-modem-mbim-xmm-fibocom.lo:  \
	NL668/$(am__dirstamp) NL668/$(DEPDIR)/$(am__dirstamp)
NL668/libmm_plugin_NL668_la-mm-broadband-modem-mbim-fibocom.lo:  \
	NL668/$(am__dirstamp) NL668/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-NL668.la: $(libmm_plugin_NL668_la_OBJECTS) $(libmm_plugin_NL668_la_DEPENDENCIES) $(EXTRA_libmm_plugin_NL668_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_NL668_la_LINK) -rpath $(pkglibdir) $(libmm_plugin_NL668_la_OBJECTS) $(libmm_plugin_NL668_la_LIBADD) $(LIBS)
altair/libmm_plugin_altair_lte_la-mm-plugin-altair-lte.lo:  \
	altair/$(am__dirstamp) altair/$(DEPDIR)/$(am__dirstamp)
altair/libmm_plugin_altair_lte_la-mm-broadband-modem-altair-lte.lo:  \
	altair/$(am__dirstamp) altair/$(DEPDIR)/$(am__dirstamp)
altair/libmm_plugin_altair_lte_la-mm-broadband-bearer-altair-lte.lo:  \
	altair/$(am__dirstamp) altair/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-altair-lte.la: $(libmm_plugin_altair_lte_la_OBJECTS) $(libmm_plugin_altair_lte_la_DEPENDENCIES) $(EXTRA_libmm_plugin_altair_lte_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_altair_lte_la_LINK) $(am_libmm_plugin_altair_lte_la_rpath) $(libmm_plugin_altair_lte_la_OBJECTS) $(libmm_plugin_altair_lte_la_LIBADD) $(LIBS)
anydata/$(am__dirstamp):
	@$(MKDIR_P) anydata
	@: > anydata/$(am__dirstamp)
anydata/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) anydata/$(DEPDIR)
	@: > anydata/$(DEPDIR)/$(am__dirstamp)
anydata/libmm_plugin_anydata_la-mm-plugin-anydata.lo:  \
	anydata/$(am__dirstamp) anydata/$(DEPDIR)/$(am__dirstamp)
anydata/libmm_plugin_anydata_la-mm-broadband-modem-anydata.lo:  \
	anydata/$(am__dirstamp) anydata/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-anydata.la: $(libmm_plugin_anydata_la_OBJECTS) $(libmm_plugin_anydata_la_DEPENDENCIES) $(EXTRA_libmm_plugin_anydata_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_anydata_la_LINK) $(am_libmm_plugin_anydata_la_rpath) $(libmm_plugin_anydata_la_OBJECTS) $(libmm_plugin_anydata_la_LIBADD) $(LIBS)
broadmobi/$(am__dirstamp):
	@$(MKDIR_P) broadmobi
	@: > broadmobi/$(am__dirstamp)
broadmobi/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) broadmobi/$(DEPDIR)
	@: > broadmobi/$(DEPDIR)/$(am__dirstamp)
broadmobi/libmm_plugin_broadmobi_la-mm-plugin-broadmobi.lo:  \
	broadmobi/$(am__dirstamp) broadmobi/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-broadmobi.la: $(libmm_plugin_broadmobi_la_OBJECTS) $(libmm_plugin_broadmobi_la_DEPENDENCIES) $(EXTRA_libmm_plugin_broadmobi_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_broadmobi_la_LINK) $(am_libmm_plugin_broadmobi_la_rpath) $(libmm_plugin_broadmobi_la_OBJECTS) $(libmm_plugin_broadmobi_la_LIBADD) $(LIBS)
cinterion/libmm_plugin_cinterion_la-mm-plugin-cinterion.lo:  \
	cinterion/$(am__dirstamp) cinterion/$(DEPDIR)/$(am__dirstamp)
cinterion/libmm_plugin_cinterion_la-mm-shared-cinterion.lo:  \
	cinterion/$(am__dirstamp) cinterion/$(DEPDIR)/$(am__dirstamp)
cinterion/libmm_plugin_cinterion_la-mm-broadband-modem-cinterion.lo:  \
	cinterion/$(am__dirstamp) cinterion/$(DEPDIR)/$(am__dirstamp)
cinterion/libmm_plugin_cinterion_la-mm-broadband-bearer-cinterion.lo:  \
	cinterion/$(am__dirstamp) cinterion/$(DEPDIR)/$(am__dirstamp)
cinterion/libmm_plugin_cinterion_la-mm-broadband-modem-qmi-cinterion.lo:  \
	cinterion/$(am__dirstamp) cinterion/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-cinterion.la: $(libmm_plugin_cinterion_la_OBJECTS) $(libmm_plugin_cinterion_la_DEPENDENCIES) $(EXTRA_libmm_plugin_cinterion_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_cinterion_la_LINK) $(am_libmm_plugin_cinterion_la_rpath) $(libmm_plugin_cinterion_la_OBJECTS) $(libmm_plugin_cinterion_la_LIBADD) $(LIBS)
dell/$(am__dirstamp):
	@$(MKDIR_P) dell
	@: > dell/$(am__dirstamp)
dell/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) dell/$(DEPDIR)
	@: > dell/$(DEPDIR)/$(am__dirstamp)
dell/libmm_plugin_dell_la-mm-plugin-dell.lo: dell/$(am__dirstamp) \
	dell/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-dell.la: $(libmm_plugin_dell_la_OBJECTS) $(libmm_plugin_dell_la_DEPENDENCIES) $(EXTRA_libmm_plugin_dell_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_dell_la_LINK) $(am_libmm_plugin_dell_la_rpath) $(libmm_plugin_dell_la_OBJECTS) $(libmm_plugin_dell_la_LIBADD) $(LIBS)
dlink/$(am__dirstamp):
	@$(MKDIR_P) dlink
	@: > dlink/$(am__dirstamp)
dlink/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) dlink/$(DEPDIR)
	@: > dlink/$(DEPDIR)/$(am__dirstamp)
dlink/libmm_plugin_dlink_la-mm-plugin-dlink.lo: dlink/$(am__dirstamp) \
	dlink/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-dlink.la: $(libmm_plugin_dlink_la_OBJECTS) $(libmm_plugin_dlink_la_DEPENDENCIES) $(EXTRA_libmm_plugin_dlink_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_dlink_la_LINK) $(am_libmm_plugin_dlink_la_rpath) $(libmm_plugin_dlink_la_OBJECTS) $(libmm_plugin_dlink_la_LIBADD) $(LIBS)
mbm/libmm_plugin_ericsson_mbm_la-mm-broadband-modem-mbm.lo:  \
	mbm/$(am__dirstamp) mbm/$(DEPDIR)/$(am__dirstamp)
mbm/libmm_plugin_ericsson_mbm_la-mm-broadband-bearer-mbm.lo:  \
	mbm/$(am__dirstamp) mbm/$(DEPDIR)/$(am__dirstamp)
mbm/libmm_plugin_ericsson_mbm_la-mm-sim-mbm.lo: mbm/$(am__dirstamp) \
	mbm/$(DEPDIR)/$(am__dirstamp)
mbm/libmm_plugin_ericsson_mbm_la-mm-plugin-mbm.lo:  \
	mbm/$(am__dirstamp) mbm/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-ericsson-mbm.la: $(libmm_plugin_ericsson_mbm_la_OBJECTS) $(libmm_plugin_ericsson_mbm_la_DEPENDENCIES) $(EXTRA_libmm_plugin_ericsson_mbm_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_ericsson_mbm_la_LINK) $(am_libmm_plugin_ericsson_mbm_la_rpath) $(libmm_plugin_ericsson_mbm_la_OBJECTS) $(libmm_plugin_ericsson_mbm_la_LIBADD) $(LIBS)
foxconn/$(am__dirstamp):
	@$(MKDIR_P) foxconn
	@: > foxconn/$(am__dirstamp)
foxconn/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) foxconn/$(DEPDIR)
	@: > foxconn/$(DEPDIR)/$(am__dirstamp)
foxconn/libmm_plugin_foxconn_la-mm-plugin-foxconn.lo:  \
	foxconn/$(am__dirstamp) foxconn/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-foxconn.la: $(libmm_plugin_foxconn_la_OBJECTS) $(libmm_plugin_foxconn_la_DEPENDENCIES) $(EXTRA_libmm_plugin_foxconn_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_foxconn_la_LINK) $(am_libmm_plugin_foxconn_la_rpath) $(libmm_plugin_foxconn_la_OBJECTS) $(libmm_plugin_foxconn_la_LIBADD) $(LIBS)
generic/$(am__dirstamp):
	@$(MKDIR_P) generic
	@: > generic/$(am__dirstamp)
generic/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) generic/$(DEPDIR)
	@: > generic/$(DEPDIR)/$(am__dirstamp)
generic/libmm_plugin_generic_la-mm-plugin-generic.lo:  \
	generic/$(am__dirstamp) generic/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-generic.la: $(libmm_plugin_generic_la_OBJECTS) $(libmm_plugin_generic_la_DEPENDENCIES) $(EXTRA_libmm_plugin_generic_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_generic_la_LINK) $(am_libmm_plugin_generic_la_rpath) $(libmm_plugin_generic_la_OBJECTS) $(libmm_plugin_generic_la_LIBADD) $(LIBS)
haier/$(am__dirstamp):
	@$(MKDIR_P) haier
	@: > haier/$(am__dirstamp)
haier/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) haier/$(DEPDIR)
	@: > haier/$(DEPDIR)/$(am__dirstamp)
haier/libmm_plugin_haier_la-mm-plugin-haier.lo: haier/$(am__dirstamp) \
	haier/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-haier.la: $(libmm_plugin_haier_la_OBJECTS) $(libmm_plugin_haier_la_DEPENDENCIES) $(EXTRA_libmm_plugin_haier_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_haier_la_LINK) $(am_libmm_plugin_haier_la_rpath) $(libmm_plugin_haier_la_OBJECTS) $(libmm_plugin_haier_la_LIBADD) $(LIBS)
huawei/libmm_plugin_huawei_la-mm-plugin-huawei.lo:  \
	huawei/$(am__dirstamp) huawei/$(DEPDIR)/$(am__dirstamp)
huawei/libmm_plugin_huawei_la-mm-sim-huawei.lo:  \
	huawei/$(am__dirstamp) huawei/$(DEPDIR)/$(am__dirstamp)
huawei/libmm_plugin_huawei_la-mm-broadband-modem-huawei.lo:  \
	huawei/$(am__dirstamp) huawei/$(DEPDIR)/$(am__dirstamp)
huawei/libmm_plugin_huawei_la-mm-broadband-bearer-huawei.lo:  \
	huawei/$(am__dirstamp) huawei/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-huawei.la: $(libmm_plugin_huawei_la_OBJECTS) $(libmm_plugin_huawei_la_DEPENDENCIES) $(EXTRA_libmm_plugin_huawei_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_huawei_la_LINK) $(am_libmm_plugin_huawei_la_rpath) $(libmm_plugin_huawei_la_OBJECTS) $(libmm_plugin_huawei_la_LIBADD) $(LIBS)
iridium/$(am__dirstamp):
	@$(MKDIR_P) iridium
	@: > iridium/$(am__dirstamp)
iridium/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) iridium/$(DEPDIR)
	@: > iridium/$(DEPDIR)/$(am__dirstamp)
iridium/libmm_plugin_iridium_la-mm-plugin-iridium.lo:  \
	iridium/$(am__dirstamp) iridium/$(DEPDIR)/$(am__dirstamp)
iridium/libmm_plugin_iridium_la-mm-broadband-modem-iridium.lo:  \
	iridium/$(am__dirstamp) iridium/$(DEPDIR)/$(am__dirstamp)
iridium/libmm_plugin_iridium_la-mm-bearer-iridium.lo:  \
	iridium/$(am__dirstamp) iridium/$(DEPDIR)/$(am__dirstamp)
iridium/libmm_plugin_iridium_la-mm-sim-iridium.lo:  \
	iridium/$(am__dirstamp) iridium/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-iridium.la: $(libmm_plugin_iridium_la_OBJECTS) $(libmm_plugin_iridium_la_DEPENDENCIES) $(EXTRA_libmm_plugin_iridium_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_iridium_la_LINK) $(am_libmm_plugin_iridium_la_rpath) $(libmm_plugin_iridium_la_OBJECTS) $(libmm_plugin_iridium_la_LIBADD) $(LIBS)
linktop/libmm_plugin_linktop_la-mm-plugin-linktop.lo:  \
	linktop/$(am__dirstamp) linktop/$(DEPDIR)/$(am__dirstamp)
linktop/libmm_plugin_linktop_la-mm-broadband-modem-linktop.lo:  \
	linktop/$(am__dirstamp) linktop/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-linktop.la: $(libmm_plugin_linktop_la_OBJECTS) $(libmm_plugin_linktop_la_DEPENDENCIES) $(EXTRA_libmm_plugin_linktop_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_linktop_la_LINK) $(am_libmm_plugin_linktop_la_rpath) $(libmm_plugin_linktop_la_OBJECTS) $(libmm_plugin_linktop_la_LIBADD) $(LIBS)
longcheer/$(am__dirstamp):
	@$(MKDIR_P) longcheer
	@: > longcheer/$(am__dirstamp)
longcheer/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) longcheer/$(DEPDIR)
	@: > longcheer/$(DEPDIR)/$(am__dirstamp)
longcheer/libmm_plugin_longcheer_la-mm-plugin-longcheer.lo:  \
	longcheer/$(am__dirstamp) longcheer/$(DEPDIR)/$(am__dirstamp)
longcheer/libmm_plugin_longcheer_la-mm-broadband-modem-longcheer.lo:  \
	longcheer/$(am__dirstamp) longcheer/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-longcheer.la: $(libmm_plugin_longcheer_la_OBJECTS) $(libmm_plugin_longcheer_la_DEPENDENCIES) $(EXTRA_libmm_plugin_longcheer_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_longcheer_la_LINK) $(am_libmm_plugin_longcheer_la_rpath) $(libmm_plugin_longcheer_la_OBJECTS) $(libmm_plugin_longcheer_la_LIBADD) $(LIBS)
me3630/$(am__dirstamp):
	@$(MKDIR_P) me3630
	@: > me3630/$(am__dirstamp)
me3630/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) me3630/$(DEPDIR)
	@: > me3630/$(DEPDIR)/$(am__dirstamp)
me3630/libmm_plugin_me3630_la-mm-plugin-me3630.lo:  \
	me3630/$(am__dirstamp) me3630/$(DEPDIR)/$(am__dirstamp)
me3630/libmm_plugin_me3630_la-mm-common-me3630.lo:  \
	me3630/$(am__dirstamp) me3630/$(DEPDIR)/$(am__dirstamp)
me3630/libmm_plugin_me3630_la-mm-broadband-modem-me3630.lo:  \
	me3630/$(am__dirstamp) me3630/$(DEPDIR)/$(am__dirstamp)
me3630/libmm_plugin_me3630_la-mm-broadband-bearer-me3630.lo:  \
	me3630/$(am__dirstamp) me3630/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-me3630.la: $(libmm_plugin_me3630_la_OBJECTS) $(libmm_plugin_me3630_la_DEPENDENCIES) $(EXTRA_libmm_plugin_me3630_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_me3630_la_LINK) -rpath $(pkglibdir) $(libmm_plugin_me3630_la_OBJECTS) $(libmm_plugin_me3630_la_LIBADD) $(LIBS)
motorola/$(am__dirstamp):
	@$(MKDIR_P) motorola
	@: > motorola/$(am__dirstamp)
motorola/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) motorola/$(DEPDIR)
	@: > motorola/$(DEPDIR)/$(am__dirstamp)
motorola/libmm_plugin_motorola_la-mm-plugin-motorola.lo:  \
	motorola/$(am__dirstamp) motorola/$(DEPDIR)/$(am__dirstamp)
motorola/libmm_plugin_motorola_la-mm-broadband-modem-motorola.lo:  \
	motorola/$(am__dirstamp) motorola/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-motorola.la: $(libmm_plugin_motorola_la_OBJECTS) $(libmm_plugin_motorola_la_DEPENDENCIES) $(EXTRA_libmm_plugin_motorola_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_motorola_la_LINK) $(am_libmm_plugin_motorola_la_rpath) $(libmm_plugin_motorola_la_OBJECTS) $(libmm_plugin_motorola_la_LIBADD) $(LIBS)
mtk/$(am__dirstamp):
	@$(MKDIR_P) mtk
	@: > mtk/$(am__dirstamp)
mtk/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) mtk/$(DEPDIR)
	@: > mtk/$(DEPDIR)/$(am__dirstamp)
mtk/libmm_plugin_mtk_la-mm-plugin-mtk.lo: mtk/$(am__dirstamp) \
	mtk/$(DEPDIR)/$(am__dirstamp)
mtk/libmm_plugin_mtk_la-mm-broadband-modem-mtk.lo:  \
	mtk/$(am__dirstamp) mtk/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-mtk.la: $(libmm_plugin_mtk_la_OBJECTS) $(libmm_plugin_mtk_la_DEPENDENCIES) $(EXTRA_libmm_plugin_mtk_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_mtk_la_LINK) $(am_libmm_plugin_mtk_la_rpath) $(libmm_plugin_mtk_la_OBJECTS) $(libmm_plugin_mtk_la_LIBADD) $(LIBS)
nokia/$(am__dirstamp):
	@$(MKDIR_P) nokia
	@: > nokia/$(am__dirstamp)
nokia/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) nokia/$(DEPDIR)
	@: > nokia/$(DEPDIR)/$(am__dirstamp)
nokia/libmm_plugin_nokia_icera_la-mm-plugin-nokia-icera.lo:  \
	nokia/$(am__dirstamp) nokia/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-nokia-icera.la: $(libmm_plugin_nokia_icera_la_OBJECTS) $(libmm_plugin_nokia_icera_la_DEPENDENCIES) $(EXTRA_libmm_plugin_nokia_icera_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_nokia_icera_la_LINK) $(am_libmm_plugin_nokia_icera_la_rpath) $(libmm_plugin_nokia_icera_la_OBJECTS) $(libmm_plugin_nokia_icera_la_LIBADD) $(LIBS)
nokia/libmm_plugin_nokia_la-mm-plugin-nokia.lo: nokia/$(am__dirstamp) \
	nokia/$(DEPDIR)/$(am__dirstamp)
nokia/libmm_plugin_nokia_la-mm-sim-nokia.lo: nokia/$(am__dirstamp) \
	nokia/$(DEPDIR)/$(am__dirstamp)
nokia/libmm_plugin_nokia_la-mm-broadband-modem-nokia.lo:  \
	nokia/$(am__dirstamp) nokia/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-nokia.la: $(libmm_plugin_nokia_la_OBJECTS) $(libmm_plugin_nokia_la_DEPENDENCIES) $(EXTRA_libmm_plugin_nokia_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_nokia_la_LINK) $(am_libmm_plugin_nokia_la_rpath) $(libmm_plugin_nokia_la_OBJECTS) $(libmm_plugin_nokia_la_LIBADD) $(LIBS)
novatel/$(am__dirstamp):
	@$(MKDIR_P) novatel
	@: > novatel/$(am__dirstamp)
novatel/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) novatel/$(DEPDIR)
	@: > novatel/$(DEPDIR)/$(am__dirstamp)
novatel/libmm_plugin_novatel_lte_la-mm-plugin-novatel-lte.lo:  \
	novatel/$(am__dirstamp) novatel/$(DEPDIR)/$(am__dirstamp)
novatel/libmm_plugin_novatel_lte_la-mm-broadband-modem-novatel-lte.lo:  \
	novatel/$(am__dirstamp) novatel/$(DEPDIR)/$(am__dirstamp)
novatel/libmm_plugin_novatel_lte_la-mm-broadband-bearer-novatel-lte.lo:  \
	novatel/$(am__dirstamp) novatel/$(DEPDIR)/$(am__dirstamp)
novatel/libmm_plugin_novatel_lte_la-mm-sim-novatel-lte.lo:  \
	novatel/$(am__dirstamp) novatel/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-novatel-lte.la: $(libmm_plugin_novatel_lte_la_OBJECTS) $(libmm_plugin_novatel_lte_la_DEPENDENCIES) $(EXTRA_libmm_plugin_novatel_lte_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_novatel_lte_la_LINK) $(am_libmm_plugin_novatel_lte_la_rpath) $(libmm_plugin_novatel_lte_la_OBJECTS) $(libmm_plugin_novatel_lte_la_LIBADD) $(LIBS)
novatel/libmm_plugin_novatel_la-mm-plugin-novatel.lo:  \
	novatel/$(am__dirstamp) novatel/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-novatel.la: $(libmm_plugin_novatel_la_OBJECTS) $(libmm_plugin_novatel_la_DEPENDENCIES) $(EXTRA_libmm_plugin_novatel_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_novatel_la_LINK) $(am_libmm_plugin_novatel_la_rpath) $(libmm_plugin_novatel_la_OBJECTS) $(libmm_plugin_novatel_la_LIBADD) $(LIBS)
option/$(am__dirstamp):
	@$(MKDIR_P) option
	@: > option/$(am__dirstamp)
option/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) option/$(DEPDIR)
	@: > option/$(DEPDIR)/$(am__dirstamp)
option/libmm_plugin_option_hso_la-mm-plugin-hso.lo:  \
	option/$(am__dirstamp) option/$(DEPDIR)/$(am__dirstamp)
option/libmm_plugin_option_hso_la-mm-broadband-bearer-hso.lo:  \
	option/$(am__dirstamp) option/$(DEPDIR)/$(am__dirstamp)
option/libmm_plugin_option_hso_la-mm-broadband-modem-hso.lo:  \
	option/$(am__dirstamp) option/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-option-hso.la: $(libmm_plugin_option_hso_la_OBJECTS) $(libmm_plugin_option_hso_la_DEPENDENCIES) $(EXTRA_libmm_plugin_option_hso_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_option_hso_la_LINK) $(am_libmm_plugin_option_hso_la_rpath) $(libmm_plugin_option_hso_la_OBJECTS) $(libmm_plugin_option_hso_la_LIBADD) $(LIBS)
option/libmm_plugin_option_la-mm-plugin-option.lo:  \
	option/$(am__dirstamp) option/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-option.la: $(libmm_plugin_option_la_OBJECTS) $(libmm_plugin_option_la_DEPENDENCIES) $(EXTRA_libmm_plugin_option_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_option_la_LINK) $(am_libmm_plugin_option_la_rpath) $(libmm_plugin_option_la_OBJECTS) $(libmm_plugin_option_la_LIBADD) $(LIBS)
pantech/$(am__dirstamp):
	@$(MKDIR_P) pantech
	@: > pantech/$(am__dirstamp)
pantech/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) pantech/$(DEPDIR)
	@: > pantech/$(DEPDIR)/$(am__dirstamp)
pantech/libmm_plugin_pantech_la-mm-plugin-pantech.lo:  \
	pantech/$(am__dirstamp) pantech/$(DEPDIR)/$(am__dirstamp)
pantech/libmm_plugin_pantech_la-mm-sim-pantech.lo:  \
	pantech/$(am__dirstamp) pantech/$(DEPDIR)/$(am__dirstamp)
pantech/libmm_plugin_pantech_la-mm-broadband-modem-pantech.lo:  \
	pantech/$(am__dirstamp) pantech/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-pantech.la: $(libmm_plugin_pantech_la_OBJECTS) $(libmm_plugin_pantech_la_DEPENDENCIES) $(EXTRA_libmm_plugin_pantech_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_pantech_la_LINK) $(am_libmm_plugin_pantech_la_rpath) $(libmm_plugin_pantech_la_OBJECTS) $(libmm_plugin_pantech_la_LIBADD) $(LIBS)
quectel/$(am__dirstamp):
	@$(MKDIR_P) quectel
	@: > quectel/$(am__dirstamp)
quectel/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) quectel/$(DEPDIR)
	@: > quectel/$(DEPDIR)/$(am__dirstamp)
quectel/libmm_plugin_quectel_la-mm-plugin-quectel.lo:  \
	quectel/$(am__dirstamp) quectel/$(DEPDIR)/$(am__dirstamp)
quectel/libmm_plugin_quectel_la-mm-shared-quectel.lo:  \
	quectel/$(am__dirstamp) quectel/$(DEPDIR)/$(am__dirstamp)
quectel/libmm_plugin_quectel_la-mm-broadband-modem-quectel.lo:  \
	quectel/$(am__dirstamp) quectel/$(DEPDIR)/$(am__dirstamp)
quectel/libmm_plugin_quectel_la-mm-broadband-modem-qmi-quectel.lo:  \
	quectel/$(am__dirstamp) quectel/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-quectel.la: $(libmm_plugin_quectel_la_OBJECTS) $(libmm_plugin_quectel_la_DEPENDENCIES) $(EXTRA_libmm_plugin_quectel_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_quectel_la_LINK) $(am_libmm_plugin_quectel_la_rpath) $(libmm_plugin_quectel_la_OBJECTS) $(libmm_plugin_quectel_la_LIBADD) $(LIBS)
samsung/$(am__dirstamp):
	@$(MKDIR_P) samsung
	@: > samsung/$(am__dirstamp)
samsung/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) samsung/$(DEPDIR)
	@: > samsung/$(DEPDIR)/$(am__dirstamp)
samsung/libmm_plugin_samsung_la-mm-plugin-samsung.lo:  \
	samsung/$(am__dirstamp) samsung/$(DEPDIR)/$(am__dirstamp)
samsung/libmm_plugin_samsung_la-mm-broadband-modem-samsung.lo:  \
	samsung/$(am__dirstamp) samsung/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-samsung.la: $(libmm_plugin_samsung_la_OBJECTS) $(libmm_plugin_samsung_la_DEPENDENCIES) $(EXTRA_libmm_plugin_samsung_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_samsung_la_LINK) $(am_libmm_plugin_samsung_la_rpath) $(libmm_plugin_samsung_la_OBJECTS) $(libmm_plugin_samsung_la_LIBADD) $(LIBS)
sierra/libmm_plugin_sierra_legacy_la-mm-plugin-sierra-legacy.lo:  \
	sierra/$(am__dirstamp) sierra/$(DEPDIR)/$(am__dirstamp)
sierra/libmm_plugin_sierra_legacy_la-mm-broadband-modem-sierra-icera.lo:  \
	sierra/$(am__dirstamp) sierra/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-sierra-legacy.la: $(libmm_plugin_sierra_legacy_la_OBJECTS) $(libmm_plugin_sierra_legacy_la_DEPENDENCIES) $(EXTRA_libmm_plugin_sierra_legacy_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_sierra_legacy_la_LINK) $(am_libmm_plugin_sierra_legacy_la_rpath) $(libmm_plugin_sierra_legacy_la_OBJECTS) $(libmm_plugin_sierra_legacy_la_LIBADD) $(LIBS)
sierra/libmm_plugin_sierra_la-mm-plugin-sierra.lo:  \
	sierra/$(am__dirstamp) sierra/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-sierra.la: $(libmm_plugin_sierra_la_OBJECTS) $(libmm_plugin_sierra_la_DEPENDENCIES) $(EXTRA_libmm_plugin_sierra_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_sierra_la_LINK) $(am_libmm_plugin_sierra_la_rpath) $(libmm_plugin_sierra_la_OBJECTS) $(libmm_plugin_sierra_la_LIBADD) $(LIBS)
simtech/libmm_plugin_simtech_la-mm-plugin-simtech.lo:  \
	simtech/$(am__dirstamp) simtech/$(DEPDIR)/$(am__dirstamp)
simtech/libmm_plugin_simtech_la-mm-shared-simtech.lo:  \
	simtech/$(am__dirstamp) simtech/$(DEPDIR)/$(am__dirstamp)
simtech/libmm_plugin_simtech_la-mm-broadband-modem-simtech.lo:  \
	simtech/$(am__dirstamp) simtech/$(DEPDIR)/$(am__dirstamp)
simtech/libmm_plugin_simtech_la-mm-broadband-modem-qmi-simtech.lo:  \
	simtech/$(am__dirstamp) simtech/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-simtech.la: $(libmm_plugin_simtech_la_OBJECTS) $(libmm_plugin_simtech_la_DEPENDENCIES) $(EXTRA_libmm_plugin_simtech_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_simtech_la_LINK) $(am_libmm_plugin_simtech_la_rpath) $(libmm_plugin_simtech_la_OBJECTS) $(libmm_plugin_simtech_la_LIBADD) $(LIBS)
telit/libmm_plugin_telit_la-mm-plugin-telit.lo: telit/$(am__dirstamp) \
	telit/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-telit.la: $(libmm_plugin_telit_la_OBJECTS) $(libmm_plugin_telit_la_DEPENDENCIES) $(EXTRA_libmm_plugin_telit_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_telit_la_LINK) $(am_libmm_plugin_telit_la_rpath) $(libmm_plugin_telit_la_OBJECTS) $(libmm_plugin_telit_la_LIBADD) $(LIBS)
thuraya/libmm_plugin_thuraya_la-mm-plugin-thuraya.lo:  \
	thuraya/$(am__dirstamp) thuraya/$(DEPDIR)/$(am__dirstamp)
thuraya/libmm_plugin_thuraya_la-mm-broadband-modem-thuraya.lo:  \
	thuraya/$(am__dirstamp) thuraya/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-thuraya.la: $(libmm_plugin_thuraya_la_OBJECTS) $(libmm_plugin_thuraya_la_DEPENDENCIES) $(EXTRA_libmm_plugin_thuraya_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_thuraya_la_LINK) $(am_libmm_plugin_thuraya_la_rpath) $(libmm_plugin_thuraya_la_OBJECTS) $(libmm_plugin_thuraya_la_LIBADD) $(LIBS)
tplink/$(am__dirstamp):
	@$(MKDIR_P) tplink
	@: > tplink/$(am__dirstamp)
tplink/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) tplink/$(DEPDIR)
	@: > tplink/$(DEPDIR)/$(am__dirstamp)
tplink/libmm_plugin_tplink_la-mm-plugin-tplink.lo:  \
	tplink/$(am__dirstamp) tplink/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-tplink.la: $(libmm_plugin_tplink_la_OBJECTS) $(libmm_plugin_tplink_la_DEPENDENCIES) $(EXTRA_libmm_plugin_tplink_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_tplink_la_LINK) $(am_libmm_plugin_tplink_la_rpath) $(libmm_plugin_tplink_la_OBJECTS) $(libmm_plugin_tplink_la_LIBADD) $(LIBS)
ublox/libmm_plugin_ublox_la-mm-plugin-ublox.lo: ublox/$(am__dirstamp) \
	ublox/$(DEPDIR)/$(am__dirstamp)
ublox/libmm_plugin_ublox_la-mm-broadband-bearer-ublox.lo:  \
	ublox/$(am__dirstamp) ublox/$(DEPDIR)/$(am__dirstamp)
ublox/libmm_plugin_ublox_la-mm-broadband-modem-ublox.lo:  \
	ublox/$(am__dirstamp) ublox/$(DEPDIR)/$(am__dirstamp)
ublox/libmm_plugin_ublox_la-mm-sim-ublox.lo: ublox/$(am__dirstamp) \
	ublox/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-ublox.la: $(libmm_plugin_ublox_la_OBJECTS) $(libmm_plugin_ublox_la_DEPENDENCIES) $(EXTRA_libmm_plugin_ublox_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_ublox_la_LINK) $(am_libmm_plugin_ublox_la_rpath) $(libmm_plugin_ublox_la_OBJECTS) $(libmm_plugin_ublox_la_LIBADD) $(LIBS)
via/$(am__dirstamp):
	@$(MKDIR_P) via
	@: > via/$(am__dirstamp)
via/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) via/$(DEPDIR)
	@: > via/$(DEPDIR)/$(am__dirstamp)
via/libmm_plugin_via_la-mm-plugin-via.lo: via/$(am__dirstamp) \
	via/$(DEPDIR)/$(am__dirstamp)
via/libmm_plugin_via_la-mm-broadband-modem-via.lo:  \
	via/$(am__dirstamp) via/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-via.la: $(libmm_plugin_via_la_OBJECTS) $(libmm_plugin_via_la_DEPENDENCIES) $(EXTRA_libmm_plugin_via_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_via_la_LINK) $(am_libmm_plugin_via_la_rpath) $(libmm_plugin_via_la_OBJECTS) $(libmm_plugin_via_la_LIBADD) $(LIBS)
wavecom/$(am__dirstamp):
	@$(MKDIR_P) wavecom
	@: > wavecom/$(am__dirstamp)
wavecom/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) wavecom/$(DEPDIR)
	@: > wavecom/$(DEPDIR)/$(am__dirstamp)
wavecom/libmm_plugin_wavecom_la-mm-plugin-wavecom.lo:  \
	wavecom/$(am__dirstamp) wavecom/$(DEPDIR)/$(am__dirstamp)
wavecom/libmm_plugin_wavecom_la-mm-broadband-modem-wavecom.lo:  \
	wavecom/$(am__dirstamp) wavecom/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-wavecom.la: $(libmm_plugin_wavecom_la_OBJECTS) $(libmm_plugin_wavecom_la_DEPENDENCIES) $(EXTRA_libmm_plugin_wavecom_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_wavecom_la_LINK) $(am_libmm_plugin_wavecom_la_rpath) $(libmm_plugin_wavecom_la_OBJECTS) $(libmm_plugin_wavecom_la_LIBADD) $(LIBS)
x22x/$(am__dirstamp):
	@$(MKDIR_P) x22x
	@: > x22x/$(am__dirstamp)
x22x/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) x22x/$(DEPDIR)
	@: > x22x/$(DEPDIR)/$(am__dirstamp)
x22x/libmm_plugin_x22x_la-mm-plugin-x22x.lo: x22x/$(am__dirstamp) \
	x22x/$(DEPDIR)/$(am__dirstamp)
x22x/libmm_plugin_x22x_la-mm-broadband-modem-x22x.lo:  \
	x22x/$(am__dirstamp) x22x/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-x22x.la: $(libmm_plugin_x22x_la_OBJECTS) $(libmm_plugin_x22x_la_DEPENDENCIES) $(EXTRA_libmm_plugin_x22x_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_x22x_la_LINK) $(am_libmm_plugin_x22x_la_rpath) $(libmm_plugin_x22x_la_OBJECTS) $(libmm_plugin_x22x_la_LIBADD) $(LIBS)
zte/$(am__dirstamp):
	@$(MKDIR_P) zte
	@: > zte/$(am__dirstamp)
zte/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) zte/$(DEPDIR)
	@: > zte/$(DEPDIR)/$(am__dirstamp)
zte/libmm_plugin_zte_la-mm-plugin-zte.lo: zte/$(am__dirstamp) \
	zte/$(DEPDIR)/$(am__dirstamp)
zte/libmm_plugin_zte_la-mm-common-zte.lo: zte/$(am__dirstamp) \
	zte/$(DEPDIR)/$(am__dirstamp)
zte/libmm_plugin_zte_la-mm-broadband-modem-zte.lo:  \
	zte/$(am__dirstamp) zte/$(DEPDIR)/$(am__dirstamp)
zte/libmm_plugin_zte_la-mm-broadband-modem-zte-icera.lo:  \
	zte/$(am__dirstamp) zte/$(DEPDIR)/$(am__dirstamp)

libmm-plugin-zte.la: $(libmm_plugin_zte_la_OBJECTS) $(libmm_plugin_zte_la_DEPENDENCIES) $(EXTRA_libmm_plugin_zte_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_plugin_zte_la_LINK) $(am_libmm_plugin_zte_la_rpath) $(libmm_plugin_zte_la_OBJECTS) $(libmm_plugin_zte_la_LIBADD) $(LIBS)
foxconn/libmm_shared_foxconn_la-mm-shared.lo: foxconn/$(am__dirstamp) \
	foxconn/$(DEPDIR)/$(am__dirstamp)
foxconn/libmm_shared_foxconn_la-mm-broadband-modem-foxconn-t77w968.lo:  \
	foxconn/$(am__dirstamp) foxconn/$(DEPDIR)/$(am__dirstamp)

libmm-shared-foxconn.la: $(libmm_shared_foxconn_la_OBJECTS) $(libmm_shared_foxconn_la_DEPENDENCIES) $(EXTRA_libmm_shared_foxconn_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_shared_foxconn_la_LINK) $(am_libmm_shared_foxconn_la_rpath) $(libmm_shared_foxconn_la_OBJECTS) $(libmm_shared_foxconn_la_LIBADD) $(LIBS)
icera/libmm_shared_icera_la-mm-shared.lo: icera/$(am__dirstamp) \
	icera/$(DEPDIR)/$(am__dirstamp)
icera/libmm_shared_icera_la-mm-broadband-modem-icera.lo:  \
	icera/$(am__dirstamp) icera/$(DEPDIR)/$(am__dirstamp)
icera/libmm_shared_icera_la-mm-broadband-bearer-icera.lo:  \
	icera/$(am__dirstamp) icera/$(DEPDIR)/$(am__dirstamp)

libmm-shared-icera.la: $(libmm_shared_icera_la_OBJECTS) $(libmm_shared_icera_la_DEPENDENCIES) $(EXTRA_libmm_shared_icera_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_shared_icera_la_LINK) $(am_libmm_shared_icera_la_rpath) $(libmm_shared_icera_la_OBJECTS) $(libmm_shared_icera_la_LIBADD) $(LIBS)
novatel/libmm_shared_novatel_la-mm-shared.lo: novatel/$(am__dirstamp) \
	novatel/$(DEPDIR)/$(am__dirstamp)
novatel/libmm_shared_novatel_la-mm-common-novatel.lo:  \
	novatel/$(am__dirstamp) novatel/$(DEPDIR)/$(am__dirstamp)
novatel/libmm_shared_novatel_la-mm-broadband-modem-novatel.lo:  \
	novatel/$(am__dirstamp) novatel/$(DEPDIR)/$(am__dirstamp)

libmm-shared-novatel.la: $(libmm_shared_novatel_la_OBJECTS) $(libmm_shared_novatel_la_DEPENDENCIES) $(EXTRA_libmm_shared_novatel_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_shared_novatel_la_LINK) $(am_libmm_shared_novatel_la_rpath) $(libmm_shared_novatel_la_OBJECTS) $(libmm_shared_novatel_la_LIBADD) $(LIBS)
option/libmm_shared_option_la-mm-shared.lo: option/$(am__dirstamp) \
	option/$(DEPDIR)/$(am__dirstamp)
option/libmm_shared_option_la-mm-broadband-modem-option.lo:  \
	option/$(am__dirstamp) option/$(DEPDIR)/$(am__dirstamp)

libmm-shared-option.la: $(libmm_shared_option_la_OBJECTS) $(libmm_shared_option_la_DEPENDENCIES) $(EXTRA_libmm_shared_option_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_shared_option_la_LINK) $(am_libmm_shared_option_la_rpath) $(libmm_shared_option_la_OBJECTS) $(libmm_shared_option_la_LIBADD) $(LIBS)
sierra/libmm_shared_sierra_la-mm-shared.lo: sierra/$(am__dirstamp) \
	sierra/$(DEPDIR)/$(am__dirstamp)
sierra/libmm_shared_sierra_la-mm-common-sierra.lo:  \
	sierra/$(am__dirstamp) sierra/$(DEPDIR)/$(am__dirstamp)
sierra/libmm_shared_sierra_la-mm-sim-sierra.lo:  \
	sierra/$(am__dirstamp) sierra/$(DEPDIR)/$(am__dirstamp)
sierra/libmm_shared_sierra_la-mm-broadband-bearer-sierra.lo:  \
	sierra/$(am__dirstamp) sierra/$(DEPDIR)/$(am__dirstamp)
sierra/libmm_shared_sierra_la-mm-broadband-modem-sierra.lo:  \
	sierra/$(am__dirstamp) sierra/$(DEPDIR)/$(am__dirstamp)

libmm-shared-sierra.la: $(libmm_shared_sierra_la_OBJECTS) $(libmm_shared_sierra_la_DEPENDENCIES) $(EXTRA_libmm_shared_sierra_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_shared_sierra_la_LINK) $(am_libmm_shared_sierra_la_rpath) $(libmm_shared_sierra_la_OBJECTS) $(libmm_shared_sierra_la_LIBADD) $(LIBS)
telit/libmm_shared_telit_la-mm-shared.lo: telit/$(am__dirstamp) \
	telit/$(DEPDIR)/$(am__dirstamp)
telit/libmm_shared_telit_la-mm-common-telit.lo: telit/$(am__dirstamp) \
	telit/$(DEPDIR)/$(am__dirstamp)
telit/libmm_shared_telit_la-mm-shared-telit.lo: telit/$(am__dirstamp) \
	telit/$(DEPDIR)/$(am__dirstamp)
telit/libmm_shared_telit_la-mm-broadband-modem-telit.lo:  \
	telit/$(am__dirstamp) telit/$(DEPDIR)/$(am__dirstamp)
telit/libmm_shared_telit_la-mm-broadband-modem-mbim-telit.lo:  \
	telit/$(am__dirstamp) telit/$(DEPDIR)/$(am__dirstamp)

libmm-shared-telit.la: $(libmm_shared_telit_la_OBJECTS) $(libmm_shared_telit_la_DEPENDENCIES) $(EXTRA_libmm_shared_telit_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_shared_telit_la_LINK) $(am_libmm_shared_telit_la_rpath) $(libmm_shared_telit_la_OBJECTS) $(libmm_shared_telit_la_LIBADD) $(LIBS)
xmm/libmm_shared_xmm_la-mm-shared.lo: xmm/$(am__dirstamp) \
	xmm/$(DEPDIR)/$(am__dirstamp)
xmm/libmm_shared_xmm_la-mm-shared-xmm.lo: xmm/$(am__dirstamp) \
	xmm/$(DEPDIR)/$(am__dirstamp)
xmm/libmm_shared_xmm_la-mm-broadband-modem-xmm.lo:  \
	xmm/$(am__dirstamp) xmm/$(DEPDIR)/$(am__dirstamp)
xmm/libmm_shared_xmm_la-mm-broadband-modem-mbim-xmm.lo:  \
	xmm/$(am__dirstamp) xmm/$(DEPDIR)/$(am__dirstamp)

libmm-shared-xmm.la: $(libmm_shared_xmm_la_OBJECTS) $(libmm_shared_xmm_la_DEPENDENCIES) $(EXTRA_libmm_shared_xmm_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_shared_xmm_la_LINK) $(am_libmm_shared_xmm_la_rpath) $(libmm_shared_xmm_la_OBJECTS) $(libmm_shared_xmm_la_LIBADD) $(LIBS)
tests/$(am__dirstamp):
	@$(MKDIR_P) tests
	@: > tests/$(am__dirstamp)
tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) tests/$(DEPDIR)
	@: > tests/$(DEPDIR)/$(am__dirstamp)
tests/libmm_test_common_la-test-fixture.lo: tests/$(am__dirstamp) \
	tests/$(DEPDIR)/$(am__dirstamp)
tests/libmm_test_common_la-test-port-context.lo:  \
	tests/$(am__dirstamp) tests/$(DEPDIR)/$(am__dirstamp)
tests/libmm_test_common_la-test-helpers.lo: tests/$(am__dirstamp) \
	tests/$(DEPDIR)/$(am__dirstamp)

libmm-test-common.la: $(libmm_test_common_la_OBJECTS) $(libmm_test_common_la_DEPENDENCIES) $(EXTRA_libmm_test_common_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK)  $(libmm_test_common_la_OBJECTS) $(libmm_test_common_la_LIBADD) $(LIBS)
tests/test-keyfiles.$(OBJEXT): tests/$(am__dirstamp) \
	tests/$(DEPDIR)/$(am__dirstamp)

test-keyfiles$(EXEEXT): $(test_keyfiles_OBJECTS) $(test_keyfiles_DEPENDENCIES) $(EXTRA_test_keyfiles_DEPENDENCIES) 
	@rm -f test-keyfiles$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_keyfiles_OBJECTS) $(test_keyfiles_LDADD) $(LIBS)
altair/tests/$(am__dirstamp):
	@$(MKDIR_P) altair/tests
	@: > altair/tests/$(am__dirstamp)
altair/tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) altair/tests/$(DEPDIR)
	@: > altair/tests/$(DEPDIR)/$(am__dirstamp)
altair/tests/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.$(OBJEXT):  \
	altair/tests/$(am__dirstamp) \
	altair/tests/$(DEPDIR)/$(am__dirstamp)

test-modem-helpers-altair-lte$(EXEEXT): $(test_modem_helpers_altair_lte_OBJECTS) $(test_modem_helpers_altair_lte_DEPENDENCIES) $(EXTRA_test_modem_helpers_altair_lte_DEPENDENCIES) 
	@rm -f test-modem-helpers-altair-lte$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_modem_helpers_altair_lte_OBJECTS) $(test_modem_helpers_altair_lte_LDADD) $(LIBS)
cinterion/tests/$(am__dirstamp):
	@$(MKDIR_P) cinterion/tests
	@: > cinterion/tests/$(am__dirstamp)
cinterion/tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) cinterion/tests/$(DEPDIR)
	@: > cinterion/tests/$(DEPDIR)/$(am__dirstamp)
cinterion/tests/test_modem_helpers_cinterion-test-modem-helpers-cinterion.$(OBJEXT):  \
	cinterion/tests/$(am__dirstamp) \
	cinterion/tests/$(DEPDIR)/$(am__dirstamp)

test-modem-helpers-cinterion$(EXEEXT): $(test_modem_helpers_cinterion_OBJECTS) $(test_modem_helpers_cinterion_DEPENDENCIES) $(EXTRA_test_modem_helpers_cinterion_DEPENDENCIES) 
	@rm -f test-modem-helpers-cinterion$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_modem_helpers_cinterion_OBJECTS) $(test_modem_helpers_cinterion_LDADD) $(LIBS)
huawei/tests/$(am__dirstamp):
	@$(MKDIR_P) huawei/tests
	@: > huawei/tests/$(am__dirstamp)
huawei/tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) huawei/tests/$(DEPDIR)
	@: > huawei/tests/$(DEPDIR)/$(am__dirstamp)
huawei/tests/test_modem_helpers_huawei-test-modem-helpers-huawei.$(OBJEXT):  \
	huawei/tests/$(am__dirstamp) \
	huawei/tests/$(DEPDIR)/$(am__dirstamp)

test-modem-helpers-huawei$(EXEEXT): $(test_modem_helpers_huawei_OBJECTS) $(test_modem_helpers_huawei_DEPENDENCIES) $(EXTRA_test_modem_helpers_huawei_DEPENDENCIES) 
	@rm -f test-modem-helpers-huawei$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_modem_helpers_huawei_OBJECTS) $(test_modem_helpers_huawei_LDADD) $(LIBS)
icera/tests/$(am__dirstamp):
	@$(MKDIR_P) icera/tests
	@: > icera/tests/$(am__dirstamp)
icera/tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) icera/tests/$(DEPDIR)
	@: > icera/tests/$(DEPDIR)/$(am__dirstamp)
icera/tests/test_modem_helpers_icera-test-modem-helpers-icera.$(OBJEXT):  \
	icera/tests/$(am__dirstamp) \
	icera/tests/$(DEPDIR)/$(am__dirstamp)

test-modem-helpers-icera$(EXEEXT): $(test_modem_helpers_icera_OBJECTS) $(test_modem_helpers_icera_DEPENDENCIES) $(EXTRA_test_modem_helpers_icera_DEPENDENCIES) 
	@rm -f test-modem-helpers-icera$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_modem_helpers_icera_OBJECTS) $(test_modem_helpers_icera_LDADD) $(LIBS)
linktop/tests/$(am__dirstamp):
	@$(MKDIR_P) linktop/tests
	@: > linktop/tests/$(am__dirstamp)
linktop/tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) linktop/tests/$(DEPDIR)
	@: > linktop/tests/$(DEPDIR)/$(am__dirstamp)
linktop/tests/test_modem_helpers_linktop-test-modem-helpers-linktop.$(OBJEXT):  \
	linktop/tests/$(am__dirstamp) \
	linktop/tests/$(DEPDIR)/$(am__dirstamp)

test-modem-helpers-linktop$(EXEEXT): $(test_modem_helpers_linktop_OBJECTS) $(test_modem_helpers_linktop_DEPENDENCIES) $(EXTRA_test_modem_helpers_linktop_DEPENDENCIES) 
	@rm -f test-modem-helpers-linktop$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_modem_helpers_linktop_OBJECTS) $(test_modem_helpers_linktop_LDADD) $(LIBS)
mbm/tests/$(am__dirstamp):
	@$(MKDIR_P) mbm/tests
	@: > mbm/tests/$(am__dirstamp)
mbm/tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) mbm/tests/$(DEPDIR)
	@: > mbm/tests/$(DEPDIR)/$(am__dirstamp)
mbm/tests/test_modem_helpers_mbm-test-modem-helpers-mbm.$(OBJEXT):  \
	mbm/tests/$(am__dirstamp) mbm/tests/$(DEPDIR)/$(am__dirstamp)

test-modem-helpers-mbm$(EXEEXT): $(test_modem_helpers_mbm_OBJECTS) $(test_modem_helpers_mbm_DEPENDENCIES) $(EXTRA_test_modem_helpers_mbm_DEPENDENCIES) 
	@rm -f test-modem-helpers-mbm$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_modem_helpers_mbm_OBJECTS) $(test_modem_helpers_mbm_LDADD) $(LIBS)
sierra/tests/$(am__dirstamp):
	@$(MKDIR_P) sierra/tests
	@: > sierra/tests/$(am__dirstamp)
sierra/tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) sierra/tests/$(DEPDIR)
	@: > sierra/tests/$(DEPDIR)/$(am__dirstamp)
sierra/tests/test_modem_helpers_sierra-test-modem-helpers-sierra.$(OBJEXT):  \
	sierra/tests/$(am__dirstamp) \
	sierra/tests/$(DEPDIR)/$(am__dirstamp)

test-modem-helpers-sierra$(EXEEXT): $(test_modem_helpers_sierra_OBJECTS) $(test_modem_helpers_sierra_DEPENDENCIES) $(EXTRA_test_modem_helpers_sierra_DEPENDENCIES) 
	@rm -f test-modem-helpers-sierra$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_modem_helpers_sierra_OBJECTS) $(test_modem_helpers_sierra_LDADD) $(LIBS)
simtech/tests/$(am__dirstamp):
	@$(MKDIR_P) simtech/tests
	@: > simtech/tests/$(am__dirstamp)
simtech/tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) simtech/tests/$(DEPDIR)
	@: > simtech/tests/$(DEPDIR)/$(am__dirstamp)
simtech/tests/test_modem_helpers_simtech-test-modem-helpers-simtech.$(OBJEXT):  \
	simtech/tests/$(am__dirstamp) \
	simtech/tests/$(DEPDIR)/$(am__dirstamp)

test-modem-helpers-simtech$(EXEEXT): $(test_modem_helpers_simtech_OBJECTS) $(test_modem_helpers_simtech_DEPENDENCIES) $(EXTRA_test_modem_helpers_simtech_DEPENDENCIES) 
	@rm -f test-modem-helpers-simtech$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_modem_helpers_simtech_OBJECTS) $(test_modem_helpers_simtech_LDADD) $(LIBS)
telit/tests/$(am__dirstamp):
	@$(MKDIR_P) telit/tests
	@: > telit/tests/$(am__dirstamp)
telit/tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) telit/tests/$(DEPDIR)
	@: > telit/tests/$(DEPDIR)/$(am__dirstamp)
telit/tests/test_modem_helpers_telit-test-mm-modem-helpers-telit.$(OBJEXT):  \
	telit/tests/$(am__dirstamp) \
	telit/tests/$(DEPDIR)/$(am__dirstamp)

test-modem-helpers-telit$(EXEEXT): $(test_modem_helpers_telit_OBJECTS) $(test_modem_helpers_telit_DEPENDENCIES) $(EXTRA_test_modem_helpers_telit_DEPENDENCIES) 
	@rm -f test-modem-helpers-telit$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_modem_helpers_telit_OBJECTS) $(test_modem_helpers_telit_LDADD) $(LIBS)
thuraya/tests/$(am__dirstamp):
	@$(MKDIR_P) thuraya/tests
	@: > thuraya/tests/$(am__dirstamp)
thuraya/tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) thuraya/tests/$(DEPDIR)
	@: > thuraya/tests/$(DEPDIR)/$(am__dirstamp)
thuraya/tests/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.$(OBJEXT):  \
	thuraya/tests/$(am__dirstamp) \
	thuraya/tests/$(DEPDIR)/$(am__dirstamp)

test-modem-helpers-thuraya$(EXEEXT): $(test_modem_helpers_thuraya_OBJECTS) $(test_modem_helpers_thuraya_DEPENDENCIES) $(EXTRA_test_modem_helpers_thuraya_DEPENDENCIES) 
	@rm -f test-modem-helpers-thuraya$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_modem_helpers_thuraya_OBJECTS) $(test_modem_helpers_thuraya_LDADD) $(LIBS)
ublox/tests/$(am__dirstamp):
	@$(MKDIR_P) ublox/tests
	@: > ublox/tests/$(am__dirstamp)
ublox/tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) ublox/tests/$(DEPDIR)
	@: > ublox/tests/$(DEPDIR)/$(am__dirstamp)
ublox/tests/test_modem_helpers_ublox-test-modem-helpers-ublox.$(OBJEXT):  \
	ublox/tests/$(am__dirstamp) \
	ublox/tests/$(DEPDIR)/$(am__dirstamp)

test-modem-helpers-ublox$(EXEEXT): $(test_modem_helpers_ublox_OBJECTS) $(test_modem_helpers_ublox_DEPENDENCIES) $(EXTRA_test_modem_helpers_ublox_DEPENDENCIES) 
	@rm -f test-modem-helpers-ublox$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_modem_helpers_ublox_OBJECTS) $(test_modem_helpers_ublox_LDADD) $(LIBS)
xmm/tests/$(am__dirstamp):
	@$(MKDIR_P) xmm/tests
	@: > xmm/tests/$(am__dirstamp)
xmm/tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) xmm/tests/$(DEPDIR)
	@: > xmm/tests/$(DEPDIR)/$(am__dirstamp)
xmm/tests/test_modem_helpers_xmm-test-modem-helpers-xmm.$(OBJEXT):  \
	xmm/tests/$(am__dirstamp) xmm/tests/$(DEPDIR)/$(am__dirstamp)

test-modem-helpers-xmm$(EXEEXT): $(test_modem_helpers_xmm_OBJECTS) $(test_modem_helpers_xmm_DEPENDENCIES) $(EXTRA_test_modem_helpers_xmm_DEPENDENCIES) 
	@rm -f test-modem-helpers-xmm$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_modem_helpers_xmm_OBJECTS) $(test_modem_helpers_xmm_LDADD) $(LIBS)
generic/tests/$(am__dirstamp):
	@$(MKDIR_P) generic/tests
	@: > generic/tests/$(am__dirstamp)
generic/tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) generic/tests/$(DEPDIR)
	@: > generic/tests/$(DEPDIR)/$(am__dirstamp)
generic/tests/test_service_generic-test-service-generic.$(OBJEXT):  \
	generic/tests/$(am__dirstamp) \
	generic/tests/$(DEPDIR)/$(am__dirstamp)

test-service-generic$(EXEEXT): $(test_service_generic_OBJECTS) $(test_service_generic_DEPENDENCIES) $(EXTRA_test_service_generic_DEPENDENCIES) 
	@rm -f test-service-generic$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_service_generic_OBJECTS) $(test_service_generic_LDADD) $(LIBS)
tests/test-udev-rules.$(OBJEXT): tests/$(am__dirstamp) \
	tests/$(DEPDIR)/$(am__dirstamp)

test-udev-rules$(EXEEXT): $(test_udev_rules_OBJECTS) $(test_udev_rules_DEPENDENCIES) $(EXTRA_test_udev_rules_DEPENDENCIES) 
	@rm -f test-udev-rules$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_udev_rules_OBJECTS) $(test_udev_rules_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f NL668/*.$(OBJEXT)
	-rm -f NL668/*.lo
	-rm -f altair/*.$(OBJEXT)
	-rm -f altair/*.lo
	-rm -f altair/tests/*.$(OBJEXT)
	-rm -f anydata/*.$(OBJEXT)
	-rm -f anydata/*.lo
	-rm -f broadmobi/*.$(OBJEXT)
	-rm -f broadmobi/*.lo
	-rm -f cinterion/*.$(OBJEXT)
	-rm -f cinterion/*.lo
	-rm -f cinterion/tests/*.$(OBJEXT)
	-rm -f dell/*.$(OBJEXT)
	-rm -f dell/*.lo
	-rm -f dlink/*.$(OBJEXT)
	-rm -f dlink/*.lo
	-rm -f foxconn/*.$(OBJEXT)
	-rm -f foxconn/*.lo
	-rm -f generic/*.$(OBJEXT)
	-rm -f generic/*.lo
	-rm -f generic/tests/*.$(OBJEXT)
	-rm -f haier/*.$(OBJEXT)
	-rm -f haier/*.lo
	-rm -f huawei/*.$(OBJEXT)
	-rm -f huawei/*.lo
	-rm -f huawei/tests/*.$(OBJEXT)
	-rm -f icera/*.$(OBJEXT)
	-rm -f icera/*.lo
	-rm -f icera/tests/*.$(OBJEXT)
	-rm -f iridium/*.$(OBJEXT)
	-rm -f iridium/*.lo
	-rm -f linktop/*.$(OBJEXT)
	-rm -f linktop/*.lo
	-rm -f linktop/tests/*.$(OBJEXT)
	-rm -f longcheer/*.$(OBJEXT)
	-rm -f longcheer/*.lo
	-rm -f mbm/*.$(OBJEXT)
	-rm -f mbm/*.lo
	-rm -f mbm/tests/*.$(OBJEXT)
	-rm -f me3630/*.$(OBJEXT)
	-rm -f me3630/*.lo
	-rm -f motorola/*.$(OBJEXT)
	-rm -f motorola/*.lo
	-rm -f mtk/*.$(OBJEXT)
	-rm -f mtk/*.lo
	-rm -f nokia/*.$(OBJEXT)
	-rm -f nokia/*.lo
	-rm -f novatel/*.$(OBJEXT)
	-rm -f novatel/*.lo
	-rm -f option/*.$(OBJEXT)
	-rm -f option/*.lo
	-rm -f pantech/*.$(OBJEXT)
	-rm -f pantech/*.lo
	-rm -f quectel/*.$(OBJEXT)
	-rm -f quectel/*.lo
	-rm -f samsung/*.$(OBJEXT)
	-rm -f samsung/*.lo
	-rm -f sierra/*.$(OBJEXT)
	-rm -f sierra/*.lo
	-rm -f sierra/tests/*.$(OBJEXT)
	-rm -f simtech/*.$(OBJEXT)
	-rm -f simtech/*.lo
	-rm -f simtech/tests/*.$(OBJEXT)
	-rm -f telit/*.$(OBJEXT)
	-rm -f telit/*.lo
	-rm -f telit/tests/*.$(OBJEXT)
	-rm -f tests/*.$(OBJEXT)
	-rm -f tests/*.lo
	-rm -f thuraya/*.$(OBJEXT)
	-rm -f thuraya/*.lo
	-rm -f thuraya/tests/*.$(OBJEXT)
	-rm -f tplink/*.$(OBJEXT)
	-rm -f tplink/*.lo
	-rm -f ublox/*.$(OBJEXT)
	-rm -f ublox/*.lo
	-rm -f ublox/tests/*.$(OBJEXT)
	-rm -f via/*.$(OBJEXT)
	-rm -f via/*.lo
	-rm -f wavecom/*.$(OBJEXT)
	-rm -f wavecom/*.lo
	-rm -f x22x/*.$(OBJEXT)
	-rm -f x22x/*.lo
	-rm -f xmm/*.$(OBJEXT)
	-rm -f xmm/*.lo
	-rm -f xmm/tests/*.$(OBJEXT)
	-rm -f zte/*.$(OBJEXT)
	-rm -f zte/*.lo

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-bearer-fibocom-ecm.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-fibocom.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-mbim-fibocom.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-mbim-xmm-fibocom.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-plugin-fibocom.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@altair/$(DEPDIR)/libhelpers_altair_lte_la-mm-modem-helpers-altair-lte.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-broadband-bearer-altair-lte.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-broadband-modem-altair-lte.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-plugin-altair-lte.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@altair/tests/$(DEPDIR)/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@anydata/$(DEPDIR)/libmm_plugin_anydata_la-mm-broadband-modem-anydata.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@anydata/$(DEPDIR)/libmm_plugin_anydata_la-mm-plugin-anydata.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@broadmobi/$(DEPDIR)/libmm_plugin_broadmobi_la-mm-plugin-broadmobi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@cinterion/$(DEPDIR)/libhelpers_cinterion_la-mm-modem-helpers-cinterion.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-bearer-cinterion.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-modem-cinterion.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-modem-qmi-cinterion.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-plugin-cinterion.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-shared-cinterion.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@cinterion/tests/$(DEPDIR)/test_modem_helpers_cinterion-test-modem-helpers-cinterion.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@dell/$(DEPDIR)/libmm_plugin_dell_la-mm-plugin-dell.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@dlink/$(DEPDIR)/libmm_plugin_dlink_la-mm-plugin-dlink.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@foxconn/$(DEPDIR)/libmm_plugin_foxconn_la-mm-plugin-foxconn.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@foxconn/$(DEPDIR)/libmm_shared_foxconn_la-mm-broadband-modem-foxconn-t77w968.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@foxconn/$(DEPDIR)/libmm_shared_foxconn_la-mm-shared.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@generic/$(DEPDIR)/libmm_plugin_generic_la-mm-plugin-generic.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@generic/tests/$(DEPDIR)/test_service_generic-test-service-generic.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@haier/$(DEPDIR)/libmm_plugin_haier_la-mm-plugin-haier.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@huawei/$(DEPDIR)/libhelpers_huawei_la-mm-modem-helpers-huawei.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-broadband-bearer-huawei.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-broadband-modem-huawei.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-plugin-huawei.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-sim-huawei.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@huawei/tests/$(DEPDIR)/test_modem_helpers_huawei-test-modem-helpers-huawei.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@icera/$(DEPDIR)/libhelpers_icera_la-mm-modem-helpers-icera.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@icera/$(DEPDIR)/libmm_shared_icera_la-mm-broadband-bearer-icera.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@icera/$(DEPDIR)/libmm_shared_icera_la-mm-broadband-modem-icera.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@icera/$(DEPDIR)/libmm_shared_icera_la-mm-shared.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@icera/tests/$(DEPDIR)/test_modem_helpers_icera-test-modem-helpers-icera.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-bearer-iridium.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-broadband-modem-iridium.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-plugin-iridium.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-sim-iridium.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@linktop/$(DEPDIR)/libhelpers_linktop_la-mm-modem-helpers-linktop.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@linktop/$(DEPDIR)/libmm_plugin_linktop_la-mm-broadband-modem-linktop.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@linktop/$(DEPDIR)/libmm_plugin_linktop_la-mm-plugin-linktop.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@linktop/tests/$(DEPDIR)/test_modem_helpers_linktop-test-modem-helpers-linktop.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@longcheer/$(DEPDIR)/libmm_plugin_longcheer_la-mm-broadband-modem-longcheer.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@longcheer/$(DEPDIR)/libmm_plugin_longcheer_la-mm-plugin-longcheer.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@mbm/$(DEPDIR)/libhelpers_mbm_la-mm-modem-helpers-mbm.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-broadband-bearer-mbm.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-broadband-modem-mbm.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-plugin-mbm.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-sim-mbm.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@mbm/tests/$(DEPDIR)/test_modem_helpers_mbm-test-modem-helpers-mbm.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-broadband-bearer-me3630.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-broadband-modem-me3630.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-common-me3630.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-plugin-me3630.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@motorola/$(DEPDIR)/libmm_plugin_motorola_la-mm-broadband-modem-motorola.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@motorola/$(DEPDIR)/libmm_plugin_motorola_la-mm-plugin-motorola.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@mtk/$(DEPDIR)/libmm_plugin_mtk_la-mm-broadband-modem-mtk.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@mtk/$(DEPDIR)/libmm_plugin_mtk_la-mm-plugin-mtk.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@nokia/$(DEPDIR)/libmm_plugin_nokia_icera_la-mm-plugin-nokia-icera.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-broadband-modem-nokia.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-plugin-nokia.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-sim-nokia.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@novatel/$(DEPDIR)/libmm_plugin_novatel_la-mm-plugin-novatel.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-broadband-bearer-novatel-lte.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-broadband-modem-novatel-lte.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-plugin-novatel-lte.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-sim-novatel-lte.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-broadband-modem-novatel.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-common-novatel.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-shared.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-broadband-bearer-hso.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-broadband-modem-hso.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-plugin-hso.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@option/$(DEPDIR)/libmm_plugin_option_la-mm-plugin-option.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@option/$(DEPDIR)/libmm_shared_option_la-mm-broadband-modem-option.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@option/$(DEPDIR)/libmm_shared_option_la-mm-shared.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-broadband-modem-pantech.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-plugin-pantech.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-sim-pantech.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-broadband-modem-qmi-quectel.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-broadband-modem-quectel.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-plugin-quectel.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-shared-quectel.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@samsung/$(DEPDIR)/libmm_plugin_samsung_la-mm-broadband-modem-samsung.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@samsung/$(DEPDIR)/libmm_plugin_samsung_la-mm-plugin-samsung.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sierra/$(DEPDIR)/libhelpers_sierra_la-mm-modem-helpers-sierra.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sierra/$(DEPDIR)/libmm_plugin_sierra_la-mm-plugin-sierra.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sierra/$(DEPDIR)/libmm_plugin_sierra_legacy_la-mm-broadband-modem-sierra-icera.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sierra/$(DEPDIR)/libmm_plugin_sierra_legacy_la-mm-plugin-sierra-legacy.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-broadband-bearer-sierra.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-broadband-modem-sierra.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-common-sierra.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-shared.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-sim-sierra.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@sierra/tests/$(DEPDIR)/test_modem_helpers_sierra-test-modem-helpers-sierra.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@simtech/$(DEPDIR)/libhelpers_simtech_la-mm-modem-helpers-simtech.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-broadband-modem-qmi-simtech.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-broadband-modem-simtech.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-plugin-simtech.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-shared-simtech.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@simtech/tests/$(DEPDIR)/test_modem_helpers_simtech-test-modem-helpers-simtech.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@telit/$(DEPDIR)/libhelpers_telit_la-mm-modem-helpers-telit.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@telit/$(DEPDIR)/libhelpers_telit_la-mm-telit-enums-types.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@telit/$(DEPDIR)/libmm_plugin_telit_la-mm-plugin-telit.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@telit/$(DEPDIR)/libmm_shared_telit_la-mm-broadband-modem-mbim-telit.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@telit/$(DEPDIR)/libmm_shared_telit_la-mm-broadband-modem-telit.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@telit/$(DEPDIR)/libmm_shared_telit_la-mm-common-telit.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@telit/$(DEPDIR)/libmm_shared_telit_la-mm-shared-telit.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@telit/$(DEPDIR)/libmm_shared_telit_la-mm-shared.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@telit/tests/$(DEPDIR)/test_modem_helpers_telit-test-mm-modem-helpers-telit.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tests/$(DEPDIR)/libmm_test_common_la-test-fixture.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tests/$(DEPDIR)/libmm_test_common_la-test-helpers.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tests/$(DEPDIR)/libmm_test_common_la-test-port-context.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tests/$(DEPDIR)/test-keyfiles.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tests/$(DEPDIR)/test-udev-rules.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@thuraya/$(DEPDIR)/libhelpers_thuraya_la-mm-modem-helpers-thuraya.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@thuraya/$(DEPDIR)/libmm_plugin_thuraya_la-mm-broadband-modem-thuraya.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@thuraya/$(DEPDIR)/libmm_plugin_thuraya_la-mm-plugin-thuraya.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@thuraya/tests/$(DEPDIR)/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tplink/$(DEPDIR)/libmm_plugin_tplink_la-mm-plugin-tplink.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ublox/$(DEPDIR)/libhelpers_ublox_la-mm-modem-helpers-ublox.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ublox/$(DEPDIR)/libhelpers_ublox_la-mm-ublox-enums-types.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-broadband-bearer-ublox.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-broadband-modem-ublox.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-plugin-ublox.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-sim-ublox.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ublox/tests/$(DEPDIR)/test_modem_helpers_ublox-test-modem-helpers-ublox.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@via/$(DEPDIR)/libmm_plugin_via_la-mm-broadband-modem-via.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@via/$(DEPDIR)/libmm_plugin_via_la-mm-plugin-via.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@wavecom/$(DEPDIR)/libmm_plugin_wavecom_la-mm-broadband-modem-wavecom.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@wavecom/$(DEPDIR)/libmm_plugin_wavecom_la-mm-plugin-wavecom.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@x22x/$(DEPDIR)/libmm_plugin_x22x_la-mm-broadband-modem-x22x.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@x22x/$(DEPDIR)/libmm_plugin_x22x_la-mm-plugin-x22x.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@xmm/$(DEPDIR)/libhelpers_xmm_la-mm-modem-helpers-xmm.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-broadband-modem-mbim-xmm.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-broadband-modem-xmm.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-shared-xmm.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-shared.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@xmm/tests/$(DEPDIR)/test_modem_helpers_xmm-test-modem-helpers-xmm.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@zte/$(DEPDIR)/libmm_plugin_zte_la-mm-broadband-modem-zte-icera.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@zte/$(DEPDIR)/libmm_plugin_zte_la-mm-broadband-modem-zte.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@zte/$(DEPDIR)/libmm_plugin_zte_la-mm-common-zte.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@zte/$(DEPDIR)/libmm_plugin_zte_la-mm-plugin-zte.Plo@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

altair/libhelpers_altair_lte_la-mm-modem-helpers-altair-lte.lo: altair/mm-modem-helpers-altair-lte.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_altair_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT altair/libhelpers_altair_lte_la-mm-modem-helpers-altair-lte.lo -MD -MP -MF altair/$(DEPDIR)/libhelpers_altair_lte_la-mm-modem-helpers-altair-lte.Tpo -c -o altair/libhelpers_altair_lte_la-mm-modem-helpers-altair-lte.lo `test -f 'altair/mm-modem-helpers-altair-lte.c' || echo '$(srcdir)/'`altair/mm-modem-helpers-altair-lte.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) altair/$(DEPDIR)/libhelpers_altair_lte_la-mm-modem-helpers-altair-lte.Tpo altair/$(DEPDIR)/libhelpers_altair_lte_la-mm-modem-helpers-altair-lte.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='altair/mm-modem-helpers-altair-lte.c' object='altair/libhelpers_altair_lte_la-mm-modem-helpers-altair-lte.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_altair_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o altair/libhelpers_altair_lte_la-mm-modem-helpers-altair-lte.lo `test -f 'altair/mm-modem-helpers-altair-lte.c' || echo '$(srcdir)/'`altair/mm-modem-helpers-altair-lte.c

cinterion/libhelpers_cinterion_la-mm-modem-helpers-cinterion.lo: cinterion/mm-modem-helpers-cinterion.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_cinterion_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT cinterion/libhelpers_cinterion_la-mm-modem-helpers-cinterion.lo -MD -MP -MF cinterion/$(DEPDIR)/libhelpers_cinterion_la-mm-modem-helpers-cinterion.Tpo -c -o cinterion/libhelpers_cinterion_la-mm-modem-helpers-cinterion.lo `test -f 'cinterion/mm-modem-helpers-cinterion.c' || echo '$(srcdir)/'`cinterion/mm-modem-helpers-cinterion.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) cinterion/$(DEPDIR)/libhelpers_cinterion_la-mm-modem-helpers-cinterion.Tpo cinterion/$(DEPDIR)/libhelpers_cinterion_la-mm-modem-helpers-cinterion.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='cinterion/mm-modem-helpers-cinterion.c' object='cinterion/libhelpers_cinterion_la-mm-modem-helpers-cinterion.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_cinterion_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o cinterion/libhelpers_cinterion_la-mm-modem-helpers-cinterion.lo `test -f 'cinterion/mm-modem-helpers-cinterion.c' || echo '$(srcdir)/'`cinterion/mm-modem-helpers-cinterion.c

huawei/libhelpers_huawei_la-mm-modem-helpers-huawei.lo: huawei/mm-modem-helpers-huawei.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_huawei_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT huawei/libhelpers_huawei_la-mm-modem-helpers-huawei.lo -MD -MP -MF huawei/$(DEPDIR)/libhelpers_huawei_la-mm-modem-helpers-huawei.Tpo -c -o huawei/libhelpers_huawei_la-mm-modem-helpers-huawei.lo `test -f 'huawei/mm-modem-helpers-huawei.c' || echo '$(srcdir)/'`huawei/mm-modem-helpers-huawei.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) huawei/$(DEPDIR)/libhelpers_huawei_la-mm-modem-helpers-huawei.Tpo huawei/$(DEPDIR)/libhelpers_huawei_la-mm-modem-helpers-huawei.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='huawei/mm-modem-helpers-huawei.c' object='huawei/libhelpers_huawei_la-mm-modem-helpers-huawei.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_huawei_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o huawei/libhelpers_huawei_la-mm-modem-helpers-huawei.lo `test -f 'huawei/mm-modem-helpers-huawei.c' || echo '$(srcdir)/'`huawei/mm-modem-helpers-huawei.c

icera/libhelpers_icera_la-mm-modem-helpers-icera.lo: icera/mm-modem-helpers-icera.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_icera_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT icera/libhelpers_icera_la-mm-modem-helpers-icera.lo -MD -MP -MF icera/$(DEPDIR)/libhelpers_icera_la-mm-modem-helpers-icera.Tpo -c -o icera/libhelpers_icera_la-mm-modem-helpers-icera.lo `test -f 'icera/mm-modem-helpers-icera.c' || echo '$(srcdir)/'`icera/mm-modem-helpers-icera.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) icera/$(DEPDIR)/libhelpers_icera_la-mm-modem-helpers-icera.Tpo icera/$(DEPDIR)/libhelpers_icera_la-mm-modem-helpers-icera.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='icera/mm-modem-helpers-icera.c' object='icera/libhelpers_icera_la-mm-modem-helpers-icera.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_icera_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o icera/libhelpers_icera_la-mm-modem-helpers-icera.lo `test -f 'icera/mm-modem-helpers-icera.c' || echo '$(srcdir)/'`icera/mm-modem-helpers-icera.c

linktop/libhelpers_linktop_la-mm-modem-helpers-linktop.lo: linktop/mm-modem-helpers-linktop.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_linktop_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT linktop/libhelpers_linktop_la-mm-modem-helpers-linktop.lo -MD -MP -MF linktop/$(DEPDIR)/libhelpers_linktop_la-mm-modem-helpers-linktop.Tpo -c -o linktop/libhelpers_linktop_la-mm-modem-helpers-linktop.lo `test -f 'linktop/mm-modem-helpers-linktop.c' || echo '$(srcdir)/'`linktop/mm-modem-helpers-linktop.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) linktop/$(DEPDIR)/libhelpers_linktop_la-mm-modem-helpers-linktop.Tpo linktop/$(DEPDIR)/libhelpers_linktop_la-mm-modem-helpers-linktop.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='linktop/mm-modem-helpers-linktop.c' object='linktop/libhelpers_linktop_la-mm-modem-helpers-linktop.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_linktop_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o linktop/libhelpers_linktop_la-mm-modem-helpers-linktop.lo `test -f 'linktop/mm-modem-helpers-linktop.c' || echo '$(srcdir)/'`linktop/mm-modem-helpers-linktop.c

mbm/libhelpers_mbm_la-mm-modem-helpers-mbm.lo: mbm/mm-modem-helpers-mbm.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_mbm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mbm/libhelpers_mbm_la-mm-modem-helpers-mbm.lo -MD -MP -MF mbm/$(DEPDIR)/libhelpers_mbm_la-mm-modem-helpers-mbm.Tpo -c -o mbm/libhelpers_mbm_la-mm-modem-helpers-mbm.lo `test -f 'mbm/mm-modem-helpers-mbm.c' || echo '$(srcdir)/'`mbm/mm-modem-helpers-mbm.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) mbm/$(DEPDIR)/libhelpers_mbm_la-mm-modem-helpers-mbm.Tpo mbm/$(DEPDIR)/libhelpers_mbm_la-mm-modem-helpers-mbm.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mbm/mm-modem-helpers-mbm.c' object='mbm/libhelpers_mbm_la-mm-modem-helpers-mbm.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_mbm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mbm/libhelpers_mbm_la-mm-modem-helpers-mbm.lo `test -f 'mbm/mm-modem-helpers-mbm.c' || echo '$(srcdir)/'`mbm/mm-modem-helpers-mbm.c

sierra/libhelpers_sierra_la-mm-modem-helpers-sierra.lo: sierra/mm-modem-helpers-sierra.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_sierra_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT sierra/libhelpers_sierra_la-mm-modem-helpers-sierra.lo -MD -MP -MF sierra/$(DEPDIR)/libhelpers_sierra_la-mm-modem-helpers-sierra.Tpo -c -o sierra/libhelpers_sierra_la-mm-modem-helpers-sierra.lo `test -f 'sierra/mm-modem-helpers-sierra.c' || echo '$(srcdir)/'`sierra/mm-modem-helpers-sierra.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sierra/$(DEPDIR)/libhelpers_sierra_la-mm-modem-helpers-sierra.Tpo sierra/$(DEPDIR)/libhelpers_sierra_la-mm-modem-helpers-sierra.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sierra/mm-modem-helpers-sierra.c' object='sierra/libhelpers_sierra_la-mm-modem-helpers-sierra.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_sierra_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o sierra/libhelpers_sierra_la-mm-modem-helpers-sierra.lo `test -f 'sierra/mm-modem-helpers-sierra.c' || echo '$(srcdir)/'`sierra/mm-modem-helpers-sierra.c

simtech/libhelpers_simtech_la-mm-modem-helpers-simtech.lo: simtech/mm-modem-helpers-simtech.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_simtech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT simtech/libhelpers_simtech_la-mm-modem-helpers-simtech.lo -MD -MP -MF simtech/$(DEPDIR)/libhelpers_simtech_la-mm-modem-helpers-simtech.Tpo -c -o simtech/libhelpers_simtech_la-mm-modem-helpers-simtech.lo `test -f 'simtech/mm-modem-helpers-simtech.c' || echo '$(srcdir)/'`simtech/mm-modem-helpers-simtech.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) simtech/$(DEPDIR)/libhelpers_simtech_la-mm-modem-helpers-simtech.Tpo simtech/$(DEPDIR)/libhelpers_simtech_la-mm-modem-helpers-simtech.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='simtech/mm-modem-helpers-simtech.c' object='simtech/libhelpers_simtech_la-mm-modem-helpers-simtech.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_simtech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o simtech/libhelpers_simtech_la-mm-modem-helpers-simtech.lo `test -f 'simtech/mm-modem-helpers-simtech.c' || echo '$(srcdir)/'`simtech/mm-modem-helpers-simtech.c

telit/libhelpers_telit_la-mm-modem-helpers-telit.lo: telit/mm-modem-helpers-telit.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT telit/libhelpers_telit_la-mm-modem-helpers-telit.lo -MD -MP -MF telit/$(DEPDIR)/libhelpers_telit_la-mm-modem-helpers-telit.Tpo -c -o telit/libhelpers_telit_la-mm-modem-helpers-telit.lo `test -f 'telit/mm-modem-helpers-telit.c' || echo '$(srcdir)/'`telit/mm-modem-helpers-telit.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) telit/$(DEPDIR)/libhelpers_telit_la-mm-modem-helpers-telit.Tpo telit/$(DEPDIR)/libhelpers_telit_la-mm-modem-helpers-telit.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='telit/mm-modem-helpers-telit.c' object='telit/libhelpers_telit_la-mm-modem-helpers-telit.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o telit/libhelpers_telit_la-mm-modem-helpers-telit.lo `test -f 'telit/mm-modem-helpers-telit.c' || echo '$(srcdir)/'`telit/mm-modem-helpers-telit.c

telit/libhelpers_telit_la-mm-telit-enums-types.lo: telit/mm-telit-enums-types.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT telit/libhelpers_telit_la-mm-telit-enums-types.lo -MD -MP -MF telit/$(DEPDIR)/libhelpers_telit_la-mm-telit-enums-types.Tpo -c -o telit/libhelpers_telit_la-mm-telit-enums-types.lo `test -f 'telit/mm-telit-enums-types.c' || echo '$(srcdir)/'`telit/mm-telit-enums-types.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) telit/$(DEPDIR)/libhelpers_telit_la-mm-telit-enums-types.Tpo telit/$(DEPDIR)/libhelpers_telit_la-mm-telit-enums-types.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='telit/mm-telit-enums-types.c' object='telit/libhelpers_telit_la-mm-telit-enums-types.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o telit/libhelpers_telit_la-mm-telit-enums-types.lo `test -f 'telit/mm-telit-enums-types.c' || echo '$(srcdir)/'`telit/mm-telit-enums-types.c

thuraya/libhelpers_thuraya_la-mm-modem-helpers-thuraya.lo: thuraya/mm-modem-helpers-thuraya.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_thuraya_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT thuraya/libhelpers_thuraya_la-mm-modem-helpers-thuraya.lo -MD -MP -MF thuraya/$(DEPDIR)/libhelpers_thuraya_la-mm-modem-helpers-thuraya.Tpo -c -o thuraya/libhelpers_thuraya_la-mm-modem-helpers-thuraya.lo `test -f 'thuraya/mm-modem-helpers-thuraya.c' || echo '$(srcdir)/'`thuraya/mm-modem-helpers-thuraya.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) thuraya/$(DEPDIR)/libhelpers_thuraya_la-mm-modem-helpers-thuraya.Tpo thuraya/$(DEPDIR)/libhelpers_thuraya_la-mm-modem-helpers-thuraya.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='thuraya/mm-modem-helpers-thuraya.c' object='thuraya/libhelpers_thuraya_la-mm-modem-helpers-thuraya.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_thuraya_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o thuraya/libhelpers_thuraya_la-mm-modem-helpers-thuraya.lo `test -f 'thuraya/mm-modem-helpers-thuraya.c' || echo '$(srcdir)/'`thuraya/mm-modem-helpers-thuraya.c

ublox/libhelpers_ublox_la-mm-modem-helpers-ublox.lo: ublox/mm-modem-helpers-ublox.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_ublox_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ublox/libhelpers_ublox_la-mm-modem-helpers-ublox.lo -MD -MP -MF ublox/$(DEPDIR)/libhelpers_ublox_la-mm-modem-helpers-ublox.Tpo -c -o ublox/libhelpers_ublox_la-mm-modem-helpers-ublox.lo `test -f 'ublox/mm-modem-helpers-ublox.c' || echo '$(srcdir)/'`ublox/mm-modem-helpers-ublox.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ublox/$(DEPDIR)/libhelpers_ublox_la-mm-modem-helpers-ublox.Tpo ublox/$(DEPDIR)/libhelpers_ublox_la-mm-modem-helpers-ublox.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ublox/mm-modem-helpers-ublox.c' object='ublox/libhelpers_ublox_la-mm-modem-helpers-ublox.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_ublox_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ublox/libhelpers_ublox_la-mm-modem-helpers-ublox.lo `test -f 'ublox/mm-modem-helpers-ublox.c' || echo '$(srcdir)/'`ublox/mm-modem-helpers-ublox.c

ublox/libhelpers_ublox_la-mm-ublox-enums-types.lo: ublox/mm-ublox-enums-types.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_ublox_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ublox/libhelpers_ublox_la-mm-ublox-enums-types.lo -MD -MP -MF ublox/$(DEPDIR)/libhelpers_ublox_la-mm-ublox-enums-types.Tpo -c -o ublox/libhelpers_ublox_la-mm-ublox-enums-types.lo `test -f 'ublox/mm-ublox-enums-types.c' || echo '$(srcdir)/'`ublox/mm-ublox-enums-types.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ublox/$(DEPDIR)/libhelpers_ublox_la-mm-ublox-enums-types.Tpo ublox/$(DEPDIR)/libhelpers_ublox_la-mm-ublox-enums-types.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ublox/mm-ublox-enums-types.c' object='ublox/libhelpers_ublox_la-mm-ublox-enums-types.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_ublox_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ublox/libhelpers_ublox_la-mm-ublox-enums-types.lo `test -f 'ublox/mm-ublox-enums-types.c' || echo '$(srcdir)/'`ublox/mm-ublox-enums-types.c

xmm/libhelpers_xmm_la-mm-modem-helpers-xmm.lo: xmm/mm-modem-helpers-xmm.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_xmm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT xmm/libhelpers_xmm_la-mm-modem-helpers-xmm.lo -MD -MP -MF xmm/$(DEPDIR)/libhelpers_xmm_la-mm-modem-helpers-xmm.Tpo -c -o xmm/libhelpers_xmm_la-mm-modem-helpers-xmm.lo `test -f 'xmm/mm-modem-helpers-xmm.c' || echo '$(srcdir)/'`xmm/mm-modem-helpers-xmm.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) xmm/$(DEPDIR)/libhelpers_xmm_la-mm-modem-helpers-xmm.Tpo xmm/$(DEPDIR)/libhelpers_xmm_la-mm-modem-helpers-xmm.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='xmm/mm-modem-helpers-xmm.c' object='xmm/libhelpers_xmm_la-mm-modem-helpers-xmm.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libhelpers_xmm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o xmm/libhelpers_xmm_la-mm-modem-helpers-xmm.lo `test -f 'xmm/mm-modem-helpers-xmm.c' || echo '$(srcdir)/'`xmm/mm-modem-helpers-xmm.c

NL668/libmm_plugin_NL668_la-mm-broadband-bearer-fibocom-ecm.lo: NL668/mm-broadband-bearer-fibocom-ecm.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_NL668_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT NL668/libmm_plugin_NL668_la-mm-broadband-bearer-fibocom-ecm.lo -MD -MP -MF NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-bearer-fibocom-ecm.Tpo -c -o NL668/libmm_plugin_NL668_la-mm-broadband-bearer-fibocom-ecm.lo `test -f 'NL668/mm-broadband-bearer-fibocom-ecm.c' || echo '$(srcdir)/'`NL668/mm-broadband-bearer-fibocom-ecm.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-bearer-fibocom-ecm.Tpo NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-bearer-fibocom-ecm.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='NL668/mm-broadband-bearer-fibocom-ecm.c' object='NL668/libmm_plugin_NL668_la-mm-broadband-bearer-fibocom-ecm.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_NL668_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o NL668/libmm_plugin_NL668_la-mm-broadband-bearer-fibocom-ecm.lo `test -f 'NL668/mm-broadband-bearer-fibocom-ecm.c' || echo '$(srcdir)/'`NL668/mm-broadband-bearer-fibocom-ecm.c

NL668/libmm_plugin_NL668_la-mm-broadband-modem-fibocom.lo: NL668/mm-broadband-modem-fibocom.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_NL668_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT NL668/libmm_plugin_NL668_la-mm-broadband-modem-fibocom.lo -MD -MP -MF NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-fibocom.Tpo -c -o NL668/libmm_plugin_NL668_la-mm-broadband-modem-fibocom.lo `test -f 'NL668/mm-broadband-modem-fibocom.c' || echo '$(srcdir)/'`NL668/mm-broadband-modem-fibocom.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-fibocom.Tpo NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-fibocom.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='NL668/mm-broadband-modem-fibocom.c' object='NL668/libmm_plugin_NL668_la-mm-broadband-modem-fibocom.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_NL668_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o NL668/libmm_plugin_NL668_la-mm-broadband-modem-fibocom.lo `test -f 'NL668/mm-broadband-modem-fibocom.c' || echo '$(srcdir)/'`NL668/mm-broadband-modem-fibocom.c

NL668/libmm_plugin_NL668_la-mm-plugin-fibocom.lo: NL668/mm-plugin-fibocom.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_NL668_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT NL668/libmm_plugin_NL668_la-mm-plugin-fibocom.lo -MD -MP -MF NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-plugin-fibocom.Tpo -c -o NL668/libmm_plugin_NL668_la-mm-plugin-fibocom.lo `test -f 'NL668/mm-plugin-fibocom.c' || echo '$(srcdir)/'`NL668/mm-plugin-fibocom.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-plugin-fibocom.Tpo NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-plugin-fibocom.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='NL668/mm-plugin-fibocom.c' object='NL668/libmm_plugin_NL668_la-mm-plugin-fibocom.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_NL668_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o NL668/libmm_plugin_NL668_la-mm-plugin-fibocom.lo `test -f 'NL668/mm-plugin-fibocom.c' || echo '$(srcdir)/'`NL668/mm-plugin-fibocom.c

NL668/libmm_plugin_NL668_la-mm-broadband-modem-mbim-xmm-fibocom.lo: NL668/mm-broadband-modem-mbim-xmm-fibocom.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_NL668_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT NL668/libmm_plugin_NL668_la-mm-broadband-modem-mbim-xmm-fibocom.lo -MD -MP -MF NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-mbim-xmm-fibocom.Tpo -c -o NL668/libmm_plugin_NL668_la-mm-broadband-modem-mbim-xmm-fibocom.lo `test -f 'NL668/mm-broadband-modem-mbim-xmm-fibocom.c' || echo '$(srcdir)/'`NL668/mm-broadband-modem-mbim-xmm-fibocom.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-mbim-xmm-fibocom.Tpo NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-mbim-xmm-fibocom.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='NL668/mm-broadband-modem-mbim-xmm-fibocom.c' object='NL668/libmm_plugin_NL668_la-mm-broadband-modem-mbim-xmm-fibocom.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_NL668_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o NL668/libmm_plugin_NL668_la-mm-broadband-modem-mbim-xmm-fibocom.lo `test -f 'NL668/mm-broadband-modem-mbim-xmm-fibocom.c' || echo '$(srcdir)/'`NL668/mm-broadband-modem-mbim-xmm-fibocom.c

NL668/libmm_plugin_NL668_la-mm-broadband-modem-mbim-fibocom.lo: NL668/mm-broadband-modem-mbim-fibocom.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_NL668_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT NL668/libmm_plugin_NL668_la-mm-broadband-modem-mbim-fibocom.lo -MD -MP -MF NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-mbim-fibocom.Tpo -c -o NL668/libmm_plugin_NL668_la-mm-broadband-modem-mbim-fibocom.lo `test -f 'NL668/mm-broadband-modem-mbim-fibocom.c' || echo '$(srcdir)/'`NL668/mm-broadband-modem-mbim-fibocom.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-mbim-fibocom.Tpo NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-mbim-fibocom.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='NL668/mm-broadband-modem-mbim-fibocom.c' object='NL668/libmm_plugin_NL668_la-mm-broadband-modem-mbim-fibocom.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_NL668_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o NL668/libmm_plugin_NL668_la-mm-broadband-modem-mbim-fibocom.lo `test -f 'NL668/mm-broadband-modem-mbim-fibocom.c' || echo '$(srcdir)/'`NL668/mm-broadband-modem-mbim-fibocom.c

altair/libmm_plugin_altair_lte_la-mm-plugin-altair-lte.lo: altair/mm-plugin-altair-lte.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_altair_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT altair/libmm_plugin_altair_lte_la-mm-plugin-altair-lte.lo -MD -MP -MF altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-plugin-altair-lte.Tpo -c -o altair/libmm_plugin_altair_lte_la-mm-plugin-altair-lte.lo `test -f 'altair/mm-plugin-altair-lte.c' || echo '$(srcdir)/'`altair/mm-plugin-altair-lte.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-plugin-altair-lte.Tpo altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-plugin-altair-lte.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='altair/mm-plugin-altair-lte.c' object='altair/libmm_plugin_altair_lte_la-mm-plugin-altair-lte.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_altair_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o altair/libmm_plugin_altair_lte_la-mm-plugin-altair-lte.lo `test -f 'altair/mm-plugin-altair-lte.c' || echo '$(srcdir)/'`altair/mm-plugin-altair-lte.c

altair/libmm_plugin_altair_lte_la-mm-broadband-modem-altair-lte.lo: altair/mm-broadband-modem-altair-lte.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_altair_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT altair/libmm_plugin_altair_lte_la-mm-broadband-modem-altair-lte.lo -MD -MP -MF altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-broadband-modem-altair-lte.Tpo -c -o altair/libmm_plugin_altair_lte_la-mm-broadband-modem-altair-lte.lo `test -f 'altair/mm-broadband-modem-altair-lte.c' || echo '$(srcdir)/'`altair/mm-broadband-modem-altair-lte.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-broadband-modem-altair-lte.Tpo altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-broadband-modem-altair-lte.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='altair/mm-broadband-modem-altair-lte.c' object='altair/libmm_plugin_altair_lte_la-mm-broadband-modem-altair-lte.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_altair_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o altair/libmm_plugin_altair_lte_la-mm-broadband-modem-altair-lte.lo `test -f 'altair/mm-broadband-modem-altair-lte.c' || echo '$(srcdir)/'`altair/mm-broadband-modem-altair-lte.c

altair/libmm_plugin_altair_lte_la-mm-broadband-bearer-altair-lte.lo: altair/mm-broadband-bearer-altair-lte.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_altair_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT altair/libmm_plugin_altair_lte_la-mm-broadband-bearer-altair-lte.lo -MD -MP -MF altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-broadband-bearer-altair-lte.Tpo -c -o altair/libmm_plugin_altair_lte_la-mm-broadband-bearer-altair-lte.lo `test -f 'altair/mm-broadband-bearer-altair-lte.c' || echo '$(srcdir)/'`altair/mm-broadband-bearer-altair-lte.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-broadband-bearer-altair-lte.Tpo altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-broadband-bearer-altair-lte.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='altair/mm-broadband-bearer-altair-lte.c' object='altair/libmm_plugin_altair_lte_la-mm-broadband-bearer-altair-lte.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_altair_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o altair/libmm_plugin_altair_lte_la-mm-broadband-bearer-altair-lte.lo `test -f 'altair/mm-broadband-bearer-altair-lte.c' || echo '$(srcdir)/'`altair/mm-broadband-bearer-altair-lte.c

anydata/libmm_plugin_anydata_la-mm-plugin-anydata.lo: anydata/mm-plugin-anydata.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_anydata_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT anydata/libmm_plugin_anydata_la-mm-plugin-anydata.lo -MD -MP -MF anydata/$(DEPDIR)/libmm_plugin_anydata_la-mm-plugin-anydata.Tpo -c -o anydata/libmm_plugin_anydata_la-mm-plugin-anydata.lo `test -f 'anydata/mm-plugin-anydata.c' || echo '$(srcdir)/'`anydata/mm-plugin-anydata.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) anydata/$(DEPDIR)/libmm_plugin_anydata_la-mm-plugin-anydata.Tpo anydata/$(DEPDIR)/libmm_plugin_anydata_la-mm-plugin-anydata.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='anydata/mm-plugin-anydata.c' object='anydata/libmm_plugin_anydata_la-mm-plugin-anydata.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_anydata_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o anydata/libmm_plugin_anydata_la-mm-plugin-anydata.lo `test -f 'anydata/mm-plugin-anydata.c' || echo '$(srcdir)/'`anydata/mm-plugin-anydata.c

anydata/libmm_plugin_anydata_la-mm-broadband-modem-anydata.lo: anydata/mm-broadband-modem-anydata.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_anydata_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT anydata/libmm_plugin_anydata_la-mm-broadband-modem-anydata.lo -MD -MP -MF anydata/$(DEPDIR)/libmm_plugin_anydata_la-mm-broadband-modem-anydata.Tpo -c -o anydata/libmm_plugin_anydata_la-mm-broadband-modem-anydata.lo `test -f 'anydata/mm-broadband-modem-anydata.c' || echo '$(srcdir)/'`anydata/mm-broadband-modem-anydata.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) anydata/$(DEPDIR)/libmm_plugin_anydata_la-mm-broadband-modem-anydata.Tpo anydata/$(DEPDIR)/libmm_plugin_anydata_la-mm-broadband-modem-anydata.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='anydata/mm-broadband-modem-anydata.c' object='anydata/libmm_plugin_anydata_la-mm-broadband-modem-anydata.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_anydata_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o anydata/libmm_plugin_anydata_la-mm-broadband-modem-anydata.lo `test -f 'anydata/mm-broadband-modem-anydata.c' || echo '$(srcdir)/'`anydata/mm-broadband-modem-anydata.c

broadmobi/libmm_plugin_broadmobi_la-mm-plugin-broadmobi.lo: broadmobi/mm-plugin-broadmobi.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_broadmobi_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT broadmobi/libmm_plugin_broadmobi_la-mm-plugin-broadmobi.lo -MD -MP -MF broadmobi/$(DEPDIR)/libmm_plugin_broadmobi_la-mm-plugin-broadmobi.Tpo -c -o broadmobi/libmm_plugin_broadmobi_la-mm-plugin-broadmobi.lo `test -f 'broadmobi/mm-plugin-broadmobi.c' || echo '$(srcdir)/'`broadmobi/mm-plugin-broadmobi.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) broadmobi/$(DEPDIR)/libmm_plugin_broadmobi_la-mm-plugin-broadmobi.Tpo broadmobi/$(DEPDIR)/libmm_plugin_broadmobi_la-mm-plugin-broadmobi.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='broadmobi/mm-plugin-broadmobi.c' object='broadmobi/libmm_plugin_broadmobi_la-mm-plugin-broadmobi.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_broadmobi_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o broadmobi/libmm_plugin_broadmobi_la-mm-plugin-broadmobi.lo `test -f 'broadmobi/mm-plugin-broadmobi.c' || echo '$(srcdir)/'`broadmobi/mm-plugin-broadmobi.c

cinterion/libmm_plugin_cinterion_la-mm-plugin-cinterion.lo: cinterion/mm-plugin-cinterion.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_cinterion_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT cinterion/libmm_plugin_cinterion_la-mm-plugin-cinterion.lo -MD -MP -MF cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-plugin-cinterion.Tpo -c -o cinterion/libmm_plugin_cinterion_la-mm-plugin-cinterion.lo `test -f 'cinterion/mm-plugin-cinterion.c' || echo '$(srcdir)/'`cinterion/mm-plugin-cinterion.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-plugin-cinterion.Tpo cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-plugin-cinterion.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='cinterion/mm-plugin-cinterion.c' object='cinterion/libmm_plugin_cinterion_la-mm-plugin-cinterion.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_cinterion_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o cinterion/libmm_plugin_cinterion_la-mm-plugin-cinterion.lo `test -f 'cinterion/mm-plugin-cinterion.c' || echo '$(srcdir)/'`cinterion/mm-plugin-cinterion.c

cinterion/libmm_plugin_cinterion_la-mm-shared-cinterion.lo: cinterion/mm-shared-cinterion.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_cinterion_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT cinterion/libmm_plugin_cinterion_la-mm-shared-cinterion.lo -MD -MP -MF cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-shared-cinterion.Tpo -c -o cinterion/libmm_plugin_cinterion_la-mm-shared-cinterion.lo `test -f 'cinterion/mm-shared-cinterion.c' || echo '$(srcdir)/'`cinterion/mm-shared-cinterion.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-shared-cinterion.Tpo cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-shared-cinterion.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='cinterion/mm-shared-cinterion.c' object='cinterion/libmm_plugin_cinterion_la-mm-shared-cinterion.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_cinterion_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o cinterion/libmm_plugin_cinterion_la-mm-shared-cinterion.lo `test -f 'cinterion/mm-shared-cinterion.c' || echo '$(srcdir)/'`cinterion/mm-shared-cinterion.c

cinterion/libmm_plugin_cinterion_la-mm-broadband-modem-cinterion.lo: cinterion/mm-broadband-modem-cinterion.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_cinterion_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT cinterion/libmm_plugin_cinterion_la-mm-broadband-modem-cinterion.lo -MD -MP -MF cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-modem-cinterion.Tpo -c -o cinterion/libmm_plugin_cinterion_la-mm-broadband-modem-cinterion.lo `test -f 'cinterion/mm-broadband-modem-cinterion.c' || echo '$(srcdir)/'`cinterion/mm-broadband-modem-cinterion.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-modem-cinterion.Tpo cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-modem-cinterion.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='cinterion/mm-broadband-modem-cinterion.c' object='cinterion/libmm_plugin_cinterion_la-mm-broadband-modem-cinterion.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_cinterion_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o cinterion/libmm_plugin_cinterion_la-mm-broadband-modem-cinterion.lo `test -f 'cinterion/mm-broadband-modem-cinterion.c' || echo '$(srcdir)/'`cinterion/mm-broadband-modem-cinterion.c

cinterion/libmm_plugin_cinterion_la-mm-broadband-bearer-cinterion.lo: cinterion/mm-broadband-bearer-cinterion.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_cinterion_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT cinterion/libmm_plugin_cinterion_la-mm-broadband-bearer-cinterion.lo -MD -MP -MF cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-bearer-cinterion.Tpo -c -o cinterion/libmm_plugin_cinterion_la-mm-broadband-bearer-cinterion.lo `test -f 'cinterion/mm-broadband-bearer-cinterion.c' || echo '$(srcdir)/'`cinterion/mm-broadband-bearer-cinterion.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-bearer-cinterion.Tpo cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-bearer-cinterion.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='cinterion/mm-broadband-bearer-cinterion.c' object='cinterion/libmm_plugin_cinterion_la-mm-broadband-bearer-cinterion.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_cinterion_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o cinterion/libmm_plugin_cinterion_la-mm-broadband-bearer-cinterion.lo `test -f 'cinterion/mm-broadband-bearer-cinterion.c' || echo '$(srcdir)/'`cinterion/mm-broadband-bearer-cinterion.c

cinterion/libmm_plugin_cinterion_la-mm-broadband-modem-qmi-cinterion.lo: cinterion/mm-broadband-modem-qmi-cinterion.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_cinterion_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT cinterion/libmm_plugin_cinterion_la-mm-broadband-modem-qmi-cinterion.lo -MD -MP -MF cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-modem-qmi-cinterion.Tpo -c -o cinterion/libmm_plugin_cinterion_la-mm-broadband-modem-qmi-cinterion.lo `test -f 'cinterion/mm-broadband-modem-qmi-cinterion.c' || echo '$(srcdir)/'`cinterion/mm-broadband-modem-qmi-cinterion.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-modem-qmi-cinterion.Tpo cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-modem-qmi-cinterion.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='cinterion/mm-broadband-modem-qmi-cinterion.c' object='cinterion/libmm_plugin_cinterion_la-mm-broadband-modem-qmi-cinterion.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_cinterion_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o cinterion/libmm_plugin_cinterion_la-mm-broadband-modem-qmi-cinterion.lo `test -f 'cinterion/mm-broadband-modem-qmi-cinterion.c' || echo '$(srcdir)/'`cinterion/mm-broadband-modem-qmi-cinterion.c

dell/libmm_plugin_dell_la-mm-plugin-dell.lo: dell/mm-plugin-dell.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_dell_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT dell/libmm_plugin_dell_la-mm-plugin-dell.lo -MD -MP -MF dell/$(DEPDIR)/libmm_plugin_dell_la-mm-plugin-dell.Tpo -c -o dell/libmm_plugin_dell_la-mm-plugin-dell.lo `test -f 'dell/mm-plugin-dell.c' || echo '$(srcdir)/'`dell/mm-plugin-dell.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) dell/$(DEPDIR)/libmm_plugin_dell_la-mm-plugin-dell.Tpo dell/$(DEPDIR)/libmm_plugin_dell_la-mm-plugin-dell.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='dell/mm-plugin-dell.c' object='dell/libmm_plugin_dell_la-mm-plugin-dell.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_dell_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o dell/libmm_plugin_dell_la-mm-plugin-dell.lo `test -f 'dell/mm-plugin-dell.c' || echo '$(srcdir)/'`dell/mm-plugin-dell.c

dlink/libmm_plugin_dlink_la-mm-plugin-dlink.lo: dlink/mm-plugin-dlink.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_dlink_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT dlink/libmm_plugin_dlink_la-mm-plugin-dlink.lo -MD -MP -MF dlink/$(DEPDIR)/libmm_plugin_dlink_la-mm-plugin-dlink.Tpo -c -o dlink/libmm_plugin_dlink_la-mm-plugin-dlink.lo `test -f 'dlink/mm-plugin-dlink.c' || echo '$(srcdir)/'`dlink/mm-plugin-dlink.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) dlink/$(DEPDIR)/libmm_plugin_dlink_la-mm-plugin-dlink.Tpo dlink/$(DEPDIR)/libmm_plugin_dlink_la-mm-plugin-dlink.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='dlink/mm-plugin-dlink.c' object='dlink/libmm_plugin_dlink_la-mm-plugin-dlink.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_dlink_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o dlink/libmm_plugin_dlink_la-mm-plugin-dlink.lo `test -f 'dlink/mm-plugin-dlink.c' || echo '$(srcdir)/'`dlink/mm-plugin-dlink.c

mbm/libmm_plugin_ericsson_mbm_la-mm-broadband-modem-mbm.lo: mbm/mm-broadband-modem-mbm.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ericsson_mbm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mbm/libmm_plugin_ericsson_mbm_la-mm-broadband-modem-mbm.lo -MD -MP -MF mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-broadband-modem-mbm.Tpo -c -o mbm/libmm_plugin_ericsson_mbm_la-mm-broadband-modem-mbm.lo `test -f 'mbm/mm-broadband-modem-mbm.c' || echo '$(srcdir)/'`mbm/mm-broadband-modem-mbm.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-broadband-modem-mbm.Tpo mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-broadband-modem-mbm.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mbm/mm-broadband-modem-mbm.c' object='mbm/libmm_plugin_ericsson_mbm_la-mm-broadband-modem-mbm.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ericsson_mbm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mbm/libmm_plugin_ericsson_mbm_la-mm-broadband-modem-mbm.lo `test -f 'mbm/mm-broadband-modem-mbm.c' || echo '$(srcdir)/'`mbm/mm-broadband-modem-mbm.c

mbm/libmm_plugin_ericsson_mbm_la-mm-broadband-bearer-mbm.lo: mbm/mm-broadband-bearer-mbm.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ericsson_mbm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mbm/libmm_plugin_ericsson_mbm_la-mm-broadband-bearer-mbm.lo -MD -MP -MF mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-broadband-bearer-mbm.Tpo -c -o mbm/libmm_plugin_ericsson_mbm_la-mm-broadband-bearer-mbm.lo `test -f 'mbm/mm-broadband-bearer-mbm.c' || echo '$(srcdir)/'`mbm/mm-broadband-bearer-mbm.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-broadband-bearer-mbm.Tpo mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-broadband-bearer-mbm.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mbm/mm-broadband-bearer-mbm.c' object='mbm/libmm_plugin_ericsson_mbm_la-mm-broadband-bearer-mbm.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ericsson_mbm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mbm/libmm_plugin_ericsson_mbm_la-mm-broadband-bearer-mbm.lo `test -f 'mbm/mm-broadband-bearer-mbm.c' || echo '$(srcdir)/'`mbm/mm-broadband-bearer-mbm.c

mbm/libmm_plugin_ericsson_mbm_la-mm-sim-mbm.lo: mbm/mm-sim-mbm.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ericsson_mbm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mbm/libmm_plugin_ericsson_mbm_la-mm-sim-mbm.lo -MD -MP -MF mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-sim-mbm.Tpo -c -o mbm/libmm_plugin_ericsson_mbm_la-mm-sim-mbm.lo `test -f 'mbm/mm-sim-mbm.c' || echo '$(srcdir)/'`mbm/mm-sim-mbm.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-sim-mbm.Tpo mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-sim-mbm.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mbm/mm-sim-mbm.c' object='mbm/libmm_plugin_ericsson_mbm_la-mm-sim-mbm.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ericsson_mbm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mbm/libmm_plugin_ericsson_mbm_la-mm-sim-mbm.lo `test -f 'mbm/mm-sim-mbm.c' || echo '$(srcdir)/'`mbm/mm-sim-mbm.c

mbm/libmm_plugin_ericsson_mbm_la-mm-plugin-mbm.lo: mbm/mm-plugin-mbm.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ericsson_mbm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mbm/libmm_plugin_ericsson_mbm_la-mm-plugin-mbm.lo -MD -MP -MF mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-plugin-mbm.Tpo -c -o mbm/libmm_plugin_ericsson_mbm_la-mm-plugin-mbm.lo `test -f 'mbm/mm-plugin-mbm.c' || echo '$(srcdir)/'`mbm/mm-plugin-mbm.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-plugin-mbm.Tpo mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-plugin-mbm.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mbm/mm-plugin-mbm.c' object='mbm/libmm_plugin_ericsson_mbm_la-mm-plugin-mbm.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ericsson_mbm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mbm/libmm_plugin_ericsson_mbm_la-mm-plugin-mbm.lo `test -f 'mbm/mm-plugin-mbm.c' || echo '$(srcdir)/'`mbm/mm-plugin-mbm.c

foxconn/libmm_plugin_foxconn_la-mm-plugin-foxconn.lo: foxconn/mm-plugin-foxconn.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_foxconn_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT foxconn/libmm_plugin_foxconn_la-mm-plugin-foxconn.lo -MD -MP -MF foxconn/$(DEPDIR)/libmm_plugin_foxconn_la-mm-plugin-foxconn.Tpo -c -o foxconn/libmm_plugin_foxconn_la-mm-plugin-foxconn.lo `test -f 'foxconn/mm-plugin-foxconn.c' || echo '$(srcdir)/'`foxconn/mm-plugin-foxconn.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) foxconn/$(DEPDIR)/libmm_plugin_foxconn_la-mm-plugin-foxconn.Tpo foxconn/$(DEPDIR)/libmm_plugin_foxconn_la-mm-plugin-foxconn.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='foxconn/mm-plugin-foxconn.c' object='foxconn/libmm_plugin_foxconn_la-mm-plugin-foxconn.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_foxconn_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o foxconn/libmm_plugin_foxconn_la-mm-plugin-foxconn.lo `test -f 'foxconn/mm-plugin-foxconn.c' || echo '$(srcdir)/'`foxconn/mm-plugin-foxconn.c

generic/libmm_plugin_generic_la-mm-plugin-generic.lo: generic/mm-plugin-generic.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_generic_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT generic/libmm_plugin_generic_la-mm-plugin-generic.lo -MD -MP -MF generic/$(DEPDIR)/libmm_plugin_generic_la-mm-plugin-generic.Tpo -c -o generic/libmm_plugin_generic_la-mm-plugin-generic.lo `test -f 'generic/mm-plugin-generic.c' || echo '$(srcdir)/'`generic/mm-plugin-generic.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) generic/$(DEPDIR)/libmm_plugin_generic_la-mm-plugin-generic.Tpo generic/$(DEPDIR)/libmm_plugin_generic_la-mm-plugin-generic.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='generic/mm-plugin-generic.c' object='generic/libmm_plugin_generic_la-mm-plugin-generic.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_generic_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o generic/libmm_plugin_generic_la-mm-plugin-generic.lo `test -f 'generic/mm-plugin-generic.c' || echo '$(srcdir)/'`generic/mm-plugin-generic.c

haier/libmm_plugin_haier_la-mm-plugin-haier.lo: haier/mm-plugin-haier.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_haier_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT haier/libmm_plugin_haier_la-mm-plugin-haier.lo -MD -MP -MF haier/$(DEPDIR)/libmm_plugin_haier_la-mm-plugin-haier.Tpo -c -o haier/libmm_plugin_haier_la-mm-plugin-haier.lo `test -f 'haier/mm-plugin-haier.c' || echo '$(srcdir)/'`haier/mm-plugin-haier.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) haier/$(DEPDIR)/libmm_plugin_haier_la-mm-plugin-haier.Tpo haier/$(DEPDIR)/libmm_plugin_haier_la-mm-plugin-haier.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='haier/mm-plugin-haier.c' object='haier/libmm_plugin_haier_la-mm-plugin-haier.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_haier_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o haier/libmm_plugin_haier_la-mm-plugin-haier.lo `test -f 'haier/mm-plugin-haier.c' || echo '$(srcdir)/'`haier/mm-plugin-haier.c

huawei/libmm_plugin_huawei_la-mm-plugin-huawei.lo: huawei/mm-plugin-huawei.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_huawei_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT huawei/libmm_plugin_huawei_la-mm-plugin-huawei.lo -MD -MP -MF huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-plugin-huawei.Tpo -c -o huawei/libmm_plugin_huawei_la-mm-plugin-huawei.lo `test -f 'huawei/mm-plugin-huawei.c' || echo '$(srcdir)/'`huawei/mm-plugin-huawei.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-plugin-huawei.Tpo huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-plugin-huawei.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='huawei/mm-plugin-huawei.c' object='huawei/libmm_plugin_huawei_la-mm-plugin-huawei.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_huawei_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o huawei/libmm_plugin_huawei_la-mm-plugin-huawei.lo `test -f 'huawei/mm-plugin-huawei.c' || echo '$(srcdir)/'`huawei/mm-plugin-huawei.c

huawei/libmm_plugin_huawei_la-mm-sim-huawei.lo: huawei/mm-sim-huawei.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_huawei_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT huawei/libmm_plugin_huawei_la-mm-sim-huawei.lo -MD -MP -MF huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-sim-huawei.Tpo -c -o huawei/libmm_plugin_huawei_la-mm-sim-huawei.lo `test -f 'huawei/mm-sim-huawei.c' || echo '$(srcdir)/'`huawei/mm-sim-huawei.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-sim-huawei.Tpo huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-sim-huawei.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='huawei/mm-sim-huawei.c' object='huawei/libmm_plugin_huawei_la-mm-sim-huawei.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_huawei_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o huawei/libmm_plugin_huawei_la-mm-sim-huawei.lo `test -f 'huawei/mm-sim-huawei.c' || echo '$(srcdir)/'`huawei/mm-sim-huawei.c

huawei/libmm_plugin_huawei_la-mm-broadband-modem-huawei.lo: huawei/mm-broadband-modem-huawei.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_huawei_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT huawei/libmm_plugin_huawei_la-mm-broadband-modem-huawei.lo -MD -MP -MF huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-broadband-modem-huawei.Tpo -c -o huawei/libmm_plugin_huawei_la-mm-broadband-modem-huawei.lo `test -f 'huawei/mm-broadband-modem-huawei.c' || echo '$(srcdir)/'`huawei/mm-broadband-modem-huawei.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-broadband-modem-huawei.Tpo huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-broadband-modem-huawei.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='huawei/mm-broadband-modem-huawei.c' object='huawei/libmm_plugin_huawei_la-mm-broadband-modem-huawei.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_huawei_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o huawei/libmm_plugin_huawei_la-mm-broadband-modem-huawei.lo `test -f 'huawei/mm-broadband-modem-huawei.c' || echo '$(srcdir)/'`huawei/mm-broadband-modem-huawei.c

huawei/libmm_plugin_huawei_la-mm-broadband-bearer-huawei.lo: huawei/mm-broadband-bearer-huawei.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_huawei_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT huawei/libmm_plugin_huawei_la-mm-broadband-bearer-huawei.lo -MD -MP -MF huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-broadband-bearer-huawei.Tpo -c -o huawei/libmm_plugin_huawei_la-mm-broadband-bearer-huawei.lo `test -f 'huawei/mm-broadband-bearer-huawei.c' || echo '$(srcdir)/'`huawei/mm-broadband-bearer-huawei.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-broadband-bearer-huawei.Tpo huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-broadband-bearer-huawei.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='huawei/mm-broadband-bearer-huawei.c' object='huawei/libmm_plugin_huawei_la-mm-broadband-bearer-huawei.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_huawei_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o huawei/libmm_plugin_huawei_la-mm-broadband-bearer-huawei.lo `test -f 'huawei/mm-broadband-bearer-huawei.c' || echo '$(srcdir)/'`huawei/mm-broadband-bearer-huawei.c

iridium/libmm_plugin_iridium_la-mm-plugin-iridium.lo: iridium/mm-plugin-iridium.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_iridium_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT iridium/libmm_plugin_iridium_la-mm-plugin-iridium.lo -MD -MP -MF iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-plugin-iridium.Tpo -c -o iridium/libmm_plugin_iridium_la-mm-plugin-iridium.lo `test -f 'iridium/mm-plugin-iridium.c' || echo '$(srcdir)/'`iridium/mm-plugin-iridium.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-plugin-iridium.Tpo iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-plugin-iridium.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='iridium/mm-plugin-iridium.c' object='iridium/libmm_plugin_iridium_la-mm-plugin-iridium.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_iridium_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o iridium/libmm_plugin_iridium_la-mm-plugin-iridium.lo `test -f 'iridium/mm-plugin-iridium.c' || echo '$(srcdir)/'`iridium/mm-plugin-iridium.c

iridium/libmm_plugin_iridium_la-mm-broadband-modem-iridium.lo: iridium/mm-broadband-modem-iridium.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_iridium_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT iridium/libmm_plugin_iridium_la-mm-broadband-modem-iridium.lo -MD -MP -MF iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-broadband-modem-iridium.Tpo -c -o iridium/libmm_plugin_iridium_la-mm-broadband-modem-iridium.lo `test -f 'iridium/mm-broadband-modem-iridium.c' || echo '$(srcdir)/'`iridium/mm-broadband-modem-iridium.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-broadband-modem-iridium.Tpo iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-broadband-modem-iridium.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='iridium/mm-broadband-modem-iridium.c' object='iridium/libmm_plugin_iridium_la-mm-broadband-modem-iridium.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_iridium_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o iridium/libmm_plugin_iridium_la-mm-broadband-modem-iridium.lo `test -f 'iridium/mm-broadband-modem-iridium.c' || echo '$(srcdir)/'`iridium/mm-broadband-modem-iridium.c

iridium/libmm_plugin_iridium_la-mm-bearer-iridium.lo: iridium/mm-bearer-iridium.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_iridium_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT iridium/libmm_plugin_iridium_la-mm-bearer-iridium.lo -MD -MP -MF iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-bearer-iridium.Tpo -c -o iridium/libmm_plugin_iridium_la-mm-bearer-iridium.lo `test -f 'iridium/mm-bearer-iridium.c' || echo '$(srcdir)/'`iridium/mm-bearer-iridium.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-bearer-iridium.Tpo iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-bearer-iridium.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='iridium/mm-bearer-iridium.c' object='iridium/libmm_plugin_iridium_la-mm-bearer-iridium.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_iridium_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o iridium/libmm_plugin_iridium_la-mm-bearer-iridium.lo `test -f 'iridium/mm-bearer-iridium.c' || echo '$(srcdir)/'`iridium/mm-bearer-iridium.c

iridium/libmm_plugin_iridium_la-mm-sim-iridium.lo: iridium/mm-sim-iridium.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_iridium_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT iridium/libmm_plugin_iridium_la-mm-sim-iridium.lo -MD -MP -MF iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-sim-iridium.Tpo -c -o iridium/libmm_plugin_iridium_la-mm-sim-iridium.lo `test -f 'iridium/mm-sim-iridium.c' || echo '$(srcdir)/'`iridium/mm-sim-iridium.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-sim-iridium.Tpo iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-sim-iridium.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='iridium/mm-sim-iridium.c' object='iridium/libmm_plugin_iridium_la-mm-sim-iridium.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_iridium_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o iridium/libmm_plugin_iridium_la-mm-sim-iridium.lo `test -f 'iridium/mm-sim-iridium.c' || echo '$(srcdir)/'`iridium/mm-sim-iridium.c

linktop/libmm_plugin_linktop_la-mm-plugin-linktop.lo: linktop/mm-plugin-linktop.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_linktop_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT linktop/libmm_plugin_linktop_la-mm-plugin-linktop.lo -MD -MP -MF linktop/$(DEPDIR)/libmm_plugin_linktop_la-mm-plugin-linktop.Tpo -c -o linktop/libmm_plugin_linktop_la-mm-plugin-linktop.lo `test -f 'linktop/mm-plugin-linktop.c' || echo '$(srcdir)/'`linktop/mm-plugin-linktop.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) linktop/$(DEPDIR)/libmm_plugin_linktop_la-mm-plugin-linktop.Tpo linktop/$(DEPDIR)/libmm_plugin_linktop_la-mm-plugin-linktop.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='linktop/mm-plugin-linktop.c' object='linktop/libmm_plugin_linktop_la-mm-plugin-linktop.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_linktop_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o linktop/libmm_plugin_linktop_la-mm-plugin-linktop.lo `test -f 'linktop/mm-plugin-linktop.c' || echo '$(srcdir)/'`linktop/mm-plugin-linktop.c

linktop/libmm_plugin_linktop_la-mm-broadband-modem-linktop.lo: linktop/mm-broadband-modem-linktop.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_linktop_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT linktop/libmm_plugin_linktop_la-mm-broadband-modem-linktop.lo -MD -MP -MF linktop/$(DEPDIR)/libmm_plugin_linktop_la-mm-broadband-modem-linktop.Tpo -c -o linktop/libmm_plugin_linktop_la-mm-broadband-modem-linktop.lo `test -f 'linktop/mm-broadband-modem-linktop.c' || echo '$(srcdir)/'`linktop/mm-broadband-modem-linktop.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) linktop/$(DEPDIR)/libmm_plugin_linktop_la-mm-broadband-modem-linktop.Tpo linktop/$(DEPDIR)/libmm_plugin_linktop_la-mm-broadband-modem-linktop.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='linktop/mm-broadband-modem-linktop.c' object='linktop/libmm_plugin_linktop_la-mm-broadband-modem-linktop.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_linktop_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o linktop/libmm_plugin_linktop_la-mm-broadband-modem-linktop.lo `test -f 'linktop/mm-broadband-modem-linktop.c' || echo '$(srcdir)/'`linktop/mm-broadband-modem-linktop.c

longcheer/libmm_plugin_longcheer_la-mm-plugin-longcheer.lo: longcheer/mm-plugin-longcheer.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_longcheer_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT longcheer/libmm_plugin_longcheer_la-mm-plugin-longcheer.lo -MD -MP -MF longcheer/$(DEPDIR)/libmm_plugin_longcheer_la-mm-plugin-longcheer.Tpo -c -o longcheer/libmm_plugin_longcheer_la-mm-plugin-longcheer.lo `test -f 'longcheer/mm-plugin-longcheer.c' || echo '$(srcdir)/'`longcheer/mm-plugin-longcheer.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) longcheer/$(DEPDIR)/libmm_plugin_longcheer_la-mm-plugin-longcheer.Tpo longcheer/$(DEPDIR)/libmm_plugin_longcheer_la-mm-plugin-longcheer.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='longcheer/mm-plugin-longcheer.c' object='longcheer/libmm_plugin_longcheer_la-mm-plugin-longcheer.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_longcheer_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o longcheer/libmm_plugin_longcheer_la-mm-plugin-longcheer.lo `test -f 'longcheer/mm-plugin-longcheer.c' || echo '$(srcdir)/'`longcheer/mm-plugin-longcheer.c

longcheer/libmm_plugin_longcheer_la-mm-broadband-modem-longcheer.lo: longcheer/mm-broadband-modem-longcheer.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_longcheer_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT longcheer/libmm_plugin_longcheer_la-mm-broadband-modem-longcheer.lo -MD -MP -MF longcheer/$(DEPDIR)/libmm_plugin_longcheer_la-mm-broadband-modem-longcheer.Tpo -c -o longcheer/libmm_plugin_longcheer_la-mm-broadband-modem-longcheer.lo `test -f 'longcheer/mm-broadband-modem-longcheer.c' || echo '$(srcdir)/'`longcheer/mm-broadband-modem-longcheer.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) longcheer/$(DEPDIR)/libmm_plugin_longcheer_la-mm-broadband-modem-longcheer.Tpo longcheer/$(DEPDIR)/libmm_plugin_longcheer_la-mm-broadband-modem-longcheer.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='longcheer/mm-broadband-modem-longcheer.c' object='longcheer/libmm_plugin_longcheer_la-mm-broadband-modem-longcheer.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_longcheer_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o longcheer/libmm_plugin_longcheer_la-mm-broadband-modem-longcheer.lo `test -f 'longcheer/mm-broadband-modem-longcheer.c' || echo '$(srcdir)/'`longcheer/mm-broadband-modem-longcheer.c

me3630/libmm_plugin_me3630_la-mm-plugin-me3630.lo: me3630/mm-plugin-me3630.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_me3630_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT me3630/libmm_plugin_me3630_la-mm-plugin-me3630.lo -MD -MP -MF me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-plugin-me3630.Tpo -c -o me3630/libmm_plugin_me3630_la-mm-plugin-me3630.lo `test -f 'me3630/mm-plugin-me3630.c' || echo '$(srcdir)/'`me3630/mm-plugin-me3630.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-plugin-me3630.Tpo me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-plugin-me3630.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='me3630/mm-plugin-me3630.c' object='me3630/libmm_plugin_me3630_la-mm-plugin-me3630.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_me3630_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o me3630/libmm_plugin_me3630_la-mm-plugin-me3630.lo `test -f 'me3630/mm-plugin-me3630.c' || echo '$(srcdir)/'`me3630/mm-plugin-me3630.c

me3630/libmm_plugin_me3630_la-mm-common-me3630.lo: me3630/mm-common-me3630.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_me3630_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT me3630/libmm_plugin_me3630_la-mm-common-me3630.lo -MD -MP -MF me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-common-me3630.Tpo -c -o me3630/libmm_plugin_me3630_la-mm-common-me3630.lo `test -f 'me3630/mm-common-me3630.c' || echo '$(srcdir)/'`me3630/mm-common-me3630.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-common-me3630.Tpo me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-common-me3630.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='me3630/mm-common-me3630.c' object='me3630/libmm_plugin_me3630_la-mm-common-me3630.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_me3630_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o me3630/libmm_plugin_me3630_la-mm-common-me3630.lo `test -f 'me3630/mm-common-me3630.c' || echo '$(srcdir)/'`me3630/mm-common-me3630.c

me3630/libmm_plugin_me3630_la-mm-broadband-modem-me3630.lo: me3630/mm-broadband-modem-me3630.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_me3630_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT me3630/libmm_plugin_me3630_la-mm-broadband-modem-me3630.lo -MD -MP -MF me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-broadband-modem-me3630.Tpo -c -o me3630/libmm_plugin_me3630_la-mm-broadband-modem-me3630.lo `test -f 'me3630/mm-broadband-modem-me3630.c' || echo '$(srcdir)/'`me3630/mm-broadband-modem-me3630.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-broadband-modem-me3630.Tpo me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-broadband-modem-me3630.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='me3630/mm-broadband-modem-me3630.c' object='me3630/libmm_plugin_me3630_la-mm-broadband-modem-me3630.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_me3630_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o me3630/libmm_plugin_me3630_la-mm-broadband-modem-me3630.lo `test -f 'me3630/mm-broadband-modem-me3630.c' || echo '$(srcdir)/'`me3630/mm-broadband-modem-me3630.c

me3630/libmm_plugin_me3630_la-mm-broadband-bearer-me3630.lo: me3630/mm-broadband-bearer-me3630.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_me3630_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT me3630/libmm_plugin_me3630_la-mm-broadband-bearer-me3630.lo -MD -MP -MF me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-broadband-bearer-me3630.Tpo -c -o me3630/libmm_plugin_me3630_la-mm-broadband-bearer-me3630.lo `test -f 'me3630/mm-broadband-bearer-me3630.c' || echo '$(srcdir)/'`me3630/mm-broadband-bearer-me3630.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-broadband-bearer-me3630.Tpo me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-broadband-bearer-me3630.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='me3630/mm-broadband-bearer-me3630.c' object='me3630/libmm_plugin_me3630_la-mm-broadband-bearer-me3630.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_me3630_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o me3630/libmm_plugin_me3630_la-mm-broadband-bearer-me3630.lo `test -f 'me3630/mm-broadband-bearer-me3630.c' || echo '$(srcdir)/'`me3630/mm-broadband-bearer-me3630.c

motorola/libmm_plugin_motorola_la-mm-plugin-motorola.lo: motorola/mm-plugin-motorola.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_motorola_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT motorola/libmm_plugin_motorola_la-mm-plugin-motorola.lo -MD -MP -MF motorola/$(DEPDIR)/libmm_plugin_motorola_la-mm-plugin-motorola.Tpo -c -o motorola/libmm_plugin_motorola_la-mm-plugin-motorola.lo `test -f 'motorola/mm-plugin-motorola.c' || echo '$(srcdir)/'`motorola/mm-plugin-motorola.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) motorola/$(DEPDIR)/libmm_plugin_motorola_la-mm-plugin-motorola.Tpo motorola/$(DEPDIR)/libmm_plugin_motorola_la-mm-plugin-motorola.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='motorola/mm-plugin-motorola.c' object='motorola/libmm_plugin_motorola_la-mm-plugin-motorola.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_motorola_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o motorola/libmm_plugin_motorola_la-mm-plugin-motorola.lo `test -f 'motorola/mm-plugin-motorola.c' || echo '$(srcdir)/'`motorola/mm-plugin-motorola.c

motorola/libmm_plugin_motorola_la-mm-broadband-modem-motorola.lo: motorola/mm-broadband-modem-motorola.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_motorola_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT motorola/libmm_plugin_motorola_la-mm-broadband-modem-motorola.lo -MD -MP -MF motorola/$(DEPDIR)/libmm_plugin_motorola_la-mm-broadband-modem-motorola.Tpo -c -o motorola/libmm_plugin_motorola_la-mm-broadband-modem-motorola.lo `test -f 'motorola/mm-broadband-modem-motorola.c' || echo '$(srcdir)/'`motorola/mm-broadband-modem-motorola.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) motorola/$(DEPDIR)/libmm_plugin_motorola_la-mm-broadband-modem-motorola.Tpo motorola/$(DEPDIR)/libmm_plugin_motorola_la-mm-broadband-modem-motorola.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='motorola/mm-broadband-modem-motorola.c' object='motorola/libmm_plugin_motorola_la-mm-broadband-modem-motorola.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_motorola_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o motorola/libmm_plugin_motorola_la-mm-broadband-modem-motorola.lo `test -f 'motorola/mm-broadband-modem-motorola.c' || echo '$(srcdir)/'`motorola/mm-broadband-modem-motorola.c

mtk/libmm_plugin_mtk_la-mm-plugin-mtk.lo: mtk/mm-plugin-mtk.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_mtk_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mtk/libmm_plugin_mtk_la-mm-plugin-mtk.lo -MD -MP -MF mtk/$(DEPDIR)/libmm_plugin_mtk_la-mm-plugin-mtk.Tpo -c -o mtk/libmm_plugin_mtk_la-mm-plugin-mtk.lo `test -f 'mtk/mm-plugin-mtk.c' || echo '$(srcdir)/'`mtk/mm-plugin-mtk.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) mtk/$(DEPDIR)/libmm_plugin_mtk_la-mm-plugin-mtk.Tpo mtk/$(DEPDIR)/libmm_plugin_mtk_la-mm-plugin-mtk.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mtk/mm-plugin-mtk.c' object='mtk/libmm_plugin_mtk_la-mm-plugin-mtk.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_mtk_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mtk/libmm_plugin_mtk_la-mm-plugin-mtk.lo `test -f 'mtk/mm-plugin-mtk.c' || echo '$(srcdir)/'`mtk/mm-plugin-mtk.c

mtk/libmm_plugin_mtk_la-mm-broadband-modem-mtk.lo: mtk/mm-broadband-modem-mtk.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_mtk_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mtk/libmm_plugin_mtk_la-mm-broadband-modem-mtk.lo -MD -MP -MF mtk/$(DEPDIR)/libmm_plugin_mtk_la-mm-broadband-modem-mtk.Tpo -c -o mtk/libmm_plugin_mtk_la-mm-broadband-modem-mtk.lo `test -f 'mtk/mm-broadband-modem-mtk.c' || echo '$(srcdir)/'`mtk/mm-broadband-modem-mtk.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) mtk/$(DEPDIR)/libmm_plugin_mtk_la-mm-broadband-modem-mtk.Tpo mtk/$(DEPDIR)/libmm_plugin_mtk_la-mm-broadband-modem-mtk.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mtk/mm-broadband-modem-mtk.c' object='mtk/libmm_plugin_mtk_la-mm-broadband-modem-mtk.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_mtk_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mtk/libmm_plugin_mtk_la-mm-broadband-modem-mtk.lo `test -f 'mtk/mm-broadband-modem-mtk.c' || echo '$(srcdir)/'`mtk/mm-broadband-modem-mtk.c

nokia/libmm_plugin_nokia_icera_la-mm-plugin-nokia-icera.lo: nokia/mm-plugin-nokia-icera.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_nokia_icera_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT nokia/libmm_plugin_nokia_icera_la-mm-plugin-nokia-icera.lo -MD -MP -MF nokia/$(DEPDIR)/libmm_plugin_nokia_icera_la-mm-plugin-nokia-icera.Tpo -c -o nokia/libmm_plugin_nokia_icera_la-mm-plugin-nokia-icera.lo `test -f 'nokia/mm-plugin-nokia-icera.c' || echo '$(srcdir)/'`nokia/mm-plugin-nokia-icera.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) nokia/$(DEPDIR)/libmm_plugin_nokia_icera_la-mm-plugin-nokia-icera.Tpo nokia/$(DEPDIR)/libmm_plugin_nokia_icera_la-mm-plugin-nokia-icera.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='nokia/mm-plugin-nokia-icera.c' object='nokia/libmm_plugin_nokia_icera_la-mm-plugin-nokia-icera.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_nokia_icera_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o nokia/libmm_plugin_nokia_icera_la-mm-plugin-nokia-icera.lo `test -f 'nokia/mm-plugin-nokia-icera.c' || echo '$(srcdir)/'`nokia/mm-plugin-nokia-icera.c

nokia/libmm_plugin_nokia_la-mm-plugin-nokia.lo: nokia/mm-plugin-nokia.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_nokia_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT nokia/libmm_plugin_nokia_la-mm-plugin-nokia.lo -MD -MP -MF nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-plugin-nokia.Tpo -c -o nokia/libmm_plugin_nokia_la-mm-plugin-nokia.lo `test -f 'nokia/mm-plugin-nokia.c' || echo '$(srcdir)/'`nokia/mm-plugin-nokia.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-plugin-nokia.Tpo nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-plugin-nokia.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='nokia/mm-plugin-nokia.c' object='nokia/libmm_plugin_nokia_la-mm-plugin-nokia.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_nokia_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o nokia/libmm_plugin_nokia_la-mm-plugin-nokia.lo `test -f 'nokia/mm-plugin-nokia.c' || echo '$(srcdir)/'`nokia/mm-plugin-nokia.c

nokia/libmm_plugin_nokia_la-mm-sim-nokia.lo: nokia/mm-sim-nokia.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_nokia_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT nokia/libmm_plugin_nokia_la-mm-sim-nokia.lo -MD -MP -MF nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-sim-nokia.Tpo -c -o nokia/libmm_plugin_nokia_la-mm-sim-nokia.lo `test -f 'nokia/mm-sim-nokia.c' || echo '$(srcdir)/'`nokia/mm-sim-nokia.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-sim-nokia.Tpo nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-sim-nokia.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='nokia/mm-sim-nokia.c' object='nokia/libmm_plugin_nokia_la-mm-sim-nokia.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_nokia_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o nokia/libmm_plugin_nokia_la-mm-sim-nokia.lo `test -f 'nokia/mm-sim-nokia.c' || echo '$(srcdir)/'`nokia/mm-sim-nokia.c

nokia/libmm_plugin_nokia_la-mm-broadband-modem-nokia.lo: nokia/mm-broadband-modem-nokia.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_nokia_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT nokia/libmm_plugin_nokia_la-mm-broadband-modem-nokia.lo -MD -MP -MF nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-broadband-modem-nokia.Tpo -c -o nokia/libmm_plugin_nokia_la-mm-broadband-modem-nokia.lo `test -f 'nokia/mm-broadband-modem-nokia.c' || echo '$(srcdir)/'`nokia/mm-broadband-modem-nokia.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-broadband-modem-nokia.Tpo nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-broadband-modem-nokia.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='nokia/mm-broadband-modem-nokia.c' object='nokia/libmm_plugin_nokia_la-mm-broadband-modem-nokia.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_nokia_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o nokia/libmm_plugin_nokia_la-mm-broadband-modem-nokia.lo `test -f 'nokia/mm-broadband-modem-nokia.c' || echo '$(srcdir)/'`nokia/mm-broadband-modem-nokia.c

novatel/libmm_plugin_novatel_lte_la-mm-plugin-novatel-lte.lo: novatel/mm-plugin-novatel-lte.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_novatel_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT novatel/libmm_plugin_novatel_lte_la-mm-plugin-novatel-lte.lo -MD -MP -MF novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-plugin-novatel-lte.Tpo -c -o novatel/libmm_plugin_novatel_lte_la-mm-plugin-novatel-lte.lo `test -f 'novatel/mm-plugin-novatel-lte.c' || echo '$(srcdir)/'`novatel/mm-plugin-novatel-lte.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-plugin-novatel-lte.Tpo novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-plugin-novatel-lte.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='novatel/mm-plugin-novatel-lte.c' object='novatel/libmm_plugin_novatel_lte_la-mm-plugin-novatel-lte.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_novatel_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o novatel/libmm_plugin_novatel_lte_la-mm-plugin-novatel-lte.lo `test -f 'novatel/mm-plugin-novatel-lte.c' || echo '$(srcdir)/'`novatel/mm-plugin-novatel-lte.c

novatel/libmm_plugin_novatel_lte_la-mm-broadband-modem-novatel-lte.lo: novatel/mm-broadband-modem-novatel-lte.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_novatel_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT novatel/libmm_plugin_novatel_lte_la-mm-broadband-modem-novatel-lte.lo -MD -MP -MF novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-broadband-modem-novatel-lte.Tpo -c -o novatel/libmm_plugin_novatel_lte_la-mm-broadband-modem-novatel-lte.lo `test -f 'novatel/mm-broadband-modem-novatel-lte.c' || echo '$(srcdir)/'`novatel/mm-broadband-modem-novatel-lte.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-broadband-modem-novatel-lte.Tpo novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-broadband-modem-novatel-lte.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='novatel/mm-broadband-modem-novatel-lte.c' object='novatel/libmm_plugin_novatel_lte_la-mm-broadband-modem-novatel-lte.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_novatel_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o novatel/libmm_plugin_novatel_lte_la-mm-broadband-modem-novatel-lte.lo `test -f 'novatel/mm-broadband-modem-novatel-lte.c' || echo '$(srcdir)/'`novatel/mm-broadband-modem-novatel-lte.c

novatel/libmm_plugin_novatel_lte_la-mm-broadband-bearer-novatel-lte.lo: novatel/mm-broadband-bearer-novatel-lte.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_novatel_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT novatel/libmm_plugin_novatel_lte_la-mm-broadband-bearer-novatel-lte.lo -MD -MP -MF novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-broadband-bearer-novatel-lte.Tpo -c -o novatel/libmm_plugin_novatel_lte_la-mm-broadband-bearer-novatel-lte.lo `test -f 'novatel/mm-broadband-bearer-novatel-lte.c' || echo '$(srcdir)/'`novatel/mm-broadband-bearer-novatel-lte.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-broadband-bearer-novatel-lte.Tpo novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-broadband-bearer-novatel-lte.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='novatel/mm-broadband-bearer-novatel-lte.c' object='novatel/libmm_plugin_novatel_lte_la-mm-broadband-bearer-novatel-lte.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_novatel_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o novatel/libmm_plugin_novatel_lte_la-mm-broadband-bearer-novatel-lte.lo `test -f 'novatel/mm-broadband-bearer-novatel-lte.c' || echo '$(srcdir)/'`novatel/mm-broadband-bearer-novatel-lte.c

novatel/libmm_plugin_novatel_lte_la-mm-sim-novatel-lte.lo: novatel/mm-sim-novatel-lte.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_novatel_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT novatel/libmm_plugin_novatel_lte_la-mm-sim-novatel-lte.lo -MD -MP -MF novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-sim-novatel-lte.Tpo -c -o novatel/libmm_plugin_novatel_lte_la-mm-sim-novatel-lte.lo `test -f 'novatel/mm-sim-novatel-lte.c' || echo '$(srcdir)/'`novatel/mm-sim-novatel-lte.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-sim-novatel-lte.Tpo novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-sim-novatel-lte.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='novatel/mm-sim-novatel-lte.c' object='novatel/libmm_plugin_novatel_lte_la-mm-sim-novatel-lte.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_novatel_lte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o novatel/libmm_plugin_novatel_lte_la-mm-sim-novatel-lte.lo `test -f 'novatel/mm-sim-novatel-lte.c' || echo '$(srcdir)/'`novatel/mm-sim-novatel-lte.c

novatel/libmm_plugin_novatel_la-mm-plugin-novatel.lo: novatel/mm-plugin-novatel.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_novatel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT novatel/libmm_plugin_novatel_la-mm-plugin-novatel.lo -MD -MP -MF novatel/$(DEPDIR)/libmm_plugin_novatel_la-mm-plugin-novatel.Tpo -c -o novatel/libmm_plugin_novatel_la-mm-plugin-novatel.lo `test -f 'novatel/mm-plugin-novatel.c' || echo '$(srcdir)/'`novatel/mm-plugin-novatel.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) novatel/$(DEPDIR)/libmm_plugin_novatel_la-mm-plugin-novatel.Tpo novatel/$(DEPDIR)/libmm_plugin_novatel_la-mm-plugin-novatel.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='novatel/mm-plugin-novatel.c' object='novatel/libmm_plugin_novatel_la-mm-plugin-novatel.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_novatel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o novatel/libmm_plugin_novatel_la-mm-plugin-novatel.lo `test -f 'novatel/mm-plugin-novatel.c' || echo '$(srcdir)/'`novatel/mm-plugin-novatel.c

option/libmm_plugin_option_hso_la-mm-plugin-hso.lo: option/mm-plugin-hso.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_option_hso_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT option/libmm_plugin_option_hso_la-mm-plugin-hso.lo -MD -MP -MF option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-plugin-hso.Tpo -c -o option/libmm_plugin_option_hso_la-mm-plugin-hso.lo `test -f 'option/mm-plugin-hso.c' || echo '$(srcdir)/'`option/mm-plugin-hso.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-plugin-hso.Tpo option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-plugin-hso.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='option/mm-plugin-hso.c' object='option/libmm_plugin_option_hso_la-mm-plugin-hso.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_option_hso_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o option/libmm_plugin_option_hso_la-mm-plugin-hso.lo `test -f 'option/mm-plugin-hso.c' || echo '$(srcdir)/'`option/mm-plugin-hso.c

option/libmm_plugin_option_hso_la-mm-broadband-bearer-hso.lo: option/mm-broadband-bearer-hso.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_option_hso_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT option/libmm_plugin_option_hso_la-mm-broadband-bearer-hso.lo -MD -MP -MF option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-broadband-bearer-hso.Tpo -c -o option/libmm_plugin_option_hso_la-mm-broadband-bearer-hso.lo `test -f 'option/mm-broadband-bearer-hso.c' || echo '$(srcdir)/'`option/mm-broadband-bearer-hso.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-broadband-bearer-hso.Tpo option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-broadband-bearer-hso.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='option/mm-broadband-bearer-hso.c' object='option/libmm_plugin_option_hso_la-mm-broadband-bearer-hso.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_option_hso_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o option/libmm_plugin_option_hso_la-mm-broadband-bearer-hso.lo `test -f 'option/mm-broadband-bearer-hso.c' || echo '$(srcdir)/'`option/mm-broadband-bearer-hso.c

option/libmm_plugin_option_hso_la-mm-broadband-modem-hso.lo: option/mm-broadband-modem-hso.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_option_hso_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT option/libmm_plugin_option_hso_la-mm-broadband-modem-hso.lo -MD -MP -MF option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-broadband-modem-hso.Tpo -c -o option/libmm_plugin_option_hso_la-mm-broadband-modem-hso.lo `test -f 'option/mm-broadband-modem-hso.c' || echo '$(srcdir)/'`option/mm-broadband-modem-hso.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-broadband-modem-hso.Tpo option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-broadband-modem-hso.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='option/mm-broadband-modem-hso.c' object='option/libmm_plugin_option_hso_la-mm-broadband-modem-hso.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_option_hso_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o option/libmm_plugin_option_hso_la-mm-broadband-modem-hso.lo `test -f 'option/mm-broadband-modem-hso.c' || echo '$(srcdir)/'`option/mm-broadband-modem-hso.c

option/libmm_plugin_option_la-mm-plugin-option.lo: option/mm-plugin-option.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_option_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT option/libmm_plugin_option_la-mm-plugin-option.lo -MD -MP -MF option/$(DEPDIR)/libmm_plugin_option_la-mm-plugin-option.Tpo -c -o option/libmm_plugin_option_la-mm-plugin-option.lo `test -f 'option/mm-plugin-option.c' || echo '$(srcdir)/'`option/mm-plugin-option.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) option/$(DEPDIR)/libmm_plugin_option_la-mm-plugin-option.Tpo option/$(DEPDIR)/libmm_plugin_option_la-mm-plugin-option.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='option/mm-plugin-option.c' object='option/libmm_plugin_option_la-mm-plugin-option.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_option_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o option/libmm_plugin_option_la-mm-plugin-option.lo `test -f 'option/mm-plugin-option.c' || echo '$(srcdir)/'`option/mm-plugin-option.c

pantech/libmm_plugin_pantech_la-mm-plugin-pantech.lo: pantech/mm-plugin-pantech.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_pantech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT pantech/libmm_plugin_pantech_la-mm-plugin-pantech.lo -MD -MP -MF pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-plugin-pantech.Tpo -c -o pantech/libmm_plugin_pantech_la-mm-plugin-pantech.lo `test -f 'pantech/mm-plugin-pantech.c' || echo '$(srcdir)/'`pantech/mm-plugin-pantech.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-plugin-pantech.Tpo pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-plugin-pantech.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pantech/mm-plugin-pantech.c' object='pantech/libmm_plugin_pantech_la-mm-plugin-pantech.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_pantech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o pantech/libmm_plugin_pantech_la-mm-plugin-pantech.lo `test -f 'pantech/mm-plugin-pantech.c' || echo '$(srcdir)/'`pantech/mm-plugin-pantech.c

pantech/libmm_plugin_pantech_la-mm-sim-pantech.lo: pantech/mm-sim-pantech.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_pantech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT pantech/libmm_plugin_pantech_la-mm-sim-pantech.lo -MD -MP -MF pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-sim-pantech.Tpo -c -o pantech/libmm_plugin_pantech_la-mm-sim-pantech.lo `test -f 'pantech/mm-sim-pantech.c' || echo '$(srcdir)/'`pantech/mm-sim-pantech.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-sim-pantech.Tpo pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-sim-pantech.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pantech/mm-sim-pantech.c' object='pantech/libmm_plugin_pantech_la-mm-sim-pantech.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_pantech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o pantech/libmm_plugin_pantech_la-mm-sim-pantech.lo `test -f 'pantech/mm-sim-pantech.c' || echo '$(srcdir)/'`pantech/mm-sim-pantech.c

pantech/libmm_plugin_pantech_la-mm-broadband-modem-pantech.lo: pantech/mm-broadband-modem-pantech.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_pantech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT pantech/libmm_plugin_pantech_la-mm-broadband-modem-pantech.lo -MD -MP -MF pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-broadband-modem-pantech.Tpo -c -o pantech/libmm_plugin_pantech_la-mm-broadband-modem-pantech.lo `test -f 'pantech/mm-broadband-modem-pantech.c' || echo '$(srcdir)/'`pantech/mm-broadband-modem-pantech.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-broadband-modem-pantech.Tpo pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-broadband-modem-pantech.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='pantech/mm-broadband-modem-pantech.c' object='pantech/libmm_plugin_pantech_la-mm-broadband-modem-pantech.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_pantech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o pantech/libmm_plugin_pantech_la-mm-broadband-modem-pantech.lo `test -f 'pantech/mm-broadband-modem-pantech.c' || echo '$(srcdir)/'`pantech/mm-broadband-modem-pantech.c

quectel/libmm_plugin_quectel_la-mm-plugin-quectel.lo: quectel/mm-plugin-quectel.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_quectel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT quectel/libmm_plugin_quectel_la-mm-plugin-quectel.lo -MD -MP -MF quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-plugin-quectel.Tpo -c -o quectel/libmm_plugin_quectel_la-mm-plugin-quectel.lo `test -f 'quectel/mm-plugin-quectel.c' || echo '$(srcdir)/'`quectel/mm-plugin-quectel.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-plugin-quectel.Tpo quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-plugin-quectel.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='quectel/mm-plugin-quectel.c' object='quectel/libmm_plugin_quectel_la-mm-plugin-quectel.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_quectel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o quectel/libmm_plugin_quectel_la-mm-plugin-quectel.lo `test -f 'quectel/mm-plugin-quectel.c' || echo '$(srcdir)/'`quectel/mm-plugin-quectel.c

quectel/libmm_plugin_quectel_la-mm-shared-quectel.lo: quectel/mm-shared-quectel.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_quectel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT quectel/libmm_plugin_quectel_la-mm-shared-quectel.lo -MD -MP -MF quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-shared-quectel.Tpo -c -o quectel/libmm_plugin_quectel_la-mm-shared-quectel.lo `test -f 'quectel/mm-shared-quectel.c' || echo '$(srcdir)/'`quectel/mm-shared-quectel.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-shared-quectel.Tpo quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-shared-quectel.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='quectel/mm-shared-quectel.c' object='quectel/libmm_plugin_quectel_la-mm-shared-quectel.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_quectel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o quectel/libmm_plugin_quectel_la-mm-shared-quectel.lo `test -f 'quectel/mm-shared-quectel.c' || echo '$(srcdir)/'`quectel/mm-shared-quectel.c

quectel/libmm_plugin_quectel_la-mm-broadband-modem-quectel.lo: quectel/mm-broadband-modem-quectel.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_quectel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT quectel/libmm_plugin_quectel_la-mm-broadband-modem-quectel.lo -MD -MP -MF quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-broadband-modem-quectel.Tpo -c -o quectel/libmm_plugin_quectel_la-mm-broadband-modem-quectel.lo `test -f 'quectel/mm-broadband-modem-quectel.c' || echo '$(srcdir)/'`quectel/mm-broadband-modem-quectel.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-broadband-modem-quectel.Tpo quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-broadband-modem-quectel.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='quectel/mm-broadband-modem-quectel.c' object='quectel/libmm_plugin_quectel_la-mm-broadband-modem-quectel.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_quectel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o quectel/libmm_plugin_quectel_la-mm-broadband-modem-quectel.lo `test -f 'quectel/mm-broadband-modem-quectel.c' || echo '$(srcdir)/'`quectel/mm-broadband-modem-quectel.c

quectel/libmm_plugin_quectel_la-mm-broadband-modem-qmi-quectel.lo: quectel/mm-broadband-modem-qmi-quectel.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_quectel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT quectel/libmm_plugin_quectel_la-mm-broadband-modem-qmi-quectel.lo -MD -MP -MF quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-broadband-modem-qmi-quectel.Tpo -c -o quectel/libmm_plugin_quectel_la-mm-broadband-modem-qmi-quectel.lo `test -f 'quectel/mm-broadband-modem-qmi-quectel.c' || echo '$(srcdir)/'`quectel/mm-broadband-modem-qmi-quectel.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-broadband-modem-qmi-quectel.Tpo quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-broadband-modem-qmi-quectel.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='quectel/mm-broadband-modem-qmi-quectel.c' object='quectel/libmm_plugin_quectel_la-mm-broadband-modem-qmi-quectel.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_quectel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o quectel/libmm_plugin_quectel_la-mm-broadband-modem-qmi-quectel.lo `test -f 'quectel/mm-broadband-modem-qmi-quectel.c' || echo '$(srcdir)/'`quectel/mm-broadband-modem-qmi-quectel.c

samsung/libmm_plugin_samsung_la-mm-plugin-samsung.lo: samsung/mm-plugin-samsung.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_samsung_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT samsung/libmm_plugin_samsung_la-mm-plugin-samsung.lo -MD -MP -MF samsung/$(DEPDIR)/libmm_plugin_samsung_la-mm-plugin-samsung.Tpo -c -o samsung/libmm_plugin_samsung_la-mm-plugin-samsung.lo `test -f 'samsung/mm-plugin-samsung.c' || echo '$(srcdir)/'`samsung/mm-plugin-samsung.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) samsung/$(DEPDIR)/libmm_plugin_samsung_la-mm-plugin-samsung.Tpo samsung/$(DEPDIR)/libmm_plugin_samsung_la-mm-plugin-samsung.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='samsung/mm-plugin-samsung.c' object='samsung/libmm_plugin_samsung_la-mm-plugin-samsung.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_samsung_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o samsung/libmm_plugin_samsung_la-mm-plugin-samsung.lo `test -f 'samsung/mm-plugin-samsung.c' || echo '$(srcdir)/'`samsung/mm-plugin-samsung.c

samsung/libmm_plugin_samsung_la-mm-broadband-modem-samsung.lo: samsung/mm-broadband-modem-samsung.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_samsung_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT samsung/libmm_plugin_samsung_la-mm-broadband-modem-samsung.lo -MD -MP -MF samsung/$(DEPDIR)/libmm_plugin_samsung_la-mm-broadband-modem-samsung.Tpo -c -o samsung/libmm_plugin_samsung_la-mm-broadband-modem-samsung.lo `test -f 'samsung/mm-broadband-modem-samsung.c' || echo '$(srcdir)/'`samsung/mm-broadband-modem-samsung.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) samsung/$(DEPDIR)/libmm_plugin_samsung_la-mm-broadband-modem-samsung.Tpo samsung/$(DEPDIR)/libmm_plugin_samsung_la-mm-broadband-modem-samsung.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='samsung/mm-broadband-modem-samsung.c' object='samsung/libmm_plugin_samsung_la-mm-broadband-modem-samsung.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_samsung_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o samsung/libmm_plugin_samsung_la-mm-broadband-modem-samsung.lo `test -f 'samsung/mm-broadband-modem-samsung.c' || echo '$(srcdir)/'`samsung/mm-broadband-modem-samsung.c

sierra/libmm_plugin_sierra_legacy_la-mm-plugin-sierra-legacy.lo: sierra/mm-plugin-sierra-legacy.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_sierra_legacy_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT sierra/libmm_plugin_sierra_legacy_la-mm-plugin-sierra-legacy.lo -MD -MP -MF sierra/$(DEPDIR)/libmm_plugin_sierra_legacy_la-mm-plugin-sierra-legacy.Tpo -c -o sierra/libmm_plugin_sierra_legacy_la-mm-plugin-sierra-legacy.lo `test -f 'sierra/mm-plugin-sierra-legacy.c' || echo '$(srcdir)/'`sierra/mm-plugin-sierra-legacy.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sierra/$(DEPDIR)/libmm_plugin_sierra_legacy_la-mm-plugin-sierra-legacy.Tpo sierra/$(DEPDIR)/libmm_plugin_sierra_legacy_la-mm-plugin-sierra-legacy.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sierra/mm-plugin-sierra-legacy.c' object='sierra/libmm_plugin_sierra_legacy_la-mm-plugin-sierra-legacy.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_sierra_legacy_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o sierra/libmm_plugin_sierra_legacy_la-mm-plugin-sierra-legacy.lo `test -f 'sierra/mm-plugin-sierra-legacy.c' || echo '$(srcdir)/'`sierra/mm-plugin-sierra-legacy.c

sierra/libmm_plugin_sierra_legacy_la-mm-broadband-modem-sierra-icera.lo: sierra/mm-broadband-modem-sierra-icera.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_sierra_legacy_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT sierra/libmm_plugin_sierra_legacy_la-mm-broadband-modem-sierra-icera.lo -MD -MP -MF sierra/$(DEPDIR)/libmm_plugin_sierra_legacy_la-mm-broadband-modem-sierra-icera.Tpo -c -o sierra/libmm_plugin_sierra_legacy_la-mm-broadband-modem-sierra-icera.lo `test -f 'sierra/mm-broadband-modem-sierra-icera.c' || echo '$(srcdir)/'`sierra/mm-broadband-modem-sierra-icera.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sierra/$(DEPDIR)/libmm_plugin_sierra_legacy_la-mm-broadband-modem-sierra-icera.Tpo sierra/$(DEPDIR)/libmm_plugin_sierra_legacy_la-mm-broadband-modem-sierra-icera.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sierra/mm-broadband-modem-sierra-icera.c' object='sierra/libmm_plugin_sierra_legacy_la-mm-broadband-modem-sierra-icera.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_sierra_legacy_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o sierra/libmm_plugin_sierra_legacy_la-mm-broadband-modem-sierra-icera.lo `test -f 'sierra/mm-broadband-modem-sierra-icera.c' || echo '$(srcdir)/'`sierra/mm-broadband-modem-sierra-icera.c

sierra/libmm_plugin_sierra_la-mm-plugin-sierra.lo: sierra/mm-plugin-sierra.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_sierra_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT sierra/libmm_plugin_sierra_la-mm-plugin-sierra.lo -MD -MP -MF sierra/$(DEPDIR)/libmm_plugin_sierra_la-mm-plugin-sierra.Tpo -c -o sierra/libmm_plugin_sierra_la-mm-plugin-sierra.lo `test -f 'sierra/mm-plugin-sierra.c' || echo '$(srcdir)/'`sierra/mm-plugin-sierra.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sierra/$(DEPDIR)/libmm_plugin_sierra_la-mm-plugin-sierra.Tpo sierra/$(DEPDIR)/libmm_plugin_sierra_la-mm-plugin-sierra.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sierra/mm-plugin-sierra.c' object='sierra/libmm_plugin_sierra_la-mm-plugin-sierra.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_sierra_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o sierra/libmm_plugin_sierra_la-mm-plugin-sierra.lo `test -f 'sierra/mm-plugin-sierra.c' || echo '$(srcdir)/'`sierra/mm-plugin-sierra.c

simtech/libmm_plugin_simtech_la-mm-plugin-simtech.lo: simtech/mm-plugin-simtech.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_simtech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT simtech/libmm_plugin_simtech_la-mm-plugin-simtech.lo -MD -MP -MF simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-plugin-simtech.Tpo -c -o simtech/libmm_plugin_simtech_la-mm-plugin-simtech.lo `test -f 'simtech/mm-plugin-simtech.c' || echo '$(srcdir)/'`simtech/mm-plugin-simtech.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-plugin-simtech.Tpo simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-plugin-simtech.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='simtech/mm-plugin-simtech.c' object='simtech/libmm_plugin_simtech_la-mm-plugin-simtech.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_simtech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o simtech/libmm_plugin_simtech_la-mm-plugin-simtech.lo `test -f 'simtech/mm-plugin-simtech.c' || echo '$(srcdir)/'`simtech/mm-plugin-simtech.c

simtech/libmm_plugin_simtech_la-mm-shared-simtech.lo: simtech/mm-shared-simtech.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_simtech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT simtech/libmm_plugin_simtech_la-mm-shared-simtech.lo -MD -MP -MF simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-shared-simtech.Tpo -c -o simtech/libmm_plugin_simtech_la-mm-shared-simtech.lo `test -f 'simtech/mm-shared-simtech.c' || echo '$(srcdir)/'`simtech/mm-shared-simtech.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-shared-simtech.Tpo simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-shared-simtech.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='simtech/mm-shared-simtech.c' object='simtech/libmm_plugin_simtech_la-mm-shared-simtech.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_simtech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o simtech/libmm_plugin_simtech_la-mm-shared-simtech.lo `test -f 'simtech/mm-shared-simtech.c' || echo '$(srcdir)/'`simtech/mm-shared-simtech.c

simtech/libmm_plugin_simtech_la-mm-broadband-modem-simtech.lo: simtech/mm-broadband-modem-simtech.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_simtech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT simtech/libmm_plugin_simtech_la-mm-broadband-modem-simtech.lo -MD -MP -MF simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-broadband-modem-simtech.Tpo -c -o simtech/libmm_plugin_simtech_la-mm-broadband-modem-simtech.lo `test -f 'simtech/mm-broadband-modem-simtech.c' || echo '$(srcdir)/'`simtech/mm-broadband-modem-simtech.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-broadband-modem-simtech.Tpo simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-broadband-modem-simtech.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='simtech/mm-broadband-modem-simtech.c' object='simtech/libmm_plugin_simtech_la-mm-broadband-modem-simtech.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_simtech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o simtech/libmm_plugin_simtech_la-mm-broadband-modem-simtech.lo `test -f 'simtech/mm-broadband-modem-simtech.c' || echo '$(srcdir)/'`simtech/mm-broadband-modem-simtech.c

simtech/libmm_plugin_simtech_la-mm-broadband-modem-qmi-simtech.lo: simtech/mm-broadband-modem-qmi-simtech.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_simtech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT simtech/libmm_plugin_simtech_la-mm-broadband-modem-qmi-simtech.lo -MD -MP -MF simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-broadband-modem-qmi-simtech.Tpo -c -o simtech/libmm_plugin_simtech_la-mm-broadband-modem-qmi-simtech.lo `test -f 'simtech/mm-broadband-modem-qmi-simtech.c' || echo '$(srcdir)/'`simtech/mm-broadband-modem-qmi-simtech.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-broadband-modem-qmi-simtech.Tpo simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-broadband-modem-qmi-simtech.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='simtech/mm-broadband-modem-qmi-simtech.c' object='simtech/libmm_plugin_simtech_la-mm-broadband-modem-qmi-simtech.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_simtech_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o simtech/libmm_plugin_simtech_la-mm-broadband-modem-qmi-simtech.lo `test -f 'simtech/mm-broadband-modem-qmi-simtech.c' || echo '$(srcdir)/'`simtech/mm-broadband-modem-qmi-simtech.c

telit/libmm_plugin_telit_la-mm-plugin-telit.lo: telit/mm-plugin-telit.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT telit/libmm_plugin_telit_la-mm-plugin-telit.lo -MD -MP -MF telit/$(DEPDIR)/libmm_plugin_telit_la-mm-plugin-telit.Tpo -c -o telit/libmm_plugin_telit_la-mm-plugin-telit.lo `test -f 'telit/mm-plugin-telit.c' || echo '$(srcdir)/'`telit/mm-plugin-telit.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) telit/$(DEPDIR)/libmm_plugin_telit_la-mm-plugin-telit.Tpo telit/$(DEPDIR)/libmm_plugin_telit_la-mm-plugin-telit.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='telit/mm-plugin-telit.c' object='telit/libmm_plugin_telit_la-mm-plugin-telit.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o telit/libmm_plugin_telit_la-mm-plugin-telit.lo `test -f 'telit/mm-plugin-telit.c' || echo '$(srcdir)/'`telit/mm-plugin-telit.c

thuraya/libmm_plugin_thuraya_la-mm-plugin-thuraya.lo: thuraya/mm-plugin-thuraya.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_thuraya_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT thuraya/libmm_plugin_thuraya_la-mm-plugin-thuraya.lo -MD -MP -MF thuraya/$(DEPDIR)/libmm_plugin_thuraya_la-mm-plugin-thuraya.Tpo -c -o thuraya/libmm_plugin_thuraya_la-mm-plugin-thuraya.lo `test -f 'thuraya/mm-plugin-thuraya.c' || echo '$(srcdir)/'`thuraya/mm-plugin-thuraya.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) thuraya/$(DEPDIR)/libmm_plugin_thuraya_la-mm-plugin-thuraya.Tpo thuraya/$(DEPDIR)/libmm_plugin_thuraya_la-mm-plugin-thuraya.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='thuraya/mm-plugin-thuraya.c' object='thuraya/libmm_plugin_thuraya_la-mm-plugin-thuraya.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_thuraya_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o thuraya/libmm_plugin_thuraya_la-mm-plugin-thuraya.lo `test -f 'thuraya/mm-plugin-thuraya.c' || echo '$(srcdir)/'`thuraya/mm-plugin-thuraya.c

thuraya/libmm_plugin_thuraya_la-mm-broadband-modem-thuraya.lo: thuraya/mm-broadband-modem-thuraya.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_thuraya_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT thuraya/libmm_plugin_thuraya_la-mm-broadband-modem-thuraya.lo -MD -MP -MF thuraya/$(DEPDIR)/libmm_plugin_thuraya_la-mm-broadband-modem-thuraya.Tpo -c -o thuraya/libmm_plugin_thuraya_la-mm-broadband-modem-thuraya.lo `test -f 'thuraya/mm-broadband-modem-thuraya.c' || echo '$(srcdir)/'`thuraya/mm-broadband-modem-thuraya.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) thuraya/$(DEPDIR)/libmm_plugin_thuraya_la-mm-broadband-modem-thuraya.Tpo thuraya/$(DEPDIR)/libmm_plugin_thuraya_la-mm-broadband-modem-thuraya.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='thuraya/mm-broadband-modem-thuraya.c' object='thuraya/libmm_plugin_thuraya_la-mm-broadband-modem-thuraya.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_thuraya_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o thuraya/libmm_plugin_thuraya_la-mm-broadband-modem-thuraya.lo `test -f 'thuraya/mm-broadband-modem-thuraya.c' || echo '$(srcdir)/'`thuraya/mm-broadband-modem-thuraya.c

tplink/libmm_plugin_tplink_la-mm-plugin-tplink.lo: tplink/mm-plugin-tplink.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_tplink_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tplink/libmm_plugin_tplink_la-mm-plugin-tplink.lo -MD -MP -MF tplink/$(DEPDIR)/libmm_plugin_tplink_la-mm-plugin-tplink.Tpo -c -o tplink/libmm_plugin_tplink_la-mm-plugin-tplink.lo `test -f 'tplink/mm-plugin-tplink.c' || echo '$(srcdir)/'`tplink/mm-plugin-tplink.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tplink/$(DEPDIR)/libmm_plugin_tplink_la-mm-plugin-tplink.Tpo tplink/$(DEPDIR)/libmm_plugin_tplink_la-mm-plugin-tplink.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tplink/mm-plugin-tplink.c' object='tplink/libmm_plugin_tplink_la-mm-plugin-tplink.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_tplink_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tplink/libmm_plugin_tplink_la-mm-plugin-tplink.lo `test -f 'tplink/mm-plugin-tplink.c' || echo '$(srcdir)/'`tplink/mm-plugin-tplink.c

ublox/libmm_plugin_ublox_la-mm-plugin-ublox.lo: ublox/mm-plugin-ublox.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ublox_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ublox/libmm_plugin_ublox_la-mm-plugin-ublox.lo -MD -MP -MF ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-plugin-ublox.Tpo -c -o ublox/libmm_plugin_ublox_la-mm-plugin-ublox.lo `test -f 'ublox/mm-plugin-ublox.c' || echo '$(srcdir)/'`ublox/mm-plugin-ublox.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-plugin-ublox.Tpo ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-plugin-ublox.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ublox/mm-plugin-ublox.c' object='ublox/libmm_plugin_ublox_la-mm-plugin-ublox.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ublox_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ublox/libmm_plugin_ublox_la-mm-plugin-ublox.lo `test -f 'ublox/mm-plugin-ublox.c' || echo '$(srcdir)/'`ublox/mm-plugin-ublox.c

ublox/libmm_plugin_ublox_la-mm-broadband-bearer-ublox.lo: ublox/mm-broadband-bearer-ublox.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ublox_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ublox/libmm_plugin_ublox_la-mm-broadband-bearer-ublox.lo -MD -MP -MF ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-broadband-bearer-ublox.Tpo -c -o ublox/libmm_plugin_ublox_la-mm-broadband-bearer-ublox.lo `test -f 'ublox/mm-broadband-bearer-ublox.c' || echo '$(srcdir)/'`ublox/mm-broadband-bearer-ublox.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-broadband-bearer-ublox.Tpo ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-broadband-bearer-ublox.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ublox/mm-broadband-bearer-ublox.c' object='ublox/libmm_plugin_ublox_la-mm-broadband-bearer-ublox.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ublox_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ublox/libmm_plugin_ublox_la-mm-broadband-bearer-ublox.lo `test -f 'ublox/mm-broadband-bearer-ublox.c' || echo '$(srcdir)/'`ublox/mm-broadband-bearer-ublox.c

ublox/libmm_plugin_ublox_la-mm-broadband-modem-ublox.lo: ublox/mm-broadband-modem-ublox.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ublox_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ublox/libmm_plugin_ublox_la-mm-broadband-modem-ublox.lo -MD -MP -MF ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-broadband-modem-ublox.Tpo -c -o ublox/libmm_plugin_ublox_la-mm-broadband-modem-ublox.lo `test -f 'ublox/mm-broadband-modem-ublox.c' || echo '$(srcdir)/'`ublox/mm-broadband-modem-ublox.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-broadband-modem-ublox.Tpo ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-broadband-modem-ublox.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ublox/mm-broadband-modem-ublox.c' object='ublox/libmm_plugin_ublox_la-mm-broadband-modem-ublox.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ublox_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ublox/libmm_plugin_ublox_la-mm-broadband-modem-ublox.lo `test -f 'ublox/mm-broadband-modem-ublox.c' || echo '$(srcdir)/'`ublox/mm-broadband-modem-ublox.c

ublox/libmm_plugin_ublox_la-mm-sim-ublox.lo: ublox/mm-sim-ublox.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ublox_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ublox/libmm_plugin_ublox_la-mm-sim-ublox.lo -MD -MP -MF ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-sim-ublox.Tpo -c -o ublox/libmm_plugin_ublox_la-mm-sim-ublox.lo `test -f 'ublox/mm-sim-ublox.c' || echo '$(srcdir)/'`ublox/mm-sim-ublox.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-sim-ublox.Tpo ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-sim-ublox.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ublox/mm-sim-ublox.c' object='ublox/libmm_plugin_ublox_la-mm-sim-ublox.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_ublox_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ublox/libmm_plugin_ublox_la-mm-sim-ublox.lo `test -f 'ublox/mm-sim-ublox.c' || echo '$(srcdir)/'`ublox/mm-sim-ublox.c

via/libmm_plugin_via_la-mm-plugin-via.lo: via/mm-plugin-via.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_via_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT via/libmm_plugin_via_la-mm-plugin-via.lo -MD -MP -MF via/$(DEPDIR)/libmm_plugin_via_la-mm-plugin-via.Tpo -c -o via/libmm_plugin_via_la-mm-plugin-via.lo `test -f 'via/mm-plugin-via.c' || echo '$(srcdir)/'`via/mm-plugin-via.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) via/$(DEPDIR)/libmm_plugin_via_la-mm-plugin-via.Tpo via/$(DEPDIR)/libmm_plugin_via_la-mm-plugin-via.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='via/mm-plugin-via.c' object='via/libmm_plugin_via_la-mm-plugin-via.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_via_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o via/libmm_plugin_via_la-mm-plugin-via.lo `test -f 'via/mm-plugin-via.c' || echo '$(srcdir)/'`via/mm-plugin-via.c

via/libmm_plugin_via_la-mm-broadband-modem-via.lo: via/mm-broadband-modem-via.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_via_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT via/libmm_plugin_via_la-mm-broadband-modem-via.lo -MD -MP -MF via/$(DEPDIR)/libmm_plugin_via_la-mm-broadband-modem-via.Tpo -c -o via/libmm_plugin_via_la-mm-broadband-modem-via.lo `test -f 'via/mm-broadband-modem-via.c' || echo '$(srcdir)/'`via/mm-broadband-modem-via.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) via/$(DEPDIR)/libmm_plugin_via_la-mm-broadband-modem-via.Tpo via/$(DEPDIR)/libmm_plugin_via_la-mm-broadband-modem-via.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='via/mm-broadband-modem-via.c' object='via/libmm_plugin_via_la-mm-broadband-modem-via.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_via_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o via/libmm_plugin_via_la-mm-broadband-modem-via.lo `test -f 'via/mm-broadband-modem-via.c' || echo '$(srcdir)/'`via/mm-broadband-modem-via.c

wavecom/libmm_plugin_wavecom_la-mm-plugin-wavecom.lo: wavecom/mm-plugin-wavecom.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_wavecom_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT wavecom/libmm_plugin_wavecom_la-mm-plugin-wavecom.lo -MD -MP -MF wavecom/$(DEPDIR)/libmm_plugin_wavecom_la-mm-plugin-wavecom.Tpo -c -o wavecom/libmm_plugin_wavecom_la-mm-plugin-wavecom.lo `test -f 'wavecom/mm-plugin-wavecom.c' || echo '$(srcdir)/'`wavecom/mm-plugin-wavecom.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) wavecom/$(DEPDIR)/libmm_plugin_wavecom_la-mm-plugin-wavecom.Tpo wavecom/$(DEPDIR)/libmm_plugin_wavecom_la-mm-plugin-wavecom.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='wavecom/mm-plugin-wavecom.c' object='wavecom/libmm_plugin_wavecom_la-mm-plugin-wavecom.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_wavecom_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o wavecom/libmm_plugin_wavecom_la-mm-plugin-wavecom.lo `test -f 'wavecom/mm-plugin-wavecom.c' || echo '$(srcdir)/'`wavecom/mm-plugin-wavecom.c

wavecom/libmm_plugin_wavecom_la-mm-broadband-modem-wavecom.lo: wavecom/mm-broadband-modem-wavecom.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_wavecom_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT wavecom/libmm_plugin_wavecom_la-mm-broadband-modem-wavecom.lo -MD -MP -MF wavecom/$(DEPDIR)/libmm_plugin_wavecom_la-mm-broadband-modem-wavecom.Tpo -c -o wavecom/libmm_plugin_wavecom_la-mm-broadband-modem-wavecom.lo `test -f 'wavecom/mm-broadband-modem-wavecom.c' || echo '$(srcdir)/'`wavecom/mm-broadband-modem-wavecom.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) wavecom/$(DEPDIR)/libmm_plugin_wavecom_la-mm-broadband-modem-wavecom.Tpo wavecom/$(DEPDIR)/libmm_plugin_wavecom_la-mm-broadband-modem-wavecom.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='wavecom/mm-broadband-modem-wavecom.c' object='wavecom/libmm_plugin_wavecom_la-mm-broadband-modem-wavecom.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_wavecom_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o wavecom/libmm_plugin_wavecom_la-mm-broadband-modem-wavecom.lo `test -f 'wavecom/mm-broadband-modem-wavecom.c' || echo '$(srcdir)/'`wavecom/mm-broadband-modem-wavecom.c

x22x/libmm_plugin_x22x_la-mm-plugin-x22x.lo: x22x/mm-plugin-x22x.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_x22x_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT x22x/libmm_plugin_x22x_la-mm-plugin-x22x.lo -MD -MP -MF x22x/$(DEPDIR)/libmm_plugin_x22x_la-mm-plugin-x22x.Tpo -c -o x22x/libmm_plugin_x22x_la-mm-plugin-x22x.lo `test -f 'x22x/mm-plugin-x22x.c' || echo '$(srcdir)/'`x22x/mm-plugin-x22x.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) x22x/$(DEPDIR)/libmm_plugin_x22x_la-mm-plugin-x22x.Tpo x22x/$(DEPDIR)/libmm_plugin_x22x_la-mm-plugin-x22x.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='x22x/mm-plugin-x22x.c' object='x22x/libmm_plugin_x22x_la-mm-plugin-x22x.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_x22x_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o x22x/libmm_plugin_x22x_la-mm-plugin-x22x.lo `test -f 'x22x/mm-plugin-x22x.c' || echo '$(srcdir)/'`x22x/mm-plugin-x22x.c

x22x/libmm_plugin_x22x_la-mm-broadband-modem-x22x.lo: x22x/mm-broadband-modem-x22x.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_x22x_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT x22x/libmm_plugin_x22x_la-mm-broadband-modem-x22x.lo -MD -MP -MF x22x/$(DEPDIR)/libmm_plugin_x22x_la-mm-broadband-modem-x22x.Tpo -c -o x22x/libmm_plugin_x22x_la-mm-broadband-modem-x22x.lo `test -f 'x22x/mm-broadband-modem-x22x.c' || echo '$(srcdir)/'`x22x/mm-broadband-modem-x22x.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) x22x/$(DEPDIR)/libmm_plugin_x22x_la-mm-broadband-modem-x22x.Tpo x22x/$(DEPDIR)/libmm_plugin_x22x_la-mm-broadband-modem-x22x.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='x22x/mm-broadband-modem-x22x.c' object='x22x/libmm_plugin_x22x_la-mm-broadband-modem-x22x.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_x22x_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o x22x/libmm_plugin_x22x_la-mm-broadband-modem-x22x.lo `test -f 'x22x/mm-broadband-modem-x22x.c' || echo '$(srcdir)/'`x22x/mm-broadband-modem-x22x.c

zte/libmm_plugin_zte_la-mm-plugin-zte.lo: zte/mm-plugin-zte.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_zte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT zte/libmm_plugin_zte_la-mm-plugin-zte.lo -MD -MP -MF zte/$(DEPDIR)/libmm_plugin_zte_la-mm-plugin-zte.Tpo -c -o zte/libmm_plugin_zte_la-mm-plugin-zte.lo `test -f 'zte/mm-plugin-zte.c' || echo '$(srcdir)/'`zte/mm-plugin-zte.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) zte/$(DEPDIR)/libmm_plugin_zte_la-mm-plugin-zte.Tpo zte/$(DEPDIR)/libmm_plugin_zte_la-mm-plugin-zte.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='zte/mm-plugin-zte.c' object='zte/libmm_plugin_zte_la-mm-plugin-zte.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_zte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o zte/libmm_plugin_zte_la-mm-plugin-zte.lo `test -f 'zte/mm-plugin-zte.c' || echo '$(srcdir)/'`zte/mm-plugin-zte.c

zte/libmm_plugin_zte_la-mm-common-zte.lo: zte/mm-common-zte.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_zte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT zte/libmm_plugin_zte_la-mm-common-zte.lo -MD -MP -MF zte/$(DEPDIR)/libmm_plugin_zte_la-mm-common-zte.Tpo -c -o zte/libmm_plugin_zte_la-mm-common-zte.lo `test -f 'zte/mm-common-zte.c' || echo '$(srcdir)/'`zte/mm-common-zte.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) zte/$(DEPDIR)/libmm_plugin_zte_la-mm-common-zte.Tpo zte/$(DEPDIR)/libmm_plugin_zte_la-mm-common-zte.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='zte/mm-common-zte.c' object='zte/libmm_plugin_zte_la-mm-common-zte.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_zte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o zte/libmm_plugin_zte_la-mm-common-zte.lo `test -f 'zte/mm-common-zte.c' || echo '$(srcdir)/'`zte/mm-common-zte.c

zte/libmm_plugin_zte_la-mm-broadband-modem-zte.lo: zte/mm-broadband-modem-zte.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_zte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT zte/libmm_plugin_zte_la-mm-broadband-modem-zte.lo -MD -MP -MF zte/$(DEPDIR)/libmm_plugin_zte_la-mm-broadband-modem-zte.Tpo -c -o zte/libmm_plugin_zte_la-mm-broadband-modem-zte.lo `test -f 'zte/mm-broadband-modem-zte.c' || echo '$(srcdir)/'`zte/mm-broadband-modem-zte.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) zte/$(DEPDIR)/libmm_plugin_zte_la-mm-broadband-modem-zte.Tpo zte/$(DEPDIR)/libmm_plugin_zte_la-mm-broadband-modem-zte.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='zte/mm-broadband-modem-zte.c' object='zte/libmm_plugin_zte_la-mm-broadband-modem-zte.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_zte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o zte/libmm_plugin_zte_la-mm-broadband-modem-zte.lo `test -f 'zte/mm-broadband-modem-zte.c' || echo '$(srcdir)/'`zte/mm-broadband-modem-zte.c

zte/libmm_plugin_zte_la-mm-broadband-modem-zte-icera.lo: zte/mm-broadband-modem-zte-icera.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_zte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT zte/libmm_plugin_zte_la-mm-broadband-modem-zte-icera.lo -MD -MP -MF zte/$(DEPDIR)/libmm_plugin_zte_la-mm-broadband-modem-zte-icera.Tpo -c -o zte/libmm_plugin_zte_la-mm-broadband-modem-zte-icera.lo `test -f 'zte/mm-broadband-modem-zte-icera.c' || echo '$(srcdir)/'`zte/mm-broadband-modem-zte-icera.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) zte/$(DEPDIR)/libmm_plugin_zte_la-mm-broadband-modem-zte-icera.Tpo zte/$(DEPDIR)/libmm_plugin_zte_la-mm-broadband-modem-zte-icera.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='zte/mm-broadband-modem-zte-icera.c' object='zte/libmm_plugin_zte_la-mm-broadband-modem-zte-icera.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_plugin_zte_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o zte/libmm_plugin_zte_la-mm-broadband-modem-zte-icera.lo `test -f 'zte/mm-broadband-modem-zte-icera.c' || echo '$(srcdir)/'`zte/mm-broadband-modem-zte-icera.c

foxconn/libmm_shared_foxconn_la-mm-shared.lo: foxconn/mm-shared.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_foxconn_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT foxconn/libmm_shared_foxconn_la-mm-shared.lo -MD -MP -MF foxconn/$(DEPDIR)/libmm_shared_foxconn_la-mm-shared.Tpo -c -o foxconn/libmm_shared_foxconn_la-mm-shared.lo `test -f 'foxconn/mm-shared.c' || echo '$(srcdir)/'`foxconn/mm-shared.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) foxconn/$(DEPDIR)/libmm_shared_foxconn_la-mm-shared.Tpo foxconn/$(DEPDIR)/libmm_shared_foxconn_la-mm-shared.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='foxconn/mm-shared.c' object='foxconn/libmm_shared_foxconn_la-mm-shared.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_foxconn_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o foxconn/libmm_shared_foxconn_la-mm-shared.lo `test -f 'foxconn/mm-shared.c' || echo '$(srcdir)/'`foxconn/mm-shared.c

foxconn/libmm_shared_foxconn_la-mm-broadband-modem-foxconn-t77w968.lo: foxconn/mm-broadband-modem-foxconn-t77w968.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_foxconn_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT foxconn/libmm_shared_foxconn_la-mm-broadband-modem-foxconn-t77w968.lo -MD -MP -MF foxconn/$(DEPDIR)/libmm_shared_foxconn_la-mm-broadband-modem-foxconn-t77w968.Tpo -c -o foxconn/libmm_shared_foxconn_la-mm-broadband-modem-foxconn-t77w968.lo `test -f 'foxconn/mm-broadband-modem-foxconn-t77w968.c' || echo '$(srcdir)/'`foxconn/mm-broadband-modem-foxconn-t77w968.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) foxconn/$(DEPDIR)/libmm_shared_foxconn_la-mm-broadband-modem-foxconn-t77w968.Tpo foxconn/$(DEPDIR)/libmm_shared_foxconn_la-mm-broadband-modem-foxconn-t77w968.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='foxconn/mm-broadband-modem-foxconn-t77w968.c' object='foxconn/libmm_shared_foxconn_la-mm-broadband-modem-foxconn-t77w968.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_foxconn_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o foxconn/libmm_shared_foxconn_la-mm-broadband-modem-foxconn-t77w968.lo `test -f 'foxconn/mm-broadband-modem-foxconn-t77w968.c' || echo '$(srcdir)/'`foxconn/mm-broadband-modem-foxconn-t77w968.c

icera/libmm_shared_icera_la-mm-shared.lo: icera/mm-shared.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_icera_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT icera/libmm_shared_icera_la-mm-shared.lo -MD -MP -MF icera/$(DEPDIR)/libmm_shared_icera_la-mm-shared.Tpo -c -o icera/libmm_shared_icera_la-mm-shared.lo `test -f 'icera/mm-shared.c' || echo '$(srcdir)/'`icera/mm-shared.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) icera/$(DEPDIR)/libmm_shared_icera_la-mm-shared.Tpo icera/$(DEPDIR)/libmm_shared_icera_la-mm-shared.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='icera/mm-shared.c' object='icera/libmm_shared_icera_la-mm-shared.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_icera_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o icera/libmm_shared_icera_la-mm-shared.lo `test -f 'icera/mm-shared.c' || echo '$(srcdir)/'`icera/mm-shared.c

icera/libmm_shared_icera_la-mm-broadband-modem-icera.lo: icera/mm-broadband-modem-icera.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_icera_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT icera/libmm_shared_icera_la-mm-broadband-modem-icera.lo -MD -MP -MF icera/$(DEPDIR)/libmm_shared_icera_la-mm-broadband-modem-icera.Tpo -c -o icera/libmm_shared_icera_la-mm-broadband-modem-icera.lo `test -f 'icera/mm-broadband-modem-icera.c' || echo '$(srcdir)/'`icera/mm-broadband-modem-icera.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) icera/$(DEPDIR)/libmm_shared_icera_la-mm-broadband-modem-icera.Tpo icera/$(DEPDIR)/libmm_shared_icera_la-mm-broadband-modem-icera.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='icera/mm-broadband-modem-icera.c' object='icera/libmm_shared_icera_la-mm-broadband-modem-icera.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_icera_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o icera/libmm_shared_icera_la-mm-broadband-modem-icera.lo `test -f 'icera/mm-broadband-modem-icera.c' || echo '$(srcdir)/'`icera/mm-broadband-modem-icera.c

icera/libmm_shared_icera_la-mm-broadband-bearer-icera.lo: icera/mm-broadband-bearer-icera.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_icera_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT icera/libmm_shared_icera_la-mm-broadband-bearer-icera.lo -MD -MP -MF icera/$(DEPDIR)/libmm_shared_icera_la-mm-broadband-bearer-icera.Tpo -c -o icera/libmm_shared_icera_la-mm-broadband-bearer-icera.lo `test -f 'icera/mm-broadband-bearer-icera.c' || echo '$(srcdir)/'`icera/mm-broadband-bearer-icera.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) icera/$(DEPDIR)/libmm_shared_icera_la-mm-broadband-bearer-icera.Tpo icera/$(DEPDIR)/libmm_shared_icera_la-mm-broadband-bearer-icera.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='icera/mm-broadband-bearer-icera.c' object='icera/libmm_shared_icera_la-mm-broadband-bearer-icera.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_icera_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o icera/libmm_shared_icera_la-mm-broadband-bearer-icera.lo `test -f 'icera/mm-broadband-bearer-icera.c' || echo '$(srcdir)/'`icera/mm-broadband-bearer-icera.c

novatel/libmm_shared_novatel_la-mm-shared.lo: novatel/mm-shared.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_novatel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT novatel/libmm_shared_novatel_la-mm-shared.lo -MD -MP -MF novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-shared.Tpo -c -o novatel/libmm_shared_novatel_la-mm-shared.lo `test -f 'novatel/mm-shared.c' || echo '$(srcdir)/'`novatel/mm-shared.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-shared.Tpo novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-shared.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='novatel/mm-shared.c' object='novatel/libmm_shared_novatel_la-mm-shared.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_novatel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o novatel/libmm_shared_novatel_la-mm-shared.lo `test -f 'novatel/mm-shared.c' || echo '$(srcdir)/'`novatel/mm-shared.c

novatel/libmm_shared_novatel_la-mm-common-novatel.lo: novatel/mm-common-novatel.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_novatel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT novatel/libmm_shared_novatel_la-mm-common-novatel.lo -MD -MP -MF novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-common-novatel.Tpo -c -o novatel/libmm_shared_novatel_la-mm-common-novatel.lo `test -f 'novatel/mm-common-novatel.c' || echo '$(srcdir)/'`novatel/mm-common-novatel.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-common-novatel.Tpo novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-common-novatel.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='novatel/mm-common-novatel.c' object='novatel/libmm_shared_novatel_la-mm-common-novatel.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_novatel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o novatel/libmm_shared_novatel_la-mm-common-novatel.lo `test -f 'novatel/mm-common-novatel.c' || echo '$(srcdir)/'`novatel/mm-common-novatel.c

novatel/libmm_shared_novatel_la-mm-broadband-modem-novatel.lo: novatel/mm-broadband-modem-novatel.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_novatel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT novatel/libmm_shared_novatel_la-mm-broadband-modem-novatel.lo -MD -MP -MF novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-broadband-modem-novatel.Tpo -c -o novatel/libmm_shared_novatel_la-mm-broadband-modem-novatel.lo `test -f 'novatel/mm-broadband-modem-novatel.c' || echo '$(srcdir)/'`novatel/mm-broadband-modem-novatel.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-broadband-modem-novatel.Tpo novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-broadband-modem-novatel.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='novatel/mm-broadband-modem-novatel.c' object='novatel/libmm_shared_novatel_la-mm-broadband-modem-novatel.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_novatel_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o novatel/libmm_shared_novatel_la-mm-broadband-modem-novatel.lo `test -f 'novatel/mm-broadband-modem-novatel.c' || echo '$(srcdir)/'`novatel/mm-broadband-modem-novatel.c

option/libmm_shared_option_la-mm-shared.lo: option/mm-shared.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_option_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT option/libmm_shared_option_la-mm-shared.lo -MD -MP -MF option/$(DEPDIR)/libmm_shared_option_la-mm-shared.Tpo -c -o option/libmm_shared_option_la-mm-shared.lo `test -f 'option/mm-shared.c' || echo '$(srcdir)/'`option/mm-shared.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) option/$(DEPDIR)/libmm_shared_option_la-mm-shared.Tpo option/$(DEPDIR)/libmm_shared_option_la-mm-shared.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='option/mm-shared.c' object='option/libmm_shared_option_la-mm-shared.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_option_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o option/libmm_shared_option_la-mm-shared.lo `test -f 'option/mm-shared.c' || echo '$(srcdir)/'`option/mm-shared.c

option/libmm_shared_option_la-mm-broadband-modem-option.lo: option/mm-broadband-modem-option.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_option_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT option/libmm_shared_option_la-mm-broadband-modem-option.lo -MD -MP -MF option/$(DEPDIR)/libmm_shared_option_la-mm-broadband-modem-option.Tpo -c -o option/libmm_shared_option_la-mm-broadband-modem-option.lo `test -f 'option/mm-broadband-modem-option.c' || echo '$(srcdir)/'`option/mm-broadband-modem-option.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) option/$(DEPDIR)/libmm_shared_option_la-mm-broadband-modem-option.Tpo option/$(DEPDIR)/libmm_shared_option_la-mm-broadband-modem-option.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='option/mm-broadband-modem-option.c' object='option/libmm_shared_option_la-mm-broadband-modem-option.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_option_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o option/libmm_shared_option_la-mm-broadband-modem-option.lo `test -f 'option/mm-broadband-modem-option.c' || echo '$(srcdir)/'`option/mm-broadband-modem-option.c

sierra/libmm_shared_sierra_la-mm-shared.lo: sierra/mm-shared.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_sierra_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT sierra/libmm_shared_sierra_la-mm-shared.lo -MD -MP -MF sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-shared.Tpo -c -o sierra/libmm_shared_sierra_la-mm-shared.lo `test -f 'sierra/mm-shared.c' || echo '$(srcdir)/'`sierra/mm-shared.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-shared.Tpo sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-shared.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sierra/mm-shared.c' object='sierra/libmm_shared_sierra_la-mm-shared.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_sierra_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o sierra/libmm_shared_sierra_la-mm-shared.lo `test -f 'sierra/mm-shared.c' || echo '$(srcdir)/'`sierra/mm-shared.c

sierra/libmm_shared_sierra_la-mm-common-sierra.lo: sierra/mm-common-sierra.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_sierra_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT sierra/libmm_shared_sierra_la-mm-common-sierra.lo -MD -MP -MF sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-common-sierra.Tpo -c -o sierra/libmm_shared_sierra_la-mm-common-sierra.lo `test -f 'sierra/mm-common-sierra.c' || echo '$(srcdir)/'`sierra/mm-common-sierra.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-common-sierra.Tpo sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-common-sierra.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sierra/mm-common-sierra.c' object='sierra/libmm_shared_sierra_la-mm-common-sierra.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_sierra_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o sierra/libmm_shared_sierra_la-mm-common-sierra.lo `test -f 'sierra/mm-common-sierra.c' || echo '$(srcdir)/'`sierra/mm-common-sierra.c

sierra/libmm_shared_sierra_la-mm-sim-sierra.lo: sierra/mm-sim-sierra.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_sierra_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT sierra/libmm_shared_sierra_la-mm-sim-sierra.lo -MD -MP -MF sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-sim-sierra.Tpo -c -o sierra/libmm_shared_sierra_la-mm-sim-sierra.lo `test -f 'sierra/mm-sim-sierra.c' || echo '$(srcdir)/'`sierra/mm-sim-sierra.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-sim-sierra.Tpo sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-sim-sierra.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sierra/mm-sim-sierra.c' object='sierra/libmm_shared_sierra_la-mm-sim-sierra.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_sierra_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o sierra/libmm_shared_sierra_la-mm-sim-sierra.lo `test -f 'sierra/mm-sim-sierra.c' || echo '$(srcdir)/'`sierra/mm-sim-sierra.c

sierra/libmm_shared_sierra_la-mm-broadband-bearer-sierra.lo: sierra/mm-broadband-bearer-sierra.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_sierra_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT sierra/libmm_shared_sierra_la-mm-broadband-bearer-sierra.lo -MD -MP -MF sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-broadband-bearer-sierra.Tpo -c -o sierra/libmm_shared_sierra_la-mm-broadband-bearer-sierra.lo `test -f 'sierra/mm-broadband-bearer-sierra.c' || echo '$(srcdir)/'`sierra/mm-broadband-bearer-sierra.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-broadband-bearer-sierra.Tpo sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-broadband-bearer-sierra.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sierra/mm-broadband-bearer-sierra.c' object='sierra/libmm_shared_sierra_la-mm-broadband-bearer-sierra.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_sierra_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o sierra/libmm_shared_sierra_la-mm-broadband-bearer-sierra.lo `test -f 'sierra/mm-broadband-bearer-sierra.c' || echo '$(srcdir)/'`sierra/mm-broadband-bearer-sierra.c

sierra/libmm_shared_sierra_la-mm-broadband-modem-sierra.lo: sierra/mm-broadband-modem-sierra.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_sierra_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT sierra/libmm_shared_sierra_la-mm-broadband-modem-sierra.lo -MD -MP -MF sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-broadband-modem-sierra.Tpo -c -o sierra/libmm_shared_sierra_la-mm-broadband-modem-sierra.lo `test -f 'sierra/mm-broadband-modem-sierra.c' || echo '$(srcdir)/'`sierra/mm-broadband-modem-sierra.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-broadband-modem-sierra.Tpo sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-broadband-modem-sierra.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sierra/mm-broadband-modem-sierra.c' object='sierra/libmm_shared_sierra_la-mm-broadband-modem-sierra.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_sierra_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o sierra/libmm_shared_sierra_la-mm-broadband-modem-sierra.lo `test -f 'sierra/mm-broadband-modem-sierra.c' || echo '$(srcdir)/'`sierra/mm-broadband-modem-sierra.c

telit/libmm_shared_telit_la-mm-shared.lo: telit/mm-shared.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT telit/libmm_shared_telit_la-mm-shared.lo -MD -MP -MF telit/$(DEPDIR)/libmm_shared_telit_la-mm-shared.Tpo -c -o telit/libmm_shared_telit_la-mm-shared.lo `test -f 'telit/mm-shared.c' || echo '$(srcdir)/'`telit/mm-shared.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) telit/$(DEPDIR)/libmm_shared_telit_la-mm-shared.Tpo telit/$(DEPDIR)/libmm_shared_telit_la-mm-shared.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='telit/mm-shared.c' object='telit/libmm_shared_telit_la-mm-shared.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o telit/libmm_shared_telit_la-mm-shared.lo `test -f 'telit/mm-shared.c' || echo '$(srcdir)/'`telit/mm-shared.c

telit/libmm_shared_telit_la-mm-common-telit.lo: telit/mm-common-telit.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT telit/libmm_shared_telit_la-mm-common-telit.lo -MD -MP -MF telit/$(DEPDIR)/libmm_shared_telit_la-mm-common-telit.Tpo -c -o telit/libmm_shared_telit_la-mm-common-telit.lo `test -f 'telit/mm-common-telit.c' || echo '$(srcdir)/'`telit/mm-common-telit.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) telit/$(DEPDIR)/libmm_shared_telit_la-mm-common-telit.Tpo telit/$(DEPDIR)/libmm_shared_telit_la-mm-common-telit.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='telit/mm-common-telit.c' object='telit/libmm_shared_telit_la-mm-common-telit.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o telit/libmm_shared_telit_la-mm-common-telit.lo `test -f 'telit/mm-common-telit.c' || echo '$(srcdir)/'`telit/mm-common-telit.c

telit/libmm_shared_telit_la-mm-shared-telit.lo: telit/mm-shared-telit.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT telit/libmm_shared_telit_la-mm-shared-telit.lo -MD -MP -MF telit/$(DEPDIR)/libmm_shared_telit_la-mm-shared-telit.Tpo -c -o telit/libmm_shared_telit_la-mm-shared-telit.lo `test -f 'telit/mm-shared-telit.c' || echo '$(srcdir)/'`telit/mm-shared-telit.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) telit/$(DEPDIR)/libmm_shared_telit_la-mm-shared-telit.Tpo telit/$(DEPDIR)/libmm_shared_telit_la-mm-shared-telit.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='telit/mm-shared-telit.c' object='telit/libmm_shared_telit_la-mm-shared-telit.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o telit/libmm_shared_telit_la-mm-shared-telit.lo `test -f 'telit/mm-shared-telit.c' || echo '$(srcdir)/'`telit/mm-shared-telit.c

telit/libmm_shared_telit_la-mm-broadband-modem-telit.lo: telit/mm-broadband-modem-telit.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT telit/libmm_shared_telit_la-mm-broadband-modem-telit.lo -MD -MP -MF telit/$(DEPDIR)/libmm_shared_telit_la-mm-broadband-modem-telit.Tpo -c -o telit/libmm_shared_telit_la-mm-broadband-modem-telit.lo `test -f 'telit/mm-broadband-modem-telit.c' || echo '$(srcdir)/'`telit/mm-broadband-modem-telit.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) telit/$(DEPDIR)/libmm_shared_telit_la-mm-broadband-modem-telit.Tpo telit/$(DEPDIR)/libmm_shared_telit_la-mm-broadband-modem-telit.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='telit/mm-broadband-modem-telit.c' object='telit/libmm_shared_telit_la-mm-broadband-modem-telit.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o telit/libmm_shared_telit_la-mm-broadband-modem-telit.lo `test -f 'telit/mm-broadband-modem-telit.c' || echo '$(srcdir)/'`telit/mm-broadband-modem-telit.c

telit/libmm_shared_telit_la-mm-broadband-modem-mbim-telit.lo: telit/mm-broadband-modem-mbim-telit.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT telit/libmm_shared_telit_la-mm-broadband-modem-mbim-telit.lo -MD -MP -MF telit/$(DEPDIR)/libmm_shared_telit_la-mm-broadband-modem-mbim-telit.Tpo -c -o telit/libmm_shared_telit_la-mm-broadband-modem-mbim-telit.lo `test -f 'telit/mm-broadband-modem-mbim-telit.c' || echo '$(srcdir)/'`telit/mm-broadband-modem-mbim-telit.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) telit/$(DEPDIR)/libmm_shared_telit_la-mm-broadband-modem-mbim-telit.Tpo telit/$(DEPDIR)/libmm_shared_telit_la-mm-broadband-modem-mbim-telit.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='telit/mm-broadband-modem-mbim-telit.c' object='telit/libmm_shared_telit_la-mm-broadband-modem-mbim-telit.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_telit_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o telit/libmm_shared_telit_la-mm-broadband-modem-mbim-telit.lo `test -f 'telit/mm-broadband-modem-mbim-telit.c' || echo '$(srcdir)/'`telit/mm-broadband-modem-mbim-telit.c

xmm/libmm_shared_xmm_la-mm-shared.lo: xmm/mm-shared.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_xmm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT xmm/libmm_shared_xmm_la-mm-shared.lo -MD -MP -MF xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-shared.Tpo -c -o xmm/libmm_shared_xmm_la-mm-shared.lo `test -f 'xmm/mm-shared.c' || echo '$(srcdir)/'`xmm/mm-shared.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-shared.Tpo xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-shared.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='xmm/mm-shared.c' object='xmm/libmm_shared_xmm_la-mm-shared.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_xmm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o xmm/libmm_shared_xmm_la-mm-shared.lo `test -f 'xmm/mm-shared.c' || echo '$(srcdir)/'`xmm/mm-shared.c

xmm/libmm_shared_xmm_la-mm-shared-xmm.lo: xmm/mm-shared-xmm.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_xmm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT xmm/libmm_shared_xmm_la-mm-shared-xmm.lo -MD -MP -MF xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-shared-xmm.Tpo -c -o xmm/libmm_shared_xmm_la-mm-shared-xmm.lo `test -f 'xmm/mm-shared-xmm.c' || echo '$(srcdir)/'`xmm/mm-shared-xmm.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-shared-xmm.Tpo xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-shared-xmm.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='xmm/mm-shared-xmm.c' object='xmm/libmm_shared_xmm_la-mm-shared-xmm.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_xmm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o xmm/libmm_shared_xmm_la-mm-shared-xmm.lo `test -f 'xmm/mm-shared-xmm.c' || echo '$(srcdir)/'`xmm/mm-shared-xmm.c

xmm/libmm_shared_xmm_la-mm-broadband-modem-xmm.lo: xmm/mm-broadband-modem-xmm.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_xmm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT xmm/libmm_shared_xmm_la-mm-broadband-modem-xmm.lo -MD -MP -MF xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-broadband-modem-xmm.Tpo -c -o xmm/libmm_shared_xmm_la-mm-broadband-modem-xmm.lo `test -f 'xmm/mm-broadband-modem-xmm.c' || echo '$(srcdir)/'`xmm/mm-broadband-modem-xmm.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-broadband-modem-xmm.Tpo xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-broadband-modem-xmm.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='xmm/mm-broadband-modem-xmm.c' object='xmm/libmm_shared_xmm_la-mm-broadband-modem-xmm.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_xmm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o xmm/libmm_shared_xmm_la-mm-broadband-modem-xmm.lo `test -f 'xmm/mm-broadband-modem-xmm.c' || echo '$(srcdir)/'`xmm/mm-broadband-modem-xmm.c

xmm/libmm_shared_xmm_la-mm-broadband-modem-mbim-xmm.lo: xmm/mm-broadband-modem-mbim-xmm.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_xmm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT xmm/libmm_shared_xmm_la-mm-broadband-modem-mbim-xmm.lo -MD -MP -MF xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-broadband-modem-mbim-xmm.Tpo -c -o xmm/libmm_shared_xmm_la-mm-broadband-modem-mbim-xmm.lo `test -f 'xmm/mm-broadband-modem-mbim-xmm.c' || echo '$(srcdir)/'`xmm/mm-broadband-modem-mbim-xmm.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-broadband-modem-mbim-xmm.Tpo xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-broadband-modem-mbim-xmm.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='xmm/mm-broadband-modem-mbim-xmm.c' object='xmm/libmm_shared_xmm_la-mm-broadband-modem-mbim-xmm.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_shared_xmm_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o xmm/libmm_shared_xmm_la-mm-broadband-modem-mbim-xmm.lo `test -f 'xmm/mm-broadband-modem-mbim-xmm.c' || echo '$(srcdir)/'`xmm/mm-broadband-modem-mbim-xmm.c

tests/libmm_test_common_la-test-fixture.lo: tests/test-fixture.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_test_common_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tests/libmm_test_common_la-test-fixture.lo -MD -MP -MF tests/$(DEPDIR)/libmm_test_common_la-test-fixture.Tpo -c -o tests/libmm_test_common_la-test-fixture.lo `test -f 'tests/test-fixture.c' || echo '$(srcdir)/'`tests/test-fixture.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tests/$(DEPDIR)/libmm_test_common_la-test-fixture.Tpo tests/$(DEPDIR)/libmm_test_common_la-test-fixture.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tests/test-fixture.c' object='tests/libmm_test_common_la-test-fixture.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_test_common_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tests/libmm_test_common_la-test-fixture.lo `test -f 'tests/test-fixture.c' || echo '$(srcdir)/'`tests/test-fixture.c

tests/libmm_test_common_la-test-port-context.lo: tests/test-port-context.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_test_common_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tests/libmm_test_common_la-test-port-context.lo -MD -MP -MF tests/$(DEPDIR)/libmm_test_common_la-test-port-context.Tpo -c -o tests/libmm_test_common_la-test-port-context.lo `test -f 'tests/test-port-context.c' || echo '$(srcdir)/'`tests/test-port-context.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tests/$(DEPDIR)/libmm_test_common_la-test-port-context.Tpo tests/$(DEPDIR)/libmm_test_common_la-test-port-context.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tests/test-port-context.c' object='tests/libmm_test_common_la-test-port-context.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_test_common_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tests/libmm_test_common_la-test-port-context.lo `test -f 'tests/test-port-context.c' || echo '$(srcdir)/'`tests/test-port-context.c

tests/libmm_test_common_la-test-helpers.lo: tests/test-helpers.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_test_common_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tests/libmm_test_common_la-test-helpers.lo -MD -MP -MF tests/$(DEPDIR)/libmm_test_common_la-test-helpers.Tpo -c -o tests/libmm_test_common_la-test-helpers.lo `test -f 'tests/test-helpers.c' || echo '$(srcdir)/'`tests/test-helpers.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tests/$(DEPDIR)/libmm_test_common_la-test-helpers.Tpo tests/$(DEPDIR)/libmm_test_common_la-test-helpers.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tests/test-helpers.c' object='tests/libmm_test_common_la-test-helpers.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_test_common_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tests/libmm_test_common_la-test-helpers.lo `test -f 'tests/test-helpers.c' || echo '$(srcdir)/'`tests/test-helpers.c

altair/tests/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.o: altair/tests/test-modem-helpers-altair-lte.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_altair_lte_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT altair/tests/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.o -MD -MP -MF altair/tests/$(DEPDIR)/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.Tpo -c -o altair/tests/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.o `test -f 'altair/tests/test-modem-helpers-altair-lte.c' || echo '$(srcdir)/'`altair/tests/test-modem-helpers-altair-lte.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) altair/tests/$(DEPDIR)/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.Tpo altair/tests/$(DEPDIR)/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='altair/tests/test-modem-helpers-altair-lte.c' object='altair/tests/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_altair_lte_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o altair/tests/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.o `test -f 'altair/tests/test-modem-helpers-altair-lte.c' || echo '$(srcdir)/'`altair/tests/test-modem-helpers-altair-lte.c

altair/tests/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.obj: altair/tests/test-modem-helpers-altair-lte.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_altair_lte_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT altair/tests/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.obj -MD -MP -MF altair/tests/$(DEPDIR)/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.Tpo -c -o altair/tests/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.obj `if test -f 'altair/tests/test-modem-helpers-altair-lte.c'; then $(CYGPATH_W) 'altair/tests/test-modem-helpers-altair-lte.c'; else $(CYGPATH_W) '$(srcdir)/altair/tests/test-modem-helpers-altair-lte.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) altair/tests/$(DEPDIR)/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.Tpo altair/tests/$(DEPDIR)/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='altair/tests/test-modem-helpers-altair-lte.c' object='altair/tests/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_altair_lte_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o altair/tests/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.obj `if test -f 'altair/tests/test-modem-helpers-altair-lte.c'; then $(CYGPATH_W) 'altair/tests/test-modem-helpers-altair-lte.c'; else $(CYGPATH_W) '$(srcdir)/altair/tests/test-modem-helpers-altair-lte.c'; fi`

cinterion/tests/test_modem_helpers_cinterion-test-modem-helpers-cinterion.o: cinterion/tests/test-modem-helpers-cinterion.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_cinterion_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT cinterion/tests/test_modem_helpers_cinterion-test-modem-helpers-cinterion.o -MD -MP -MF cinterion/tests/$(DEPDIR)/test_modem_helpers_cinterion-test-modem-helpers-cinterion.Tpo -c -o cinterion/tests/test_modem_helpers_cinterion-test-modem-helpers-cinterion.o `test -f 'cinterion/tests/test-modem-helpers-cinterion.c' || echo '$(srcdir)/'`cinterion/tests/test-modem-helpers-cinterion.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) cinterion/tests/$(DEPDIR)/test_modem_helpers_cinterion-test-modem-helpers-cinterion.Tpo cinterion/tests/$(DEPDIR)/test_modem_helpers_cinterion-test-modem-helpers-cinterion.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='cinterion/tests/test-modem-helpers-cinterion.c' object='cinterion/tests/test_modem_helpers_cinterion-test-modem-helpers-cinterion.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_cinterion_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o cinterion/tests/test_modem_helpers_cinterion-test-modem-helpers-cinterion.o `test -f 'cinterion/tests/test-modem-helpers-cinterion.c' || echo '$(srcdir)/'`cinterion/tests/test-modem-helpers-cinterion.c

cinterion/tests/test_modem_helpers_cinterion-test-modem-helpers-cinterion.obj: cinterion/tests/test-modem-helpers-cinterion.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_cinterion_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT cinterion/tests/test_modem_helpers_cinterion-test-modem-helpers-cinterion.obj -MD -MP -MF cinterion/tests/$(DEPDIR)/test_modem_helpers_cinterion-test-modem-helpers-cinterion.Tpo -c -o cinterion/tests/test_modem_helpers_cinterion-test-modem-helpers-cinterion.obj `if test -f 'cinterion/tests/test-modem-helpers-cinterion.c'; then $(CYGPATH_W) 'cinterion/tests/test-modem-helpers-cinterion.c'; else $(CYGPATH_W) '$(srcdir)/cinterion/tests/test-modem-helpers-cinterion.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) cinterion/tests/$(DEPDIR)/test_modem_helpers_cinterion-test-modem-helpers-cinterion.Tpo cinterion/tests/$(DEPDIR)/test_modem_helpers_cinterion-test-modem-helpers-cinterion.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='cinterion/tests/test-modem-helpers-cinterion.c' object='cinterion/tests/test_modem_helpers_cinterion-test-modem-helpers-cinterion.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_cinterion_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o cinterion/tests/test_modem_helpers_cinterion-test-modem-helpers-cinterion.obj `if test -f 'cinterion/tests/test-modem-helpers-cinterion.c'; then $(CYGPATH_W) 'cinterion/tests/test-modem-helpers-cinterion.c'; else $(CYGPATH_W) '$(srcdir)/cinterion/tests/test-modem-helpers-cinterion.c'; fi`

huawei/tests/test_modem_helpers_huawei-test-modem-helpers-huawei.o: huawei/tests/test-modem-helpers-huawei.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_huawei_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT huawei/tests/test_modem_helpers_huawei-test-modem-helpers-huawei.o -MD -MP -MF huawei/tests/$(DEPDIR)/test_modem_helpers_huawei-test-modem-helpers-huawei.Tpo -c -o huawei/tests/test_modem_helpers_huawei-test-modem-helpers-huawei.o `test -f 'huawei/tests/test-modem-helpers-huawei.c' || echo '$(srcdir)/'`huawei/tests/test-modem-helpers-huawei.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) huawei/tests/$(DEPDIR)/test_modem_helpers_huawei-test-modem-helpers-huawei.Tpo huawei/tests/$(DEPDIR)/test_modem_helpers_huawei-test-modem-helpers-huawei.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='huawei/tests/test-modem-helpers-huawei.c' object='huawei/tests/test_modem_helpers_huawei-test-modem-helpers-huawei.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_huawei_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o huawei/tests/test_modem_helpers_huawei-test-modem-helpers-huawei.o `test -f 'huawei/tests/test-modem-helpers-huawei.c' || echo '$(srcdir)/'`huawei/tests/test-modem-helpers-huawei.c

huawei/tests/test_modem_helpers_huawei-test-modem-helpers-huawei.obj: huawei/tests/test-modem-helpers-huawei.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_huawei_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT huawei/tests/test_modem_helpers_huawei-test-modem-helpers-huawei.obj -MD -MP -MF huawei/tests/$(DEPDIR)/test_modem_helpers_huawei-test-modem-helpers-huawei.Tpo -c -o huawei/tests/test_modem_helpers_huawei-test-modem-helpers-huawei.obj `if test -f 'huawei/tests/test-modem-helpers-huawei.c'; then $(CYGPATH_W) 'huawei/tests/test-modem-helpers-huawei.c'; else $(CYGPATH_W) '$(srcdir)/huawei/tests/test-modem-helpers-huawei.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) huawei/tests/$(DEPDIR)/test_modem_helpers_huawei-test-modem-helpers-huawei.Tpo huawei/tests/$(DEPDIR)/test_modem_helpers_huawei-test-modem-helpers-huawei.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='huawei/tests/test-modem-helpers-huawei.c' object='huawei/tests/test_modem_helpers_huawei-test-modem-helpers-huawei.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_huawei_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o huawei/tests/test_modem_helpers_huawei-test-modem-helpers-huawei.obj `if test -f 'huawei/tests/test-modem-helpers-huawei.c'; then $(CYGPATH_W) 'huawei/tests/test-modem-helpers-huawei.c'; else $(CYGPATH_W) '$(srcdir)/huawei/tests/test-modem-helpers-huawei.c'; fi`

icera/tests/test_modem_helpers_icera-test-modem-helpers-icera.o: icera/tests/test-modem-helpers-icera.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_icera_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT icera/tests/test_modem_helpers_icera-test-modem-helpers-icera.o -MD -MP -MF icera/tests/$(DEPDIR)/test_modem_helpers_icera-test-modem-helpers-icera.Tpo -c -o icera/tests/test_modem_helpers_icera-test-modem-helpers-icera.o `test -f 'icera/tests/test-modem-helpers-icera.c' || echo '$(srcdir)/'`icera/tests/test-modem-helpers-icera.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) icera/tests/$(DEPDIR)/test_modem_helpers_icera-test-modem-helpers-icera.Tpo icera/tests/$(DEPDIR)/test_modem_helpers_icera-test-modem-helpers-icera.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='icera/tests/test-modem-helpers-icera.c' object='icera/tests/test_modem_helpers_icera-test-modem-helpers-icera.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_icera_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o icera/tests/test_modem_helpers_icera-test-modem-helpers-icera.o `test -f 'icera/tests/test-modem-helpers-icera.c' || echo '$(srcdir)/'`icera/tests/test-modem-helpers-icera.c

icera/tests/test_modem_helpers_icera-test-modem-helpers-icera.obj: icera/tests/test-modem-helpers-icera.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_icera_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT icera/tests/test_modem_helpers_icera-test-modem-helpers-icera.obj -MD -MP -MF icera/tests/$(DEPDIR)/test_modem_helpers_icera-test-modem-helpers-icera.Tpo -c -o icera/tests/test_modem_helpers_icera-test-modem-helpers-icera.obj `if test -f 'icera/tests/test-modem-helpers-icera.c'; then $(CYGPATH_W) 'icera/tests/test-modem-helpers-icera.c'; else $(CYGPATH_W) '$(srcdir)/icera/tests/test-modem-helpers-icera.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) icera/tests/$(DEPDIR)/test_modem_helpers_icera-test-modem-helpers-icera.Tpo icera/tests/$(DEPDIR)/test_modem_helpers_icera-test-modem-helpers-icera.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='icera/tests/test-modem-helpers-icera.c' object='icera/tests/test_modem_helpers_icera-test-modem-helpers-icera.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_icera_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o icera/tests/test_modem_helpers_icera-test-modem-helpers-icera.obj `if test -f 'icera/tests/test-modem-helpers-icera.c'; then $(CYGPATH_W) 'icera/tests/test-modem-helpers-icera.c'; else $(CYGPATH_W) '$(srcdir)/icera/tests/test-modem-helpers-icera.c'; fi`

linktop/tests/test_modem_helpers_linktop-test-modem-helpers-linktop.o: linktop/tests/test-modem-helpers-linktop.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_linktop_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT linktop/tests/test_modem_helpers_linktop-test-modem-helpers-linktop.o -MD -MP -MF linktop/tests/$(DEPDIR)/test_modem_helpers_linktop-test-modem-helpers-linktop.Tpo -c -o linktop/tests/test_modem_helpers_linktop-test-modem-helpers-linktop.o `test -f 'linktop/tests/test-modem-helpers-linktop.c' || echo '$(srcdir)/'`linktop/tests/test-modem-helpers-linktop.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) linktop/tests/$(DEPDIR)/test_modem_helpers_linktop-test-modem-helpers-linktop.Tpo linktop/tests/$(DEPDIR)/test_modem_helpers_linktop-test-modem-helpers-linktop.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='linktop/tests/test-modem-helpers-linktop.c' object='linktop/tests/test_modem_helpers_linktop-test-modem-helpers-linktop.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_linktop_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o linktop/tests/test_modem_helpers_linktop-test-modem-helpers-linktop.o `test -f 'linktop/tests/test-modem-helpers-linktop.c' || echo '$(srcdir)/'`linktop/tests/test-modem-helpers-linktop.c

linktop/tests/test_modem_helpers_linktop-test-modem-helpers-linktop.obj: linktop/tests/test-modem-helpers-linktop.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_linktop_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT linktop/tests/test_modem_helpers_linktop-test-modem-helpers-linktop.obj -MD -MP -MF linktop/tests/$(DEPDIR)/test_modem_helpers_linktop-test-modem-helpers-linktop.Tpo -c -o linktop/tests/test_modem_helpers_linktop-test-modem-helpers-linktop.obj `if test -f 'linktop/tests/test-modem-helpers-linktop.c'; then $(CYGPATH_W) 'linktop/tests/test-modem-helpers-linktop.c'; else $(CYGPATH_W) '$(srcdir)/linktop/tests/test-modem-helpers-linktop.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) linktop/tests/$(DEPDIR)/test_modem_helpers_linktop-test-modem-helpers-linktop.Tpo linktop/tests/$(DEPDIR)/test_modem_helpers_linktop-test-modem-helpers-linktop.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='linktop/tests/test-modem-helpers-linktop.c' object='linktop/tests/test_modem_helpers_linktop-test-modem-helpers-linktop.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_linktop_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o linktop/tests/test_modem_helpers_linktop-test-modem-helpers-linktop.obj `if test -f 'linktop/tests/test-modem-helpers-linktop.c'; then $(CYGPATH_W) 'linktop/tests/test-modem-helpers-linktop.c'; else $(CYGPATH_W) '$(srcdir)/linktop/tests/test-modem-helpers-linktop.c'; fi`

mbm/tests/test_modem_helpers_mbm-test-modem-helpers-mbm.o: mbm/tests/test-modem-helpers-mbm.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_mbm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mbm/tests/test_modem_helpers_mbm-test-modem-helpers-mbm.o -MD -MP -MF mbm/tests/$(DEPDIR)/test_modem_helpers_mbm-test-modem-helpers-mbm.Tpo -c -o mbm/tests/test_modem_helpers_mbm-test-modem-helpers-mbm.o `test -f 'mbm/tests/test-modem-helpers-mbm.c' || echo '$(srcdir)/'`mbm/tests/test-modem-helpers-mbm.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) mbm/tests/$(DEPDIR)/test_modem_helpers_mbm-test-modem-helpers-mbm.Tpo mbm/tests/$(DEPDIR)/test_modem_helpers_mbm-test-modem-helpers-mbm.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mbm/tests/test-modem-helpers-mbm.c' object='mbm/tests/test_modem_helpers_mbm-test-modem-helpers-mbm.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_mbm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mbm/tests/test_modem_helpers_mbm-test-modem-helpers-mbm.o `test -f 'mbm/tests/test-modem-helpers-mbm.c' || echo '$(srcdir)/'`mbm/tests/test-modem-helpers-mbm.c

mbm/tests/test_modem_helpers_mbm-test-modem-helpers-mbm.obj: mbm/tests/test-modem-helpers-mbm.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_mbm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mbm/tests/test_modem_helpers_mbm-test-modem-helpers-mbm.obj -MD -MP -MF mbm/tests/$(DEPDIR)/test_modem_helpers_mbm-test-modem-helpers-mbm.Tpo -c -o mbm/tests/test_modem_helpers_mbm-test-modem-helpers-mbm.obj `if test -f 'mbm/tests/test-modem-helpers-mbm.c'; then $(CYGPATH_W) 'mbm/tests/test-modem-helpers-mbm.c'; else $(CYGPATH_W) '$(srcdir)/mbm/tests/test-modem-helpers-mbm.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) mbm/tests/$(DEPDIR)/test_modem_helpers_mbm-test-modem-helpers-mbm.Tpo mbm/tests/$(DEPDIR)/test_modem_helpers_mbm-test-modem-helpers-mbm.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mbm/tests/test-modem-helpers-mbm.c' object='mbm/tests/test_modem_helpers_mbm-test-modem-helpers-mbm.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_mbm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mbm/tests/test_modem_helpers_mbm-test-modem-helpers-mbm.obj `if test -f 'mbm/tests/test-modem-helpers-mbm.c'; then $(CYGPATH_W) 'mbm/tests/test-modem-helpers-mbm.c'; else $(CYGPATH_W) '$(srcdir)/mbm/tests/test-modem-helpers-mbm.c'; fi`

sierra/tests/test_modem_helpers_sierra-test-modem-helpers-sierra.o: sierra/tests/test-modem-helpers-sierra.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_sierra_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT sierra/tests/test_modem_helpers_sierra-test-modem-helpers-sierra.o -MD -MP -MF sierra/tests/$(DEPDIR)/test_modem_helpers_sierra-test-modem-helpers-sierra.Tpo -c -o sierra/tests/test_modem_helpers_sierra-test-modem-helpers-sierra.o `test -f 'sierra/tests/test-modem-helpers-sierra.c' || echo '$(srcdir)/'`sierra/tests/test-modem-helpers-sierra.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sierra/tests/$(DEPDIR)/test_modem_helpers_sierra-test-modem-helpers-sierra.Tpo sierra/tests/$(DEPDIR)/test_modem_helpers_sierra-test-modem-helpers-sierra.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sierra/tests/test-modem-helpers-sierra.c' object='sierra/tests/test_modem_helpers_sierra-test-modem-helpers-sierra.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_sierra_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o sierra/tests/test_modem_helpers_sierra-test-modem-helpers-sierra.o `test -f 'sierra/tests/test-modem-helpers-sierra.c' || echo '$(srcdir)/'`sierra/tests/test-modem-helpers-sierra.c

sierra/tests/test_modem_helpers_sierra-test-modem-helpers-sierra.obj: sierra/tests/test-modem-helpers-sierra.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_sierra_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT sierra/tests/test_modem_helpers_sierra-test-modem-helpers-sierra.obj -MD -MP -MF sierra/tests/$(DEPDIR)/test_modem_helpers_sierra-test-modem-helpers-sierra.Tpo -c -o sierra/tests/test_modem_helpers_sierra-test-modem-helpers-sierra.obj `if test -f 'sierra/tests/test-modem-helpers-sierra.c'; then $(CYGPATH_W) 'sierra/tests/test-modem-helpers-sierra.c'; else $(CYGPATH_W) '$(srcdir)/sierra/tests/test-modem-helpers-sierra.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) sierra/tests/$(DEPDIR)/test_modem_helpers_sierra-test-modem-helpers-sierra.Tpo sierra/tests/$(DEPDIR)/test_modem_helpers_sierra-test-modem-helpers-sierra.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='sierra/tests/test-modem-helpers-sierra.c' object='sierra/tests/test_modem_helpers_sierra-test-modem-helpers-sierra.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_sierra_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o sierra/tests/test_modem_helpers_sierra-test-modem-helpers-sierra.obj `if test -f 'sierra/tests/test-modem-helpers-sierra.c'; then $(CYGPATH_W) 'sierra/tests/test-modem-helpers-sierra.c'; else $(CYGPATH_W) '$(srcdir)/sierra/tests/test-modem-helpers-sierra.c'; fi`

simtech/tests/test_modem_helpers_simtech-test-modem-helpers-simtech.o: simtech/tests/test-modem-helpers-simtech.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_simtech_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT simtech/tests/test_modem_helpers_simtech-test-modem-helpers-simtech.o -MD -MP -MF simtech/tests/$(DEPDIR)/test_modem_helpers_simtech-test-modem-helpers-simtech.Tpo -c -o simtech/tests/test_modem_helpers_simtech-test-modem-helpers-simtech.o `test -f 'simtech/tests/test-modem-helpers-simtech.c' || echo '$(srcdir)/'`simtech/tests/test-modem-helpers-simtech.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) simtech/tests/$(DEPDIR)/test_modem_helpers_simtech-test-modem-helpers-simtech.Tpo simtech/tests/$(DEPDIR)/test_modem_helpers_simtech-test-modem-helpers-simtech.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='simtech/tests/test-modem-helpers-simtech.c' object='simtech/tests/test_modem_helpers_simtech-test-modem-helpers-simtech.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_simtech_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o simtech/tests/test_modem_helpers_simtech-test-modem-helpers-simtech.o `test -f 'simtech/tests/test-modem-helpers-simtech.c' || echo '$(srcdir)/'`simtech/tests/test-modem-helpers-simtech.c

simtech/tests/test_modem_helpers_simtech-test-modem-helpers-simtech.obj: simtech/tests/test-modem-helpers-simtech.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_simtech_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT simtech/tests/test_modem_helpers_simtech-test-modem-helpers-simtech.obj -MD -MP -MF simtech/tests/$(DEPDIR)/test_modem_helpers_simtech-test-modem-helpers-simtech.Tpo -c -o simtech/tests/test_modem_helpers_simtech-test-modem-helpers-simtech.obj `if test -f 'simtech/tests/test-modem-helpers-simtech.c'; then $(CYGPATH_W) 'simtech/tests/test-modem-helpers-simtech.c'; else $(CYGPATH_W) '$(srcdir)/simtech/tests/test-modem-helpers-simtech.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) simtech/tests/$(DEPDIR)/test_modem_helpers_simtech-test-modem-helpers-simtech.Tpo simtech/tests/$(DEPDIR)/test_modem_helpers_simtech-test-modem-helpers-simtech.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='simtech/tests/test-modem-helpers-simtech.c' object='simtech/tests/test_modem_helpers_simtech-test-modem-helpers-simtech.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_simtech_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o simtech/tests/test_modem_helpers_simtech-test-modem-helpers-simtech.obj `if test -f 'simtech/tests/test-modem-helpers-simtech.c'; then $(CYGPATH_W) 'simtech/tests/test-modem-helpers-simtech.c'; else $(CYGPATH_W) '$(srcdir)/simtech/tests/test-modem-helpers-simtech.c'; fi`

telit/tests/test_modem_helpers_telit-test-mm-modem-helpers-telit.o: telit/tests/test-mm-modem-helpers-telit.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_telit_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT telit/tests/test_modem_helpers_telit-test-mm-modem-helpers-telit.o -MD -MP -MF telit/tests/$(DEPDIR)/test_modem_helpers_telit-test-mm-modem-helpers-telit.Tpo -c -o telit/tests/test_modem_helpers_telit-test-mm-modem-helpers-telit.o `test -f 'telit/tests/test-mm-modem-helpers-telit.c' || echo '$(srcdir)/'`telit/tests/test-mm-modem-helpers-telit.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) telit/tests/$(DEPDIR)/test_modem_helpers_telit-test-mm-modem-helpers-telit.Tpo telit/tests/$(DEPDIR)/test_modem_helpers_telit-test-mm-modem-helpers-telit.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='telit/tests/test-mm-modem-helpers-telit.c' object='telit/tests/test_modem_helpers_telit-test-mm-modem-helpers-telit.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_telit_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o telit/tests/test_modem_helpers_telit-test-mm-modem-helpers-telit.o `test -f 'telit/tests/test-mm-modem-helpers-telit.c' || echo '$(srcdir)/'`telit/tests/test-mm-modem-helpers-telit.c

telit/tests/test_modem_helpers_telit-test-mm-modem-helpers-telit.obj: telit/tests/test-mm-modem-helpers-telit.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_telit_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT telit/tests/test_modem_helpers_telit-test-mm-modem-helpers-telit.obj -MD -MP -MF telit/tests/$(DEPDIR)/test_modem_helpers_telit-test-mm-modem-helpers-telit.Tpo -c -o telit/tests/test_modem_helpers_telit-test-mm-modem-helpers-telit.obj `if test -f 'telit/tests/test-mm-modem-helpers-telit.c'; then $(CYGPATH_W) 'telit/tests/test-mm-modem-helpers-telit.c'; else $(CYGPATH_W) '$(srcdir)/telit/tests/test-mm-modem-helpers-telit.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) telit/tests/$(DEPDIR)/test_modem_helpers_telit-test-mm-modem-helpers-telit.Tpo telit/tests/$(DEPDIR)/test_modem_helpers_telit-test-mm-modem-helpers-telit.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='telit/tests/test-mm-modem-helpers-telit.c' object='telit/tests/test_modem_helpers_telit-test-mm-modem-helpers-telit.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_telit_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o telit/tests/test_modem_helpers_telit-test-mm-modem-helpers-telit.obj `if test -f 'telit/tests/test-mm-modem-helpers-telit.c'; then $(CYGPATH_W) 'telit/tests/test-mm-modem-helpers-telit.c'; else $(CYGPATH_W) '$(srcdir)/telit/tests/test-mm-modem-helpers-telit.c'; fi`

thuraya/tests/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.o: thuraya/tests/test-mm-modem-helpers-thuraya.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_thuraya_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT thuraya/tests/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.o -MD -MP -MF thuraya/tests/$(DEPDIR)/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.Tpo -c -o thuraya/tests/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.o `test -f 'thuraya/tests/test-mm-modem-helpers-thuraya.c' || echo '$(srcdir)/'`thuraya/tests/test-mm-modem-helpers-thuraya.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) thuraya/tests/$(DEPDIR)/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.Tpo thuraya/tests/$(DEPDIR)/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='thuraya/tests/test-mm-modem-helpers-thuraya.c' object='thuraya/tests/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_thuraya_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o thuraya/tests/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.o `test -f 'thuraya/tests/test-mm-modem-helpers-thuraya.c' || echo '$(srcdir)/'`thuraya/tests/test-mm-modem-helpers-thuraya.c

thuraya/tests/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.obj: thuraya/tests/test-mm-modem-helpers-thuraya.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_thuraya_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT thuraya/tests/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.obj -MD -MP -MF thuraya/tests/$(DEPDIR)/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.Tpo -c -o thuraya/tests/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.obj `if test -f 'thuraya/tests/test-mm-modem-helpers-thuraya.c'; then $(CYGPATH_W) 'thuraya/tests/test-mm-modem-helpers-thuraya.c'; else $(CYGPATH_W) '$(srcdir)/thuraya/tests/test-mm-modem-helpers-thuraya.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) thuraya/tests/$(DEPDIR)/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.Tpo thuraya/tests/$(DEPDIR)/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='thuraya/tests/test-mm-modem-helpers-thuraya.c' object='thuraya/tests/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_thuraya_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o thuraya/tests/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.obj `if test -f 'thuraya/tests/test-mm-modem-helpers-thuraya.c'; then $(CYGPATH_W) 'thuraya/tests/test-mm-modem-helpers-thuraya.c'; else $(CYGPATH_W) '$(srcdir)/thuraya/tests/test-mm-modem-helpers-thuraya.c'; fi`

ublox/tests/test_modem_helpers_ublox-test-modem-helpers-ublox.o: ublox/tests/test-modem-helpers-ublox.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_ublox_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ublox/tests/test_modem_helpers_ublox-test-modem-helpers-ublox.o -MD -MP -MF ublox/tests/$(DEPDIR)/test_modem_helpers_ublox-test-modem-helpers-ublox.Tpo -c -o ublox/tests/test_modem_helpers_ublox-test-modem-helpers-ublox.o `test -f 'ublox/tests/test-modem-helpers-ublox.c' || echo '$(srcdir)/'`ublox/tests/test-modem-helpers-ublox.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ublox/tests/$(DEPDIR)/test_modem_helpers_ublox-test-modem-helpers-ublox.Tpo ublox/tests/$(DEPDIR)/test_modem_helpers_ublox-test-modem-helpers-ublox.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ublox/tests/test-modem-helpers-ublox.c' object='ublox/tests/test_modem_helpers_ublox-test-modem-helpers-ublox.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_ublox_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ublox/tests/test_modem_helpers_ublox-test-modem-helpers-ublox.o `test -f 'ublox/tests/test-modem-helpers-ublox.c' || echo '$(srcdir)/'`ublox/tests/test-modem-helpers-ublox.c

ublox/tests/test_modem_helpers_ublox-test-modem-helpers-ublox.obj: ublox/tests/test-modem-helpers-ublox.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_ublox_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ublox/tests/test_modem_helpers_ublox-test-modem-helpers-ublox.obj -MD -MP -MF ublox/tests/$(DEPDIR)/test_modem_helpers_ublox-test-modem-helpers-ublox.Tpo -c -o ublox/tests/test_modem_helpers_ublox-test-modem-helpers-ublox.obj `if test -f 'ublox/tests/test-modem-helpers-ublox.c'; then $(CYGPATH_W) 'ublox/tests/test-modem-helpers-ublox.c'; else $(CYGPATH_W) '$(srcdir)/ublox/tests/test-modem-helpers-ublox.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ublox/tests/$(DEPDIR)/test_modem_helpers_ublox-test-modem-helpers-ublox.Tpo ublox/tests/$(DEPDIR)/test_modem_helpers_ublox-test-modem-helpers-ublox.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ublox/tests/test-modem-helpers-ublox.c' object='ublox/tests/test_modem_helpers_ublox-test-modem-helpers-ublox.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_ublox_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ublox/tests/test_modem_helpers_ublox-test-modem-helpers-ublox.obj `if test -f 'ublox/tests/test-modem-helpers-ublox.c'; then $(CYGPATH_W) 'ublox/tests/test-modem-helpers-ublox.c'; else $(CYGPATH_W) '$(srcdir)/ublox/tests/test-modem-helpers-ublox.c'; fi`

xmm/tests/test_modem_helpers_xmm-test-modem-helpers-xmm.o: xmm/tests/test-modem-helpers-xmm.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_xmm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT xmm/tests/test_modem_helpers_xmm-test-modem-helpers-xmm.o -MD -MP -MF xmm/tests/$(DEPDIR)/test_modem_helpers_xmm-test-modem-helpers-xmm.Tpo -c -o xmm/tests/test_modem_helpers_xmm-test-modem-helpers-xmm.o `test -f 'xmm/tests/test-modem-helpers-xmm.c' || echo '$(srcdir)/'`xmm/tests/test-modem-helpers-xmm.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) xmm/tests/$(DEPDIR)/test_modem_helpers_xmm-test-modem-helpers-xmm.Tpo xmm/tests/$(DEPDIR)/test_modem_helpers_xmm-test-modem-helpers-xmm.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='xmm/tests/test-modem-helpers-xmm.c' object='xmm/tests/test_modem_helpers_xmm-test-modem-helpers-xmm.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_xmm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o xmm/tests/test_modem_helpers_xmm-test-modem-helpers-xmm.o `test -f 'xmm/tests/test-modem-helpers-xmm.c' || echo '$(srcdir)/'`xmm/tests/test-modem-helpers-xmm.c

xmm/tests/test_modem_helpers_xmm-test-modem-helpers-xmm.obj: xmm/tests/test-modem-helpers-xmm.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_xmm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT xmm/tests/test_modem_helpers_xmm-test-modem-helpers-xmm.obj -MD -MP -MF xmm/tests/$(DEPDIR)/test_modem_helpers_xmm-test-modem-helpers-xmm.Tpo -c -o xmm/tests/test_modem_helpers_xmm-test-modem-helpers-xmm.obj `if test -f 'xmm/tests/test-modem-helpers-xmm.c'; then $(CYGPATH_W) 'xmm/tests/test-modem-helpers-xmm.c'; else $(CYGPATH_W) '$(srcdir)/xmm/tests/test-modem-helpers-xmm.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) xmm/tests/$(DEPDIR)/test_modem_helpers_xmm-test-modem-helpers-xmm.Tpo xmm/tests/$(DEPDIR)/test_modem_helpers_xmm-test-modem-helpers-xmm.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='xmm/tests/test-modem-helpers-xmm.c' object='xmm/tests/test_modem_helpers_xmm-test-modem-helpers-xmm.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_modem_helpers_xmm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o xmm/tests/test_modem_helpers_xmm-test-modem-helpers-xmm.obj `if test -f 'xmm/tests/test-modem-helpers-xmm.c'; then $(CYGPATH_W) 'xmm/tests/test-modem-helpers-xmm.c'; else $(CYGPATH_W) '$(srcdir)/xmm/tests/test-modem-helpers-xmm.c'; fi`

generic/tests/test_service_generic-test-service-generic.o: generic/tests/test-service-generic.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_service_generic_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT generic/tests/test_service_generic-test-service-generic.o -MD -MP -MF generic/tests/$(DEPDIR)/test_service_generic-test-service-generic.Tpo -c -o generic/tests/test_service_generic-test-service-generic.o `test -f 'generic/tests/test-service-generic.c' || echo '$(srcdir)/'`generic/tests/test-service-generic.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) generic/tests/$(DEPDIR)/test_service_generic-test-service-generic.Tpo generic/tests/$(DEPDIR)/test_service_generic-test-service-generic.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='generic/tests/test-service-generic.c' object='generic/tests/test_service_generic-test-service-generic.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_service_generic_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o generic/tests/test_service_generic-test-service-generic.o `test -f 'generic/tests/test-service-generic.c' || echo '$(srcdir)/'`generic/tests/test-service-generic.c

generic/tests/test_service_generic-test-service-generic.obj: generic/tests/test-service-generic.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_service_generic_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT generic/tests/test_service_generic-test-service-generic.obj -MD -MP -MF generic/tests/$(DEPDIR)/test_service_generic-test-service-generic.Tpo -c -o generic/tests/test_service_generic-test-service-generic.obj `if test -f 'generic/tests/test-service-generic.c'; then $(CYGPATH_W) 'generic/tests/test-service-generic.c'; else $(CYGPATH_W) '$(srcdir)/generic/tests/test-service-generic.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) generic/tests/$(DEPDIR)/test_service_generic-test-service-generic.Tpo generic/tests/$(DEPDIR)/test_service_generic-test-service-generic.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='generic/tests/test-service-generic.c' object='generic/tests/test_service_generic-test-service-generic.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_service_generic_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o generic/tests/test_service_generic-test-service-generic.obj `if test -f 'generic/tests/test-service-generic.c'; then $(CYGPATH_W) 'generic/tests/test-service-generic.c'; else $(CYGPATH_W) '$(srcdir)/generic/tests/test-service-generic.c'; fi`

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
	-rm -rf NL668/.libs NL668/_libs
	-rm -rf altair/.libs altair/_libs
	-rm -rf anydata/.libs anydata/_libs
	-rm -rf broadmobi/.libs broadmobi/_libs
	-rm -rf cinterion/.libs cinterion/_libs
	-rm -rf dell/.libs dell/_libs
	-rm -rf dlink/.libs dlink/_libs
	-rm -rf foxconn/.libs foxconn/_libs
	-rm -rf generic/.libs generic/_libs
	-rm -rf haier/.libs haier/_libs
	-rm -rf huawei/.libs huawei/_libs
	-rm -rf icera/.libs icera/_libs
	-rm -rf iridium/.libs iridium/_libs
	-rm -rf linktop/.libs linktop/_libs
	-rm -rf longcheer/.libs longcheer/_libs
	-rm -rf mbm/.libs mbm/_libs
	-rm -rf me3630/.libs me3630/_libs
	-rm -rf motorola/.libs motorola/_libs
	-rm -rf mtk/.libs mtk/_libs
	-rm -rf nokia/.libs nokia/_libs
	-rm -rf novatel/.libs novatel/_libs
	-rm -rf option/.libs option/_libs
	-rm -rf pantech/.libs pantech/_libs
	-rm -rf quectel/.libs quectel/_libs
	-rm -rf samsung/.libs samsung/_libs
	-rm -rf sierra/.libs sierra/_libs
	-rm -rf simtech/.libs simtech/_libs
	-rm -rf telit/.libs telit/_libs
	-rm -rf tests/.libs tests/_libs
	-rm -rf thuraya/.libs thuraya/_libs
	-rm -rf tplink/.libs tplink/_libs
	-rm -rf ublox/.libs ublox/_libs
	-rm -rf via/.libs via/_libs
	-rm -rf wavecom/.libs wavecom/_libs
	-rm -rf x22x/.libs x22x/_libs
	-rm -rf xmm/.libs xmm/_libs
	-rm -rf zte/.libs zte/_libs
install-dist_pkgdataDATA: $(dist_pkgdata_DATA)
	@$(NORMAL_INSTALL)
	@list='$(dist_pkgdata_DATA)'; test -n "$(pkgdatadir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgdatadir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgdatadir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(pkgdatadir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(pkgdatadir)" || exit $$?; \
	done

uninstall-dist_pkgdataDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(dist_pkgdata_DATA)'; test -n "$(pkgdatadir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgdatadir)'; $(am__uninstall_files_from_dir)
install-dist_udevrulesDATA: $(dist_udevrules_DATA)
	@$(NORMAL_INSTALL)
	@list='$(dist_udevrules_DATA)'; test -n "$(udevrulesdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(udevrulesdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(udevrulesdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(udevrulesdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(udevrulesdir)" || exit $$?; \
	done

uninstall-dist_udevrulesDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(dist_udevrules_DATA)'; test -n "$(udevrulesdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(udevrulesdir)'; $(am__uninstall_files_from_dir)

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
	$(MAKE) $(AM_MAKEFLAGS) check-local
check: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) check-am
all-am: Makefile $(PROGRAMS) $(LTLIBRARIES) $(DATA)
installdirs:
	for dir in "$(DESTDIR)$(pkglibdir)" "$(DESTDIR)$(pkgdatadir)" "$(DESTDIR)$(udevrulesdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f NL668/$(DEPDIR)/$(am__dirstamp)
	-rm -f NL668/$(am__dirstamp)
	-rm -f altair/$(DEPDIR)/$(am__dirstamp)
	-rm -f altair/$(am__dirstamp)
	-rm -f altair/tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f altair/tests/$(am__dirstamp)
	-rm -f anydata/$(DEPDIR)/$(am__dirstamp)
	-rm -f anydata/$(am__dirstamp)
	-rm -f broadmobi/$(DEPDIR)/$(am__dirstamp)
	-rm -f broadmobi/$(am__dirstamp)
	-rm -f cinterion/$(DEPDIR)/$(am__dirstamp)
	-rm -f cinterion/$(am__dirstamp)
	-rm -f cinterion/tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f cinterion/tests/$(am__dirstamp)
	-rm -f dell/$(DEPDIR)/$(am__dirstamp)
	-rm -f dell/$(am__dirstamp)
	-rm -f dlink/$(DEPDIR)/$(am__dirstamp)
	-rm -f dlink/$(am__dirstamp)
	-rm -f foxconn/$(DEPDIR)/$(am__dirstamp)
	-rm -f foxconn/$(am__dirstamp)
	-rm -f generic/$(DEPDIR)/$(am__dirstamp)
	-rm -f generic/$(am__dirstamp)
	-rm -f generic/tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f generic/tests/$(am__dirstamp)
	-rm -f haier/$(DEPDIR)/$(am__dirstamp)
	-rm -f haier/$(am__dirstamp)
	-rm -f huawei/$(DEPDIR)/$(am__dirstamp)
	-rm -f huawei/$(am__dirstamp)
	-rm -f huawei/tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f huawei/tests/$(am__dirstamp)
	-rm -f icera/$(DEPDIR)/$(am__dirstamp)
	-rm -f icera/$(am__dirstamp)
	-rm -f icera/tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f icera/tests/$(am__dirstamp)
	-rm -f iridium/$(DEPDIR)/$(am__dirstamp)
	-rm -f iridium/$(am__dirstamp)
	-rm -f linktop/$(DEPDIR)/$(am__dirstamp)
	-rm -f linktop/$(am__dirstamp)
	-rm -f linktop/tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f linktop/tests/$(am__dirstamp)
	-rm -f longcheer/$(DEPDIR)/$(am__dirstamp)
	-rm -f longcheer/$(am__dirstamp)
	-rm -f mbm/$(DEPDIR)/$(am__dirstamp)
	-rm -f mbm/$(am__dirstamp)
	-rm -f mbm/tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f mbm/tests/$(am__dirstamp)
	-rm -f me3630/$(DEPDIR)/$(am__dirstamp)
	-rm -f me3630/$(am__dirstamp)
	-rm -f motorola/$(DEPDIR)/$(am__dirstamp)
	-rm -f motorola/$(am__dirstamp)
	-rm -f mtk/$(DEPDIR)/$(am__dirstamp)
	-rm -f mtk/$(am__dirstamp)
	-rm -f nokia/$(DEPDIR)/$(am__dirstamp)
	-rm -f nokia/$(am__dirstamp)
	-rm -f novatel/$(DEPDIR)/$(am__dirstamp)
	-rm -f novatel/$(am__dirstamp)
	-rm -f option/$(DEPDIR)/$(am__dirstamp)
	-rm -f option/$(am__dirstamp)
	-rm -f pantech/$(DEPDIR)/$(am__dirstamp)
	-rm -f pantech/$(am__dirstamp)
	-rm -f quectel/$(DEPDIR)/$(am__dirstamp)
	-rm -f quectel/$(am__dirstamp)
	-rm -f samsung/$(DEPDIR)/$(am__dirstamp)
	-rm -f samsung/$(am__dirstamp)
	-rm -f sierra/$(DEPDIR)/$(am__dirstamp)
	-rm -f sierra/$(am__dirstamp)
	-rm -f sierra/tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f sierra/tests/$(am__dirstamp)
	-rm -f simtech/$(DEPDIR)/$(am__dirstamp)
	-rm -f simtech/$(am__dirstamp)
	-rm -f simtech/tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f simtech/tests/$(am__dirstamp)
	-rm -f telit/$(DEPDIR)/$(am__dirstamp)
	-rm -f telit/$(am__dirstamp)
	-rm -f telit/tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f telit/tests/$(am__dirstamp)
	-rm -f tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f tests/$(am__dirstamp)
	-rm -f thuraya/$(DEPDIR)/$(am__dirstamp)
	-rm -f thuraya/$(am__dirstamp)
	-rm -f thuraya/tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f thuraya/tests/$(am__dirstamp)
	-rm -f tplink/$(DEPDIR)/$(am__dirstamp)
	-rm -f tplink/$(am__dirstamp)
	-rm -f ublox/$(DEPDIR)/$(am__dirstamp)
	-rm -f ublox/$(am__dirstamp)
	-rm -f ublox/tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f ublox/tests/$(am__dirstamp)
	-rm -f via/$(DEPDIR)/$(am__dirstamp)
	-rm -f via/$(am__dirstamp)
	-rm -f wavecom/$(DEPDIR)/$(am__dirstamp)
	-rm -f wavecom/$(am__dirstamp)
	-rm -f x22x/$(DEPDIR)/$(am__dirstamp)
	-rm -f x22x/$(am__dirstamp)
	-rm -f xmm/$(DEPDIR)/$(am__dirstamp)
	-rm -f xmm/$(am__dirstamp)
	-rm -f xmm/tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f xmm/tests/$(am__dirstamp)
	-rm -f zte/$(DEPDIR)/$(am__dirstamp)
	-rm -f zte/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
	-test -z "$(BUILT_SOURCES)" || rm -f $(BUILT_SOURCES)
clean: clean-am

clean-am: clean-generic clean-libtool clean-noinstLTLIBRARIES \
	clean-noinstPROGRAMS clean-pkglibLTLIBRARIES mostlyclean-am

distclean: distclean-am
		-rm -f NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-bearer-fibocom-ecm.Plo
	-rm -f NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-fibocom.Plo
	-rm -f NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-mbim-fibocom.Plo
	-rm -f NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-mbim-xmm-fibocom.Plo
	-rm -f NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-plugin-fibocom.Plo
	-rm -f altair/$(DEPDIR)/libhelpers_altair_lte_la-mm-modem-helpers-altair-lte.Plo
	-rm -f altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-broadband-bearer-altair-lte.Plo
	-rm -f altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-broadband-modem-altair-lte.Plo
	-rm -f altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-plugin-altair-lte.Plo
	-rm -f altair/tests/$(DEPDIR)/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.Po
	-rm -f anydata/$(DEPDIR)/libmm_plugin_anydata_la-mm-broadband-modem-anydata.Plo
	-rm -f anydata/$(DEPDIR)/libmm_plugin_anydata_la-mm-plugin-anydata.Plo
	-rm -f broadmobi/$(DEPDIR)/libmm_plugin_broadmobi_la-mm-plugin-broadmobi.Plo
	-rm -f cinterion/$(DEPDIR)/libhelpers_cinterion_la-mm-modem-helpers-cinterion.Plo
	-rm -f cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-bearer-cinterion.Plo
	-rm -f cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-modem-cinterion.Plo
	-rm -f cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-modem-qmi-cinterion.Plo
	-rm -f cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-plugin-cinterion.Plo
	-rm -f cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-shared-cinterion.Plo
	-rm -f cinterion/tests/$(DEPDIR)/test_modem_helpers_cinterion-test-modem-helpers-cinterion.Po
	-rm -f dell/$(DEPDIR)/libmm_plugin_dell_la-mm-plugin-dell.Plo
	-rm -f dlink/$(DEPDIR)/libmm_plugin_dlink_la-mm-plugin-dlink.Plo
	-rm -f foxconn/$(DEPDIR)/libmm_plugin_foxconn_la-mm-plugin-foxconn.Plo
	-rm -f foxconn/$(DEPDIR)/libmm_shared_foxconn_la-mm-broadband-modem-foxconn-t77w968.Plo
	-rm -f foxconn/$(DEPDIR)/libmm_shared_foxconn_la-mm-shared.Plo
	-rm -f generic/$(DEPDIR)/libmm_plugin_generic_la-mm-plugin-generic.Plo
	-rm -f generic/tests/$(DEPDIR)/test_service_generic-test-service-generic.Po
	-rm -f haier/$(DEPDIR)/libmm_plugin_haier_la-mm-plugin-haier.Plo
	-rm -f huawei/$(DEPDIR)/libhelpers_huawei_la-mm-modem-helpers-huawei.Plo
	-rm -f huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-broadband-bearer-huawei.Plo
	-rm -f huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-broadband-modem-huawei.Plo
	-rm -f huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-plugin-huawei.Plo
	-rm -f huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-sim-huawei.Plo
	-rm -f huawei/tests/$(DEPDIR)/test_modem_helpers_huawei-test-modem-helpers-huawei.Po
	-rm -f icera/$(DEPDIR)/libhelpers_icera_la-mm-modem-helpers-icera.Plo
	-rm -f icera/$(DEPDIR)/libmm_shared_icera_la-mm-broadband-bearer-icera.Plo
	-rm -f icera/$(DEPDIR)/libmm_shared_icera_la-mm-broadband-modem-icera.Plo
	-rm -f icera/$(DEPDIR)/libmm_shared_icera_la-mm-shared.Plo
	-rm -f icera/tests/$(DEPDIR)/test_modem_helpers_icera-test-modem-helpers-icera.Po
	-rm -f iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-bearer-iridium.Plo
	-rm -f iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-broadband-modem-iridium.Plo
	-rm -f iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-plugin-iridium.Plo
	-rm -f iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-sim-iridium.Plo
	-rm -f linktop/$(DEPDIR)/libhelpers_linktop_la-mm-modem-helpers-linktop.Plo
	-rm -f linktop/$(DEPDIR)/libmm_plugin_linktop_la-mm-broadband-modem-linktop.Plo
	-rm -f linktop/$(DEPDIR)/libmm_plugin_linktop_la-mm-plugin-linktop.Plo
	-rm -f linktop/tests/$(DEPDIR)/test_modem_helpers_linktop-test-modem-helpers-linktop.Po
	-rm -f longcheer/$(DEPDIR)/libmm_plugin_longcheer_la-mm-broadband-modem-longcheer.Plo
	-rm -f longcheer/$(DEPDIR)/libmm_plugin_longcheer_la-mm-plugin-longcheer.Plo
	-rm -f mbm/$(DEPDIR)/libhelpers_mbm_la-mm-modem-helpers-mbm.Plo
	-rm -f mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-broadband-bearer-mbm.Plo
	-rm -f mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-broadband-modem-mbm.Plo
	-rm -f mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-plugin-mbm.Plo
	-rm -f mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-sim-mbm.Plo
	-rm -f mbm/tests/$(DEPDIR)/test_modem_helpers_mbm-test-modem-helpers-mbm.Po
	-rm -f me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-broadband-bearer-me3630.Plo
	-rm -f me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-broadband-modem-me3630.Plo
	-rm -f me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-common-me3630.Plo
	-rm -f me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-plugin-me3630.Plo
	-rm -f motorola/$(DEPDIR)/libmm_plugin_motorola_la-mm-broadband-modem-motorola.Plo
	-rm -f motorola/$(DEPDIR)/libmm_plugin_motorola_la-mm-plugin-motorola.Plo
	-rm -f mtk/$(DEPDIR)/libmm_plugin_mtk_la-mm-broadband-modem-mtk.Plo
	-rm -f mtk/$(DEPDIR)/libmm_plugin_mtk_la-mm-plugin-mtk.Plo
	-rm -f nokia/$(DEPDIR)/libmm_plugin_nokia_icera_la-mm-plugin-nokia-icera.Plo
	-rm -f nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-broadband-modem-nokia.Plo
	-rm -f nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-plugin-nokia.Plo
	-rm -f nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-sim-nokia.Plo
	-rm -f novatel/$(DEPDIR)/libmm_plugin_novatel_la-mm-plugin-novatel.Plo
	-rm -f novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-broadband-bearer-novatel-lte.Plo
	-rm -f novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-broadband-modem-novatel-lte.Plo
	-rm -f novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-plugin-novatel-lte.Plo
	-rm -f novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-sim-novatel-lte.Plo
	-rm -f novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-broadband-modem-novatel.Plo
	-rm -f novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-common-novatel.Plo
	-rm -f novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-shared.Plo
	-rm -f option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-broadband-bearer-hso.Plo
	-rm -f option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-broadband-modem-hso.Plo
	-rm -f option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-plugin-hso.Plo
	-rm -f option/$(DEPDIR)/libmm_plugin_option_la-mm-plugin-option.Plo
	-rm -f option/$(DEPDIR)/libmm_shared_option_la-mm-broadband-modem-option.Plo
	-rm -f option/$(DEPDIR)/libmm_shared_option_la-mm-shared.Plo
	-rm -f pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-broadband-modem-pantech.Plo
	-rm -f pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-plugin-pantech.Plo
	-rm -f pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-sim-pantech.Plo
	-rm -f quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-broadband-modem-qmi-quectel.Plo
	-rm -f quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-broadband-modem-quectel.Plo
	-rm -f quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-plugin-quectel.Plo
	-rm -f quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-shared-quectel.Plo
	-rm -f samsung/$(DEPDIR)/libmm_plugin_samsung_la-mm-broadband-modem-samsung.Plo
	-rm -f samsung/$(DEPDIR)/libmm_plugin_samsung_la-mm-plugin-samsung.Plo
	-rm -f sierra/$(DEPDIR)/libhelpers_sierra_la-mm-modem-helpers-sierra.Plo
	-rm -f sierra/$(DEPDIR)/libmm_plugin_sierra_la-mm-plugin-sierra.Plo
	-rm -f sierra/$(DEPDIR)/libmm_plugin_sierra_legacy_la-mm-broadband-modem-sierra-icera.Plo
	-rm -f sierra/$(DEPDIR)/libmm_plugin_sierra_legacy_la-mm-plugin-sierra-legacy.Plo
	-rm -f sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-broadband-bearer-sierra.Plo
	-rm -f sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-broadband-modem-sierra.Plo
	-rm -f sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-common-sierra.Plo
	-rm -f sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-shared.Plo
	-rm -f sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-sim-sierra.Plo
	-rm -f sierra/tests/$(DEPDIR)/test_modem_helpers_sierra-test-modem-helpers-sierra.Po
	-rm -f simtech/$(DEPDIR)/libhelpers_simtech_la-mm-modem-helpers-simtech.Plo
	-rm -f simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-broadband-modem-qmi-simtech.Plo
	-rm -f simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-broadband-modem-simtech.Plo
	-rm -f simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-plugin-simtech.Plo
	-rm -f simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-shared-simtech.Plo
	-rm -f simtech/tests/$(DEPDIR)/test_modem_helpers_simtech-test-modem-helpers-simtech.Po
	-rm -f telit/$(DEPDIR)/libhelpers_telit_la-mm-modem-helpers-telit.Plo
	-rm -f telit/$(DEPDIR)/libhelpers_telit_la-mm-telit-enums-types.Plo
	-rm -f telit/$(DEPDIR)/libmm_plugin_telit_la-mm-plugin-telit.Plo
	-rm -f telit/$(DEPDIR)/libmm_shared_telit_la-mm-broadband-modem-mbim-telit.Plo
	-rm -f telit/$(DEPDIR)/libmm_shared_telit_la-mm-broadband-modem-telit.Plo
	-rm -f telit/$(DEPDIR)/libmm_shared_telit_la-mm-common-telit.Plo
	-rm -f telit/$(DEPDIR)/libmm_shared_telit_la-mm-shared-telit.Plo
	-rm -f telit/$(DEPDIR)/libmm_shared_telit_la-mm-shared.Plo
	-rm -f telit/tests/$(DEPDIR)/test_modem_helpers_telit-test-mm-modem-helpers-telit.Po
	-rm -f tests/$(DEPDIR)/libmm_test_common_la-test-fixture.Plo
	-rm -f tests/$(DEPDIR)/libmm_test_common_la-test-helpers.Plo
	-rm -f tests/$(DEPDIR)/libmm_test_common_la-test-port-context.Plo
	-rm -f tests/$(DEPDIR)/test-keyfiles.Po
	-rm -f tests/$(DEPDIR)/test-udev-rules.Po
	-rm -f thuraya/$(DEPDIR)/libhelpers_thuraya_la-mm-modem-helpers-thuraya.Plo
	-rm -f thuraya/$(DEPDIR)/libmm_plugin_thuraya_la-mm-broadband-modem-thuraya.Plo
	-rm -f thuraya/$(DEPDIR)/libmm_plugin_thuraya_la-mm-plugin-thuraya.Plo
	-rm -f thuraya/tests/$(DEPDIR)/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.Po
	-rm -f tplink/$(DEPDIR)/libmm_plugin_tplink_la-mm-plugin-tplink.Plo
	-rm -f ublox/$(DEPDIR)/libhelpers_ublox_la-mm-modem-helpers-ublox.Plo
	-rm -f ublox/$(DEPDIR)/libhelpers_ublox_la-mm-ublox-enums-types.Plo
	-rm -f ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-broadband-bearer-ublox.Plo
	-rm -f ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-broadband-modem-ublox.Plo
	-rm -f ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-plugin-ublox.Plo
	-rm -f ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-sim-ublox.Plo
	-rm -f ublox/tests/$(DEPDIR)/test_modem_helpers_ublox-test-modem-helpers-ublox.Po
	-rm -f via/$(DEPDIR)/libmm_plugin_via_la-mm-broadband-modem-via.Plo
	-rm -f via/$(DEPDIR)/libmm_plugin_via_la-mm-plugin-via.Plo
	-rm -f wavecom/$(DEPDIR)/libmm_plugin_wavecom_la-mm-broadband-modem-wavecom.Plo
	-rm -f wavecom/$(DEPDIR)/libmm_plugin_wavecom_la-mm-plugin-wavecom.Plo
	-rm -f x22x/$(DEPDIR)/libmm_plugin_x22x_la-mm-broadband-modem-x22x.Plo
	-rm -f x22x/$(DEPDIR)/libmm_plugin_x22x_la-mm-plugin-x22x.Plo
	-rm -f xmm/$(DEPDIR)/libhelpers_xmm_la-mm-modem-helpers-xmm.Plo
	-rm -f xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-broadband-modem-mbim-xmm.Plo
	-rm -f xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-broadband-modem-xmm.Plo
	-rm -f xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-shared-xmm.Plo
	-rm -f xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-shared.Plo
	-rm -f xmm/tests/$(DEPDIR)/test_modem_helpers_xmm-test-modem-helpers-xmm.Po
	-rm -f zte/$(DEPDIR)/libmm_plugin_zte_la-mm-broadband-modem-zte-icera.Plo
	-rm -f zte/$(DEPDIR)/libmm_plugin_zte_la-mm-broadband-modem-zte.Plo
	-rm -f zte/$(DEPDIR)/libmm_plugin_zte_la-mm-common-zte.Plo
	-rm -f zte/$(DEPDIR)/libmm_plugin_zte_la-mm-plugin-zte.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-dist_pkgdataDATA install-dist_udevrulesDATA

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-pkglibLTLIBRARIES

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-bearer-fibocom-ecm.Plo
	-rm -f NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-fibocom.Plo
	-rm -f NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-mbim-fibocom.Plo
	-rm -f NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-broadband-modem-mbim-xmm-fibocom.Plo
	-rm -f NL668/$(DEPDIR)/libmm_plugin_NL668_la-mm-plugin-fibocom.Plo
	-rm -f altair/$(DEPDIR)/libhelpers_altair_lte_la-mm-modem-helpers-altair-lte.Plo
	-rm -f altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-broadband-bearer-altair-lte.Plo
	-rm -f altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-broadband-modem-altair-lte.Plo
	-rm -f altair/$(DEPDIR)/libmm_plugin_altair_lte_la-mm-plugin-altair-lte.Plo
	-rm -f altair/tests/$(DEPDIR)/test_modem_helpers_altair_lte-test-modem-helpers-altair-lte.Po
	-rm -f anydata/$(DEPDIR)/libmm_plugin_anydata_la-mm-broadband-modem-anydata.Plo
	-rm -f anydata/$(DEPDIR)/libmm_plugin_anydata_la-mm-plugin-anydata.Plo
	-rm -f broadmobi/$(DEPDIR)/libmm_plugin_broadmobi_la-mm-plugin-broadmobi.Plo
	-rm -f cinterion/$(DEPDIR)/libhelpers_cinterion_la-mm-modem-helpers-cinterion.Plo
	-rm -f cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-bearer-cinterion.Plo
	-rm -f cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-modem-cinterion.Plo
	-rm -f cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-broadband-modem-qmi-cinterion.Plo
	-rm -f cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-plugin-cinterion.Plo
	-rm -f cinterion/$(DEPDIR)/libmm_plugin_cinterion_la-mm-shared-cinterion.Plo
	-rm -f cinterion/tests/$(DEPDIR)/test_modem_helpers_cinterion-test-modem-helpers-cinterion.Po
	-rm -f dell/$(DEPDIR)/libmm_plugin_dell_la-mm-plugin-dell.Plo
	-rm -f dlink/$(DEPDIR)/libmm_plugin_dlink_la-mm-plugin-dlink.Plo
	-rm -f foxconn/$(DEPDIR)/libmm_plugin_foxconn_la-mm-plugin-foxconn.Plo
	-rm -f foxconn/$(DEPDIR)/libmm_shared_foxconn_la-mm-broadband-modem-foxconn-t77w968.Plo
	-rm -f foxconn/$(DEPDIR)/libmm_shared_foxconn_la-mm-shared.Plo
	-rm -f generic/$(DEPDIR)/libmm_plugin_generic_la-mm-plugin-generic.Plo
	-rm -f generic/tests/$(DEPDIR)/test_service_generic-test-service-generic.Po
	-rm -f haier/$(DEPDIR)/libmm_plugin_haier_la-mm-plugin-haier.Plo
	-rm -f huawei/$(DEPDIR)/libhelpers_huawei_la-mm-modem-helpers-huawei.Plo
	-rm -f huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-broadband-bearer-huawei.Plo
	-rm -f huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-broadband-modem-huawei.Plo
	-rm -f huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-plugin-huawei.Plo
	-rm -f huawei/$(DEPDIR)/libmm_plugin_huawei_la-mm-sim-huawei.Plo
	-rm -f huawei/tests/$(DEPDIR)/test_modem_helpers_huawei-test-modem-helpers-huawei.Po
	-rm -f icera/$(DEPDIR)/libhelpers_icera_la-mm-modem-helpers-icera.Plo
	-rm -f icera/$(DEPDIR)/libmm_shared_icera_la-mm-broadband-bearer-icera.Plo
	-rm -f icera/$(DEPDIR)/libmm_shared_icera_la-mm-broadband-modem-icera.Plo
	-rm -f icera/$(DEPDIR)/libmm_shared_icera_la-mm-shared.Plo
	-rm -f icera/tests/$(DEPDIR)/test_modem_helpers_icera-test-modem-helpers-icera.Po
	-rm -f iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-bearer-iridium.Plo
	-rm -f iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-broadband-modem-iridium.Plo
	-rm -f iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-plugin-iridium.Plo
	-rm -f iridium/$(DEPDIR)/libmm_plugin_iridium_la-mm-sim-iridium.Plo
	-rm -f linktop/$(DEPDIR)/libhelpers_linktop_la-mm-modem-helpers-linktop.Plo
	-rm -f linktop/$(DEPDIR)/libmm_plugin_linktop_la-mm-broadband-modem-linktop.Plo
	-rm -f linktop/$(DEPDIR)/libmm_plugin_linktop_la-mm-plugin-linktop.Plo
	-rm -f linktop/tests/$(DEPDIR)/test_modem_helpers_linktop-test-modem-helpers-linktop.Po
	-rm -f longcheer/$(DEPDIR)/libmm_plugin_longcheer_la-mm-broadband-modem-longcheer.Plo
	-rm -f longcheer/$(DEPDIR)/libmm_plugin_longcheer_la-mm-plugin-longcheer.Plo
	-rm -f mbm/$(DEPDIR)/libhelpers_mbm_la-mm-modem-helpers-mbm.Plo
	-rm -f mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-broadband-bearer-mbm.Plo
	-rm -f mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-broadband-modem-mbm.Plo
	-rm -f mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-plugin-mbm.Plo
	-rm -f mbm/$(DEPDIR)/libmm_plugin_ericsson_mbm_la-mm-sim-mbm.Plo
	-rm -f mbm/tests/$(DEPDIR)/test_modem_helpers_mbm-test-modem-helpers-mbm.Po
	-rm -f me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-broadband-bearer-me3630.Plo
	-rm -f me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-broadband-modem-me3630.Plo
	-rm -f me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-common-me3630.Plo
	-rm -f me3630/$(DEPDIR)/libmm_plugin_me3630_la-mm-plugin-me3630.Plo
	-rm -f motorola/$(DEPDIR)/libmm_plugin_motorola_la-mm-broadband-modem-motorola.Plo
	-rm -f motorola/$(DEPDIR)/libmm_plugin_motorola_la-mm-plugin-motorola.Plo
	-rm -f mtk/$(DEPDIR)/libmm_plugin_mtk_la-mm-broadband-modem-mtk.Plo
	-rm -f mtk/$(DEPDIR)/libmm_plugin_mtk_la-mm-plugin-mtk.Plo
	-rm -f nokia/$(DEPDIR)/libmm_plugin_nokia_icera_la-mm-plugin-nokia-icera.Plo
	-rm -f nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-broadband-modem-nokia.Plo
	-rm -f nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-plugin-nokia.Plo
	-rm -f nokia/$(DEPDIR)/libmm_plugin_nokia_la-mm-sim-nokia.Plo
	-rm -f novatel/$(DEPDIR)/libmm_plugin_novatel_la-mm-plugin-novatel.Plo
	-rm -f novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-broadband-bearer-novatel-lte.Plo
	-rm -f novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-broadband-modem-novatel-lte.Plo
	-rm -f novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-plugin-novatel-lte.Plo
	-rm -f novatel/$(DEPDIR)/libmm_plugin_novatel_lte_la-mm-sim-novatel-lte.Plo
	-rm -f novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-broadband-modem-novatel.Plo
	-rm -f novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-common-novatel.Plo
	-rm -f novatel/$(DEPDIR)/libmm_shared_novatel_la-mm-shared.Plo
	-rm -f option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-broadband-bearer-hso.Plo
	-rm -f option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-broadband-modem-hso.Plo
	-rm -f option/$(DEPDIR)/libmm_plugin_option_hso_la-mm-plugin-hso.Plo
	-rm -f option/$(DEPDIR)/libmm_plugin_option_la-mm-plugin-option.Plo
	-rm -f option/$(DEPDIR)/libmm_shared_option_la-mm-broadband-modem-option.Plo
	-rm -f option/$(DEPDIR)/libmm_shared_option_la-mm-shared.Plo
	-rm -f pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-broadband-modem-pantech.Plo
	-rm -f pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-plugin-pantech.Plo
	-rm -f pantech/$(DEPDIR)/libmm_plugin_pantech_la-mm-sim-pantech.Plo
	-rm -f quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-broadband-modem-qmi-quectel.Plo
	-rm -f quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-broadband-modem-quectel.Plo
	-rm -f quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-plugin-quectel.Plo
	-rm -f quectel/$(DEPDIR)/libmm_plugin_quectel_la-mm-shared-quectel.Plo
	-rm -f samsung/$(DEPDIR)/libmm_plugin_samsung_la-mm-broadband-modem-samsung.Plo
	-rm -f samsung/$(DEPDIR)/libmm_plugin_samsung_la-mm-plugin-samsung.Plo
	-rm -f sierra/$(DEPDIR)/libhelpers_sierra_la-mm-modem-helpers-sierra.Plo
	-rm -f sierra/$(DEPDIR)/libmm_plugin_sierra_la-mm-plugin-sierra.Plo
	-rm -f sierra/$(DEPDIR)/libmm_plugin_sierra_legacy_la-mm-broadband-modem-sierra-icera.Plo
	-rm -f sierra/$(DEPDIR)/libmm_plugin_sierra_legacy_la-mm-plugin-sierra-legacy.Plo
	-rm -f sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-broadband-bearer-sierra.Plo
	-rm -f sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-broadband-modem-sierra.Plo
	-rm -f sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-common-sierra.Plo
	-rm -f sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-shared.Plo
	-rm -f sierra/$(DEPDIR)/libmm_shared_sierra_la-mm-sim-sierra.Plo
	-rm -f sierra/tests/$(DEPDIR)/test_modem_helpers_sierra-test-modem-helpers-sierra.Po
	-rm -f simtech/$(DEPDIR)/libhelpers_simtech_la-mm-modem-helpers-simtech.Plo
	-rm -f simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-broadband-modem-qmi-simtech.Plo
	-rm -f simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-broadband-modem-simtech.Plo
	-rm -f simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-plugin-simtech.Plo
	-rm -f simtech/$(DEPDIR)/libmm_plugin_simtech_la-mm-shared-simtech.Plo
	-rm -f simtech/tests/$(DEPDIR)/test_modem_helpers_simtech-test-modem-helpers-simtech.Po
	-rm -f telit/$(DEPDIR)/libhelpers_telit_la-mm-modem-helpers-telit.Plo
	-rm -f telit/$(DEPDIR)/libhelpers_telit_la-mm-telit-enums-types.Plo
	-rm -f telit/$(DEPDIR)/libmm_plugin_telit_la-mm-plugin-telit.Plo
	-rm -f telit/$(DEPDIR)/libmm_shared_telit_la-mm-broadband-modem-mbim-telit.Plo
	-rm -f telit/$(DEPDIR)/libmm_shared_telit_la-mm-broadband-modem-telit.Plo
	-rm -f telit/$(DEPDIR)/libmm_shared_telit_la-mm-common-telit.Plo
	-rm -f telit/$(DEPDIR)/libmm_shared_telit_la-mm-shared-telit.Plo
	-rm -f telit/$(DEPDIR)/libmm_shared_telit_la-mm-shared.Plo
	-rm -f telit/tests/$(DEPDIR)/test_modem_helpers_telit-test-mm-modem-helpers-telit.Po
	-rm -f tests/$(DEPDIR)/libmm_test_common_la-test-fixture.Plo
	-rm -f tests/$(DEPDIR)/libmm_test_common_la-test-helpers.Plo
	-rm -f tests/$(DEPDIR)/libmm_test_common_la-test-port-context.Plo
	-rm -f tests/$(DEPDIR)/test-keyfiles.Po
	-rm -f tests/$(DEPDIR)/test-udev-rules.Po
	-rm -f thuraya/$(DEPDIR)/libhelpers_thuraya_la-mm-modem-helpers-thuraya.Plo
	-rm -f thuraya/$(DEPDIR)/libmm_plugin_thuraya_la-mm-broadband-modem-thuraya.Plo
	-rm -f thuraya/$(DEPDIR)/libmm_plugin_thuraya_la-mm-plugin-thuraya.Plo
	-rm -f thuraya/tests/$(DEPDIR)/test_modem_helpers_thuraya-test-mm-modem-helpers-thuraya.Po
	-rm -f tplink/$(DEPDIR)/libmm_plugin_tplink_la-mm-plugin-tplink.Plo
	-rm -f ublox/$(DEPDIR)/libhelpers_ublox_la-mm-modem-helpers-ublox.Plo
	-rm -f ublox/$(DEPDIR)/libhelpers_ublox_la-mm-ublox-enums-types.Plo
	-rm -f ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-broadband-bearer-ublox.Plo
	-rm -f ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-broadband-modem-ublox.Plo
	-rm -f ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-plugin-ublox.Plo
	-rm -f ublox/$(DEPDIR)/libmm_plugin_ublox_la-mm-sim-ublox.Plo
	-rm -f ublox/tests/$(DEPDIR)/test_modem_helpers_ublox-test-modem-helpers-ublox.Po
	-rm -f via/$(DEPDIR)/libmm_plugin_via_la-mm-broadband-modem-via.Plo
	-rm -f via/$(DEPDIR)/libmm_plugin_via_la-mm-plugin-via.Plo
	-rm -f wavecom/$(DEPDIR)/libmm_plugin_wavecom_la-mm-broadband-modem-wavecom.Plo
	-rm -f wavecom/$(DEPDIR)/libmm_plugin_wavecom_la-mm-plugin-wavecom.Plo
	-rm -f x22x/$(DEPDIR)/libmm_plugin_x22x_la-mm-broadband-modem-x22x.Plo
	-rm -f x22x/$(DEPDIR)/libmm_plugin_x22x_la-mm-plugin-x22x.Plo
	-rm -f xmm/$(DEPDIR)/libhelpers_xmm_la-mm-modem-helpers-xmm.Plo
	-rm -f xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-broadband-modem-mbim-xmm.Plo
	-rm -f xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-broadband-modem-xmm.Plo
	-rm -f xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-shared-xmm.Plo
	-rm -f xmm/$(DEPDIR)/libmm_shared_xmm_la-mm-shared.Plo
	-rm -f xmm/tests/$(DEPDIR)/test_modem_helpers_xmm-test-modem-helpers-xmm.Po
	-rm -f zte/$(DEPDIR)/libmm_plugin_zte_la-mm-broadband-modem-zte-icera.Plo
	-rm -f zte/$(DEPDIR)/libmm_plugin_zte_la-mm-broadband-modem-zte.Plo
	-rm -f zte/$(DEPDIR)/libmm_plugin_zte_la-mm-common-zte.Plo
	-rm -f zte/$(DEPDIR)/libmm_plugin_zte_la-mm-plugin-zte.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-dist_pkgdataDATA uninstall-dist_udevrulesDATA \
	uninstall-pkglibLTLIBRARIES

.MAKE: all check check-am install install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am \
	check-local clean clean-generic clean-libtool \
	clean-noinstLTLIBRARIES clean-noinstPROGRAMS \
	clean-pkglibLTLIBRARIES cscopelist-am ctags ctags-am distclean \
	distclean-compile distclean-generic distclean-libtool \
	distclean-tags distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am \
	install-dist_pkgdataDATA install-dist_udevrulesDATA \
	install-dvi install-dvi-am install-exec install-exec-am \
	install-html install-html-am install-info install-info-am \
	install-man install-pdf install-pdf-am \
	install-pkglibLTLIBRARIES install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	maintainer-clean maintainer-clean-generic mostlyclean \
	mostlyclean-compile mostlyclean-generic mostlyclean-libtool \
	pdf pdf-am ps ps-am tags tags-am uninstall uninstall-am \
	uninstall-dist_pkgdataDATA uninstall-dist_udevrulesDATA \
	uninstall-pkglibLTLIBRARIES

.PRECIOUS: Makefile


### testing rules

# test: run all tests in cwd and subdirs
test: test-nonrecursive
	@ for subdir in $(SUBDIRS) . ; do \
	    test "$$subdir" = "." -o "$$subdir" = "po" || \
	    ( cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) $@ ) || exit $? ; \
	  done

# test-nonrecursive: run tests only in cwd
test-nonrecursive: ${TEST_PROGS}
	@test -z "${TEST_PROGS}" || G_DEBUG=gc-friendly MALLOC_CHECK_=2 MALLOC_PERTURB_=$$(($${RANDOM:-256} % 256)) ${GTESTER} --verbose ${TEST_PROGS}

# test-report: run tests in subdirs and generate report
# perf-report: run tests in subdirs with -m perf and generate report
# full-report: like test-report: with -m perf and -m slow
test-report perf-report full-report:	${TEST_PROGS}
	@test -z "${TEST_PROGS}" || { \
	  case $@ in \
	  test-report) test_options="-k";; \
	  perf-report) test_options="-k -m=perf";; \
	  full-report) test_options="-k -m=perf -m=slow";; \
	  esac ; \
	  if test -z "$$GTESTER_LOGDIR" ; then	\
	    ${GTESTER} --verbose $$test_options -o test-report.xml ${TEST_PROGS} ; \
	  elif test -n "${TEST_PROGS}" ; then \
	    ${GTESTER} --verbose $$test_options -o `mktemp "$$GTESTER_LOGDIR/log-XXXXXX"` ${TEST_PROGS} ; \
	  fi ; \
	}
	@ ignore_logdir=true ; \
	  if test -z "$$GTESTER_LOGDIR" ; then \
	    GTESTER_LOGDIR=`mktemp -d "\`pwd\`/.testlogs-XXXXXX"`; export GTESTER_LOGDIR ; \
	    ignore_logdir=false ; \
	  fi ; \
	  if test -d "$(top_srcdir)/.git" ; then \
	    REVISION=`git describe` ; \
	  else \
	    REVISION=$(VERSION) ; \
	  fi ; \
	  for subdir in $(SUBDIRS) . ; do \
	    test "$$subdir" = "." -o "$$subdir" = "po" || \
	    ( cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) $@ ) || exit $? ; \
	  done ; \
	  $$ignore_logdir || { \
	    echo '<?xml version="1.0"?>'              > $@.xml ; \
	    echo '<report-collection>'               >> $@.xml ; \
	    echo '<info>'                            >> $@.xml ; \
	    echo '  <package>$(PACKAGE)</package>'   >> $@.xml ; \
	    echo '  <version>$(VERSION)</version>'   >> $@.xml ; \
	    echo "  <revision>$$REVISION</revision>" >> $@.xml ; \
	    echo '</info>'                           >> $@.xml ; \
	    for lf in `ls -L "$$GTESTER_LOGDIR"/.` ; do \
	      sed '1,1s/^<?xml\b[^>?]*?>//' <"$$GTESTER_LOGDIR"/"$$lf" >> $@.xml ; \
	    done ; \
	    echo >> $@.xml ; \
	    echo '</report-collection>' >> $@.xml ; \
	    rm -rf "$$GTESTER_LOGDIR"/ ; \
	    ${GTESTER_REPORT} --version 2>/dev/null 1>&2 ; test "$$?" != 0 || ${GTESTER_REPORT} $@.xml >$@.html ; \
	  }
.PHONY: test test-report perf-report full-report test-nonrecursive

# run tests in cwd as part of make check
check-local: test-nonrecursive
@WITH_SHARED_ICERA_TRUE@	-DMM_MODULE_NAME=\"shared-icera\" \
@WITH_SHARED_ICERA_TRUE@	$(NULL)

@WITH_SHARED_TELIT_TRUE@telit/mm-telit-enums-types.h: Makefile.am $(TELIT_ENUMS_INPUTS) $(top_srcdir)/build-aux/mm-enums-template.h
@WITH_SHARED_TELIT_TRUE@	$(AM_V_GEN) \
@WITH_SHARED_TELIT_TRUE@		$(MKDIR_P) telit; \
@WITH_SHARED_TELIT_TRUE@		$(GLIB_MKENUMS) \
@WITH_SHARED_TELIT_TRUE@			--fhead "#include \"mm-modem-helpers-telit.h\"\n#ifndef __MM_TELIT_ENUMS_TYPES_H__\n#define __MM_TELIT_ENUMS_TYPES_H__\n" \
@WITH_SHARED_TELIT_TRUE@			--template $(top_srcdir)/build-aux/mm-enums-template.h \
@WITH_SHARED_TELIT_TRUE@			--ftail "#endif /* __MM_TELIT_ENUMS_TYPES_H__ */\n" \
@WITH_SHARED_TELIT_TRUE@			$(TELIT_ENUMS_INPUTS) > $@

@WITH_SHARED_TELIT_TRUE@telit/mm-telit-enums-types.c: Makefile.am $(top_srcdir)/build-aux/mm-enums-template.c telit/mm-telit-enums-types.h
@WITH_SHARED_TELIT_TRUE@	$(AM_V_GEN) \
@WITH_SHARED_TELIT_TRUE@		$(MKDIR_P) telit; \
@WITH_SHARED_TELIT_TRUE@		$(GLIB_MKENUMS) \
@WITH_SHARED_TELIT_TRUE@			--fhead "#include \"mm-telit-enums-types.h\"" \
@WITH_SHARED_TELIT_TRUE@			--template $(top_srcdir)/build-aux/mm-enums-template.c \
@WITH_SHARED_TELIT_TRUE@			$(TELIT_ENUMS_INPUTS) > $@

@ENABLE_PLUGIN_UBLOX_TRUE@ublox/mm-ublox-enums-types.h: Makefile.am $(UBLOX_ENUMS_INPUTS) $(top_srcdir)/build-aux/mm-enums-template.h
@ENABLE_PLUGIN_UBLOX_TRUE@	$(AM_V_GEN) \
@ENABLE_PLUGIN_UBLOX_TRUE@		$(MKDIR_P) ublox; \
@ENABLE_PLUGIN_UBLOX_TRUE@		$(GLIB_MKENUMS) \
@ENABLE_PLUGIN_UBLOX_TRUE@			--fhead "#include \"mm-modem-helpers-ublox.h\"\n#ifndef __MM_UBLOX_ENUMS_TYPES_H__\n#define __MM_UBLOX_ENUMS_TYPES_H__\n" \
@ENABLE_PLUGIN_UBLOX_TRUE@			--template $(top_srcdir)/build-aux/mm-enums-template.h \
@ENABLE_PLUGIN_UBLOX_TRUE@			--ftail "#endif /* __MM_UBLOX_ENUMS_TYPES_H__ */\n" \
@ENABLE_PLUGIN_UBLOX_TRUE@			$(UBLOX_ENUMS_INPUTS) > $@

@ENABLE_PLUGIN_UBLOX_TRUE@ublox/mm-ublox-enums-types.c: Makefile.am $(top_srcdir)/build-aux/mm-enums-template.c ublox/mm-ublox-enums-types.h
@ENABLE_PLUGIN_UBLOX_TRUE@	$(AM_V_GEN) \
@ENABLE_PLUGIN_UBLOX_TRUE@		$(MKDIR_P) ublox; \
@ENABLE_PLUGIN_UBLOX_TRUE@		$(GLIB_MKENUMS) \
@ENABLE_PLUGIN_UBLOX_TRUE@			--fhead "#include \"mm-ublox-enums-types.h\"" \
@ENABLE_PLUGIN_UBLOX_TRUE@			--template $(top_srcdir)/build-aux/mm-enums-template.c \
@ENABLE_PLUGIN_UBLOX_TRUE@			$(UBLOX_ENUMS_INPUTS) > $@

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:


# do not edit this file, it will be overwritten on update
ACTION!="add|change|move|bind", GOTO="mm_sierra_end"
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1199", GOTO="mm_sierra"
GOTO="mm_sierra_end"

LABEL="mm_sierra"
SUBSYSTEMS=="usb", ATTRS{bInterfaceNumber}=="?*", ENV{.MM_USBIFNUM}="$attr{bInterfaceNumber}"

# Netgear AC341U: enable connection status polling explicitly
ATTRS{idVendor}=="1199", ATTRS{idProduct}=="9057", ENV{ID_MM_QMI_CONNECTION_STATUS_POLLING_ENABLE}="1"

# MC74XX: Add port hints
#  if 03: primary port
#  if 02: raw NMEA port
#  if 00: diag/qcdm port
ATTRS{idVendor}=="1199", ATTRS{idProduct}=="9071", <PERSON><PERSON><PERSON>{.MM_USBIFNUM}=="03", ENV{ID_MM_PORT_TYPE_AT_PRIMARY}="1"
ATTRS{idVendor}=="1199", ATTRS{idProduct}=="9071", ENV{.MM_USBIFNUM}=="02", ENV{ID_MM_PORT_TYPE_GPS}="1"
ATTRS{idVendor}=="1199", ATTRS{idProduct}=="9071", ENV{.MM_USBIFNUM}=="00", ENV{ID_MM_PORT_TYPE_QCDM}="1"

# EM7565: Add port hints
#  if 03: primary port
#  if 02: raw NMEA port
#  if 00: diag/qcdm port
ATTRS{idVendor}=="1199", ATTRS{idProduct}=="9091", ENV{.MM_USBIFNUM}=="03", ENV{ID_MM_PORT_TYPE_AT_PRIMARY}="1"
ATTRS{idVendor}=="1199", ATTRS{idProduct}=="9091", ENV{.MM_USBIFNUM}=="02", ENV{ID_MM_PORT_TYPE_GPS}="1"
ATTRS{idVendor}=="1199", ATTRS{idProduct}=="9091", ENV{.MM_USBIFNUM}=="00", ENV{ID_MM_PORT_TYPE_QCDM}="1"

LABEL="mm_sierra_end"

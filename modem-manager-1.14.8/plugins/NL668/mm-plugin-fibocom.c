/* -*- Mode: C; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/*
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details:
 *
 * Copyright (C) 2018-2020 Aleksander Morgado <<EMAIL>>
 */

#include <stdlib.h>
#include <string.h>
#include <gmodule.h>

#define _LIBMM_INSIDE_MM
#include <libmm-glib.h>

#include "mm-log-object.h"
#include "mm-plugin-fibocom.h"
#include "mm-broadband-modem.h"
#include "mm-broadband-modem-xmm.h"
#include "mm-broadband-modem-fibocom.h"

#if defined WITH_MBIM
#include "mm-broadband-modem-mbim.h"
#include "mm-broadband-modem-mbim-fibocom.h"
#include "mm-broadband-modem-mbim-xmm.h"
#include "mm-broadband-modem-mbim-xmm-fibocom.h"
#endif

#if defined WITH_QMI
#include "mm-broadband-modem-qmi.h"
#endif

G_DEFINE_TYPE (MMPluginFibocom, mm_plugin_fibocom, MM_TYPE_PLUGIN)

MM_PLUGIN_DEFINE_MAJOR_VERSION
MM_PLUGIN_DEFINE_MINOR_VERSION

/*****************************************************************************/

static MMBaseModem *
create_modem (MMPlugin     *self,
              const gchar  *uid,
              const gchar **drivers,
              guint16       vendor,
              guint16       product,
              guint16       subsystem_vendor,
              GList        *probes,
              GError      **error)
{
#if defined WITH_MBIM
    if (mm_port_probe_list_has_mbim_port (probes)) {
        if (mm_port_probe_list_is_xmm (probes)) {
            mm_obj_dbg (self, "MBIM-powered XMM-based Fibocom modem found...");
            return MM_BASE_MODEM (mm_broadband_modem_mbim_xmm_fibocom_new (uid,
                                                                           drivers,
                                                                           mm_plugin_get_name (self),
                                                                           vendor,
                                                                           product));
        }
        mm_obj_dbg (self, "MBIM-powered Fibocom modem found...");
        return MM_BASE_MODEM (mm_broadband_modem_mbim_fibocom_new (uid,
                                                                   drivers,
                                                                   mm_plugin_get_name (self),
                                                                   vendor,
                                                                   product));
    }
#endif

#if defined WITH_QMI
    if (mm_port_probe_list_has_qmi_port (probes)) {
        mm_obj_dbg (self, "QMI-powered Fibocom modem found...");
        return MM_BASE_MODEM (mm_broadband_modem_qmi_new (uid,
                                                          drivers,
                                                          mm_plugin_get_name (self),
                                                          vendor,
                                                          product));
    }
#endif

    if (mm_port_probe_list_is_xmm (probes)) {
        mm_obj_dbg (self, "XMM-based Fibocom modem found...");
        return MM_BASE_MODEM (mm_broadband_modem_xmm_new (uid,
                                                          drivers,
                                                          mm_plugin_get_name (self),
                                                          vendor,
                                                          product));
    }

    mm_obj_dbg (self, "Fibocom modem found...");
    return MM_BASE_MODEM (mm_broadband_modem_fibocom_new (uid,
                                                          drivers,
                                                          mm_plugin_get_name (self),
                                                          vendor,
                                                          product));
}

/*****************************************************************************/
/* custom setup step: set USBMODE */
static gboolean
nl668_custom_init_finish (MMPortProbe *probe,
                    GAsyncResult *result,
                    GError **error)
{
    return g_task_propagate_boolean (G_TASK (result), error);
}

static void
usbmode_ready (MMPortSerialAt *port,
              GAsyncResult   *res,
              GTask          *task)
{
    MMPortProbe *probe;
    const gchar *response;

    probe = g_task_get_source_object (task);

    /* Ignore errors, just avoid tagging */
    response = mm_port_serial_at_command_finish (port, res, NULL);
    // if (!response || !strstr(response, "OK")) {
    //     mm_obj_dbg(probe, "can't set usbmode to 18");
    //     g_task_return_new_error (task,
    //                              MM_CORE_ERROR,
    //                              MM_CORE_ERROR_WRONG_STATE,
    //                              "X200 cannot be supported with the Longcheer plugin");
    // } else {
    if (strstr(response, "OK")) {
        mm_obj_dbg(probe, "set usbmode to 18");
    }
    g_task_return_boolean (task, TRUE);
    g_object_unref (task);
}

static void
nl668_custom_init (MMPortProbe         *probe,
                   MMPortSerialAt      *port,
                   GCancellable        *cancellable,
                   GAsyncReadyCallback  callback,
                   gpointer             user_data)
{
    GTask *task;
    const char *device;

    task = g_task_new (probe, cancellable, callback, user_data);

    device = mm_port_get_device (MM_PORT (port));
    if (strcmp(device, "ttyUSB1") == 0) {
        mm_port_serial_at_command (
            port,
            "AT+GTUSBMODE=18",
            3,
            FALSE, /* raw */
            FALSE, /* allow cached */
            cancellable,
            (GAsyncReadyCallback) usbmode_ready,
            task);
    } else {
        g_task_return_boolean (task, TRUE);
        g_object_unref (task);
    }
}

/*****************************************************************************/

static gboolean
grab_port (MMPlugin *self,
           MMBaseModem *modem,
           MMPortProbe *probe,
           GError **error)
{
    MMKernelDevice *port;
    MMPortType port_type;

    port_type = mm_port_probe_get_port_type (probe);
    port = mm_port_probe_peek_port (probe);

    return mm_base_modem_grab_port (modem,
                                    port,
                                    port_type,
                                    MM_PORT_SERIAL_AT_FLAG_NONE,
                                    error);
}

/*****************************************************************************/


G_MODULE_EXPORT MMPlugin *
mm_plugin_create (void)
{
    static const gchar *subsystems[] = { "tty", "net", "usbmisc", NULL };
    static const guint16 vendor_ids[] = { 0x2cb7, 0x1782, 0x1508, 0 };
    // static const gchar *drivers[] = { "cdc_mbim", "qmi_wwan", "cdc_ether", "option", NULL };
    static const MMAsyncMethod custom_init = {
        .async  = G_CALLBACK (nl668_custom_init),
        .finish = G_CALLBACK (nl668_custom_init_finish),
    };

    return MM_PLUGIN (
        g_object_new (MM_TYPE_PLUGIN_FIBOCOM,
                      MM_PLUGIN_NAME,               MM_MODULE_NAME,
                      MM_PLUGIN_ALLOWED_SUBSYSTEMS, subsystems,
                      MM_PLUGIN_ALLOWED_VENDOR_IDS, vendor_ids,
                    //   MM_PLUGIN_ALLOWED_DRIVERS,    drivers,
                      MM_PLUGIN_ALLOWED_AT,         TRUE,
                    //   MM_PLUGIN_ALLOWED_MBIM,       TRUE,
                    //   MM_PLUGIN_ALLOWED_QMI,        TRUE,
                    //   MM_PLUGIN_XMM_PROBE,          TRUE,
                    //   MM_PLUGIN_CUSTOM_INIT,        &custom_init,
                      NULL));
}

static void
mm_plugin_fibocom_init (MMPluginFibocom *self)
{
}

static void
mm_plugin_fibocom_class_init (MMPluginFibocomClass *klass)
{
    MMPluginClass *plugin_class = MM_PLUGIN_CLASS (klass);

    plugin_class->create_modem = create_modem;
    // plugin_class->grab_port = grab_port;
}

1 Notes on the Free Translation Project
***************************************

Free software is going international!  The Free Translation Project is a
way to get maintainers of free software, translators, and users all
together, so that free software will gradually become able to speak many
languages.  A few packages already provide translations for their
messages.

   If you found this 'ABOUT-NLS' file inside a distribution, you may
assume that the distributed package does use GNU 'gettext' internally,
itself available at your nearest GNU archive site.  But you do _not_
need to install GNU 'gettext' prior to configuring, installing or using
this package with messages translated.

   Installers will find here some useful hints.  These notes also
explain how users should proceed for getting the programs to use the
available translations.  They tell how people wanting to contribute and
work on translations can contact the appropriate team.

1.1 INSTALL Matters
===================

Some packages are "localizable" when properly installed; the programs
they contain can be made to speak your own native language.  Most such
packages use GNU 'gettext'.  Other packages have their own ways to
internationalization, predating GNU 'gettext'.

   By default, this package will be installed to allow translation of
messages.  It will automatically detect whether the system already
provides the GNU 'gettext' functions.  Installers may use special
options at configuration time for changing the default behaviour.  The
command:

     ./configure --disable-nls

will _totally_ disable translation of messages.

   When you already have GNU 'gettext' installed on your system and run
configure without an option for your new package, 'configure' will
probably detect the previously built and installed 'libintl' library and
will decide to use it.  If not, you may have to to use the
'--with-libintl-prefix' option to tell 'configure' where to look for it.

   Internationalized packages usually have many 'po/LL.po' files, where
LL gives an ISO 639 two-letter code identifying the language.  Unless
translations have been forbidden at 'configure' time by using the
'--disable-nls' switch, all available translations are installed
together with the package.  However, the environment variable 'LINGUAS'
may be set, prior to configuration, to limit the installed set.
'LINGUAS' should then contain a space separated list of two-letter
codes, stating which languages are allowed.

1.2 Using This Package
======================

As a user, if your language has been installed for this package, you
only have to set the 'LANG' environment variable to the appropriate
'LL_CC' combination.  If you happen to have the 'LC_ALL' or some other
'LC_xxx' environment variables set, you should unset them before setting
'LANG', otherwise the setting of 'LANG' will not have the desired
effect.  Here 'LL' is an ISO 639 two-letter language code, and 'CC' is
an ISO 3166 two-letter country code.  For example, let's suppose that
you speak German and live in Germany.  At the shell prompt, merely
execute 'setenv LANG de_DE' (in 'csh'), 'export LANG; LANG=de_DE' (in
'sh') or 'export LANG=de_DE' (in 'bash').  This can be done from your
'.login' or '.profile' file, once and for all.

   You might think that the country code specification is redundant.
But in fact, some languages have dialects in different countries.  For
example, 'de_AT' is used for Austria, and 'pt_BR' for Brazil.  The
country code serves to distinguish the dialects.

   The locale naming convention of 'LL_CC', with 'LL' denoting the
language and 'CC' denoting the country, is the one use on systems based
on GNU libc.  On other systems, some variations of this scheme are used,
such as 'LL' or 'LL_CC.ENCODING'.  You can get the list of locales
supported by your system for your language by running the command
'locale -a | grep '^LL''.

   Not all programs have translations for all languages.  By default, an
English message is shown in place of a nonexistent translation.  If you
understand other languages, you can set up a priority list of languages.
This is done through a different environment variable, called
'LANGUAGE'.  GNU 'gettext' gives preference to 'LANGUAGE' over 'LANG'
for the purpose of message handling, but you still need to have 'LANG'
set to the primary language; this is required by other parts of the
system libraries.  For example, some Swedish users who would rather read
translations in German than English for when Swedish is not available,
set 'LANGUAGE' to 'sv:de' while leaving 'LANG' to 'sv_SE'.

   Special advice for Norwegian users: The language code for Norwegian
bokma*l changed from 'no' to 'nb' recently (in 2003).  During the
transition period, while some message catalogs for this language are
installed under 'nb' and some older ones under 'no', it's recommended
for Norwegian users to set 'LANGUAGE' to 'nb:no' so that both newer and
older translations are used.

   In the 'LANGUAGE' environment variable, but not in the 'LANG'
environment variable, 'LL_CC' combinations can be abbreviated as 'LL' to
denote the language's main dialect.  For example, 'de' is equivalent to
'de_DE' (German as spoken in Germany), and 'pt' to 'pt_PT' (Portuguese
as spoken in Portugal) in this context.

1.3 Translating Teams
=====================

For the Free Translation Project to be a success, we need interested
people who like their own language and write it well, and who are also
able to synergize with other translators speaking the same language.
Each translation team has its own mailing list.  The up-to-date list of
teams can be found at the Free Translation Project's homepage,
'http://translationproject.org/', in the "Teams" area.

   If you'd like to volunteer to _work_ at translating messages, you
should become a member of the translating team for your own language.
The subscribing address is _not_ the same as the list itself, it has
'-request' appended.  For example, speakers of Swedish can send a
message to '<EMAIL>', having this message body:

     subscribe

   Keep in mind that team members are expected to participate _actively_
in translations, or at solving translational difficulties, rather than
merely lurking around.  If your team does not exist yet and you want to
start one, or if you are unsure about what to do or how to get started,
please write to '<EMAIL>' to reach the
coordinator for all translator teams.

   The English team is special.  It works at improving and uniformizing
the terminology in use.  Proven linguistic skills are praised more than
programming skills, here.

1.4 Available Packages
======================

Languages are not equally supported in all packages.  The following
matrix shows the current state of internationalization, as of Jun 2014.
The matrix shows, in regard of each package, for which languages PO
files have been submitted to translation coordination, with a
translation percentage of at least 50%.

     Ready PO files       af am an ar as ast az be bg bn bn_IN bs ca crh cs
                        +---------------------------------------------------+
     a2ps               |                       []                []     [] |
     aegis              |                                                   |
     anubis             |                                                   |
     aspell             |                []                       []     [] |
     bash               |                          []             []     [] |
     bfd                |                                                   |
     binutils           |                                         []        |
     bison              |                                                   |
     bison-runtime      |                []                                 |
     buzztrax           |                                                [] |
     ccd2cue            |                                                   |
     ccide              |                                                   |
     cflow              |                                                   |
     clisp              |                                                   |
     coreutils          |                                         []     [] |
     cpio               |                                                   |
     cppi               |                                                   |
     cpplib             |                                         []        |
     cryptsetup         |                                                [] |
     datamash           |                                                   |
     denemo             |                                         []     [] |
     dfarc              |                                         []        |
     dialog             |       []                                []     [] |
     dico               |                                                   |
     diffutils          |                                                [] |
     dink               |                                         []        |
     direvent           |                                                   |
     doodle             |                                                [] |
     dos2unix           |                                                   |
     dos2unix-man       |                                                   |
     e2fsprogs          |                                         []     [] |
     enscript           |                                         []        |
     exif               |                                                [] |
     fetchmail          |                                         []     [] |
     findutils          |                                                [] |
     flex               |                                         []        |
     freedink           |                                         []     [] |
     fusionforge        |                                                   |
     gas                |                                                   |
     gawk               |                                         []        |
     gcal               |                                         []        |
     gcc                |                                                   |
     gdbm               |                                                   |
     gettext-examples   | []             []        []             []     [] |
     gettext-runtime    |                          []             []     [] |
     gettext-tools      |                          []             []        |
     gjay               |                                                   |
     glunarclock        |                []        []                    [] |
     gnubiff            |                                                [] |
     gnubik             |          []                                       |
     gnucash            |          ()              ()             []        |
     gnuchess           |                                                   |
     gnulib             |                                                [] |
     gnunet             |                                                   |
     gnunet-gtk         |                                                   |
     gold               |                                                   |
     gphoto2            |                                                [] |
     gprof              |                          []                       |
     gramadoir          |                                                   |
     grep               |                          []             []     [] |
     grub               |                                         []        |
     gsasl              |                                                   |
     gss                |                                                   |
     gst-plugins-bad    |                          []                    [] |
     gst-plugins-base   |                          []             []     [] |
     gst-plugins-good   |                          []             []     [] |
     gst-plugins-ugly   |                          []             []     [] |
     gstreamer          |                []        []             []     [] |
     gtick              |                                                [] |
     gtkam              |                       []                       [] |
     gtkspell           | []             []     []                []     [] |
     guix               |                                                   |
     guix-packages      |                                                   |
     gutenprint         |                                         []        |
     hello              |                                         []        |
     help2man           |                                                   |
     help2man-texi      |                                                   |
     hylafax            |                                                   |
     idutils            |                                                   |
     iso_15924          |                                                [] |
     iso_3166           | []          []        [] [] []  []   [] [] []  [] |
     iso_3166_2         |                                                   |
     iso_4217           |                                                [] |
     iso_639            |             [] []     [] [] []  []      [] []  [] |
     iso_639_3          |                []                          []     |
     iso_639_5          |                                                   |
     jwhois             |                                                   |
     kbd                |                                                [] |
     klavaro            |          []              [] []          []     [] |
     ld                 |                          []                       |
     leafpad            |                       [] []             []     [] |
     libc               |                          []             []     [] |
     libexif            |                       ()                          |
     libextractor       |                                                   |
     libgnutls          |                                                [] |
     libgphoto2         |                                                [] |
     libgphoto2_port    |                                                [] |
     libgsasl           |                                                   |
     libiconv           |                          []                    [] |
     libidn             |                                                [] |
     liferea            |          []    []                       []     [] |
     lilypond           |                                         []     [] |
     lordsawar          |                                         []        |
     lprng              |                                                   |
     lynx               |                                         []     [] |
     m4                 |                                                [] |
     mailfromd          |                                                   |
     mailutils          |                                                   |
     make               |                                                [] |
     man-db             |                                         []     [] |
     man-db-manpages    |                                                   |
     midi-instruments   |          []                             []     [] |
     minicom            |                                                [] |
     mkisofs            |                                                [] |
     myserver           |                                                [] |
     nano               |                          []             []     [] |
     opcodes            |                                                   |
     parted             |                                                [] |
     pies               |                                                   |
     pnmixer            |                                                   |
     popt               |                                                [] |
     procps-ng          |                                                   |
     procps-ng-man      |                                                   |
     psmisc             |                                                [] |
     pspp               |                                         []        |
     pushover           |                                                [] |
     pwdutils           |                                                   |
     pyspread           |                                                   |
     radius             |                                         []        |
     recode             |                       []                []     [] |
     recutils           |                                                   |
     rpm                |                                                   |
     rush               |                                                   |
     sarg               |                                                   |
     sed                |                []        []             []     [] |
     sharutils          |                                                [] |
     shishi             |                                                   |
     skribilo           |                                                   |
     solfege            |                                         []     [] |
     solfege-manual     |                                                   |
     spotmachine        |                                                   |
     sudo               |                                         []     [] |
     sudoers            |                                         []     [] |
     sysstat            |                                                [] |
     tar                |                          []             []     [] |
     texinfo            |                                         []     [] |
     texinfo_document   |                                         []     [] |
     tigervnc           |                          []                       |
     tin                |                                                   |
     tin-man            |                                                   |
     tracgoogleappsa... |                                                   |
     trader             |                                                   |
     util-linux         |                                                [] |
     ve                 |                                                   |
     vice               |                                                   |
     vmm                |                                                   |
     vorbis-tools       |                                                [] |
     wastesedge         |                                                   |
     wcd                |                                                   |
     wcd-man            |                                                   |
     wdiff              |                                         []     [] |
     wget               |                                                [] |
     wyslij-po          |                                                   |
     xboard             |                                                   |
     xdg-user-dirs      | []    []    [] []     [] []     []      [] []  [] |
     xkeyboard-config   |                          []             []     [] |
                        +---------------------------------------------------+
                          af am an ar as ast az be bg bn bn_IN bs ca crh cs
                           4  0  2  5  3 11   0  8 25  3   3    1 55  4  74

                          da  de  el en en_GB en_ZA eo es et eu fa fi  fr 
                        +--------------------------------------------------+
     a2ps               | []  []  []     []         [] [] []       []  []  |
     aegis              | []  []                       []              []  |
     anubis             | []  []                       []          []  []  |
     aspell             | []  []         []         [] []          []  []  |
     bash               |                           [] []              []  |
     bfd                | []                           []          []  []  |
     binutils           |                              []          []  []  |
     bison              | []  []  []                [] [] []       []  []  |
     bison-runtime      | []  []  []                [] [] []       []  []  |
     buzztrax           | []  []                                   []  []  |
     ccd2cue            | []  []                    []                 []  |
     ccide              | []  []                    [] []          []  []  |
     cflow              | []  []                    []             []  []  |
     clisp              | []  []     []                []              []  |
     coreutils          | []  []                       [] []           []  |
     cpio               | []  []                       []          []  []  |
     cppi               | []  []                    []             []  []  |
     cpplib             | []  []                    [] []          []  []  |
     cryptsetup         | []  []                       []          []  []  |
     datamash           | []  []                    []                 []  |
     denemo             | []                                               |
     dfarc              | []  []                    [] []          []  []  |
     dialog             | []  []  []                [] []    [] [] []  []  |
     dico               | []  []                                   []  []  |
     diffutils          | []  []  []                [] []              []  |
     dink               | []  []                    [] []          []  []  |
     direvent           | []  []                    []                 []  |
     doodle             | []  []                    []             []      |
     dos2unix           | []  []                    [] []              []  |
     dos2unix-man       |     []                       []              []  |
     e2fsprogs          | []  []                    [] []              []  |
     enscript           | []  []         []         []             []  []  |
     exif               | []  []                    [] []          []  []  |
     fetchmail          | []  ()  []     []         [] []              []  |
     findutils          | []  []  []                [] [] []       []  []  |
     flex               | []  []                    [] []          []  []  |
     freedink           | []  []  []                [] []    []    []  []  |
     fusionforge        |     []                       []              []  |
     gas                |                              []          []  []  |
     gawk               | []  []                       []          []  []  |
     gcal               | []  []                       []              []  |
     gcc                |     []                                           |
     gdbm               | []  []                    []             []  []  |
     gettext-examples   | []  []  []                [] []          []  []  |
     gettext-runtime    | []  []                    [] []          []  []  |
     gettext-tools      | []  []                       []          []  []  |
     gjay               |     []                    []             []  []  |
     glunarclock        | []  []                    []             []  []  |
     gnubiff            |     ()                    []             []  ()  |
     gnubik             | []  []                    []             []  []  |
     gnucash            | []  ()  ()     ()            ()          ()  ()  |
     gnuchess           |     []                    [] []              []  |
     gnulib             | []  []                    [] [] []       []  []  |
     gnunet             |                              []                  |
     gnunet-gtk         |     []                                           |
     gold               |                              []          []  []  |
     gphoto2            | []  ()                    []                 []  |
     gprof              | []  []                    [] []          []  []  |
     gramadoir          | []  []                    []             []  []  |
     grep               | []  []                    [] [] []       []  []  |
     grub               | []  []                       []          []  []  |
     gsasl              | []  []                    []             []  []  |
     gss                | []  []                    []             []  []  |
     gst-plugins-bad    | []  []                                       []  |
     gst-plugins-base   | []  []  []                   []          []  []  |
     gst-plugins-good   | []  []  []                   []    []    []  []  |
     gst-plugins-ugly   | []  []  []                [] []    []    []  []  |
     gstreamer          | []  []  []                   []    []    []  []  |
     gtick              | []  ()                    []             []  []  |
     gtkam              | []  ()                    [] []          []  []  |
     gtkspell           | []  []  []                [] []    []    []  []  |
     guix               | []                        []                     |
     guix-packages      |                                                  |
     gutenprint         | []  []                                   []  []  |
     hello              | []  []  []                [] [] []       []  []  |
     help2man           | []  []  []                [] []          []  []  |
     help2man-texi      |     []                       []              []  |
     hylafax            |     []                       []                  |
     idutils            | []  []                    []             []  []  |
     iso_15924          | []  ()                    [] []    ()    []  ()  |
     iso_3166           | []  ()  []                [] [] [] ()    []  ()  |
     iso_3166_2         | []  ()                             ()        ()  |
     iso_4217           | []  ()  []                   [] [] ()    []  ()  |
     iso_639            | []  ()                    [] []    ()    []  ()  |
     iso_639_3          |     ()                             ()        ()  |
     iso_639_5          |     ()                             ()        ()  |
     jwhois             |     []                    [] []          []  []  |
     kbd                | []  []  []                [] []              []  |
     klavaro            | []  []  []                [] []    []        []  |
     ld                 | []                           []          []  []  |
     leafpad            | []  []  []                [] []    []    []  []  |
     libc               | []  []                       []          []  []  |
     libexif            | []  []         ()            []              []  |
     libextractor       |     []                                           |
     libgnutls          |     []                    []             []  []  |
     libgphoto2         | []  ()                                       []  |
     libgphoto2_port    | []  ()                       []    []    []  []  |
     libgsasl           | []  []                    []             []  []  |
     libiconv           | []  []                    [] [] []       []  []  |
     libidn             | []  []                    []             []  []  |
     liferea            | []  ()  []                   []    []    []  []  |
     lilypond           | []  []  []                [] []              []  |
     lordsawar          | []  []                                           |
     lprng              |                                                  |
     lynx               | []  []                    []    []       []  []  |
     m4                 | []  []  []                []             []  []  |
     mailfromd          |                                              []  |
     mailutils          |     []                       []          []  []  |
     make               | []  []                       []          []  []  |
     man-db             | []  []                    []                 []  |
     man-db-manpages    |     []                                       []  |
     midi-instruments   | []  []  []                [] [] []    [] []  []  |
     minicom            | []  []                       []          []  []  |
     mkisofs            |                           []             []  []  |
     myserver           |     []                    []             []  []  |
     nano               | []  []                    [] []    []    []  []  |
     opcodes            | []  []                       []          []  []  |
     parted             | []  []                                       []  |
     pies               |     []                                           |
     pnmixer            |     []                                       []  |
     popt               | []  []                    [] []          []  []  |
     procps-ng          |     []                                       []  |
     procps-ng-man      |     []                                       []  |
     psmisc             | []  []  []                []       []    []  []  |
     pspp               |     []                       []              []  |
     pushover           |     ()                    [] []              []  |
     pwdutils           | []  []                                       []  |
     pyspread           | []  []                                       []  |
     radius             |                              []              []  |
     recode             | []  []  []                [] []          []  []  |
     recutils           |     []                       []          []  []  |
     rpm                | []  []                    []             []  []  |
     rush               |     []                                   []  []  |
     sarg               | []                                           []  |
     sed                | []  []  []                [] [] []       []  []  |
     sharutils          |     []                    []    []           []  |
     shishi             |     []                                   []  []  |
     skribilo           | []                           []              []  |
     solfege            | []  []                    [] [] []    [] []  []  |
     solfege-manual     |     []                    [] [] []           []  |
     spotmachine        | []  []                    []             []  []  |
     sudo               | []  []                    [] []          []  []  |
     sudoers            | []  []  []                []             []  []  |
     sysstat            | []  []                    [] []          []  []  |
     tar                | []  []                    [] [] []       []  []  |
     texinfo            | []  []                    [] []              []  |
     texinfo_document   |     []                    [] []              []  |
     tigervnc           | []  []  []                []             []  []  |
     tin                | []  []                          []           []  |
     tin-man            |                []                                |
     tracgoogleappsa... | []  []                    []             []  []  |
     trader             | []  []         []         []             []  []  |
     util-linux         | []  []                       []              []  |
     ve                 |     []                    [] []          []  []  |
     vice               | ()  ()                                       ()  |
     vmm                |     []                                   []      |
     vorbis-tools       | []  []                    []                 []  |
     wastesedge         | []                                               |
     wcd                |     []                    [] []          []      |
     wcd-man            |     []                                           |
     wdiff              | []  []                    [] [] []       []  []  |
     wget               |     []                    [] [] []       []  []  |
     wyslij-po          |     []                    []             []  []  |
     xboard             | []  []                       []              []  |
     xdg-user-dirs      | []  []  []                [] [] [] [] [] []  []  |
     xkeyboard-config   | []  []  []                [] []          []  []  |
                        +--------------------------------------------------+
                          da  de  el en en_GB en_ZA eo es et eu fa fi  fr 
                          119 131 32  1   6     0   94 95 22 13  4 102 139

                          ga gd gl gu he hi hr hu hy ia id is it ja ka kk
                        +-------------------------------------------------+
     a2ps               |                   []          []    [] []       |
     aegis              |                                     []          |
     anubis             |                   [] []       []    []          |
     aspell             | []                []          []    [] []       |
     bash               |                      []       []    [] []       |
     bfd                |                               []       []       |
     binutils           |                               []    [] []       |
     bison              |                   []                            |
     bison-runtime      | []    []          [] []    [] []    [] []       |
     buzztrax           |                                                 |
     ccd2cue            |                      []                         |
     ccide              |                   [] []                         |
     cflow              | []                []          []                |
     clisp              |                                                 |
     coreutils          |                      []                []       |
     cpio               | []                [] []       []    [] []       |
     cppi               |       []          [] []             [] []       |
     cpplib             |                               []       []       |
     cryptsetup         |                                     []          |
     datamash           |                                                 |
     denemo             |                                     []          |
     dfarc              |                   [] []             []          |
     dialog             | [] [] []          [] []    [] [] [] [] []       |
     dico               |                                                 |
     diffutils          |                      []       []    [] []       |
     dink               |                      []                         |
     direvent           |                      []                         |
     doodle             | []                                  []          |
     dos2unix           |                      []                []       |
     dos2unix-man       |                                                 |
     e2fsprogs          |                      []       []                |
     enscript           | []                []          []                |
     exif               |       []          []          [] [] [] []       |
     fetchmail          |                               []    [] []       |
     findutils          | []    []          [] []       []    [] []       |
     flex               | []                                              |
     freedink           |                   [] []       []    []          |
     fusionforge        |                                                 |
     gas                |                               []                |
     gawk               |                               []    () []       |
     gcal               |                                                 |
     gcc                |                                                 |
     gdbm               |                                                 |
     gettext-examples   | []    []          [] []       []    [] []       |
     gettext-runtime    | []    []          [] []       []    [] []       |
     gettext-tools      |                               []    [] []       |
     gjay               |       []                                        |
     glunarclock        | []    []          [] []       []    []          |
     gnubiff            |                      []       []    ()          |
     gnubik             |       []          []                []          |
     gnucash            |          () () ()    ()             ()          |
     gnuchess           |                                                 |
     gnulib             | []    []             []             [] []       |
     gnunet             |                                                 |
     gnunet-gtk         |                                                 |
     gold               |                               []    []          |
     gphoto2            |                      []       []    [] []       |
     gprof              | []                   []       []    []          |
     gramadoir          | []                   []       []                |
     grep               | []    []          [] []       []    [] []       |
     grub               |       []             []             []          |
     gsasl              | []                [] []       []    []          |
     gss                | []                [] []       []    []          |
     gst-plugins-bad    |                   [] []       []                |
     gst-plugins-base   |       []          [] []       []                |
     gst-plugins-good   |       []          [] []       []    [] []       |
     gst-plugins-ugly   |       []          [] []       []    [] []       |
     gstreamer          |       []          [] []       []    []          |
     gtick              | []    []             []       []    []          |
     gtkam              |                      []       [] [] [] []       |
     gtkspell           | []    []    []    [] [] []    [] [] [] []       |
     guix               |                                                 |
     guix-packages      |                                                 |
     gutenprint         |       []             []             []          |
     hello              | []    []          [] []       []                |
     help2man           |                   []                [] []       |
     help2man-texi      |                                                 |
     hylafax            |                               []                |
     idutils            |                      []       []                |
     iso_15924          |       []             []    [] [] [] []          |
     iso_3166           | []    [] [] [] [] [] []    [] [] [] [] []    [] |
     iso_3166_2         |                               []    []          |
     iso_4217           |                   [] []       [] [] [] []       |
     iso_639            | []    [] []       [] []       [] [] [] []       |
     iso_639_3          |       []                            []          |
     iso_639_5          |                                                 |
     jwhois             |       []             []       []    []          |
     kbd                |                      []       []    []          |
     klavaro            |       []          [] []             []       [] |
     ld                 | []                            []    [] []       |
     leafpad            | []    []    []    [] []       []    [] ()       |
     libc               |       []          []          []    [] []       |
     libexif            |                                     []          |
     libextractor       |                                                 |
     libgnutls          |                                     []          |
     libgphoto2         |                                     [] []       |
     libgphoto2_port    |                                     [] []       |
     libgsasl           | []                   []       []    []          |
     libiconv           | []    []          [] []       []    [] []       |
     libidn             |                   [] []       []    []          |
     liferea            |       []    []       []             [] []       |
     lilypond           |                                     []          |
     lordsawar          |                                                 |
     lprng              |                               []                |
     lynx               |                      []       []    [] []       |
     m4                 | []    []          []          []       []       |
     mailfromd          |                                                 |
     mailutils          |                                                 |
     make               |                   []          []    [] []       |
     man-db             |                               []       []       |
     man-db-manpages    |                               []       []       |
     midi-instruments   |       []    []    [] [] []    [] [] [] []       |
     minicom            |                      []       []       []       |
     mkisofs            |                               []    []          |
     myserver           |                                     []          |
     nano               | []    []          [] []             [] []       |
     opcodes            | []                            []    []          |
     parted             |       []             []       []    [] []       |
     pies               |                                                 |
     pnmixer            |                   []                []          |
     popt               | []    [] []       [] []    [] [] [] [] []       |
     procps-ng          |                                                 |
     procps-ng-man      |                                                 |
     psmisc             |                   [] []       []    []          |
     pspp               |       []                               []       |
     pushover           |                                     []          |
     pwdutils           |                               []                |
     pyspread           |                                                 |
     radius             |                               []                |
     recode             | []    []    []    [] []       []    []          |
     recutils           |                                                 |
     rpm                |                               []                |
     rush               |       []                                        |
     sarg               |                                                 |
     sed                | []    []          [] []       []    [] []       |
     sharutils          |                                                 |
     shishi             |                                                 |
     skribilo           |                      []                         |
     solfege            |       []                            []          |
     solfege-manual     |                                                 |
     spotmachine        |                                                 |
     sudo               |       []          []                [] []       |
     sudoers            |                   []                [] []       |
     sysstat            |                   [] []       []       []       |
     tar                | []                [] []       []    [] []       |
     texinfo            |                   []          []    []          |
     texinfo_document   |                   [] []             []          |
     tigervnc           |                                                 |
     tin                |                                                 |
     tin-man            |                                                 |
     tracgoogleappsa... |       []    []    [] []                         |
     trader             |                   [] []                         |
     util-linux         |                                        []       |
     ve                 |                                     []          |
     vice               |                      ()             ()          |
     vmm                |                                                 |
     vorbis-tools       |                   []          []                |
     wastesedge         |                                     []          |
     wcd                |                                                 |
     wcd-man            |                                                 |
     wdiff              |       []             []             []          |
     wget               |                   [] []             [] []       |
     wyslij-po          |       []          []          []                |
     xboard             |                                                 |
     xdg-user-dirs      | [] [] [] [] [] [] [] []    [] [] [] [] []    [] |
     xkeyboard-config   |       []          [] []       []    [] []       |
                        +-------------------------------------------------+
                          ga gd gl gu he hi hr hu hy ia id is it ja ka kk
                          35  2 47  4  8  2 60 71  2  6 81 11 87 57  0  3

                          kn ko ku ky lg lt lv mk ml mn mr ms mt nb ne nl 
                        +--------------------------------------------------+
     a2ps               |                                  []          []  |
     aegis              |                                              []  |
     anubis             |                                  []    []    []  |
     aspell             |                            []                []  |
     bash               |                                        []    []  |
     bfd                |                                                  |
     binutils           |                                                  |
     bison              |                                              []  |
     bison-runtime      |          []    [] []             []    []    []  |
     buzztrax           |                                                  |
     ccd2cue            |                                                  |
     ccide              |                   []                         []  |
     cflow              |                                              []  |
     clisp              |                                              []  |
     coreutils          |                                        []    []  |
     cpio               |                                              []  |
     cppi               |                                                  |
     cpplib             |                                              []  |
     cryptsetup         |                                              []  |
     datamash           |                                        []    []  |
     denemo             |                                                  |
     dfarc              |                      []                      []  |
     dialog             |       []       [] []             []    []    []  |
     dico               |                                                  |
     diffutils          |                   []                   []    []  |
     dink               |                                              []  |
     direvent           |                                              []  |
     doodle             |                                              []  |
     dos2unix           |                                        []    []  |
     dos2unix-man       |                                              []  |
     e2fsprogs          |                                              []  |
     enscript           |                                              []  |
     exif               |    []             []                         []  |
     fetchmail          |                                              []  |
     findutils          |                                        []    []  |
     flex               |                                              []  |
     freedink           |                                        []    []  |
     fusionforge        |                                                  |
     gas                |                                                  |
     gawk               |                                              []  |
     gcal               |                                                  |
     gcc                |                                                  |
     gdbm               |                                                  |
     gettext-examples   |          []       []             [] [] []    []  |
     gettext-runtime    |    []                                  []    []  |
     gettext-tools      |    []                                            |
     gjay               |                                                  |
     glunarclock        |                   []                         []  |
     gnubiff            |                                              []  |
     gnubik             |                                        []    []  |
     gnucash            | () ()          () ()          ()       () () []  |
     gnuchess           |                                        []    []  |
     gnulib             |                                              []  |
     gnunet             |                                                  |
     gnunet-gtk         |                                                  |
     gold               |                                                  |
     gphoto2            |                                              []  |
     gprof              |                                  []          []  |
     gramadoir          |                                              []  |
     grep               |                                        []    []  |
     grub               |                []                      []    []  |
     gsasl              |                                              []  |
     gss                |                                                  |
     gst-plugins-bad    |                   []                   []    []  |
     gst-plugins-base   |                   []                   []    []  |
     gst-plugins-good   |                [] []                   []    []  |
     gst-plugins-ugly   |                   []             [] [] []    []  |
     gstreamer          |                []                      []    []  |
     gtick              |                                              []  |
     gtkam              |                                        []    []  |
     gtkspell           |          []    [] []       []    []    []    []  |
     guix               |                                                  |
     guix-packages      |                                                  |
     gutenprint         |                                              []  |
     hello              |                   []                   []    []  |
     help2man           |                                        []        |
     help2man-texi      |                                                  |
     hylafax            |                                              []  |
     idutils            |                                              []  |
     iso_15924          |                () []                         []  |
     iso_3166           | [] [] []       () [] [] []    []       []    []  |
     iso_3166_2         |                ()                            []  |
     iso_4217           |                () []                   []    []  |
     iso_639            | [] []          () []    []    []             []  |
     iso_639_3          | []             ()             []                 |
     iso_639_5          |                ()                                |
     jwhois             |                   []                         []  |
     kbd                |                                              []  |
     klavaro            |                                        []    []  |
     ld                 |                                                  |
     leafpad            |    []    []    [] []                         []  |
     libc               |    []                                        []  |
     libexif            |                                              []  |
     libextractor       |                                              []  |
     libgnutls          |                                  []          []  |
     libgphoto2         |                                              []  |
     libgphoto2_port    |                                              []  |
     libgsasl           |                                              []  |
     libiconv           |                []                            []  |
     libidn             |                                              []  |
     liferea            |                [] []                         []  |
     lilypond           |                                              []  |
     lordsawar          |                                                  |
     lprng              |                                                  |
     lynx               |                                              []  |
     m4                 |                                              []  |
     mailfromd          |                                                  |
     mailutils          |                                                  |
     make               |    []                                        []  |
     man-db             |                                              []  |
     man-db-manpages    |                                              []  |
     midi-instruments   |    [] []       []          []    []       [] []  |
     minicom            |                                        []        |
     mkisofs            |                                              []  |
     myserver           |                                                  |
     nano               |                                  []    []    []  |
     opcodes            |                                              []  |
     parted             |    []                                        []  |
     pies               |                                                  |
     pnmixer            |                                              []  |
     popt               | [] []             []                   []    []  |
     procps-ng          |                                                  |
     procps-ng-man      |                                                  |
     psmisc             |                                              []  |
     pspp               |                []                            []  |
     pushover           |                                                  |
     pwdutils           |                                              []  |
     pyspread           |                                                  |
     radius             |                                              []  |
     recode             |                                        []    []  |
     recutils           |                                              []  |
     rpm                |                                              []  |
     rush               |                                              []  |
     sarg               |                                                  |
     sed                |                                        []    []  |
     sharutils          |                                              []  |
     shishi             |                                                  |
     skribilo           |                                                  |
     solfege            |                                        []    []  |
     solfege-manual     |                                              []  |
     spotmachine        |                                              []  |
     sudo               |    []                                  []    []  |
     sudoers            |    []                                  []    []  |
     sysstat            |                                        []    []  |
     tar                |          []                            []    []  |
     texinfo            |                                              []  |
     texinfo_document   |                                              []  |
     tigervnc           |                                              []  |
     tin                |                                                  |
     tin-man            |                                                  |
     tracgoogleappsa... |                   []                   []    []  |
     trader             |                                        []        |
     util-linux         |                                              []  |
     ve                 |                                              []  |
     vice               |                                              []  |
     vmm                |                                              []  |
     vorbis-tools       |                                              []  |
     wastesedge         |                                              []  |
     wcd                |                                              []  |
     wcd-man            |                                              []  |
     wdiff              |                                              []  |
     wget               |                                        []    []  |
     wyslij-po          |                                              []  |
     xboard             |                                              []  |
     xdg-user-dirs      | [] [] [] []    [] [] [] []    []       []    []  |
     xkeyboard-config   |    []          []                            []  |
                        +--------------------------------------------------+
                          kn ko ku ky lg lt lv mk ml mn mr ms mt nb ne nl 
                           5 15  4  6  0 13 23  3  3  3  4 11  2 42  1 125

                          nn or pa pl  ps pt pt_BR ro ru rw sk sl sq sr 
                        +------------------------------------------------+
     a2ps               |          []     []  []   [] []       []    []  |
     aegis              |                     []      []                 |
     anubis             |          []                 []             []  |
     aspell             |          []         []   [] []    [] []    []  |
     bash               |          []         []      []    [] []    []  |
     bfd                |                             []             []  |
     binutils           |                             []             []  |
     bison              |          []         []                     []  |
     bison-runtime      |          []     []  []   [] []       [] [] []  |
     buzztrax           |                     []                         |
     ccd2cue            |                     []                     []  |
     ccide              |          []                 []             []  |
     cflow              |          []         []                     []  |
     clisp              |                             []                 |
     coreutils          |          []                 []       []    []  |
     cpio               |          []                 []             []  |
     cppi               |          []         []                     []  |
     cpplib             |                     []      []             []  |
     cryptsetup         |          []         []                     []  |
     datamash           |                     []                     []  |
     denemo             |                                                |
     dfarc              |          []         []                     []  |
     dialog             |          []         []   [] []    [] []    []  |
     dico               |          []                                    |
     diffutils          |          []         []                     []  |
     dink               |                                                |
     direvent           |          []         []                     []  |
     doodle             |                                         [] []  |
     dos2unix           |          []         []      []             []  |
     dos2unix-man       |          []         []                         |
     e2fsprogs          |          []                                    |
     enscript           |          []         []   [] []       []    []  |
     exif               |          []         []   [] []    []       []  |
     fetchmail          |          []                 []          []     |
     findutils          |          []         []      []    [] []    []  |
     flex               |          []         []   [] []             []  |
     freedink           |          []         []      []       []    []  |
     fusionforge        |                                                |
     gas                |                                                |
     gawk               |          []                                    |
     gcal               |                                                |
     gcc                |                                                |
     gdbm               |          []         []                     []  |
     gettext-examples   |          []     []  []   [] []    [] []    []  |
     gettext-runtime    | []       []     []  []   [] []    [] []    []  |
     gettext-tools      |          []         []   [] []    [] []    []  |
     gjay               |                                            []  |
     glunarclock        |          []         []   []       [] []    []  |
     gnubiff            |                                            []  |
     gnubik             |          []         []               []    []  |
     gnucash            |          ()     ()  ()   () ()             []  |
     gnuchess           |                     []                     []  |
     gnulib             |          []         []      []       []    []  |
     gnunet             |                                                |
     gnunet-gtk         |                                                |
     gold               |                                                |
     gphoto2            |          []         []   [] []             []  |
     gprof              |                     []   [] []             []  |
     gramadoir          |                                   []       []  |
     grep               |          []         []      []    [] []    []  |
     grub               |          []         []      []       []    []  |
     gsasl              |          []                       []       []  |
     gss                |          []              []       []       []  |
     gst-plugins-bad    |          []         []      []    []       []  |
     gst-plugins-base   |          []         []      []    [] []    []  |
     gst-plugins-good   |          []         []   [] []    [] []    []  |
     gst-plugins-ugly   |          []         []   [] []    [] []    []  |
     gstreamer          |          []         []   [] []    [] []    []  |
     gtick              |          []         []      []    []       []  |
     gtkam              |       [] []         []      []    []       []  |
     gtkspell           |          []     []  []   [] []    [] [] [] []  |
     guix               |                                                |
     guix-packages      |                                                |
     gutenprint         |                                   [] []        |
     hello              |          []         []      []    [] []    []  |
     help2man           |          []         []      []             []  |
     help2man-texi      |          []                                    |
     hylafax            |                                                |
     idutils            |          []                 []             []  |
     iso_15924          |          []     ()       [] []       []    []  |
     iso_3166           | [] [] [] []     ()  []   [] [] [] [] [] [] []  |
     iso_3166_2         |          []     ()                         []  |
     iso_4217           | []       []     ()       [] [] []    []    []  |
     iso_639            |    [] [] []     ()       [] [] [] [] []    []  |
     iso_639_3          |       []        ()                             |
     iso_639_5          |                 ()                         []  |
     jwhois             |          []         []   []                []  |
     kbd                |          []                 []                 |
     klavaro            |       [] []         []      []       []        |
     ld                 |                                                |
     leafpad            | []       []     []  []      []    [] []    []  |
     libc               |          []                 []    []           |
     libexif            |          []         ()            []           |
     libextractor       |          []                                    |
     libgnutls          |          []                                    |
     libgphoto2         |          []                                    |
     libgphoto2_port    |          []         []      []    []       []  |
     libgsasl           |          []              []       []       []  |
     libiconv           |          []         []            [] []    []  |
     libidn             |          []         []                     []  |
     liferea            |          []     []  []   [] ()    []    []     |
     lilypond           |                                                |
     lordsawar          |                                                |
     lprng              |          []                                    |
     lynx               |                     []      []                 |
     m4                 |          []         []   [] []             []  |
     mailfromd          |          []                                    |
     mailutils          |          []                                    |
     make               |          []         []      []                 |
     man-db             |          []                 []             []  |
     man-db-manpages    |          []                 []             []  |
     midi-instruments   |          []     []  []   [] []    [] []    []  |
     minicom            |          []         []   [] []                 |
     mkisofs            |          []                 []             []  |
     myserver           |                                      []    []  |
     nano               |          []         []   [] []       []    []  |
     opcodes            |                                                |
     parted             |          []         []      []    [] []    []  |
     pies               |          []                                    |
     pnmixer            |                             []                 |
     popt               |          []     []  []      []       []    []  |
     procps-ng          |          []                                    |
     procps-ng-man      |          []                                    |
     psmisc             |          []         []      []             []  |
     pspp               |          []                 []                 |
     pushover           |                                                |
     pwdutils           |          []                                    |
     pyspread           | []                  []                         |
     radius             |          []                 []                 |
     recode             |          []     []  []   [] []    [] []    []  |
     recutils           |                     []                     []  |
     rpm                |          []                                    |
     rush               |          []         []                     []  |
     sarg               |                     []      []                 |
     sed                |          []     []  []   [] []    [] []    []  |
     sharutils          |          []         []                     []  |
     shishi             |          []                                []  |
     skribilo           |                                            []  |
     solfege            |          []         []      []                 |
     solfege-manual     |          []         []                         |
     spotmachine        |                     []                     []  |
     sudo               |          []         []      []    [] []    []  |
     sudoers            |          []         []               []    []  |
     sysstat            |          []         []      []    []       []  |
     tar                |          []         []      []       []    []  |
     texinfo            |          []         []      []                 |
     texinfo_document   |          []         []                         |
     tigervnc           |                     []      []             []  |
     tin                |                             []                 |
     tin-man            |                                                |
     tracgoogleappsa... |          []         []      []             []  |
     trader             |                             []             []  |
     util-linux         |          []         []                         |
     ve                 |          []         []                     []  |
     vice               |                                                |
     vmm                |                                                |
     vorbis-tools       |          []                          []    []  |
     wastesedge         |                                                |
     wcd                |                                                |
     wcd-man            |                                                |
     wdiff              |          []         []      []       []    []  |
     wget               |          []         []      []    []       []  |
     wyslij-po          | []       []         []                     []  |
     xboard             |          []                 []             []  |
     xdg-user-dirs      | [] [] [] []  [] []  []   [] []    [] [] [] []  |
     xkeyboard-config   |          []         []      []       []        |
                        +------------------------------------------------+
                          nn or pa pl  ps pt pt_BR ro ru rw sk sl sq sr 
                           7  3  6 114  1 12  88   32 82  3 40 45  7 101

                          sv  sw ta te tg th tr uk  ur vi  wa wo zh_CN
                        +----------------------------------------------+
     a2ps               | []              [] [] []     []              |
     aegis              |                              []              |
     anubis             | []                 [] []     []              |
     aspell             | []                    []     []  []     []   |
     bash               | []                    []     []         []   |
     bfd                | []                    []     []              |
     binutils           | []                    []     []              |
     bison              | []                    []     []         []   |
     bison-runtime      | []              [] [] []     []         []   |
     buzztrax           | []                           []         []   |
     ccd2cue            |                       []     []         []   |
     ccide              | []                    []     []         []   |
     cflow              | []                    []     []         []   |
     clisp              |                                              |
     coreutils          | []                    []     []              |
     cpio               | []                 [] []     []         []   |
     cppi               | []                    []     []         []   |
     cpplib             | []                 [] []     []         []   |
     cryptsetup         |                       []     []         []   |
     datamash           | []                    []     []              |
     denemo             |                                         []   |
     dfarc              | []                           []              |
     dialog             | []  []          []           []  []     []   |
     dico               |                       []                     |
     diffutils          | []                 [] []     []         []   |
     dink               | []                                           |
     direvent           |                       []     []              |
     doodle             | []                           []              |
     dos2unix           | []                    []     []         []   |
     dos2unix-man       | []                    []                []   |
     e2fsprogs          | []                    []     []         []   |
     enscript           | []                 [] []     []              |
     exif               | []                 [] []     []         []   |
     fetchmail          | []                 []        []         []   |
     findutils          | []                 [] []     []         []   |
     flex               | []                 []        []         []   |
     freedink           | []              []           []              |
     fusionforge        |                                              |
     gas                |                       []                     |
     gawk               | []                           []         []   |
     gcal               | []                 []                   []   |
     gcc                | []                                           |
     gdbm               |                       []     []              |
     gettext-examples   | []                 [] []     []         []   |
     gettext-runtime    | []                 [] []     []         []   |
     gettext-tools      | []                 [] []     []         []   |
     gjay               |                 []           []         []   |
     glunarclock        | []                           []  []     []   |
     gnubiff            | []                           []              |
     gnubik             | []                    []     []         []   |
     gnucash            |        () ()              () ()         []   |
     gnuchess           |                       []     []         []   |
     gnulib             | []                    []     []         []   |
     gnunet             |                                              |
     gnunet-gtk         |                                              |
     gold               |                       []     []              |
     gphoto2            | []                    []     []         []   |
     gprof              | []                 [] []     []              |
     gramadoir          | []                           []         []   |
     grep               | []              []    []     []         []   |
     grub               | []                 [] []     []              |
     gsasl              | []                    []     []         []   |
     gss                | []                           []         []   |
     gst-plugins-bad    | []                 [] []     []         []   |
     gst-plugins-base   | []                 [] []     []         []   |
     gst-plugins-good   | []                 [] []     []         []   |
     gst-plugins-ugly   | []                 [] []     []         []   |
     gstreamer          | []                 [] []     []         []   |
     gtick              |                       []     []         []   |
     gtkam              | []                    []     []         []   |
     gtkspell           | []              [] [] []     []  []     []   |
     guix               |                                              |
     guix-packages      |                                              |
     gutenprint         |                    [] []     []         []   |
     hello              | []              [] [] []     []         []   |
     help2man           |                       []     []         []   |
     help2man-texi      |                       []                     |
     hylafax            |                              []              |
     idutils            |                       []     []         []   |
     iso_15924          | []              () [] []     ()         []   |
     iso_3166           | []        []    () [] []     ()  []     []   |
     iso_3166_2         |                 () [] []     ()         []   |
     iso_4217           | []              () [] []     ()         []   |
     iso_639            | []     [] []    () [] []     ()  []     []   |
     iso_639_3          |        []       () [] []     ()              |
     iso_639_5          |                 ()    []     ()              |
     jwhois             | []                 []        []         []   |
     kbd                | []                    []     []         []   |
     klavaro            | []                    []  [] []     []  []   |
     ld                 | []                 [] []     []         []   |
     leafpad            | []              [] [] []     []         []   |
     libc               | []                 [] []     []         []   |
     libexif            | []                           []         ()   |
     libextractor       |                       []     []              |
     libgnutls          | []                    []     []         []   |
     libgphoto2         | []                    []     []              |
     libgphoto2_port    | []                    []     []         []   |
     libgsasl           | []                    []     []         []   |
     libiconv           | []                    []     []  []     []   |
     libidn             | ()                    []     []         []   |
     liferea            | []                 [] []     []         []   |
     lilypond           |                              []              |
     lordsawar          |                                              |
     lprng              |                              []              |
     lynx               | []                 [] []     []              |
     m4                 | []                           []         []   |
     mailfromd          |                       []     []              |
     mailutils          |                              []              |
     make               | []                    []     []         []   |
     man-db             | []                           []         []   |
     man-db-manpages    | []                                      []   |
     midi-instruments   | []              [] [] []     []         []   |
     minicom            | []                           []              |
     mkisofs            |                       []     []         []   |
     myserver           |                              []              |
     nano               | []                    []     []         []   |
     opcodes            |                       []     []         []   |
     parted             | []                 [] []     []         []   |
     pies               |                       []     []              |
     pnmixer            |                       []     []         []   |
     popt               | []     []       [] [] []     []         []   |
     procps-ng          |                       []     []              |
     procps-ng-man      |                       []                     |
     psmisc             | []                    []     []         []   |
     pspp               |                    [] []                []   |
     pushover           | []                                           |
     pwdutils           | []                           []              |
     pyspread           |                       []                     |
     radius             |                       []     []              |
     recode             | []                 []        []         []   |
     recutils           | []                    []     []              |
     rpm                | []                    []     []         []   |
     rush               |                       []     []              |
     sarg               |                                              |
     sed                | []                 [] []     []         []   |
     sharutils          | []                    []     []         []   |
     shishi             |                              []         []   |
     skribilo           | []                    []                     |
     solfege            | []                 []        []         []   |
     solfege-manual     |                    []                        |
     spotmachine        | []                    []     []              |
     sudo               | []                 [] []     []         []   |
     sudoers            | []                    []     []         []   |
     sysstat            | []                 [] []     []         []   |
     tar                | []                 [] []     []         []   |
     texinfo            |                    [] []     []              |
     texinfo_document   |                       []                     |
     tigervnc           | []                    []                []   |
     tin                |                                         []   |
     tin-man            |                                              |
     tracgoogleappsa... | []              []    []     []         []   |
     trader             | []                                           |
     util-linux         | []                    []     []         []   |
     ve                 | []                    []     []         []   |
     vice               | ()                 ()                        |
     vmm                |                                              |
     vorbis-tools       | []                           []              |
     wastesedge         |                                              |
     wcd                |                       []     []         []   |
     wcd-man            |                       []                     |
     wdiff              | []                    []     []         []   |
     wget               |                       []     []         []   |
     wyslij-po          |                       []     []              |
     xboard             |                       []                []   |
     xdg-user-dirs      | []     [] []    [] [] []     []         []   |
     xkeyboard-config   | []                 [] []     []              |
                        +----------------------------------------------+
                          sv  sw ta te tg th tr uk  ur vi  wa wo zh_CN
                          106  1  4  3  0 13 51 115  1 125  7  1  100 

                          zh_HK zh_TW
                        +-------------+
     a2ps               |             | 30
     aegis              |             |  9
     anubis             |             | 19
     aspell             |             | 29
     bash               |        []   | 23
     bfd                |             | 11
     binutils           |             | 12
     bison              |        []   | 18
     bison-runtime      |        []   | 38
     buzztrax           |             |  9
     ccd2cue            |             | 10
     ccide              |             | 17
     cflow              |             | 16
     clisp              |             | 10
     coreutils          |             | 18
     cpio               |             | 20
     cppi               |             | 17
     cpplib             |        []   | 19
     cryptsetup         |             | 14
     datamash           |             | 11
     denemo             |             |  5
     dfarc              |             | 17
     dialog             |        []   | 42
     dico               |             |  6
     diffutils          |             | 22
     dink               |             | 10
     direvent           |             | 11
     doodle             |             | 12
     dos2unix           |        []   | 18
     dos2unix-man       |             |  9
     e2fsprogs          |             | 15
     enscript           |             | 21
     exif               |             | 27
     fetchmail          |             | 19
     findutils          |             | 29
     flex               |        []   | 19
     freedink           |             | 24
     fusionforge        |             |  3
     gas                |             |  5
     gawk               |             | 13
     gcal               |             |  8
     gcc                |             |  2
     gdbm               |             | 10
     gettext-examples   |  []    []   | 40
     gettext-runtime    |  []    []   | 35
     gettext-tools      |        []   | 24
     gjay               |             |  9
     glunarclock        |        []   | 27
     gnubiff            |             |  9
     gnubik             |             | 19
     gnucash            |        ()   |  6
     gnuchess           |             | 11
     gnulib             |             | 23
     gnunet             |             |  1
     gnunet-gtk         |             |  1
     gold               |             |  7
     gphoto2            |        []   | 19
     gprof              |             | 21
     gramadoir          |             | 14
     grep               |        []   | 31
     grub               |             | 21
     gsasl              |        []   | 19
     gss                |             | 17
     gst-plugins-bad    |             | 21
     gst-plugins-base   |             | 27
     gst-plugins-good   |             | 32
     gst-plugins-ugly   |             | 34
     gstreamer          |        []   | 32
     gtick              |             | 19
     gtkam              |             | 24
     gtkspell           |  []    []   | 48
     guix               |             |  2
     guix-packages      |             |  0
     gutenprint         |             | 15
     hello              |        []   | 30
     help2man           |             | 18
     help2man-texi      |             |  5
     hylafax            |             |  5
     idutils            |             | 14
     iso_15924          |        []   | 23
     iso_3166           |  []    []   | 58
     iso_3166_2         |             |  9
     iso_4217           |  []    []   | 28
     iso_639            |  []    []   | 46
     iso_639_3          |             | 10
     iso_639_5          |             |  2
     jwhois             |        []   | 20
     kbd                |             | 17
     klavaro            |             | 30
     ld                 |        []   | 15
     leafpad            |        []   | 39
     libc               |        []   | 24
     libexif            |             | 10
     libextractor       |             |  5
     libgnutls          |             | 13
     libgphoto2         |             | 10
     libgphoto2_port    |        []   | 19
     libgsasl           |             | 18
     libiconv           |        []   | 29
     libidn             |             | 17
     liferea            |             | 29
     lilypond           |             | 11
     lordsawar          |             |  3
     lprng              |             |  3
     lynx               |             | 19
     m4                 |        []   | 22
     mailfromd          |             |  4
     mailutils          |             |  6
     make               |             | 19
     man-db             |             | 15
     man-db-manpages    |             | 10
     midi-instruments   |        []   | 43
     minicom            |        []   | 17
     mkisofs            |             | 13
     myserver           |             |  9
     nano               |        []   | 30
     opcodes            |             | 12
     parted             |        []   | 23
     pies               |             |  4
     pnmixer            |             |  9
     popt               |        []   | 36
     procps-ng          |             |  5
     procps-ng-man      |             |  4
     psmisc             |        []   | 22
     pspp               |             | 13
     pushover           |             |  6
     pwdutils           |             |  8
     pyspread           |             |  6
     radius             |             |  9
     recode             |             | 31
     recutils           |             | 10
     rpm                |        []   | 13
     rush               |             | 10
     sarg               |             |  4
     sed                |        []   | 35
     sharutils          |             | 13
     shishi             |             |  7
     skribilo           |             |  7
     solfege            |             | 21
     solfege-manual     |             |  9
     spotmachine        |             | 11
     sudo               |             | 26
     sudoers            |             | 22
     sysstat            |             | 23
     tar                |        []   | 30
     texinfo            |             | 17
     texinfo_document   |             | 13
     tigervnc           |             | 14
     tin                |        []   |  7
     tin-man            |             |  1
     tracgoogleappsa... |        []   | 22
     trader             |             | 12
     util-linux         |             | 13
     ve                 |             | 14
     vice               |             |  1
     vmm                |             |  3
     vorbis-tools       |             | 13
     wastesedge         |             |  3
     wcd                |             |  8
     wcd-man            |             |  3
     wdiff              |        []   | 23
     wget               |             | 21
     wyslij-po          |             | 14
     xboard             |             | 10
     xdg-user-dirs      |  []    []   | 68
     xkeyboard-config   |        []   | 28
                        +-------------+
       89 <USER>           <GROUP> zh_TW
      166 domains           7    42    2809

   Some counters in the preceding matrix are higher than the number of
visible blocks let us expect.  This is because a few extra PO files are
used for implementing regional variants of languages, or language
dialects.

   For a PO file in the matrix above to be effective, the package to
which it applies should also have been internationalized and distributed
as such by its maintainer.  There might be an observable lag between the
mere existence a PO file and its wide availability in a distribution.

   If Jun 2014 seems to be old, you may fetch a more recent copy of this
'ABOUT-NLS' file on most GNU archive sites.  The most up-to-date matrix
with full percentage details can be found at
'http://translationproject.org/extra/matrix.html'.

1.5 Using 'gettext' in new packages
===================================

If you are writing a freely available program and want to
internationalize it you are welcome to use GNU 'gettext' in your
package.  Of course you have to respect the GNU Lesser General Public
License which covers the use of the GNU 'gettext' library.  This means
in particular that even non-free programs can use 'libintl' as a shared
library, whereas only free software can use 'libintl' as a static
library or use modified versions of 'libintl'.

   Once the sources are changed appropriately and the setup can handle
the use of 'gettext' the only thing missing are the translations.  The
Free Translation Project is also available for packages which are not
developed inside the GNU project.  Therefore the information given above
applies also for every other Free Software Project.  Contact
'<EMAIL>' to make the '.pot' files available
to the translation teams.

#!/usr/bin/env python
# -*- Mode: python; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*-
#
# This program is free software; you can redistribute it and/or modify it under
# the terms of the GNU Lesser General Public License as published by the Free
# Software Foundation; either version 2 of the License, or (at your option) any
# later version.
#
# This program is distributed in the hope that it will be useful, but WITHOUT
# ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
# FOR A PARTICULAR PURPOSE.  See the GNU Lesser General Public License for more
# details.
#
# You should have received a copy of the GNU Lesser General Public License along
# with this program; if not, write to the Free Software Foundation, Inc., 51
# Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.
#
# <AUTHOR> <EMAIL>
#

import sys, signal, gi

gi.require_version('ModemManager', '1.0')
from gi.repository import GLib, GObject, Gio, ModemManager

if __name__ == "__main__":

    # Process input arguments
    if len(sys.argv) != 1:
        sys.stderr.write('error: wrong number of arguments\n')
        sys.stdout.write('usage: network-scan-python\n')
        sys.exit(1)

    # Connection to ModemManager
    connection = Gio.bus_get_sync (Gio.BusType.SYSTEM, None)
    manager = ModemManager.Manager.new_sync (connection, Gio.DBusObjectManagerClientFlags.DO_NOT_AUTO_START, None)
    if manager.get_name_owner() is None:
        sys.stderr.write('ModemManager not found in bus')
        sys.exit(2)

    # Iterate modems and scan network with each one by one
    for obj in manager.get_objects():
        modem3gpp = obj.get_modem_3gpp()
        modem3gpp.set_default_timeout(300000)
        print('%s: starting network scan...' % modem3gpp.get_object_path())
        networks = modem3gpp.scan_sync()
        if networks:
            for network in networks:
                print('%s: %s - %s (%s, %s)' % (
                      network.get_operator_code(),
                      network.get_operator_short(),
                      network.get_operator_short(),
                      ModemManager.modem_access_technology_build_string_from_mask (network.get_access_technology()),
                      ModemManager.Modem3gppNetworkAvailability.get_string(network.get_availability())))
        else:
            print('no networks found')

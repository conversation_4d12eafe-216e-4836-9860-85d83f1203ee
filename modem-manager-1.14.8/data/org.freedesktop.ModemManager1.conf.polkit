<!DOCTYPE busconfig PUBLIC
 "-//freedesktop//DTD D-BUS Bus Configuration 1.0//EN"
 "http://www.freedesktop.org/standards/dbus/1.0/busconfig.dtd">
<busconfig>
  <policy context="default">
    <deny send_destination="org.freedesktop.ModemManager1"
          send_type="method_call"/>

    <!-- Methods listed here are explicitly allowed or PolicyKit protected.
         The rest are restricted to root for security.
      -->

    <!-- org.freedesktop.ModemManager1.xml -->

    <!-- Allowed for everyone -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.DBus.Introspectable"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.DBus.Properties"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.DBus.ObjectManager"/>

    <!-- Protected by the Control policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1"
           send_member="ScanDevices"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1"
           send_member="SetLogging"/>

    <!-- org.freedesktop.ModemManager1.Modem.xml -->

    <!-- Allowed for everyone -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem"
           send_member="ListBearers"/>

    <!-- Protected by the Device.Control policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem"
           send_member="Enable"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem"
           send_member="CreateBearer"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem"
           send_member="DeleteBearer"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem"
           send_member="SetPowerState"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem"
           send_member="Reset"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem"
           send_member="FactoryReset"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem"
           send_member="SetCurrentCapabilities"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem"
           send_member="SetCurrentModes"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem"
           send_member="SetCurrentBands"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem"
           send_member="Command"/>

    <!-- org.freedesktop.ModemManager1.Modem.Firmware.xml -->

    <!-- Protected by the Device.Control policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Firmware"
           send_member="List"/>
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Firmware"
           send_member="Select"/>

    <!-- org.freedesktop.ModemManager1.Modem.Simple.xml -->

    <!-- Allowed for everyone -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Simple"
           send_member="GetStatus"/>

    <!-- Protected by the Device.Control policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Simple"
           send_member="Connect"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Simple"
           send_member="Disconnect"/>

    <!-- org.freedesktop.ModemManager1.Modem.Modem3gpp.xml -->

    <!-- Protected by the Device.Control policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Modem3gpp"
           send_member="Register"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Modem3gpp"
           send_member="Scan"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Modem3gpp"
           send_member="SetEpsUeModeOperation"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Modem3gpp"
           send_member="SetInitialEpsBearerSettings"/>

    <!-- org.freedesktop.ModemManager1.Modem.ModemCdma.xml -->

    <!-- Protected by the Device.Control policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.ModemCdma"
           send_member="Activate"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.ModemCdma"
           send_member="ActivateManual"/>

    <!-- org.freedesktop.ModemManager1.Modem.Oma.xml -->

    <!-- Protected by the Device.Control policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Oma"
           send_member="Setup"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Oma"
           send_member="StartClientInitiatedSession"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Oma"
           send_member="AcceptNetworkInitiatedSession"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Oma"
           send_member="CancelSession"/>

    <!-- org.freedesktop.ModemManager1.Sim.xml -->

    <!-- Protected by the Device.Control policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Sim"
           send_member="SendPin"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Sim"
           send_member="SendPuk"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Sim"
           send_member="EnablePin"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Sim"
           send_member="ChangePin"/>

    <!-- org.freedesktop.ModemManager1.Bearer.xml -->

    <!-- Protected by the Device.Control policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Bearer"
           send_member="Connect"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Bearer"
           send_member="Disconnect"/>

    <!-- org.freedesktop.ModemManager1.Modem.Modem3gpp.Ussd.xml -->

    <!-- Protected by the USSD policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Modem3gpp.Ussd"
           send_member="Initiate"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Modem3gpp.Ussd"
           send_member="Respond"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Modem3gpp.Ussd"
           send_member="Cancel"/>

    <!-- org.freedesktop.ModemManager1.Modem.Location.xml -->

    <!-- Protected by the Device.Control policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Location"
           send_member="Setup"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Location"
           send_member="SetSuplServer"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Location"
           send_member="InjectAssistanceData"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Location"
           send_member="SetGpsRefreshRate"/>

    <!-- Protected by the Location policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Location"
           send_member="GetLocation"/>

    <!-- org.freedesktop.ModemManager1.Modem.Messaging.xml -->

    <!-- Allowed for everyone -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Messaging"
           send_member="List"/>

    <!-- Protected by the Messaging policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Messaging"
           send_member="Create"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Messaging"
           send_member="Delete"/>

    <!-- org.freedesktop.ModemManager1.Sms.xml -->

    <!-- Protected by the Messaging policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Sms"
           send_member="Store"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Sms"
           send_member="Send"/>

    <!-- org.freedesktop.ModemManager1.Modem.Voice.xml -->

    <!-- Allowed for everyone -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Voice"
           send_member="ListCalls"/>

    <!-- Protected by the Voice policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Voice"
           send_member="CreateCall"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Voice"
           send_member="DeleteCall"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Voice"
           send_member="HoldAndAccept"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Voice"
           send_member="HangupAndAccept"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Voice"
           send_member="HangupAll"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Voice"
           send_member="Transfer"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Voice"
           send_member="CallWaitingSetup"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Voice"
           send_member="CallWaitingQuery"/>

    <!-- org.freedesktop.ModemManager1.Call.xml -->

    <!-- Protected by the Voice policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Call"
           send_member="Start"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Call"
           send_member="Accept"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Call"
           send_member="Deflect"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Call"
           send_member="JoinMultiparty"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Call"
           send_member="LeaveMultiparty"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Call"
           send_member="Hangup"/>

    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Call"
           send_member="SendDtmf"/>

    <!-- org.freedesktop.ModemManager1.Modem.Signal.xml -->

    <!-- Protected by the Device.Control policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Signal"
           send_member="Setup"/>

    <!-- org.freedesktop.ModemManager1.Modem.Time.xml -->

    <!-- Protected by the Time policy rule -->
    <allow send_destination="org.freedesktop.ModemManager1"
           send_interface="org.freedesktop.ModemManager1.Modem.Time"
           send_member="GetNetworkTime"/>

  </policy>

  <policy user="root">
    <allow own="org.freedesktop.ModemManager1"/>
    <allow send_destination="org.freedesktop.ModemManager1"/>
  </policy>
</busconfig>

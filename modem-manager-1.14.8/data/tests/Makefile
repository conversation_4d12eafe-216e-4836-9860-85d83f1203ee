# Makefile.in generated by automake 1.16.1 from Makefile.am.
# data/tests/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.



am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/ModemManager
pkgincludedir = $(includedir)/ModemManager
pkglibdir = $(libdir)/ModemManager
pkglibexecdir = $(libexecdir)/ModemManager
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-linux-gnu
host_triplet = arm-buildroot-linux-gnueabihf
subdir = data/tests
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_code_coverage.m4 \
	$(top_srcdir)/m4/gettext.m4 $(top_srcdir)/m4/gtk-doc.m4 \
	$(top_srcdir)/m4/iconv.m4 $(top_srcdir)/m4/intlmacosx.m4 \
	$(top_srcdir)/m4/introspection.m4 $(top_srcdir)/m4/lib-ld.m4 \
	$(top_srcdir)/m4/lib-link.m4 $(top_srcdir)/m4/lib-prefix.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/mm-enable-plugin.m4 $(top_srcdir)/m4/nls.m4 \
	$(top_srcdir)/m4/po.m4 $(top_srcdir)/m4/progtest.m4 \
	$(top_srcdir)/m4/vapigen.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES = org.freedesktop.ModemManager1.service
CONFIG_CLEAN_VPATH_FILES =
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
SOURCES =
DIST_SOURCES =
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
am__DIST_COMMON = $(srcdir)/Makefile.in \
	$(srcdir)/org.freedesktop.ModemManager1.service.in
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 0
AR = ar
AUTOCONF = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing autoconf
AUTOHEADER = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing autoheader
AUTOMAKE = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing automake-1.16
AWK = gawk
CC = /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc
CCDEPMODE = depmode=none
CFLAGS = -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -std=gnu89
CODE_COVERAGE_CFLAGS = 
CODE_COVERAGE_ENABLED = no
CODE_COVERAGE_LDFLAGS = 
CPP = /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-cpp
CPPFLAGS = -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64
CYGPATH_W = echo
DBUS_SYS_DIR = /etc/dbus-1/system.d
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
DLLTOOL = false
DSYMUTIL = 
DUMPBIN = :
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
EXEEXT = 
FGREP = /usr/bin/grep -F
GCOV = 
GDBUS_CODEGEN = gdbus-codegen
GENHTML = 
GETTEXT_MACRO_VERSION = 0.19
GETTEXT_PACKAGE = ModemManager
GLIB_MKENUMS = glib-mkenums
GMSGFMT = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
GMSGFMT_015 = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
GREP = /usr/bin/grep
GTKDOC_CHECK = 
GTKDOC_CHECK_PATH = 
GTKDOC_DEPS_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include 
GTKDOC_DEPS_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgobject-2.0 -lglib-2.0 
GTKDOC_MKPDF = 
GTKDOC_REBASE = true
GUDEV_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gudev-1.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include 
GUDEV_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgudev-1.0 -lgobject-2.0 -lglib-2.0 
HTML_DIR = ${datadir}/gtk-doc/html
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
INTLLIBS = 
INTL_MACOSX_LIBS = 
INTROSPECTION_CFLAGS = 
INTROSPECTION_COMPILER = 
INTROSPECTION_GENERATE = 
INTROSPECTION_GIRDIR = 
INTROSPECTION_LIBS = 
INTROSPECTION_MAKEFILE = 
INTROSPECTION_SCANNER = 
INTROSPECTION_TYPELIBDIR = 
LCOV = 
LD = /opt/gcc-arm/arm-none-linux-gnueabihf/bin/ld
LDFLAGS = 
LIBICONV = -liconv
LIBINTL = 
LIBMM_GLIB_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gio-unix-2.0 -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS
LIBMM_GLIB_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgio-2.0 -lgobject-2.0 -lglib-2.0 
LIBOBJS = 
LIBS = 
LIBSYSTEMD_CFLAGS = 
LIBSYSTEMD_LIBS = 
LIBSYSTEMD_LOGIN_CFLAGS = 
LIBSYSTEMD_LOGIN_LIBS = 
LIBTOOL = $(SHELL) $(top_builddir)/libtool
LIPO = 
LN_S = ln -s
LTLIBICONV = -liconv
LTLIBINTL = 
LTLIBOBJS = 
LT_SYS_LIBRARY_PATH = 
MAINT = 
MAKEINFO = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing makeinfo
MANIFEST_TOOL = :
MBIM_CFLAGS = 
MBIM_LIBS = 
MKDIR_P = /usr/bin/mkdir -p
MMCLI_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS
MMCLI_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgio-2.0 -lgobject-2.0 -lglib-2.0 
MM_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gio-unix-2.0 -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS
MM_DEFAULT_USER_POLICY = 
MM_GLIB_LT_AGE = 6
MM_GLIB_LT_CURRENT = 6
MM_GLIB_LT_REVISION = 0
MM_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -Wl,--export-dynamic -lgmodule-2.0 -pthread -lglib-2.0 -lgio-2.0 -lgobject-2.0 -lglib-2.0 
MM_MAJOR_VERSION = 1
MM_MICRO_VERSION = 8
MM_MINOR_VERSION = 14
MM_POLKIT_SERVICE = 
MM_VERSION = 1.14.8
MSGFMT = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
MSGFMT_015 = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
MSGMERGE = /root/buildroot-2021.02/dev_out/host/bin/msgmerge
NM = nm
NMEDIT = 
OBJDUMP = objdump
OBJEXT = o
OTOOL = 
OTOOL64 = 
PACKAGE = ModemManager
PACKAGE_BUGREPORT = https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues
PACKAGE_NAME = ModemManager
PACKAGE_STRING = ModemManager 1.14.8
PACKAGE_TARNAME = ModemManager
PACKAGE_URL = 
PACKAGE_VERSION = 1.14.8
PATH_SEPARATOR = :
PKG_CONFIG = /root/buildroot-2021.02/dev_out/host/bin/pkg-config
PKG_CONFIG_LIBDIR = 
PKG_CONFIG_PATH = 
POLKIT_CFLAGS = 
POLKIT_LIBS = 
POSUB = po
QMI_CFLAGS = 
QMI_LIBS = 
RANLIB = ranlib
SED = /usr/bin/sed
SET_MAKE = 
SHELL = /bin/bash
STRIP = strip
SYSTEMD_UNIT_DIR = 
UDEV_BASE_DIR = /lib/udev
USE_NLS = yes
VAPIGEN = 
VAPIGEN_MAKEFILE = 
VAPIGEN_VAPIDIR = 
VERSION = 1.14.8
WARN_CFLAGS = -fno-strict-aliasing -Wnested-externs -Wmissing-prototypes -Wstrict-prototypes -Wdeclaration-after-statement -Wimplicit-function-declaration -Wold-style-definition -Wjump-misses-init -Wall -Wextra -Wundef -Wwrite-strings -Wpointer-arith -Wmissing-declarations -Wredundant-decls -Wno-unused-parameter -Wno-missing-field-initializers -Wformat=2 -Wcast-align -Wformat-nonliteral -Wformat-security -Wsign-compare -Wstrict-aliasing -Wshadow -Winline -Wpacked -Wmissing-format-attribute -Wmissing-noreturn -Winit-self -Wmissing-include-dirs -Wunused-but-set-variable -Warray-bounds -Wreturn-type -Wswitch-enum -Wswitch-default -Wduplicated-cond -Wduplicated-branches -Wlogical-op -Wrestrict -Wnull-dereference -Wdouble-promotion -Wno-error=unused-parameter -Wno-error=missing-field-initializers -Wno-cast-function-type -Wno-packed -Wno-error=cast-function-type -Wno-error=packed
WARN_LDFLAGS = -Wl,--no-as-needed
WARN_SCANNERFLAGS =               --warn-all                                                             
XGETTEXT = /root/buildroot-2021.02/dev_out/host/bin/xgettext
XGETTEXT_015 = /root/buildroot-2021.02/dev_out/host/bin/xgettext
XGETTEXT_EXTRA_OPTIONS = 
XSLTPROC_CHECK = yes
abs_builddir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/data/tests
abs_srcdir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/data/tests
abs_top_builddir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8
abs_top_srcdir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8
ac_ct_AR = ar
ac_ct_CC = 
ac_ct_DUMPBIN = link -dump
am__include = include
am__leading_dot = .
am__quote = 
am__tar = tar --format=ustar -chf - "$$tardir"
am__untar = tar -xf -
bindir = ${exec_prefix}/bin
build = x86_64-pc-linux-gnu
build_alias = x86_64-pc-linux-gnu
build_cpu = x86_64
build_os = linux-gnu
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = /usr
host = arm-buildroot-linux-gnueabihf
host_alias = arm-buildroot-linux-gnueabihf
host_cpu = arm
host_os = linux-gnueabihf
host_vendor = buildroot
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
localedir = ${datarootdir}/locale
localstatedir = /var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /usr
program_transform_name = s&^&&
psdir = ${docdir}
runstatedir = ${localstatedir}/run
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = .
sysconfdir = /etc
target_alias = arm-buildroot-linux-gnueabihf
top_build_prefix = ../../
top_builddir = ../..
top_srcdir = ../..
EXTRA_DIST = org.freedesktop.ModemManager1.service.in
all: all-am

.SUFFIXES:
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu data/tests/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu data/tests/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
org.freedesktop.ModemManager1.service: $(top_builddir)/config.status $(srcdir)/org.freedesktop.ModemManager1.service.in
	cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
tags TAGS:

ctags CTAGS:

cscope cscopelist:


distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile
installdirs:
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool mostlyclean-am

distclean: distclean-am
	-rm -f Makefile
distclean-am: clean-am distclean-generic

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-generic mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am:

.MAKE: install-am install-strip

.PHONY: all all-am check check-am clean clean-generic clean-libtool \
	cscopelist-am ctags-am distclean distclean-generic \
	distclean-libtool distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am install-man \
	install-pdf install-pdf-am install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	maintainer-clean maintainer-clean-generic mostlyclean \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags-am uninstall uninstall-am

.PRECIOUS: Makefile


# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

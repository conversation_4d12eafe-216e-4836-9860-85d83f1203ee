# Makefile.in generated by automake 1.16.1 from Makefile.am.
# libqcdm/tests/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.




am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/ModemManager
pkgincludedir = $(includedir)/ModemManager
pkglibdir = $(libdir)/ModemManager
pkglibexecdir = $(libexecdir)/ModemManager
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-linux-gnu
host_triplet = arm-buildroot-linux-gnueabihf
noinst_PROGRAMS = test-qcdm$(EXEEXT) modepref$(EXEEXT) \
	ipv6pref$(EXEEXT) reset$(EXEEXT)
#am__append_1 = $(top_builddir)/src/libqcdm.la
#am__append_2 = $(top_builddir)/src/libqcdm.la
#am__append_3 = $(top_builddir)/src/libqcdm.la
#am__append_4 = $(top_builddir)/src/libqcdm.la
am__append_5 = $(top_builddir)/libqcdm/src/libqcdm.la
am__append_6 = $(top_builddir)/libqcdm/src/libqcdm.la
am__append_7 = $(top_builddir)/libqcdm/src/libqcdm.la
am__append_8 = $(top_builddir)/libqcdm/src/libqcdm.la
subdir = libqcdm/tests
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_code_coverage.m4 \
	$(top_srcdir)/m4/gettext.m4 $(top_srcdir)/m4/gtk-doc.m4 \
	$(top_srcdir)/m4/iconv.m4 $(top_srcdir)/m4/intlmacosx.m4 \
	$(top_srcdir)/m4/introspection.m4 $(top_srcdir)/m4/lib-ld.m4 \
	$(top_srcdir)/m4/lib-link.m4 $(top_srcdir)/m4/lib-prefix.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/mm-enable-plugin.m4 $(top_srcdir)/m4/nls.m4 \
	$(top_srcdir)/m4/po.m4 $(top_srcdir)/m4/progtest.m4 \
	$(top_srcdir)/m4/vapigen.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
PROGRAMS = $(noinst_PROGRAMS)
am_ipv6pref_OBJECTS = ipv6pref-ipv6pref.$(OBJEXT)
ipv6pref_OBJECTS = $(am_ipv6pref_OBJECTS)
am__DEPENDENCIES_1 =
ipv6pref_DEPENDENCIES = $(am__DEPENDENCIES_1) $(am__append_3) \
	$(am__append_7)
AM_V_lt = $(am__v_lt_$(V))
am__v_lt_ = $(am__v_lt_$(AM_DEFAULT_VERBOSITY))
am__v_lt_0 = --silent
am__v_lt_1 = 
am_modepref_OBJECTS = modepref-modepref.$(OBJEXT)
modepref_OBJECTS = $(am_modepref_OBJECTS)
modepref_DEPENDENCIES = $(am__DEPENDENCIES_1) $(am__append_2) \
	$(am__append_6)
am_reset_OBJECTS = reset-reset.$(OBJEXT)
reset_OBJECTS = $(am_reset_OBJECTS)
reset_DEPENDENCIES = $(am__DEPENDENCIES_1) $(am__append_4) \
	$(am__append_8)
am_test_qcdm_OBJECTS = test_qcdm-test-qcdm-crc.$(OBJEXT) \
	test_qcdm-test-qcdm-escaping.$(OBJEXT) \
	test_qcdm-test-qcdm-utils.$(OBJEXT) \
	test_qcdm-test-qcdm-com.$(OBJEXT) \
	test_qcdm-test-qcdm-result.$(OBJEXT) \
	test_qcdm-test-qcdm.$(OBJEXT)
test_qcdm_OBJECTS = $(am_test_qcdm_OBJECTS)
test_qcdm_DEPENDENCIES = $(am__DEPENDENCIES_1) $(am__append_1) \
	$(am__append_5)
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I. -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/ipv6pref-ipv6pref.Po \
	./$(DEPDIR)/modepref-modepref.Po ./$(DEPDIR)/reset-reset.Po \
	./$(DEPDIR)/test_qcdm-test-qcdm-com.Po \
	./$(DEPDIR)/test_qcdm-test-qcdm-crc.Po \
	./$(DEPDIR)/test_qcdm-test-qcdm-escaping.Po \
	./$(DEPDIR)/test_qcdm-test-qcdm-result.Po \
	./$(DEPDIR)/test_qcdm-test-qcdm-utils.Po \
	./$(DEPDIR)/test_qcdm-test-qcdm.Po
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_$(V))
am__v_CC_ = $(am__v_CC_$(AM_DEFAULT_VERBOSITY))
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_$(V))
am__v_CCLD_ = $(am__v_CCLD_$(AM_DEFAULT_VERBOSITY))
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(ipv6pref_SOURCES) $(modepref_SOURCES) $(reset_SOURCES) \
	$(test_qcdm_SOURCES)
DIST_SOURCES = $(ipv6pref_SOURCES) $(modepref_SOURCES) \
	$(reset_SOURCES) $(test_qcdm_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp \
	$(top_srcdir)/gtester.make
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 0
AR = ar
AUTOCONF = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing autoconf
AUTOHEADER = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing autoheader
AUTOMAKE = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing automake-1.16
AWK = gawk
CC = /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc
CCDEPMODE = depmode=none
CFLAGS = -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -std=gnu89
CODE_COVERAGE_CFLAGS = 
CODE_COVERAGE_ENABLED = no
CODE_COVERAGE_LDFLAGS = 
CPP = /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-cpp
CPPFLAGS = -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64
CYGPATH_W = echo
DBUS_SYS_DIR = /etc/dbus-1/system.d
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
DLLTOOL = false
DSYMUTIL = 
DUMPBIN = :
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
EXEEXT = 
FGREP = /usr/bin/grep -F
GCOV = 
GDBUS_CODEGEN = gdbus-codegen
GENHTML = 
GETTEXT_MACRO_VERSION = 0.19
GETTEXT_PACKAGE = ModemManager
GLIB_MKENUMS = glib-mkenums
GMSGFMT = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
GMSGFMT_015 = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
GREP = /usr/bin/grep
GTKDOC_CHECK = 
GTKDOC_CHECK_PATH = 
GTKDOC_DEPS_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include 
GTKDOC_DEPS_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgobject-2.0 -lglib-2.0 
GTKDOC_MKPDF = 
GTKDOC_REBASE = true
GUDEV_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gudev-1.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include 
GUDEV_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgudev-1.0 -lgobject-2.0 -lglib-2.0 
HTML_DIR = ${datadir}/gtk-doc/html
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
INTLLIBS = 
INTL_MACOSX_LIBS = 
INTROSPECTION_CFLAGS = 
INTROSPECTION_COMPILER = 
INTROSPECTION_GENERATE = 
INTROSPECTION_GIRDIR = 
INTROSPECTION_LIBS = 
INTROSPECTION_MAKEFILE = 
INTROSPECTION_SCANNER = 
INTROSPECTION_TYPELIBDIR = 
LCOV = 
LD = /opt/gcc-arm/arm-none-linux-gnueabihf/bin/ld
LDFLAGS = 
LIBICONV = -liconv
LIBINTL = 
LIBMM_GLIB_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gio-unix-2.0 -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS
LIBMM_GLIB_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgio-2.0 -lgobject-2.0 -lglib-2.0 
LIBOBJS = 
LIBS = 
LIBSYSTEMD_CFLAGS = 
LIBSYSTEMD_LIBS = 
LIBSYSTEMD_LOGIN_CFLAGS = 
LIBSYSTEMD_LOGIN_LIBS = 
LIBTOOL = $(SHELL) $(top_builddir)/libtool
LIPO = 
LN_S = ln -s
LTLIBICONV = -liconv
LTLIBINTL = 
LTLIBOBJS = 
LT_SYS_LIBRARY_PATH = 
MAINT = 
MAKEINFO = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing makeinfo
MANIFEST_TOOL = :
MBIM_CFLAGS = 
MBIM_LIBS = 
MKDIR_P = /usr/bin/mkdir -p
MMCLI_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS
MMCLI_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgio-2.0 -lgobject-2.0 -lglib-2.0 
MM_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gio-unix-2.0 -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS
MM_DEFAULT_USER_POLICY = 
MM_GLIB_LT_AGE = 6
MM_GLIB_LT_CURRENT = 6
MM_GLIB_LT_REVISION = 0
MM_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -Wl,--export-dynamic -lgmodule-2.0 -pthread -lglib-2.0 -lgio-2.0 -lgobject-2.0 -lglib-2.0 
MM_MAJOR_VERSION = 1
MM_MICRO_VERSION = 8
MM_MINOR_VERSION = 14
MM_POLKIT_SERVICE = 
MM_VERSION = 1.14.8
MSGFMT = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
MSGFMT_015 = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
MSGMERGE = /root/buildroot-2021.02/dev_out/host/bin/msgmerge
NM = nm
NMEDIT = 
OBJDUMP = objdump
OBJEXT = o
OTOOL = 
OTOOL64 = 
PACKAGE = ModemManager
PACKAGE_BUGREPORT = https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues
PACKAGE_NAME = ModemManager
PACKAGE_STRING = ModemManager 1.14.8
PACKAGE_TARNAME = ModemManager
PACKAGE_URL = 
PACKAGE_VERSION = 1.14.8
PATH_SEPARATOR = :
PKG_CONFIG = /root/buildroot-2021.02/dev_out/host/bin/pkg-config
PKG_CONFIG_LIBDIR = 
PKG_CONFIG_PATH = 
POLKIT_CFLAGS = 
POLKIT_LIBS = 
POSUB = po
QMI_CFLAGS = 
QMI_LIBS = 
RANLIB = ranlib
SED = /usr/bin/sed
SET_MAKE = 
SHELL = /bin/bash
STRIP = strip
SYSTEMD_UNIT_DIR = 
UDEV_BASE_DIR = /lib/udev
USE_NLS = yes
VAPIGEN = 
VAPIGEN_MAKEFILE = 
VAPIGEN_VAPIDIR = 
VERSION = 1.14.8
WARN_CFLAGS = -fno-strict-aliasing -Wnested-externs -Wmissing-prototypes -Wstrict-prototypes -Wdeclaration-after-statement -Wimplicit-function-declaration -Wold-style-definition -Wjump-misses-init -Wall -Wextra -Wundef -Wwrite-strings -Wpointer-arith -Wmissing-declarations -Wredundant-decls -Wno-unused-parameter -Wno-missing-field-initializers -Wformat=2 -Wcast-align -Wformat-nonliteral -Wformat-security -Wsign-compare -Wstrict-aliasing -Wshadow -Winline -Wpacked -Wmissing-format-attribute -Wmissing-noreturn -Winit-self -Wmissing-include-dirs -Wunused-but-set-variable -Warray-bounds -Wreturn-type -Wswitch-enum -Wswitch-default -Wduplicated-cond -Wduplicated-branches -Wlogical-op -Wrestrict -Wnull-dereference -Wdouble-promotion -Wno-error=unused-parameter -Wno-error=missing-field-initializers -Wno-cast-function-type -Wno-packed -Wno-error=cast-function-type -Wno-error=packed
WARN_LDFLAGS = -Wl,--no-as-needed
WARN_SCANNERFLAGS =               --warn-all                                                             
XGETTEXT = /root/buildroot-2021.02/dev_out/host/bin/xgettext
XGETTEXT_015 = /root/buildroot-2021.02/dev_out/host/bin/xgettext
XGETTEXT_EXTRA_OPTIONS = 
XSLTPROC_CHECK = yes
abs_builddir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/libqcdm/tests
abs_srcdir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/libqcdm/tests
abs_top_builddir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8
abs_top_srcdir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8
ac_ct_AR = ar
ac_ct_CC = 
ac_ct_DUMPBIN = link -dump
am__include = include
am__leading_dot = .
am__quote = 
am__tar = tar --format=ustar -chf - "$$tardir"
am__untar = tar -xf -
bindir = ${exec_prefix}/bin
build = x86_64-pc-linux-gnu
build_alias = x86_64-pc-linux-gnu
build_cpu = x86_64
build_os = linux-gnu
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = /usr
host = arm-buildroot-linux-gnueabihf
host_alias = arm-buildroot-linux-gnueabihf
host_cpu = arm
host_os = linux-gnueabihf
host_vendor = buildroot
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
localedir = ${datarootdir}/locale
localstatedir = /var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /usr
program_transform_name = s&^&&
psdir = ${docdir}
runstatedir = ${localstatedir}/run
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = .
sysconfdir = /etc
target_alias = arm-buildroot-linux-gnueabihf
top_build_prefix = ../../
top_builddir = ../..
top_srcdir = ../..
GTESTER = gtester
GTESTER_REPORT = gtester-report

# initialize variables for unconditional += appending
EXTRA_DIST = 
TEST_PROGS = test-qcdm
AM_CFLAGS = \
	$(WARN_CFLAGS) \
	$(CODE_COVERAGE_CFLAGS) \
	$(NULL)

AM_LDFLAGS = \
	$(WARN_LDFLAGS) \
	$(CODE_COVERAGE_LDFLAGS) \
	$(NULL)

test_qcdm_SOURCES = \
	test-qcdm-crc.c \
	test-qcdm-crc.h \
	test-qcdm-escaping.c \
	test-qcdm-escaping.h \
	test-qcdm-utils.c \
	test-qcdm-utils.h \
	test-qcdm-com.c \
	test-qcdm-com.h \
	test-qcdm-result.c \
	test-qcdm-result.h \
	test-qcdm.c

test_qcdm_CPPFLAGS = \
	$(MM_CFLAGS) \
	-I$(top_srcdir)/libqcdm/src \
	-I$(top_srcdir)/src

test_qcdm_LDADD = $(MM_LIBS) $(am__append_1) $(am__append_5)
modepref_SOURCES = modepref.c
modepref_CPPFLAGS = \
	$(MM_CFLAGS) \
	-I$(top_srcdir)/libqcdm/src \
	-I$(top_srcdir)/src

modepref_LDADD = $(MM_LIBS) $(am__append_2) $(am__append_6)
ipv6pref_SOURCES = ipv6pref.c
ipv6pref_CPPFLAGS = \
	$(MM_CFLAGS) \
	-I$(top_srcdir)/libqcdm/src \
	-I$(top_srcdir)/src

ipv6pref_LDADD = $(MM_LIBS) $(am__append_3) $(am__append_7)
reset_SOURCES = reset.c
reset_CPPFLAGS = \
	$(MM_CFLAGS) \
	-I$(top_srcdir)/libqcdm/src \
	-I$(top_srcdir)/src

reset_LDADD = $(MM_LIBS) $(am__append_4) $(am__append_8)
all: all-am

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am $(top_srcdir)/gtester.make $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu libqcdm/tests/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu libqcdm/tests/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;
$(top_srcdir)/gtester.make $(am__empty):

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

clean-noinstPROGRAMS:
	@list='$(noinst_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

ipv6pref$(EXEEXT): $(ipv6pref_OBJECTS) $(ipv6pref_DEPENDENCIES) $(EXTRA_ipv6pref_DEPENDENCIES) 
	@rm -f ipv6pref$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(ipv6pref_OBJECTS) $(ipv6pref_LDADD) $(LIBS)

modepref$(EXEEXT): $(modepref_OBJECTS) $(modepref_DEPENDENCIES) $(EXTRA_modepref_DEPENDENCIES) 
	@rm -f modepref$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(modepref_OBJECTS) $(modepref_LDADD) $(LIBS)

reset$(EXEEXT): $(reset_OBJECTS) $(reset_DEPENDENCIES) $(EXTRA_reset_DEPENDENCIES) 
	@rm -f reset$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(reset_OBJECTS) $(reset_LDADD) $(LIBS)

test-qcdm$(EXEEXT): $(test_qcdm_OBJECTS) $(test_qcdm_DEPENDENCIES) $(EXTRA_test_qcdm_DEPENDENCIES) 
	@rm -f test-qcdm$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_qcdm_OBJECTS) $(test_qcdm_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

#include ./$(DEPDIR)/ipv6pref-ipv6pref.Po # am--include-marker
#include ./$(DEPDIR)/modepref-modepref.Po # am--include-marker
#include ./$(DEPDIR)/reset-reset.Po # am--include-marker
#include ./$(DEPDIR)/test_qcdm-test-qcdm-com.Po # am--include-marker
#include ./$(DEPDIR)/test_qcdm-test-qcdm-crc.Po # am--include-marker
#include ./$(DEPDIR)/test_qcdm-test-qcdm-escaping.Po # am--include-marker
#include ./$(DEPDIR)/test_qcdm-test-qcdm-result.Po # am--include-marker
#include ./$(DEPDIR)/test_qcdm-test-qcdm-utils.Po # am--include-marker
#include ./$(DEPDIR)/test_qcdm-test-qcdm.Po # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
#	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
#	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
#	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(COMPILE) -c -o $@ $<

.c.obj:
#	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
#	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
#	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
#	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
#	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
#	$(am__mv) $$depbase.Tpo $$depbase.Plo
#	$(AM_V_CC)source='$<' object='$@' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LTCOMPILE) -c -o $@ $<

ipv6pref-ipv6pref.o: ipv6pref.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ipv6pref_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ipv6pref-ipv6pref.o -MD -MP -MF $(DEPDIR)/ipv6pref-ipv6pref.Tpo -c -o ipv6pref-ipv6pref.o `test -f 'ipv6pref.c' || echo '$(srcdir)/'`ipv6pref.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/ipv6pref-ipv6pref.Tpo $(DEPDIR)/ipv6pref-ipv6pref.Po
#	$(AM_V_CC)source='ipv6pref.c' object='ipv6pref-ipv6pref.o' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ipv6pref_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ipv6pref-ipv6pref.o `test -f 'ipv6pref.c' || echo '$(srcdir)/'`ipv6pref.c

ipv6pref-ipv6pref.obj: ipv6pref.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ipv6pref_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ipv6pref-ipv6pref.obj -MD -MP -MF $(DEPDIR)/ipv6pref-ipv6pref.Tpo -c -o ipv6pref-ipv6pref.obj `if test -f 'ipv6pref.c'; then $(CYGPATH_W) 'ipv6pref.c'; else $(CYGPATH_W) '$(srcdir)/ipv6pref.c'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/ipv6pref-ipv6pref.Tpo $(DEPDIR)/ipv6pref-ipv6pref.Po
#	$(AM_V_CC)source='ipv6pref.c' object='ipv6pref-ipv6pref.obj' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ipv6pref_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ipv6pref-ipv6pref.obj `if test -f 'ipv6pref.c'; then $(CYGPATH_W) 'ipv6pref.c'; else $(CYGPATH_W) '$(srcdir)/ipv6pref.c'; fi`

modepref-modepref.o: modepref.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(modepref_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT modepref-modepref.o -MD -MP -MF $(DEPDIR)/modepref-modepref.Tpo -c -o modepref-modepref.o `test -f 'modepref.c' || echo '$(srcdir)/'`modepref.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/modepref-modepref.Tpo $(DEPDIR)/modepref-modepref.Po
#	$(AM_V_CC)source='modepref.c' object='modepref-modepref.o' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(modepref_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o modepref-modepref.o `test -f 'modepref.c' || echo '$(srcdir)/'`modepref.c

modepref-modepref.obj: modepref.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(modepref_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT modepref-modepref.obj -MD -MP -MF $(DEPDIR)/modepref-modepref.Tpo -c -o modepref-modepref.obj `if test -f 'modepref.c'; then $(CYGPATH_W) 'modepref.c'; else $(CYGPATH_W) '$(srcdir)/modepref.c'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/modepref-modepref.Tpo $(DEPDIR)/modepref-modepref.Po
#	$(AM_V_CC)source='modepref.c' object='modepref-modepref.obj' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(modepref_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o modepref-modepref.obj `if test -f 'modepref.c'; then $(CYGPATH_W) 'modepref.c'; else $(CYGPATH_W) '$(srcdir)/modepref.c'; fi`

reset-reset.o: reset.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(reset_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT reset-reset.o -MD -MP -MF $(DEPDIR)/reset-reset.Tpo -c -o reset-reset.o `test -f 'reset.c' || echo '$(srcdir)/'`reset.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/reset-reset.Tpo $(DEPDIR)/reset-reset.Po
#	$(AM_V_CC)source='reset.c' object='reset-reset.o' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(reset_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o reset-reset.o `test -f 'reset.c' || echo '$(srcdir)/'`reset.c

reset-reset.obj: reset.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(reset_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT reset-reset.obj -MD -MP -MF $(DEPDIR)/reset-reset.Tpo -c -o reset-reset.obj `if test -f 'reset.c'; then $(CYGPATH_W) 'reset.c'; else $(CYGPATH_W) '$(srcdir)/reset.c'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/reset-reset.Tpo $(DEPDIR)/reset-reset.Po
#	$(AM_V_CC)source='reset.c' object='reset-reset.obj' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(reset_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o reset-reset.obj `if test -f 'reset.c'; then $(CYGPATH_W) 'reset.c'; else $(CYGPATH_W) '$(srcdir)/reset.c'; fi`

test_qcdm-test-qcdm-crc.o: test-qcdm-crc.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT test_qcdm-test-qcdm-crc.o -MD -MP -MF $(DEPDIR)/test_qcdm-test-qcdm-crc.Tpo -c -o test_qcdm-test-qcdm-crc.o `test -f 'test-qcdm-crc.c' || echo '$(srcdir)/'`test-qcdm-crc.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/test_qcdm-test-qcdm-crc.Tpo $(DEPDIR)/test_qcdm-test-qcdm-crc.Po
#	$(AM_V_CC)source='test-qcdm-crc.c' object='test_qcdm-test-qcdm-crc.o' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o test_qcdm-test-qcdm-crc.o `test -f 'test-qcdm-crc.c' || echo '$(srcdir)/'`test-qcdm-crc.c

test_qcdm-test-qcdm-crc.obj: test-qcdm-crc.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT test_qcdm-test-qcdm-crc.obj -MD -MP -MF $(DEPDIR)/test_qcdm-test-qcdm-crc.Tpo -c -o test_qcdm-test-qcdm-crc.obj `if test -f 'test-qcdm-crc.c'; then $(CYGPATH_W) 'test-qcdm-crc.c'; else $(CYGPATH_W) '$(srcdir)/test-qcdm-crc.c'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/test_qcdm-test-qcdm-crc.Tpo $(DEPDIR)/test_qcdm-test-qcdm-crc.Po
#	$(AM_V_CC)source='test-qcdm-crc.c' object='test_qcdm-test-qcdm-crc.obj' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o test_qcdm-test-qcdm-crc.obj `if test -f 'test-qcdm-crc.c'; then $(CYGPATH_W) 'test-qcdm-crc.c'; else $(CYGPATH_W) '$(srcdir)/test-qcdm-crc.c'; fi`

test_qcdm-test-qcdm-escaping.o: test-qcdm-escaping.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT test_qcdm-test-qcdm-escaping.o -MD -MP -MF $(DEPDIR)/test_qcdm-test-qcdm-escaping.Tpo -c -o test_qcdm-test-qcdm-escaping.o `test -f 'test-qcdm-escaping.c' || echo '$(srcdir)/'`test-qcdm-escaping.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/test_qcdm-test-qcdm-escaping.Tpo $(DEPDIR)/test_qcdm-test-qcdm-escaping.Po
#	$(AM_V_CC)source='test-qcdm-escaping.c' object='test_qcdm-test-qcdm-escaping.o' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o test_qcdm-test-qcdm-escaping.o `test -f 'test-qcdm-escaping.c' || echo '$(srcdir)/'`test-qcdm-escaping.c

test_qcdm-test-qcdm-escaping.obj: test-qcdm-escaping.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT test_qcdm-test-qcdm-escaping.obj -MD -MP -MF $(DEPDIR)/test_qcdm-test-qcdm-escaping.Tpo -c -o test_qcdm-test-qcdm-escaping.obj `if test -f 'test-qcdm-escaping.c'; then $(CYGPATH_W) 'test-qcdm-escaping.c'; else $(CYGPATH_W) '$(srcdir)/test-qcdm-escaping.c'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/test_qcdm-test-qcdm-escaping.Tpo $(DEPDIR)/test_qcdm-test-qcdm-escaping.Po
#	$(AM_V_CC)source='test-qcdm-escaping.c' object='test_qcdm-test-qcdm-escaping.obj' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o test_qcdm-test-qcdm-escaping.obj `if test -f 'test-qcdm-escaping.c'; then $(CYGPATH_W) 'test-qcdm-escaping.c'; else $(CYGPATH_W) '$(srcdir)/test-qcdm-escaping.c'; fi`

test_qcdm-test-qcdm-utils.o: test-qcdm-utils.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT test_qcdm-test-qcdm-utils.o -MD -MP -MF $(DEPDIR)/test_qcdm-test-qcdm-utils.Tpo -c -o test_qcdm-test-qcdm-utils.o `test -f 'test-qcdm-utils.c' || echo '$(srcdir)/'`test-qcdm-utils.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/test_qcdm-test-qcdm-utils.Tpo $(DEPDIR)/test_qcdm-test-qcdm-utils.Po
#	$(AM_V_CC)source='test-qcdm-utils.c' object='test_qcdm-test-qcdm-utils.o' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o test_qcdm-test-qcdm-utils.o `test -f 'test-qcdm-utils.c' || echo '$(srcdir)/'`test-qcdm-utils.c

test_qcdm-test-qcdm-utils.obj: test-qcdm-utils.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT test_qcdm-test-qcdm-utils.obj -MD -MP -MF $(DEPDIR)/test_qcdm-test-qcdm-utils.Tpo -c -o test_qcdm-test-qcdm-utils.obj `if test -f 'test-qcdm-utils.c'; then $(CYGPATH_W) 'test-qcdm-utils.c'; else $(CYGPATH_W) '$(srcdir)/test-qcdm-utils.c'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/test_qcdm-test-qcdm-utils.Tpo $(DEPDIR)/test_qcdm-test-qcdm-utils.Po
#	$(AM_V_CC)source='test-qcdm-utils.c' object='test_qcdm-test-qcdm-utils.obj' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o test_qcdm-test-qcdm-utils.obj `if test -f 'test-qcdm-utils.c'; then $(CYGPATH_W) 'test-qcdm-utils.c'; else $(CYGPATH_W) '$(srcdir)/test-qcdm-utils.c'; fi`

test_qcdm-test-qcdm-com.o: test-qcdm-com.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT test_qcdm-test-qcdm-com.o -MD -MP -MF $(DEPDIR)/test_qcdm-test-qcdm-com.Tpo -c -o test_qcdm-test-qcdm-com.o `test -f 'test-qcdm-com.c' || echo '$(srcdir)/'`test-qcdm-com.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/test_qcdm-test-qcdm-com.Tpo $(DEPDIR)/test_qcdm-test-qcdm-com.Po
#	$(AM_V_CC)source='test-qcdm-com.c' object='test_qcdm-test-qcdm-com.o' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o test_qcdm-test-qcdm-com.o `test -f 'test-qcdm-com.c' || echo '$(srcdir)/'`test-qcdm-com.c

test_qcdm-test-qcdm-com.obj: test-qcdm-com.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT test_qcdm-test-qcdm-com.obj -MD -MP -MF $(DEPDIR)/test_qcdm-test-qcdm-com.Tpo -c -o test_qcdm-test-qcdm-com.obj `if test -f 'test-qcdm-com.c'; then $(CYGPATH_W) 'test-qcdm-com.c'; else $(CYGPATH_W) '$(srcdir)/test-qcdm-com.c'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/test_qcdm-test-qcdm-com.Tpo $(DEPDIR)/test_qcdm-test-qcdm-com.Po
#	$(AM_V_CC)source='test-qcdm-com.c' object='test_qcdm-test-qcdm-com.obj' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o test_qcdm-test-qcdm-com.obj `if test -f 'test-qcdm-com.c'; then $(CYGPATH_W) 'test-qcdm-com.c'; else $(CYGPATH_W) '$(srcdir)/test-qcdm-com.c'; fi`

test_qcdm-test-qcdm-result.o: test-qcdm-result.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT test_qcdm-test-qcdm-result.o -MD -MP -MF $(DEPDIR)/test_qcdm-test-qcdm-result.Tpo -c -o test_qcdm-test-qcdm-result.o `test -f 'test-qcdm-result.c' || echo '$(srcdir)/'`test-qcdm-result.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/test_qcdm-test-qcdm-result.Tpo $(DEPDIR)/test_qcdm-test-qcdm-result.Po
#	$(AM_V_CC)source='test-qcdm-result.c' object='test_qcdm-test-qcdm-result.o' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o test_qcdm-test-qcdm-result.o `test -f 'test-qcdm-result.c' || echo '$(srcdir)/'`test-qcdm-result.c

test_qcdm-test-qcdm-result.obj: test-qcdm-result.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT test_qcdm-test-qcdm-result.obj -MD -MP -MF $(DEPDIR)/test_qcdm-test-qcdm-result.Tpo -c -o test_qcdm-test-qcdm-result.obj `if test -f 'test-qcdm-result.c'; then $(CYGPATH_W) 'test-qcdm-result.c'; else $(CYGPATH_W) '$(srcdir)/test-qcdm-result.c'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/test_qcdm-test-qcdm-result.Tpo $(DEPDIR)/test_qcdm-test-qcdm-result.Po
#	$(AM_V_CC)source='test-qcdm-result.c' object='test_qcdm-test-qcdm-result.obj' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o test_qcdm-test-qcdm-result.obj `if test -f 'test-qcdm-result.c'; then $(CYGPATH_W) 'test-qcdm-result.c'; else $(CYGPATH_W) '$(srcdir)/test-qcdm-result.c'; fi`

test_qcdm-test-qcdm.o: test-qcdm.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT test_qcdm-test-qcdm.o -MD -MP -MF $(DEPDIR)/test_qcdm-test-qcdm.Tpo -c -o test_qcdm-test-qcdm.o `test -f 'test-qcdm.c' || echo '$(srcdir)/'`test-qcdm.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/test_qcdm-test-qcdm.Tpo $(DEPDIR)/test_qcdm-test-qcdm.Po
#	$(AM_V_CC)source='test-qcdm.c' object='test_qcdm-test-qcdm.o' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o test_qcdm-test-qcdm.o `test -f 'test-qcdm.c' || echo '$(srcdir)/'`test-qcdm.c

test_qcdm-test-qcdm.obj: test-qcdm.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT test_qcdm-test-qcdm.obj -MD -MP -MF $(DEPDIR)/test_qcdm-test-qcdm.Tpo -c -o test_qcdm-test-qcdm.obj `if test -f 'test-qcdm.c'; then $(CYGPATH_W) 'test-qcdm.c'; else $(CYGPATH_W) '$(srcdir)/test-qcdm.c'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/test_qcdm-test-qcdm.Tpo $(DEPDIR)/test_qcdm-test-qcdm.Po
#	$(AM_V_CC)source='test-qcdm.c' object='test_qcdm-test-qcdm.obj' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(test_qcdm_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o test_qcdm-test-qcdm.obj `if test -f 'test-qcdm.c'; then $(CYGPATH_W) 'test-qcdm.c'; else $(CYGPATH_W) '$(srcdir)/test-qcdm.c'; fi`

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
	$(MAKE) $(AM_MAKEFLAGS) check-local
check: check-am
all-am: Makefile $(PROGRAMS)
installdirs:
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool clean-noinstPROGRAMS \
	mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/ipv6pref-ipv6pref.Po
	-rm -f ./$(DEPDIR)/modepref-modepref.Po
	-rm -f ./$(DEPDIR)/reset-reset.Po
	-rm -f ./$(DEPDIR)/test_qcdm-test-qcdm-com.Po
	-rm -f ./$(DEPDIR)/test_qcdm-test-qcdm-crc.Po
	-rm -f ./$(DEPDIR)/test_qcdm-test-qcdm-escaping.Po
	-rm -f ./$(DEPDIR)/test_qcdm-test-qcdm-result.Po
	-rm -f ./$(DEPDIR)/test_qcdm-test-qcdm-utils.Po
	-rm -f ./$(DEPDIR)/test_qcdm-test-qcdm.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/ipv6pref-ipv6pref.Po
	-rm -f ./$(DEPDIR)/modepref-modepref.Po
	-rm -f ./$(DEPDIR)/reset-reset.Po
	-rm -f ./$(DEPDIR)/test_qcdm-test-qcdm-com.Po
	-rm -f ./$(DEPDIR)/test_qcdm-test-qcdm-crc.Po
	-rm -f ./$(DEPDIR)/test_qcdm-test-qcdm-escaping.Po
	-rm -f ./$(DEPDIR)/test_qcdm-test-qcdm-result.Po
	-rm -f ./$(DEPDIR)/test_qcdm-test-qcdm-utils.Po
	-rm -f ./$(DEPDIR)/test_qcdm-test-qcdm.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am:

.MAKE: check-am install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am \
	check-local clean clean-generic clean-libtool \
	clean-noinstPROGRAMS cscopelist-am ctags ctags-am distclean \
	distclean-compile distclean-generic distclean-libtool \
	distclean-tags distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am install-man \
	install-pdf install-pdf-am install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	maintainer-clean maintainer-clean-generic mostlyclean \
	mostlyclean-compile mostlyclean-generic mostlyclean-libtool \
	pdf pdf-am ps ps-am tags tags-am uninstall uninstall-am

.PRECIOUS: Makefile


### testing rules

# test: run all tests in cwd and subdirs
test: test-nonrecursive
	@ for subdir in $(SUBDIRS) . ; do \
	    test "$$subdir" = "." -o "$$subdir" = "po" || \
	    ( cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) $@ ) || exit $? ; \
	  done

# test-nonrecursive: run tests only in cwd
test-nonrecursive: ${TEST_PROGS}
	@test -z "${TEST_PROGS}" || G_DEBUG=gc-friendly MALLOC_CHECK_=2 MALLOC_PERTURB_=$$(($${RANDOM:-256} % 256)) ${GTESTER} --verbose ${TEST_PROGS}

# test-report: run tests in subdirs and generate report
# perf-report: run tests in subdirs with -m perf and generate report
# full-report: like test-report: with -m perf and -m slow
test-report perf-report full-report:	${TEST_PROGS}
	@test -z "${TEST_PROGS}" || { \
	  case $@ in \
	  test-report) test_options="-k";; \
	  perf-report) test_options="-k -m=perf";; \
	  full-report) test_options="-k -m=perf -m=slow";; \
	  esac ; \
	  if test -z "$$GTESTER_LOGDIR" ; then	\
	    ${GTESTER} --verbose $$test_options -o test-report.xml ${TEST_PROGS} ; \
	  elif test -n "${TEST_PROGS}" ; then \
	    ${GTESTER} --verbose $$test_options -o `mktemp "$$GTESTER_LOGDIR/log-XXXXXX"` ${TEST_PROGS} ; \
	  fi ; \
	}
	@ ignore_logdir=true ; \
	  if test -z "$$GTESTER_LOGDIR" ; then \
	    GTESTER_LOGDIR=`mktemp -d "\`pwd\`/.testlogs-XXXXXX"`; export GTESTER_LOGDIR ; \
	    ignore_logdir=false ; \
	  fi ; \
	  if test -d "$(top_srcdir)/.git" ; then \
	    REVISION=`git describe` ; \
	  else \
	    REVISION=$(VERSION) ; \
	  fi ; \
	  for subdir in $(SUBDIRS) . ; do \
	    test "$$subdir" = "." -o "$$subdir" = "po" || \
	    ( cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) $@ ) || exit $? ; \
	  done ; \
	  $$ignore_logdir || { \
	    echo '<?xml version="1.0"?>'              > $@.xml ; \
	    echo '<report-collection>'               >> $@.xml ; \
	    echo '<info>'                            >> $@.xml ; \
	    echo '  <package>$(PACKAGE)</package>'   >> $@.xml ; \
	    echo '  <version>$(VERSION)</version>'   >> $@.xml ; \
	    echo "  <revision>$$REVISION</revision>" >> $@.xml ; \
	    echo '</info>'                           >> $@.xml ; \
	    for lf in `ls -L "$$GTESTER_LOGDIR"/.` ; do \
	      sed '1,1s/^<?xml\b[^>?]*?>//' <"$$GTESTER_LOGDIR"/"$$lf" >> $@.xml ; \
	    done ; \
	    echo >> $@.xml ; \
	    echo '</report-collection>' >> $@.xml ; \
	    rm -rf "$$GTESTER_LOGDIR"/ ; \
	    ${GTESTER_REPORT} --version 2>/dev/null 1>&2 ; test "$$?" != 0 || ${GTESTER_REPORT} $@.xml >$@.html ; \
	  }
.PHONY: test test-report perf-report full-report test-nonrecursive

# run tests in cwd as part of make check
check-local: test-nonrecursive

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

/* config.h.  Generated from config.h.in by configure.  */
/* config.h.in.  Generated from configure.ac by autoheader.  */

/* Define to 1 if translation of program messages to the user's native
   language is requested. */
#define ENABLE_NLS 1

/* Define if altair-lte plugin is enabled */
#define ENABLE_PLUGIN_ALTAIR_LTE 1

/* Define if anydata plugin is enabled */
#define ENABLE_PLUGIN_ANYDATA 1

/* Define if broadmobi plugin is enabled */
#define ENABLE_PLUGIN_BROADMOBI 1

/* Define if cinterion plugin is enabled */
#define ENABLE_PLUGIN_CINTERION 1

/* Define if dell plugin is enabled */
#define ENABLE_PLUGIN_DELL 1

/* Define if dlink plugin is enabled */
#define ENABLE_PLUGIN_DLINK 1

/* Define if foxconn plugin is enabled */
#define ENABLE_PLUGIN_FOXCONN 1

/* Define if generic plugin is enabled */
#define ENABLE_PLUGIN_GENERIC 1

/* Define if haier plugin is enabled */
#define ENABLE_PLUGIN_HAIER 1

/* Define if huawei plugin is enabled */
#define ENABLE_PLUGIN_HUAWEI 1

/* Define if iridium plugin is enabled */
#define ENABLE_PLUGIN_IRIDIUM 1

/* Define if linktop plugin is enabled */
#define ENABLE_PLUGIN_LINKTOP 1

/* Define if longcheer plugin is enabled */
#define ENABLE_PLUGIN_LONGCHEER 1

/* Define if mbm plugin is enabled */
#define ENABLE_PLUGIN_MBM 1

/* Define if me3630 plugin is enabled */
#define ENABLE_PLUGIN_ME3630 1

/* Define if motorola plugin is enabled */
#define ENABLE_PLUGIN_MOTOROLA 1

/* Define if mtk plugin is enabled */
#define ENABLE_PLUGIN_MTK 1

/* Define if nokia plugin is enabled */
#define ENABLE_PLUGIN_NOKIA 1

/* Define if nokia-icera plugin is enabled */
#define ENABLE_PLUGIN_NOKIA_ICERA 1

/* Define if novatel plugin is enabled */
#define ENABLE_PLUGIN_NOVATEL 1

/* Define if novatel-lte plugin is enabled */
#define ENABLE_PLUGIN_NOVATEL_LTE 1

/* Define if option plugin is enabled */
#define ENABLE_PLUGIN_OPTION 1

/* Define if option-hso plugin is enabled */
#define ENABLE_PLUGIN_OPTION_HSO 1

/* Define if pantech plugin is enabled */
#define ENABLE_PLUGIN_PANTECH 1

/* Define if quectel plugin is enabled */
#define ENABLE_PLUGIN_QUECTEL 1

/* Define if samsung plugin is enabled */
#define ENABLE_PLUGIN_SAMSUNG 1

/* Define if sierra plugin is enabled */
#define ENABLE_PLUGIN_SIERRA 1

/* Define if sierra-legacy plugin is enabled */
#define ENABLE_PLUGIN_SIERRA_LEGACY 1

/* Define if simtech plugin is enabled */
#define ENABLE_PLUGIN_SIMTECH 1

/* Define if telit plugin is enabled */
#define ENABLE_PLUGIN_TELIT 1

/* Define if thuraya plugin is enabled */
#define ENABLE_PLUGIN_THURAYA 1

/* Define if tplink plugin is enabled */
#define ENABLE_PLUGIN_TPLINK 1

/* Define if ublox plugin is enabled */
#define ENABLE_PLUGIN_UBLOX 1

/* Define if via plugin is enabled */
#define ENABLE_PLUGIN_VIA 1

/* Define if wavecom plugin is enabled */
#define ENABLE_PLUGIN_WAVECOM 1

/* Define if x22x plugin is enabled */
#define ENABLE_PLUGIN_X22X 1

/* Define if zte plugin is enabled */
#define ENABLE_PLUGIN_ZTE 1

/* Gettext package */
#define GETTEXT_PACKAGE "ModemManager"

/* Define to 1 if you have the Mac OS X function CFLocaleCopyCurrent in the
   CoreFoundation framework. */
/* #undef HAVE_CFLOCALECOPYCURRENT */

/* Define to 1 if you have the Mac OS X function CFPreferencesCopyAppValue in
   the CoreFoundation framework. */
/* #undef HAVE_CFPREFERENCESCOPYAPPVALUE */

/* Define if the GNU dcgettext() function is already present or preinstalled.
   */
#define HAVE_DCGETTEXT 1

/* Define to 1 if you have the <dlfcn.h> header file. */
#define HAVE_DLFCN_H 1

/* Define if the GNU gettext() function is already present or preinstalled. */
#define HAVE_GETTEXT 1

/* Define if you have the iconv() function and it works. */
/* #undef HAVE_ICONV */

/* Define to 1 if you have the <inttypes.h> header file. */
#define HAVE_INTTYPES_H 1

/* Define to 1 if you have the <memory.h> header file. */
#define HAVE_MEMORY_H 1

/* Define to 1 if you have the <stdint.h> header file. */
#define HAVE_STDINT_H 1

/* Define to 1 if you have the <stdlib.h> header file. */
#define HAVE_STDLIB_H 1

/* Define to 1 if you have the <strings.h> header file. */
#define HAVE_STRINGS_H 1

/* Define to 1 if you have the <string.h> header file. */
#define HAVE_STRING_H 1

/* Define to 1 if you have the <sys/stat.h> header file. */
#define HAVE_SYS_STAT_H 1

/* Define to 1 if you have the <sys/types.h> header file. */
#define HAVE_SYS_TYPES_H 1

/* Define to 1 if you have the <unistd.h> header file. */
#define HAVE_UNISTD_H 1

/* Define to the sub-directory where libtool stores uninstalled libraries. */
#define LT_OBJDIR ".libs/"

/* Define the distribution version string */
/* #undef MM_DIST_VERSION */

/* Name of package */
#define PACKAGE "ModemManager"

/* Define to the address where bug reports for this package should be sent. */
#define PACKAGE_BUGREPORT "https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues"

/* Define to the full name of this package. */
#define PACKAGE_NAME "ModemManager"

/* Define to the full name and version of this package. */
#define PACKAGE_STRING "ModemManager 1.14.8"

/* Define to the one symbol short name of this package. */
#define PACKAGE_TARNAME "ModemManager"

/* Define to the home page for this package. */
#define PACKAGE_URL ""

/* Define to the version of this package. */
#define PACKAGE_VERSION "1.14.8"

/* Define to 1 if you have the ANSI C header files. */
#define STDC_HEADERS 1

/* Enable extensions on AIX 3, Interix.  */
#ifndef _ALL_SOURCE
# define _ALL_SOURCE 1
#endif
/* Enable GNU extensions on systems that have them.  */
#ifndef _GNU_SOURCE
# define _GNU_SOURCE 1
#endif
/* Enable threading extensions on Solaris.  */
#ifndef _POSIX_PTHREAD_SEMANTICS
# define _POSIX_PTHREAD_SEMANTICS 1
#endif
/* Enable extensions on HP NonStop.  */
#ifndef _TANDEM_SOURCE
# define _TANDEM_SOURCE 1
#endif
/* Enable general extensions on Solaris.  */
#ifndef __EXTENSIONS__
# define __EXTENSIONS__ 1
#endif


/* Version number of package */
#define VERSION "1.14.8"

/* Define if you want to enable AT commands via DBus */
/* #undef WITH_AT_COMMAND_VIA_DBUS */

/* Define if you want MBIM support */
/* #undef WITH_MBIM */

/* Define if you have PolicyKit support */
/* #undef WITH_POLKIT */

/* Define if you want QMI support */
/* #undef WITH_QMI */

/* Define if foxconn utils are built */
#define WITH_SHARED_FOXCONN 1

/* Define if icera utils are built */
#define WITH_SHARED_ICERA 1

/* Define if novatel utils are built */
#define WITH_SHARED_NOVATEL 1

/* Define if option utils are built */
#define WITH_SHARED_OPTION 1

/* Define if sierra utils are built */
#define WITH_SHARED_SIERRA 1

/* Define if telit utils are built */
#define WITH_SHARED_TELIT 1

/* Define if xmm utils are built */
#define WITH_SHARED_XMM 1

/* Define if you want systemd journal support */
/* #undef WITH_SYSTEMD_JOURNAL */

/* Define if you have systemd suspend-resume support */
/* #undef WITH_SYSTEMD_SUSPEND_RESUME */

/* Define if you want udev support */
#define WITH_UDEV 1

/* Define to 1 if on MINIX. */
/* #undef _MINIX */

/* Define to 2 if the system does not provide POSIX.1 features except with
   this defined. */
/* #undef _POSIX_1_SOURCE */

/* Define to 1 if you need to in order for `stat' and other things to work. */
/* #undef _POSIX_SOURCE */


ModemManager 1.14.8
-------------------------------------------
This is a new bugfix release of ModemManager.

 * Build:
   ** Fixed distcheck with new gtk-doc releases.
   ** ModemManager-names.h was being included in the dist tarball, but then
      removed on the 'clean' target. Fix that, by only removing it on the
      'maintainer-clean' target. Therefore, 'xsltproc' is now only needed in
      git builds, not needed when building from a dist tarball.

 * QMI:
   ** Fix daemon crash when the device is removed during the initialization
      sequence.

 * Several other minor improvements and fixes.


ModemManager 1.14.6
-------------------------------------------
This is a new bugfix release of ModemManager.

 * QMI:
   ** Fix missing GError initialization.

 * plugins:
   ** xmm: fix missing GError initialization.
   ** cinterion: fix missing GError initialization.
   ** simtech: fix missing GError initialization.


ModemManager 1.14.4
-------------------------------------------
This is a new bugfix release of ModemManager.

 * Location interface:
   ** Allow cell ID only updates.

 * Messaging interface:
   ** Fixed memory leak when SMS parts being processed but device goes away.
   ** Fixed CDMA SMS UTF-8 translation.
   ** Allow sending UTF-16 as if it were UCS-2.

 * QMI:
   ** Increased port probing timeout to 25s.
   ** Return error in facility locks loading if PIN status check fails.
   ** Return FIXED_DIALING lock status when checking PIN2.

 * MBIM:
   ** Don't fail IPv4 connection if IPv4v6 was requested.

 * Plugins:
   ** telit: new ID_MM_TELIT_PORT_DELAY tag to flag devices that require
      extended AT probing.
   ** telit: added FN980 and LM9x0 MBIM compositions rules.
   ** telit: fixed LM9x0 udev rules.
   ** cinterion: configure the PLAS9 to correctly send URCs.
   ** simtech: add SIM7070/SIM7080/SIM7090 port type hints

 * i18n:
   ** Updated Swedish and Slovak translations.
   ** Updated package bugreport to use the gitlab issues URL.

 * Several other minor improvements and fixes.


ModemManager 1.14.2
-------------------------------------------
This is a new bugfix release of ModemManager.

 * Modem interface:
   ** Avoid connection status flapping if briefly unregistered.

 * QMI:
   ** Fixed parsing of USSD indications with UTF-16 data.
   ** Perform network registration using SSSP if possible, so that settings like
      allowed modes are not silently modified.
   ** Fixed connection sequence so that early attempt aborts end up correctly
      releasing network handles that may have been allocated already.

  * Plugins:
   ** telit: added port type hints for the default MEx10G1 composition.
   ** quectel: added port type hints for the Quectel 5G RM500.
   ** quectel: added support for MBIM devices.
   ** cinterion: increased SWWAN connection attempt timeout to 120s.
   ** cinterion: if user OR password given, don't set the other as (null).
   ** cinterion: quote user/password strings in AT^SGAUTH calls.
   ** cinterion: support ^SGAUTH syntax in IMT family.
   ** cinterion: ignore ^SYSSTART URC.
   ** cinterion: simplify parsing of radio/band single SCFG line.
   ** cinterion: remove limitation to IPv4 only PDP contexts.

  * Several other minor improvements and fixes.


ModemManager 1.14.0
-------------------------------------------
This is a new stable release of ModemManager.

The following notes are directed to package maintainers:

 * This version requires:
   ** GLib/GObject/GIO >= 2.48.0
   ** libmbim >= 1.24.0 (for the optional MBIM support)
   ** libqmi >= 1.26.0 (for the optional QMI support)

 * Build updated with several improvements:
   ** The build has been updated to use by default all warnings enabled by
      AX_COMPILER_FLAGS(), and therefore when building the release from a git
      checkout, autoconf-archive >= 2017.03.21 is now required. This new build
      dependency isn't required when building from the release tarball.
   ** Also when building from a git checkout, beware because by default
      --enable-compile-warnings=error is enabled, which implies -Werror. If
      you'd like to build from git and avoid -Werror, you should explicitly use
      --enable-compile-warnings=yes (to keep the warnings but without being
      errors), --enable-compile-warnings=no (to disable all the extra warnings
      enabled by default) or --disable-Werror (to unconditionally make all
      compiler warnings non-fatal).
   ** Users can now preselect which plugins to build and install during
      configure, with the --enable-plugin-[PLUGIN-NAME] options. The user can
      also build and install all except for some, just by using the
      --enable-all-plugins option followed by --disable-plugin-[PLUGIN-NAME].
      By default all plugins are enabled, so all of them built and installed.
      This new set of options are useful for users building custom systems
      where they already know what kind of devices they're going to have, it
      isn't recommended for standard distributions.

The only updates in the public API are the following:

 * Modem interface:
   ** Added support for AT-based and/or QMI-based 5G devices, which will report
      the new MM_MODEM_CAPABILITY_5GNR capability.
   ** Deprecated the MM_MODEM_CAPABILITY_LTE_ADVANCED capability, as it was
      never used in any implementation.

 * Bearer interface:
   ** Added additional 'attempts', 'failed-attempts', 'total-rx-bytes',
      'total-tx-bytes' and 'total-duration' values in the 'Stats' property
      exposed by the Bearer objects.

The most important features and changes in this release are the following:

 * The daemon switched to 'STRICT' filter mode by default. The old 'DEFAULT'
   mode is renamed to 'LEGACY' and is considered now deprecated. Under the
   'STRICT' filter mode, the TTY blacklist and greylist are ignored, and the
   port probing mechanism uses its own heuristics to guess whether a given TTY
   is owned by a modem or not.

 * Added a new implicit whitelist rules applicable in 'STRICT' filter mode, so
   that all devices with a USB vid matching one of the vids allowed by the
   different installed plugins are implicitly allowed.

 * Updated daemon logging so that we always prefix the messages with a string
   identifying which modem the log refers to, making it easy to grep logs for
   one specific device if the system has more than one.

 * Updated the probing logic to make sure we don't attempt a re-probe when the
   device is gone.

 * Probing logic now allows new ports detected up to 1500ms since last port
   added, useful on OpenWrt setups where ports are notified one by one via
   hotplug events.

 * AT:
   ** Moved the charset definition logic to the initialization phase instead of
      the enabling phase, because the feature support checks may already require
      string processing based on the current charset.
   ** Updated manual registration operation to attempt using current charset
      (e.g. UCS2) if ASCII fails.

 * QMI:
   ** Devices using the LOC service for GNSS will now also setup the list of
      required NMEA traces before starting the engine.
   ** Implemented 3GPP USSD support using the Voice service.
   ** Update carrier code if registration changes from one roaming operator to
      another.
   ** Explicitly disable autoconnect during modem enabling phase, because it
      interferes with our connection management logic.
   ** Fallback to raw-ip if WDA Get Data Format requests arguments, as in most
      new 5G devices.
   ** Updated to always use the asynchronous close() operation.
   ** Handle disconnection indications during connection attempts.

 * MBIM:
   ** Update carrier code if registration changes from one roaming operator to
      another.
   ** Implement reset in Intel-based and Qualcomm-based devices.
   ** Avoid LTE attach config/status if unsupported.
   ** Updated to make sure all allocated QMI CIDs are released during shutdown.

 * SIM interface:
   ** Don't allow sending PIN/PUK if not required.

 * 3GPP interface:
   ** Fixed manual re-registration to the same operator.

 * CDMA interface:
   ** Don't allow multiple concurrent activation attempts.
   ** Disallow empty carrier code in automatic activation.

 * Bearer interface:
   ** Updated to avoid connection checks or stats updates while disconnecting.

 * libmm-glib:
   ** New 'mm_location_gps_nmea_get_traces()' method to retrieve a NULL
      terminated array of strings with all cached NMEA traces.
   ** Deprecated the 'mm_location_gps_nmea_build_full()' method.

 * mmcli:
   ** Added a new 'any' lookup keyword for the --modem and --sim options, useful
      when the system is only expected to have one single device.

 * Plugins:
   ** broadmobi: new plugin, right now just with port type hints for the BM818.
   ** foxconn: new plugin to support the T77W968 (both with and without eSIM).
   ** dell,dw5821e: added support for the DW5821e with eSIM variant.
   ** huawei: don't delay reporting network initiated disconnects.
   ** huawei: try to read port type hints from interface descriptions.
   ** huawei: avoid using the QCDM port during a voice call.
   ** cinterion: skip sim ready check for modules that don't support it.
   ** cinterion: implemented radio/band handling for LTE modems.
   ** cinterion: added Signal interface support bsaed on AT^SMONI.
   ** cinterion: added support for MBIM based devices like the PLS62-W.
   ** quectel: updated to detect SIM hot swap via +QUSIM URCs.
   ** fibocom: added support for QMI based devices like the FM150.
   ** ublox: ignore error when disconnecting last LTE bearer.
   ** ublox: implement support to enable and detect +UUDTMF URCs.
   ** ublox: added blacklist rules for GPS modules in the plugin itself.
   ** sierra: implement manual and automatic CDMA activation.
   ** novatel: implement manual and automatic CDMA activation.

All the features and fixes which were backported to 1.12.x releases are also
present in ModemManager 1.14.0.


ModemManager 1.12.0
-------------------------------------------
This is a new stable release of ModemManager.

The following notes are directed to package maintainers:

 * This version requires:
   ** libmbim >= 1.18.0 (for the optional MBIM support)
   ** libqmi >= 1.24.0 (for the optional QMI support)

 * The build allows to configure with '--with-at-command-via-dbus' in order to
   unconditionally enable the Modem.Command() method to allow running AT
   commands through ModemManager. This is not suggested for standard
   distributions, though.

The most important features and changes in this release are the following:

 * Modem interface:
   ** Updated logic to avoid assuming that setting bands or modes is immediate,
      the daemon will now actively monitor for those updates to happen before
      returning success.

 * 3GPP interface:
   ** libmm-glib: deprecated the mm_pco_list_free() helper method.

 * Simple interface:
   ** api,libmm-glib: deprecated the 'subscription state' property.

 * Location interface:
   ** Fixed 'unknown' lat/long/alt numeric values.
   ** Added support for MSB A-GPS in addition to MSA A-GPS.

 * Voice interface:
   ** Improved voice call management with call id detection and tracking.
   ** Improved detailed call state transitions on generic modems that support
      call list polling.
   ** Added support for GSM supplementary services, including call waiting,
      call transfer, call deflection, multiparty calls...
   ** Added emergency call support, allowing voice call to emergency numbers
      even without SIM or with SIM-PIN locked.
   ** Deprecated all properties except for 'number' in the CreateCall()
      method.

 * Messaging interface:
   ** Updated to report SMS timestamps in correct ISO8601 format.

 * Bearer:
   ** Improved unused CID lookup to allow selecting non-sequential CIDs.
   ** Disabled all AT protocol based context monitoring when PPP is used for
      the connection, in order to properly sync with pppd, which should be the
      one detecting the disconnections (already in 1.10.6).

 * QMI:
   ** Improved support to list stored firmware images in Sierra devices.
   ** Added additional lock check retries on 'SIM not inserted' errors.
   ** Updated explicit registration attempt to report success only when the
      target requested network is registered.
   ** Added MSB A-GPS support.
   ** Implemented automatic carrier configuration selection using PDC service
      (already in 1.10.2).

 * mmcli:
   ** New machine-readable JSON output with '--output-json'.
   ** Updated to allow using the modem UID to specify SIM operations.

 * udev:
   ** New ID_MM_PORT_TYPE_AUDIO generic udev tag to identify ports that
      should be used for in-band audio.
   ** Removed support for the ID_MM_PLATFORM_DRIVER_PROBE udev tag, as it
      is no longer required given that the more generic explicit whitelist
      may be used to flag which devices should be probed.
   ** Renamed ID_MM_DEVICE_MANUAL_SCAN_ONLY to ID_MM_TTY_MANUAL_SCAN_ONLY,
      given that the tag only applies to TTYs.
   ** ID_MM_DEVICE_IGNORE is no longer used internally in ModemManager, and
      is instead targeted to users that want to explicitly ignore specific
      devices regardless of what filter type is in use (already in 1.10.6).

 * dbus:
   ** Updated to always report the registered MM_CORE_ERROR_CANCELLED error
      instead of the implicit G_IO_ERROR_CANCELLED ones generated by GLib.

 * GObject introspection:
   ** Fixed setup to explicitly skip all non-API methods.

 * Plugins:
   ** tplink: new plugin.
   ** dlink: new plugin.
   ** xmm: added MSB A-GPS support.
   ** dell,dw5821e: update to allow unmanaged GPS support on the TTY even
      when raw/NMEA GPS is enabled via QMI/LOC.
   ** quectel: updated to allow TTY-only devices.
   ** telit: added GPS support.
   ** telit: improved band management with #BND.
   ** simtech: added improved voice call support.
   ** simtech: added support for LTE devices.
   ** simtech: improved signal quality reporting logic.
   ** simtech: added GPS support for the SIM7000/SIM7600 family.
   ** cinterion: added support for time updates.
   ** cinterion: added improved voice call support.
   ** ublox: added improved voice call support.
   ** ublox: improved band management with UBANDSEL.

All the features and fixes which were backported to 1.10.x releases are also
present in ModemManager 1.12.0.


ModemManager 1.10.0
-------------------------------------------
This is a new stable release of ModemManager.

The following notes are directed to package maintainers:

 * This version requires:
   ** libmbim >= 1.18.0 (for the optional MBIM support)
   ** libqmi >= 1.22.0 (for the optional QMI support)

The most important features and changes in this release are the following:

 * udev:
   ** Consolidated common tag names among all the supported plugins. E.g.
      ID_MM_PORT_TYPE_GPS, ID_MM_PORT_TYPE_AT_*, ID_MM_PORT_TYPE_QCDM...
      All these generic tags are included as symbols in the API, and
      compatibility will be maintained for these. Custom setups of
      ModemManager relying on previously available per-plugin udev tags may
      need to manually port them to this new generic subset.
   ** New tag to allow specifying flow control settings to use in serial ports.

 * Core:
   ** Avoid probing other protocols on TTYs tagged in udev with specific port
      type tags (e.g. avoid probing QCDM if a port is tagged as AT). This allows
      faster port probing and modem detection for known modem layouts.
   ** Implemented support to enable and handle +CGEV URCs for asynchronous
      connection state updates in AT-controlled devices.

 * Manager interface:
   ** New runtime daemon version reporting.
   ** New support for requesting device inhibition, e.g. so that ModemManager
      stops completely using a modem device until the inhibition is released.
      This feature is implemented to allow fwupd taking over of a device
      completely for as long as it needs during a firmware update.

 * Modem interface:
   ** All methods are always connected, even in Failed state.
   ** Allow parallel Enable()/Disable() calls.
   ** Deprecated redundant ListBearers() method, the read-only Bearers property
      is already showing the same information.

 * Bearer interface:
   ** New 'BearerType' property, e.g. to specify whether a bearer is the initial
      LTE default bearer or not.
   ** Deprecated 'number' field in bearer settings. Applications do not need to
      send the 'number' field in Bearer.Connect() or in Modem.Simple.Connect(),
      as the setting is totally ignored.

 * mmcli:
   ** New 'key-value' output, easier to parse by scripts than the default.
   ** Removed redundant '--location-get-XXX' actions, as the '--location-get'
      already reports the location information for all sources.
   ** Removed redundant '--simple-status' action, as the same information can be
      obtained through different mmcli operations.
   ** New manager '--inhibit-device' action and modem-specific '--inhibit', to
      allow requesting device inhibition.

 * 3GPP interface:
   ** New support for exposing the network reported Protocol Configuration
      Options (PCO), to be used instead of the new deprecated Subscription
      State property.
   ** New support for exposing the initial LTE default bearer status.
   ** New support for configuring the initial LTE default bearer settings.

 * Location interface:
   ** New LTE Tracking Area Code (TAC) in 3GPP location information.
   ** New support for injecting assistance data (e.g. Qualcomm XTRA) into the
      GNSS engine, useful when there is no mobile connection to use MSA A-GPS.

 * Firmware interface:
   ** Support for reporting firmware update support properties, e.g. specifying
      which update methods are supported. This information will be consumed by
      fwupd in order to allow upgrading firmware in devices managed by
      ModemManager.

 * Voice interface:
   ** Multiple improvements and fixes in the voice call management logic
      implemented with generic AT commands.
   ** Added AudioPort and AudioFormat properties to the Call object.
   ** Added new generic audio channel setup/cleanup handlers in the Call object.

 * QMI:
   ** New LOC service based GNSS support, including A-GPS setup via SUPL server.
   ** New support for the "extended" LTE band list.
   ** New support for reading IMSI and ICCID with the UIM service.

 * MBIM:
   ** Implemented support for processing Protocol Configuration Options using
      Microsoft-defined Basic Connect Extensions.
   ** Implemented support for LTE attach status and configuration using
      Microsoft-defined Basic Connect Extensions.
   ** Implemented support for the extended signal interface and for 3GPP location
      details using the AT&T specific service.
   ** Implemented support for 3GPP USSD operations using the standard USSD
      service.
   ** For Qualcomm-based MBIM devices, those with QMI-over-MBIM support, a whole
      new set of features is now available, including: QMI LOC/PDS location
      support, allowed/preferred mode management, frequency band selection,
      power management operations...

 * Plugins:
   ** xmm: new XMM plugin, with shared logic (allowed/preferred mode management,
      frequency band selection, power management operations, extended signal
      quality reporting, GPS/A-GPS...) for Intel XMM based devices.
   ** fibocom: new plugin, with support for generic MBIM and XMM-based devices.
   ** dell: added support for XMM-based devices, like the DW5820e.
   ** dell: added custom support for the DW5821e, including 'unmanaged' GPS and
      firmware update integration details.
   ** cinterion: new shared interface to include all logic shared between Option
      and Option/HSO devices.
   ** sierra-legacy: implemented connection monitoring support.
   ** u-blox: added support for extended call state transitions.
   ** u-blox: added CDC-ECM support for SARA/LISA-U2xx modems.
   ** altair-lte: migrated from SubscriptionState to PCO.

All the features and fixes which were backported to 1.8.x releases are also present
in ModemManager 1.10.0.


ModemManager 1.8.0
-------------------------------------------
This is a new stable release of ModemManager.

The following notes are directed to package maintainers:

 * This version requires:
   ** GLib 2.36.0
   ** gettext 0.19.8 (for the optional rebuild of documentation)
   ** libmbim >= 1.16.0 (for the optional MBIM support)
   ** libqmi >= 1.20.0 (for the optional QMI support)
   ** libsystemd >= 209 or libsystemd-login >= 183 (for the optional suspend
      and resume support)
   ** libsystemd >= 209 (for the optional systemd journal support)
   ** polkit >= 0.97 (for the optional PolicyKit support)

 * This version no longer requires:
   ** intltool (replaced by new features in gettext)

 * Distributions using systemd should explicitly use the following configure
   options:
   ** '--with-systemd-suspend-resume': the only supported source for suspend and
   resume events is now systemd, so the previous '--with-suspend-resume=[]'
   option was renamed.
   ** '--with-systemd-journal' to use the new journal support.

 * Distributions wanting to avoid ModemManager poking TTY ports that isn't
   supposed to touch may start using the new 'STRICT' filter policy, given
   as an option to the ModemManager daemon (e.g. patching the default systemd
   service file provided). See below for more info about the filter policy and
   the side effects of using the STRICT approach:
   ** E.g. ModemManager --filter-policy=STRICT

The most important features and changes in this release are the following:

 * New 'filter policy' setting in the ModemManager daemon to decide which ports
   are probed and how. Currently these levels are defined:
   ** WHITELIST-ONLY: Only devices or ports explicitly whitelisted with the new
      'ID_MM_DEVICE_PROCESS' udev tag are probed.
   ** DEFAULT: All ports are allowed to be probed except for the ones explicitly
      greylisted as RS232 adapters or completely blacklisted. This is the
      default approach that was used until now, and the default as well in this
      release if a different one isn't requested.
   ** STRICT: The daemon defines a set of heuristics to try to detect modems and
      ports to probe. Only the TTY ports that are very very likely to be modem
      ports are probed, therefore completely avoiding the need of having a
      separate blacklist or RS232 adapter greylist. But note that this policy
      may end up ignoring some devices, like TTY controlled modems without an
      associated network port.
   ** PARANOID: This is equivalent to running the STRICT mode but also applying
      the blacklist and RS232 greylist filters explicitly.

 * Device 'naming'. This release includes logic to allow 'naming' devices with
   the ID_MM_PHYSDEV_UID udev tag, so that the names can then be used in e.g.
   mmcli and also exposed in the 'Device' property in the Modem interface. This
   new setup makes it possible to give the devices unique names that are kept
   across reboots.

 * Allow skipping the automatic scan for devices in the daemon with the new
   '--no-auto-scan' daemon option. Instead, the daemon may be called with the
   '--initial-kernel-events=[PATH]' option including a predefined list of ports
   or otherwise get the port additions reported during runtime with the
   mmcli --report-kernel-event=[] command.

 * Allow building and running without 'udev'. In this setup, the previously
   explained '--no-auto-scan' is enabled by default, so ports are not
   automatically detected .Note that this setup is not suggested for standard
   distributions: if udev is available in the system, it is the preferred method
   to manage the port addition and removal.

 * SIM hot swap. The core includes the needed logic to support SIM hot swap in
   the different devices, although for now it's only tested for Telit and MBIM
   modems. If a SIM hot swap is detected, the modem is flagged as failed and
   reprobed from scratch.

 * Connection status monitoring logic. In order to try to detect network
   initiated disconnections, a generic setup is provided to plugins so that they
   can implement explicit connection status checks that would be executed
   periodically.

 * New support for 3GPP CSFB states and operation modes. We now support
   registration states reported as "SMS only" or "CSFB not preferred", and
   provide APIs to set and get the "UE mode of operation for EPS".

This version comes with the following new features:

 * Build and system:
   ** Updated the systemd service file with additional security related rules.
   ** Added support for systemd journal logging.
   ** Updated most of the code to use GTask instead of the deprecated
      GSimpleAsyncResult based operations.
   ** ChangeLog is built from git during the dist tarball generation.

 * New translations: Polish, Brazilian Portuguese, Slovak, Hungarian, Czech,
   Ukrainian, Swedish and Indonesian.

 * API:
   ** Defined additional GSM, UMTS and LTE frequency bands.
   ** The MMModemBand enumeration values (EUTRAN, UTRAN and CDMA) have been
      renamed to consolidate how they are defined. A compatibility layer has
      been provided to avoid breaking the API.
   ** New 'HardwareRevision' property in the Modem interface.
   ** New 'EpsUeModeOperation' property and 'SetEpsUeModeOperation' method in
      the Modem 3GPP interface.

 * Core:
   ** Updated libqcdm to load signal strength from QCDM EVDO Pilot Sets.
   ** Updated udev rules with new per-vendor rules for quicker processing.
   ** Explicitly ignored ports are never probed now, but they will be reported
      as owned by the device and exposed in the Ports property.
   ** New 'ID_MM_TTY_BAUDRATE' udev tag to specify the baudrate to use in RS232
      TTY ports.
   ** If using UCS2, still assume that the operator name may be given in ASCII.
   ** Explicitly open QCDM ports anytime it's needed, instead of assuming they
      are open when enabled.
   ** Query supported ME event reporting options and automatically set the best
      choice based on the supported ones.
   ** Query supported flow control modems and automatically set the best choice
      based on the supported ones.
   ** Explicitly configure flow control settings on the TTY as soon as it is
      connected, only applicable for RS232 devices.
   ** Implemented generic unlock retries loading.

 * MBIM:
   ** Explicitly reprobe the modem when the mbim-proxy is detected dead.
   ** Workaround implemented to keep track of the PIN1 unlock retries as the
      protocol isn't very good allowing this.
   ** Load and expose HW revision.

 * QMI:
   ** Explicitly reprobe the modem when the qmi-proxy is detected dead.
   ** Load and expose HW revision.
   ** Detect port closed and forbid client allocation operations.
   ** New optional connection status monitoring support, enabled by default for
      the Netgear AC341U.

 * Location interface:
   ** Disabled by default for MBIM modems.

 * Messaging interface:
   ** Try decoding with UTF-16BE when UCS-2 reported.

 * Plugins:
   ** u-blox: new plugin, currently supporting the TOBY-L2, TOBY-L4, TOBY-R2,
      LARA-R2 and LISA-R2.
   ** quectel: new plugin, supporting generic AT/QMI based modems.
   ** cinterion: implemented support for devices exposing a WWAN network
      interface.
   ** cinterion: support changing modes in LTE capable devices.
   ** cinterion: added GPS support for devices controlled only with AT^SGPSC.
   ** cinterion: use ^SIND unsolicited messages for access tech reporting.
   ** huawei: implemented Signal interface.
   ** telit: added support for RS232, QMI and MBIM modems.
   ** novatel: cleaned up registration state and access tech reporting.
   ** novatel-lte: implemented unlock retries loading.
   ** dell: speed probing time up and reduce udev dependency.
   ** mbm: added GPS support for the Dell DW5560.

The following features which were backported to 1.6.x releases are also present
in ModemManager 1.8.0:

 * Build and System:
   ** Explicitly use -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_36 and
      -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_36 so that we don't get warnings
      for GLib features that are flagged as deprecated in newer versions.
   ** Dropped After=syslog.target rule in systemd service file.
   ** Added all ModemManager udev tags also in "bind" events.

 * Core:
   ** Improved detection of 4G-only modems.
   ** TTY is set as connected as soon as ATD replies.
   ** Removed the default ID_MM_PLATFORM_DRIVER_PROBE whitelist.

 * Signal interface:
   ** Report RSCP if available.
   ** New generic implementation of the Signal interface in AT-based devices
      using AT+CESQ.

 * QMI:
   ** Run FCC auth sequence if "InvalidTransition" is reported when going
      online.
   ** Added WDS reporting event support.

 * Plugins:
   ** huawei: let the E3372 run NDISDUP via TTY (when the exposed cdc-wdm is
      non-functional).
   ** telit: added support for the GE910.

ModemManager 1.6.0
-------------------------------------------
This is a new stable release of ModemManager.

 * This version requires:
   ** GLib 2.36.0
   ** gettext 0.19.3
   ** libmbim >= 1.14.0 (for the optional MBIM support)
   ** libqmi >= 1.16.0 (for the optional QMI support)
   ** libsystemd >= 209 or libsystemd-login >= 183 (for the optional suspend
      and resume support)

 * For distributions using systemd, it is suggested that the new optional
   suspend/resume is explicitly requested during configure with the new
   '--with-suspend-resume=systemd' argument.

This version comes with the following new features:

 * Core:
   ** Implemented support for suspend/resume detection, currently working
      when systemd is in use. Whenever the system is suspended, we'll flag the
      modems as invalid so that they are re-probed from scratch when the system
      is resumed.
   ** Added cancellation support for the probing operations.
   ** Reworked and simplified the serial port response processing.

 * Location interface:
   ** Added A-GPS support, currently available only for QMI based modems with
      PDS service.
   ** Added support for updating the default GPS refresh time.

 * Time interface:
   ** New default implementation for all AT-based modems.

 * Voice interface:
   ** New DBus interface to allow the management of voice calls, which currently
      assumes that the audio channel is setup out of ModemManager.

 * Bearer:
   ** New support for reporting statistics of the ongoing connection with a new
      'Stats' property, currently available for QMI and MBIM based modems.

 * QMI:
   ** Implemented support for devices which only work in "raw IP" mode, like
      the Sierra MC7455.
   ** Implemented support for SIM related operations using the UIM service, as
      newer modems with multi-SIM capabilities don't suppor the legacy DMS UIM
      operations.
   ** Implemented support for detecting network-initiated disconnections.

 * MBIM:
   ** If online mode fails, try to use the 'DMS Set FCC Authentication' QMI
      message via the QMI-over-MBIM support, if supported by the device.

 * udev:
   ** Added new supported 'ID_MM_PORT_IGNORE' tag to allow fully ignoring ports
      specified by the user.

 * mmcli:
   ** Added command completion.
   ** Added new operations to use the Voice interface.
   ** Added new operations to manage the A-GPS settings.

 * Build:
   ** Added code coverage support.

 * Plugins:
   ** haier: new plugin to support the Haier CE81B.
   ** thuraya: new plugin for Thuraya satellite modems.
   ** sierra-legacy,sierra: the implementation for Sierra modems is now split
      into two different plugins: a 'legacy' one for the old PPP and DirectIP
      based modems and the standard one for the newer QMI and MBIM based ones.
   ** dell: new plugin for Dell rebranded devices from Novatel, Sierra or
      Ericsson.
   ** gobi: removed the plugin. All non-vendor specific QMI devices should now
      be managed by the generic plugin.
   ** mbm: dynamically load the list of supported modes.
   ** mbm: fixed several connection/disconnection issues.
   ** simtech: support QMI devices.
   ** huawei: implemented Voice call management support.
   ** huawei: use static IP addressing in NDISDUP capable devices if the AT^DHCP
      response provides the IP details.

The following features which were backported to 1.4.x releases are also present
in ModemManager 1.6.0:

 * MBIM:
   ** The mbim-proxy is used by default.
   ** Implemented support for disconnection status notification while connected.
   ** Disabled CDMA capabilities, until properly supported.

 * QMI:
   ** The qmi-proxy is used by default.
   ** If online mode fails, use 'DMS Set FCC Authentication', required by some
      rebranded Sierra modems (e.g. Dell branded ones).
   ** Implemented support for loading SIM operator id and name.
   ** Implemented power-cycle reset functionality.

 * Plugins:
   ** telit: added support for new devices, like HE910, UE910 and UL865.
   ** telit: implemented dynamic port identification.
   ** telit: implemented unlock retries loading.
   ** telit: implemented supported/current bands management.
   ** telit: implemented supported/current modes management.
   ** telit: implemented modem reset and power down.
   ** mbm: implemented GPS support for Ericsson HS2350 and H5321gw modems.


ModemManager 1.4.0
-------------------------------------------
This is a new stable release of ModemManager.

 * This version requires libmbim >= 1.10.0.

This version comes with the following updates in the interfaces:

 * Updated the logic around the IP configuration properties in the Bearer:
   ** Setting DHCP as IP method in the IPv6 settings means that SLAAC should
      be used to retrieve correct addressing and routing details.
   ** DHCP IP method may now be combined with an explicit static IP address, as
      IPv6 SLAAC may require the link-local address to be present.
   ** MTU is now also included in the IP configuration properties, if specified
      by the modem, and applicable to both DHCP and STATIC methods.
 * New 'OFF' power state, which fully switches off the modem device. After
   setting the modem in this state, no further use of it can be done. Currently
   available in Wavecom and Cinterion.
 * Location interface: new 'unmanaged GPS' support, which allows to start/stop
   the GPS module in the modem, while leaving the location information retrieval
   to other processes. Currently available in modems with independent GPS TTYs,
   like Option/HSO, Cinterion and Huawei.
 * New Test DBus interface: not to be installed, just for internal system tests.

Other notable changes include:
 * MBIM: support for ZTE and Sequans Communications modems.
 * Ericsson MBM: Support for AT-capable /dev/cdc-wdm ports.
 * Huawei: improved support for Network time retrieval.
 * Huawei: implemented GPS support.
 * Huawei: support for /dev/cdc-wdm AT ports via the new huawei-cdc-ncm driver.
 * Cinterion: implemented GPS support.
 * Cinterion: implemented unlock retries loading.
 * Cinterion: gather port types for multi-tty devices.
 * Cinterion: custom wait for SIM readiness after SIM-PIN unlock.
 * Wavecom: custom wait for SIM readiness after SIM-PIN unlock.
 * Probing: new flag to identify hotplugged devices which don't need full reset.
 * Tests: internal refactor of the ports handling code, allowing test-driven
   virtual ports and system tests run during 'make check'. This new feature also
   comes with a new internal 'Test' DBus interface, as well as new --test-[*]
   options in the ModemManager program.
 * and many more fixes...


ModemManager 1.2.0
-------------------------------------------
This is a new stable release of ModemManager.

This version comes with the following updates in the interfaces:

 * Signal interface: new interface for extended signal quality information
 * OMA interface: new interface to expose the Device Management capabilities
   defined by the Open Mobile Alliance
 * Messaging interface: new 'Messages' property
 * Modem interface: new 'Bearers' property
 * 3GPP interface: new 'SubscriptionState' property

Other notable changes include:

 * QMI: Implemented Manual CDMA activation logic
 * QMI: Implemented 3GPP2/CDMA SMS support
 * QMI: Added support for QMI modems in the ZTE, x22x and Cinterion plugins.
 * Huawei: multiple improvements and fixes for the ^NDISDUP support
 * Huawei: new mode/switching logic with ^SYSCFGEX for LTE-capable devices
 * Altair-LTE: set subscription state based on PCO
 * MediaTek: new 'mtk' plugin added for MediaTek devices
 * libmm-glib: Added GObject Introspection and Vala support
 * and many more fixes...


ModemManager 1.0.0
-------------------------------------------

This is a new stable release of ModemManager.  Notable changes include:

* More flexible D-Bus API that accounts for the capabilities of modern devices
* Native support for Gobi and QMI-based Qualcomm devices via libqmi
* Native support for MBIM-based devices via libmbim
* Preliminary support for GPS-based Location Services with some devices
* More complete messaging API
* New libmm-glib client library
* New fully-featured command-line client (mmcli)
* systemd integration
* and much more...

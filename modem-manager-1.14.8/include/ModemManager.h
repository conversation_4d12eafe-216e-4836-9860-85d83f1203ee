/*
 * ModemManager Interface Specification
 * version 1.x
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the
 * Free Software Foundation, Inc., 51 Franklin Street, Fifth Floor,
 * Boston, MA 02110-1301 USA.
 *
 * Copyright (C) 2008 - 2009 Novell, Inc.
 * Copyright (C) 2009 - 2013 Red Hat, Inc.
 * Copyright (C) 2011 - 2013 Google, Inc.
 * Copyright (C) 2011 - 2013 Lanedo Gmbh
 */

#ifndef _MODEM_MANAGER_H_
#define _MODEM_MANAGER_H_

#define __MODEM_MANAGER_H_INSIDE__

/* Public header with DBus Interface, Method, Signal and Property names */
#include <ModemManager-names.h>

/* Public header with enumerations and flags */
#include <ModemManager-enums.h>

/* Public header with errors */
#include <ModemManager-errors.h>

/* Public header with compatibility types and methods */
#include <ModemManager-compat.h>

/* Public header with version info */
#include <ModemManager-version.h>

#endif /*  _MODEM_MANAGER_H_ */

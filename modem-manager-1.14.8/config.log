This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by ModemManager configure 1.14.8, which was
generated by GNU Autoconf 2.69.  Invocation command line was

  $ ./configure --target=arm-buildroot-linux-gnueabihf --host=arm-buildroot-linux-gnueabihf --build=x86_64-pc-linux-gnu --prefix=/usr --exec-prefix=/usr --sysconfdir=/etc --localstatedir=/var --program-prefix= --disable-gtk-doc --disable-gtk-doc-html --disable-doc --disable-docs --disable-documentation --with-xmlto=no --with-fop=no --disable-dependency-tracking --enable-ipv6 --enable-nls --disable-static --enable-shared --disable-more-warnings --without-qmi --with-udev --without-mbim build_alias=x86_64-pc-linux-gnu host_alias=arm-buildroot-linux-gnueabihf target_alias=arm-buildroot-linux-gnueabihf CC=/root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc CFLAGS=-D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   LDFLAGS= CPPFLAGS=-D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 CPP=/root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-cpp PKG_CONFIG=/root/buildroot-2021.02/dev_out/host/bin/pkg-config --no-create --no-recursion

## --------- ##
## Platform. ##
## --------- ##

hostname = a2e26749330e
uname -m = x86_64
uname -r = **********-microsoft-standard-WSL2
uname -s = Linux
uname -v = #1 SMP Thu Oct 5 21:02:42 UTC 2023

/usr/bin/uname -p = x86_64
/bin/uname -X     = unknown

/bin/arch              = x86_64
/usr/bin/arch -k       = unknown
/usr/convex/getsysinfo = unknown
/usr/bin/hostinfo      = unknown
/bin/machine           = unknown
/usr/bin/oslevel       = unknown
/bin/universe          = unknown

PATH: /root/buildroot-2021.02/dev_out/host/bin
PATH: /root/buildroot-2021.02/dev_out/host/sbin
PATH: /root/.vscode-server/bin/05047486b6df5eb8d44b2ecd70ea3bdf775fd937/bin/remote-cli
PATH: /usr/local/sbin
PATH: /usr/local/bin
PATH: /usr/sbin
PATH: /usr/bin
PATH: /sbin
PATH: /bin


## ----------- ##
## Core tests. ##
## ----------- ##

configure:2688: checking for a BSD-compatible install
configure:2756: result: /usr/bin/install -c
configure:2767: checking whether build environment is sane
configure:2822: result: yes
configure:2879: checking for arm-buildroot-linux-gnueabihf-strip
configure:2909: result: no
configure:2919: checking for strip
configure:2935: found /usr/bin/strip
configure:2946: result: strip
configure:2958: WARNING: using cross tools not prefixed with host triplet
configure:2971: checking for a thread-safe mkdir -p
configure:3010: result: /usr/bin/mkdir -p
configure:3017: checking for gawk
configure:3033: found /usr/bin/gawk
configure:3044: result: gawk
configure:3055: checking whether make sets $(MAKE)
configure:3077: result: yes
configure:3106: checking whether make supports nested variables
configure:3123: result: yes
configure:3212: checking whether UID '0' is supported by ustar format
configure:3215: result: yes
configure:3222: checking whether GID '0' is supported by ustar format
configure:3225: result: yes
configure:3233: checking how to create a ustar tar archive
configure:3244: tar --version
tar (GNU tar) 1.30
Copyright (C) 2017 Free Software Foundation, Inc.
License GPLv3+: GNU GPL version 3 or later <https://gnu.org/licenses/gpl.html>.
This is free software: you are free to change and redistribute it.
There is NO WARRANTY, to the extent permitted by law.

Written by John Gilmore and Jay Fenlason.
configure:3247: $? = 0
configure:3287: tardir=conftest.dir && eval tar --format=ustar -chf - "$tardir" >conftest.tar
configure:3290: $? = 0
configure:3294: tar -xf - <conftest.tar
configure:3297: $? = 0
configure:3299: cat conftest.dir/file
GrepMe
configure:3302: $? = 0
configure:3315: result: gnutar
configure:3376: checking whether make supports nested variables
configure:3393: result: yes
configure:3406: checking whether to enable maintainer-specific portions of Makefiles
configure:3415: result: yes
configure:3440: checking whether make supports nested variables
configure:3457: result: yes
configure:3478: checking whether make supports the include directive
configure:3493: make -f confmf.GNU && cat confinc.out
make[3]: Entering directory '/root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8'
make[3]: Leaving directory '/root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8'
this is the am__doit target
configure:3496: $? = 0
configure:3515: result: yes (GNU style)
configure:3545: checking for arm-buildroot-linux-gnueabihf-gcc
configure:3572: result: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc
configure:3841: checking for C compiler version
configure:3850: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc --version >&5
arm-none-linux-gnueabihf-gcc (GNU Toolchain for the A-profile Architecture 10.2-2020.11 (arm-10.16)) 10.2.1 20201103
Copyright (C) 2020 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:3861: $? = 0
configure:3850: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -v >&5
Using built-in specs.
COLLECT_GCC=/opt/gcc-arm/bin/arm-none-linux-gnueabihf-gcc
COLLECT_LTO_WRAPPER=/opt/gcc-arm/bin/../libexec/gcc/arm-none-linux-gnueabihf/10.2.1/lto-wrapper
Target: arm-none-linux-gnueabihf
Configured with: /tmp/dgboter/bbs/build03--cen7x86_64/buildbot/cen7x86_64--arm-none-linux-gnueabihf/build/src/gcc/configure --target=arm-none-linux-gnueabihf --prefix= --with-sysroot=/arm-none-linux-gnueabihf/libc --with-build-sysroot=/tmp/dgboter/bbs/build03--cen7x86_64/buildbot/cen7x86_64--arm-none-linux-gnueabihf/build/build-arm-none-linux-gnueabihf/install//arm-none-linux-gnueabihf/libc --with-bugurl=https://bugs.linaro.org/ --enable-gnu-indirect-function --enable-shared --disable-libssp --disable-libmudflap --enable-checking=release --enable-languages=c,c++,fortran --with-gmp=/tmp/dgboter/bbs/build03--cen7x86_64/buildbot/cen7x86_64--arm-none-linux-gnueabihf/build/build-arm-none-linux-gnueabihf/host-tools --with-mpfr=/tmp/dgboter/bbs/build03--cen7x86_64/buildbot/cen7x86_64--arm-none-linux-gnueabihf/build/build-arm-none-linux-gnueabihf/host-tools --with-mpc=/tmp/dgboter/bbs/build03--cen7x86_64/buildbot/cen7x86_64--arm-none-linux-gnueabihf/build/build-arm-none-linux-gnueabihf/host-tools --with-isl=/tmp/dgboter/bbs/build03--cen7x86_64/buildbot/cen7x86_64--arm-none-linux-gnueabihf/build/build-arm-none-linux-gnueabihf/host-tools --with-arch=armv7-a --with-fpu=neon --with-float=hard --with-mode=thumb --with-arch=armv7-a --with-pkgversion='GNU Toolchain for the A-profile Architecture 10.2-2020.11 (arm-10.16)'
Thread model: posix
Supported LTO compression algorithms: zlib
gcc version 10.2.1 20201103 (GNU Toolchain for the A-profile Architecture 10.2-2020.11 (arm-10.16)) 
configure:3861: $? = 0
configure:3850: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -V >&5
arm-none-linux-gnueabihf-gcc: error: unrecognized command-line option '-V'
arm-none-linux-gnueabihf-gcc: fatal error: no input files
compilation terminated.
configure:3861: $? = 1
configure:3850: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -qversion >&5
arm-none-linux-gnueabihf-gcc: error: unrecognized command-line option '-qversion'; did you mean '--version'?
arm-none-linux-gnueabihf-gcc: fatal error: no input files
compilation terminated.
configure:3861: $? = 1
configure:3881: checking whether the C compiler works
configure:3903: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  conftest.c  >&5
configure:3907: $? = 0
configure:3955: result: yes
configure:3958: checking for C compiler default output file name
configure:3960: result: a.out
configure:3966: checking for suffix of executables
configure:3973: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  conftest.c  >&5
configure:3977: $? = 0
configure:3999: result: 
configure:4021: checking whether we are cross compiling
configure:4059: result: yes
configure:4064: checking for suffix of object files
configure:4086: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:4090: $? = 0
configure:4111: result: o
configure:4115: checking whether we are using the GNU C compiler
configure:4134: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:4134: $? = 0
configure:4143: result: yes
configure:4152: checking whether /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc accepts -g
configure:4172: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -g -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:4172: $? = 0
configure:4213: result: yes
configure:4230: checking for /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc option to accept ISO C89
configure:4293: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc  -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:4293: $? = 0
configure:4306: result: none needed
configure:4331: checking whether /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc understands -c and -o together
configure:4353: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c conftest.c -o conftest2.o
configure:4356: $? = 0
configure:4353: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c conftest.c -o conftest2.o
configure:4356: $? = 0
configure:4368: result: yes
configure:4387: checking dependency style of /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc
configure:4498: result: none
configure:4519: checking how to run the C preprocessor
configure:4589: result: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-cpp
configure:4609: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-cpp -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c
configure:4609: $? = 0
configure:4623: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-cpp -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c
conftest.c:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:4623: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "ModemManager"
| #define PACKAGE_TARNAME "ModemManager"
| #define PACKAGE_VERSION "1.14.8"
| #define PACKAGE_STRING "ModemManager 1.14.8"
| #define PACKAGE_BUGREPORT "https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues"
| #define PACKAGE_URL ""
| #define PACKAGE "ModemManager"
| #define VERSION "1.14.8"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:4652: checking for grep that handles long lines and -e
configure:4710: result: /usr/bin/grep
configure:4715: checking for egrep
configure:4777: result: /usr/bin/grep -E
configure:4782: checking for ANSI C header files
configure:4802: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:4802: $? = 0
configure:4886: result: yes
configure:4899: checking for sys/types.h
configure:4899: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:4899: $? = 0
configure:4899: result: yes
configure:4899: checking for sys/stat.h
configure:4899: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:4899: $? = 0
configure:4899: result: yes
configure:4899: checking for stdlib.h
configure:4899: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:4899: $? = 0
configure:4899: result: yes
configure:4899: checking for string.h
configure:4899: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:4899: $? = 0
configure:4899: result: yes
configure:4899: checking for memory.h
configure:4899: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:4899: $? = 0
configure:4899: result: yes
configure:4899: checking for strings.h
configure:4899: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:4899: $? = 0
configure:4899: result: yes
configure:4899: checking for inttypes.h
configure:4899: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:4899: $? = 0
configure:4899: result: yes
configure:4899: checking for stdint.h
configure:4899: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:4899: $? = 0
configure:4899: result: yes
configure:4899: checking for unistd.h
configure:4899: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:4899: $? = 0
configure:4899: result: yes
configure:4912: checking minix/config.h usability
configure:4912: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
conftest.c:54:10: fatal error: minix/config.h: No such file or directory
   54 | #include <minix/config.h>
      |          ^~~~~~~~~~~~~~~~
compilation terminated.
configure:4912: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "ModemManager"
| #define PACKAGE_TARNAME "ModemManager"
| #define PACKAGE_VERSION "1.14.8"
| #define PACKAGE_STRING "ModemManager 1.14.8"
| #define PACKAGE_BUGREPORT "https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues"
| #define PACKAGE_URL ""
| #define PACKAGE "ModemManager"
| #define VERSION "1.14.8"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| /* end confdefs.h.  */
| #include <stdio.h>
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef STDC_HEADERS
| # include <stdlib.h>
| # include <stddef.h>
| #else
| # ifdef HAVE_STDLIB_H
| #  include <stdlib.h>
| # endif
| #endif
| #ifdef HAVE_STRING_H
| # if !defined STDC_HEADERS && defined HAVE_MEMORY_H
| #  include <memory.h>
| # endif
| # include <string.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| #include <minix/config.h>
configure:4912: result: no
configure:4912: checking minix/config.h presence
configure:4912: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-cpp -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c
conftest.c:21:10: fatal error: minix/config.h: No such file or directory
   21 | #include <minix/config.h>
      |          ^~~~~~~~~~~~~~~~
compilation terminated.
configure:4912: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "ModemManager"
| #define PACKAGE_TARNAME "ModemManager"
| #define PACKAGE_VERSION "1.14.8"
| #define PACKAGE_STRING "ModemManager 1.14.8"
| #define PACKAGE_BUGREPORT "https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues"
| #define PACKAGE_URL ""
| #define PACKAGE "ModemManager"
| #define VERSION "1.14.8"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| /* end confdefs.h.  */
| #include <minix/config.h>
configure:4912: result: no
configure:4912: checking for minix/config.h
configure:4912: result: no
configure:4933: checking whether it is safe to define __EXTENSIONS__
configure:4951: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:4951: $? = 0
configure:4958: result: yes
configure:4981: checking for arm-buildroot-linux-gnueabihf-gcc
configure:5008: result: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc
configure:5277: checking for C compiler version
configure:5286: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc --version >&5
arm-none-linux-gnueabihf-gcc (GNU Toolchain for the A-profile Architecture 10.2-2020.11 (arm-10.16)) 10.2.1 20201103
Copyright (C) 2020 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:5297: $? = 0
configure:5286: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -v >&5
Using built-in specs.
COLLECT_GCC=/opt/gcc-arm/bin/arm-none-linux-gnueabihf-gcc
COLLECT_LTO_WRAPPER=/opt/gcc-arm/bin/../libexec/gcc/arm-none-linux-gnueabihf/10.2.1/lto-wrapper
Target: arm-none-linux-gnueabihf
Configured with: /tmp/dgboter/bbs/build03--cen7x86_64/buildbot/cen7x86_64--arm-none-linux-gnueabihf/build/src/gcc/configure --target=arm-none-linux-gnueabihf --prefix= --with-sysroot=/arm-none-linux-gnueabihf/libc --with-build-sysroot=/tmp/dgboter/bbs/build03--cen7x86_64/buildbot/cen7x86_64--arm-none-linux-gnueabihf/build/build-arm-none-linux-gnueabihf/install//arm-none-linux-gnueabihf/libc --with-bugurl=https://bugs.linaro.org/ --enable-gnu-indirect-function --enable-shared --disable-libssp --disable-libmudflap --enable-checking=release --enable-languages=c,c++,fortran --with-gmp=/tmp/dgboter/bbs/build03--cen7x86_64/buildbot/cen7x86_64--arm-none-linux-gnueabihf/build/build-arm-none-linux-gnueabihf/host-tools --with-mpfr=/tmp/dgboter/bbs/build03--cen7x86_64/buildbot/cen7x86_64--arm-none-linux-gnueabihf/build/build-arm-none-linux-gnueabihf/host-tools --with-mpc=/tmp/dgboter/bbs/build03--cen7x86_64/buildbot/cen7x86_64--arm-none-linux-gnueabihf/build/build-arm-none-linux-gnueabihf/host-tools --with-isl=/tmp/dgboter/bbs/build03--cen7x86_64/buildbot/cen7x86_64--arm-none-linux-gnueabihf/build/build-arm-none-linux-gnueabihf/host-tools --with-arch=armv7-a --with-fpu=neon --with-float=hard --with-mode=thumb --with-arch=armv7-a --with-pkgversion='GNU Toolchain for the A-profile Architecture 10.2-2020.11 (arm-10.16)'
Thread model: posix
Supported LTO compression algorithms: zlib
gcc version 10.2.1 20201103 (GNU Toolchain for the A-profile Architecture 10.2-2020.11 (arm-10.16)) 
configure:5297: $? = 0
configure:5286: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -V >&5
arm-none-linux-gnueabihf-gcc: error: unrecognized command-line option '-V'
arm-none-linux-gnueabihf-gcc: fatal error: no input files
compilation terminated.
configure:5297: $? = 1
configure:5286: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -qversion >&5
arm-none-linux-gnueabihf-gcc: error: unrecognized command-line option '-qversion'; did you mean '--version'?
arm-none-linux-gnueabihf-gcc: fatal error: no input files
compilation terminated.
configure:5297: $? = 1
configure:5301: checking whether we are using the GNU C compiler
configure:5329: result: yes
configure:5338: checking whether /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc accepts -g
configure:5399: result: yes
configure:5416: checking for /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc option to accept ISO C89
configure:5492: result: none needed
configure:5517: checking whether /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc understands -c and -o together
configure:5554: result: yes
configure:5573: checking dependency style of /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc
configure:5684: result: none
configure:5733: checking build system type
configure:5747: result: x86_64-pc-linux-gnu
configure:5767: checking host system type
configure:5780: result: arm-buildroot-linux-gnueabihf
configure:5821: checking how to print strings
configure:5848: result: printf
configure:5869: checking for a sed that does not truncate output
configure:5933: result: /usr/bin/sed
configure:5951: checking for fgrep
configure:6013: result: /usr/bin/grep -F
configure:6048: checking for ld used by /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc
configure:6115: result: /opt/gcc-arm/arm-none-linux-gnueabihf/bin/ld
configure:6122: checking if the linker (/opt/gcc-arm/arm-none-linux-gnueabihf/bin/ld) is GNU ld
configure:6137: result: yes
configure:6149: checking for BSD- or MS-compatible name lister (nm)
configure:6203: result: no
configure:6217: checking for arm-buildroot-linux-gnueabihf-dumpbin
configure:6247: result: no
configure:6217: checking for arm-buildroot-linux-gnueabihf-link
configure:6247: result: no
configure:6261: checking for dumpbin
configure:6291: result: no
configure:6261: checking for link
configure:6277: found /usr/bin/link
configure:6288: result: link -dump
configure:6333: checking the name lister (nm) interface
configure:6340: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:6343: nm "conftest.o"
configure:6346: output
00000000 b $d
00000000 B some_variable
configure:6347: result: BSD nm
configure:6350: checking whether ln -s works
configure:6354: result: yes
configure:6362: checking the maximum length of command line arguments
configure:6493: result: 1572864
configure:6541: checking how to convert x86_64-pc-linux-gnu file names to arm-buildroot-linux-gnueabihf format
configure:6581: result: func_convert_file_noop
configure:6588: checking how to convert x86_64-pc-linux-gnu file names to toolchain format
configure:6608: result: func_convert_file_noop
configure:6615: checking for /opt/gcc-arm/arm-none-linux-gnueabihf/bin/ld option to reload object files
configure:6622: result: -r
configure:6656: checking for arm-buildroot-linux-gnueabihf-objdump
configure:6686: result: no
configure:6696: checking for objdump
configure:6712: found /usr/bin/objdump
configure:6723: result: objdump
configure:6755: checking how to recognize dependent libraries
configure:6955: result: pass_all
configure:7000: checking for arm-buildroot-linux-gnueabihf-dlltool
configure:7030: result: no
configure:7040: checking for dlltool
configure:7070: result: no
configure:7100: checking how to associate runtime and link libraries
configure:7127: result: printf %s\n
configure:7143: checking for arm-buildroot-linux-gnueabihf-ar
configure:7173: result: no
configure:7187: checking for ar
configure:7203: found /usr/bin/ar
configure:7214: result: ar
configure:7251: checking for archiver @FILE support
configure:7268: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:7268: $? = 0
configure:7271: ar cr libconftest.a @conftest.lst >&5
configure:7274: $? = 0
configure:7279: ar cr libconftest.a @conftest.lst >&5
ar: conftest.o: No such file or directory
configure:7282: $? = 1
configure:7281: result: @
configure:7299: checking for arm-buildroot-linux-gnueabihf-strip
configure:7326: result: strip
configure:7398: checking for arm-buildroot-linux-gnueabihf-ranlib
configure:7428: result: no
configure:7438: checking for ranlib
configure:7454: found /usr/bin/ranlib
configure:7465: result: ranlib
configure:7567: checking command to parse nm output from /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc object
configure:7720: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:7723: $? = 0
configure:7727: nm conftest.o | sed -n -e 's/^.*[	 ]\([ABCDGIRSTW][ABCDGIRSTW]*\)[	 ][	 ]*\([_A-Za-z][_A-Za-z0-9]*\)$/\1 \2 \2/p' | sed '/ __gnu_lto/d' > conftest.nm
configure:7793: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  conftest.c conftstm.o >&5
configure:7796: $? = 0
configure:7834: result: ok
configure:7881: checking for sysroot
configure:7911: result: no
configure:7918: checking for a working dd
configure:7956: result: /usr/bin/dd
configure:7960: checking how to truncate binary pipes
configure:7975: result: /usr/bin/dd bs=4096 count=1
configure:8264: checking for arm-buildroot-linux-gnueabihf-mt
configure:8294: result: no
configure:8304: checking for mt
configure:8320: found /usr/bin/mt
configure:8331: result: mt
configure:8354: checking if mt is a manifest tool
configure:8360: mt '-?'
configure:8368: result: no
configure:9042: checking for dlfcn.h
configure:9042: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:9042: $? = 0
configure:9042: result: yes
configure:9297: checking for objdir
configure:9312: result: .libs
configure:9576: checking if /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc supports -fno-rtti -fno-exceptions
configure:9594: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 -fno-rtti -fno-exceptions conftest.c >&5
cc1: warning: command-line option '-fno-rtti' is valid for C++/D/ObjC++ but not for C
configure:9598: $? = 0
configure:9611: result: no
configure:9975: checking for /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc option to produce PIC
configure:9982: result: -fPIC -DPIC
configure:9990: checking if /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc PIC flag -fPIC -DPIC works
configure:10008: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 -fPIC -DPIC -DPIC conftest.c >&5
configure:10012: $? = 0
configure:10025: result: yes
configure:10054: checking if /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc static flag -static works
configure:10082: result: yes
configure:10097: checking if /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc supports -c -o file.o
configure:10118: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 -o out/conftest2.o conftest.c >&5
configure:10122: $? = 0
configure:10144: result: yes
configure:10152: checking if /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc supports -c -o file.o
configure:10199: result: yes
configure:10232: checking whether the /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc linker (/opt/gcc-arm/arm-none-linux-gnueabihf/bin/ld) supports shared libraries
configure:11495: result: yes
configure:11532: checking whether -lc should be explicitly linked in
configure:11540: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:11543: $? = 0
configure:11558: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -shared  -fPIC -DPIC conftest.o  -v -Wl,-soname -Wl,conftest -o conftest 2\>\&1 \| /usr/bin/grep  -lc  \>/dev/null 2\>\&1
configure:11561: $? = 0
configure:11575: result: no
configure:11735: checking dynamic linker characteristics
configure:12316: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Wl,-rpath -Wl,/foo conftest.c  >&5
configure:12316: $? = 0
configure:12565: result: GNU/Linux ld.so
configure:12687: checking how to hardcode library paths into programs
configure:12712: result: immediate
configure:13260: checking whether stripping libraries is possible
configure:13265: result: yes
configure:13300: checking if libtool supports shared libraries
configure:13302: result: yes
configure:13305: checking whether to build shared libraries
configure:13330: result: yes
configure:13333: checking whether to build static libraries
configure:13337: result: no
configure:10248: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
conftest.c:30:9: error: #error "no C++"
   30 |        #error "no C++"
      |         ^~~~~
configure:10248: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "ModemManager"
| #define PACKAGE_TARNAME "ModemManager"
| #define PACKAGE_VERSION "1.14.8"
| #define PACKAGE_STRING "ModemManager 1.14.8"
| #define PACKAGE_BUGREPORT "https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues"
| #define PACKAGE_URL ""
| #define PACKAGE "ModemManager"
| #define VERSION "1.14.8"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| /* end confdefs.h.  */
| 
|       #ifndef __cplusplus
|        #error "no C++"
|        #endif
| int
| main ()
| {
| 
|   ;
|   return 0;
| }
configure:10260: checking whether C compiler accepts -Werror=unknown-warning-option
configure:10279: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Werror=unknown-warning-option -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
cc1: error: '-Werror=unknown-warning-option': no option '-Wunknown-warning-option'
configure:10279: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "ModemManager"
| #define PACKAGE_TARNAME "ModemManager"
| #define PACKAGE_VERSION "1.14.8"
| #define PACKAGE_STRING "ModemManager 1.14.8"
| #define PACKAGE_BUGREPORT "https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues"
| #define PACKAGE_URL ""
| #define PACKAGE "ModemManager"
| #define VERSION "1.14.8"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| /* end confdefs.h.  */
| 
| int
| main ()
| {
| 
|   ;
|   return 0;
| }
configure:10287: result: no
configure:10301: checking whether C compiler accepts -Wno-suggest-attribute=format
configure:10320: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wno-suggest-attribute=format -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10320: $? = 0
configure:10328: result: yes
configure:10348: checking whether C compiler accepts -fno-strict-aliasing
configure:10367: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -fno-strict-aliasing -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10367: $? = 0
configure:10376: result: yes
configure:10403: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10406: $? = 0
configure:10428: checking whether C compiler accepts -Wnested-externs
configure:10447: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wnested-externs -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10447: $? = 0
configure:10456: result: yes
configure:10472: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10475: $? = 0
configure:10428: checking whether C compiler accepts -Wmissing-prototypes
configure:10447: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wmissing-prototypes -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10447: $? = 0
configure:10456: result: yes
configure:10472: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10475: $? = 0
configure:10428: checking whether C compiler accepts -Wstrict-prototypes
configure:10447: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wstrict-prototypes -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
conftest.c:30:1: warning: function declaration isn't a prototype [-Wstrict-prototypes]
   30 | main ()
      | ^~~~
configure:10447: $? = 0
configure:10456: result: yes
configure:10472: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10475: $? = 0
configure:10428: checking whether C compiler accepts -Wdeclaration-after-statement
configure:10447: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wdeclaration-after-statement -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10447: $? = 0
configure:10456: result: yes
configure:10472: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10475: $? = 0
configure:10428: checking whether C compiler accepts -Wimplicit-function-declaration
configure:10447: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wimplicit-function-declaration -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10447: $? = 0
configure:10456: result: yes
configure:10472: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10475: $? = 0
configure:10428: checking whether C compiler accepts -Wold-style-definition
configure:10447: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wold-style-definition -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
conftest.c: In function 'main':
conftest.c:30:1: warning: old-style function definition [-Wold-style-definition]
   30 | main ()
      | ^~~~
configure:10447: $? = 0
configure:10456: result: yes
configure:10472: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10475: $? = 0
configure:10428: checking whether C compiler accepts -Wjump-misses-init
configure:10447: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wjump-misses-init -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10447: $? = 0
configure:10456: result: yes
configure:10472: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10475: $? = 0
configure:10506: checking whether C compiler accepts -Wall
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wall -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wextra
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wextra -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wundef
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wundef -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wwrite-strings
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wwrite-strings -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wpointer-arith
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wpointer-arith -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wmissing-declarations
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wmissing-declarations -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wredundant-decls
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wredundant-decls -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wno-unused-parameter
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wno-unused-parameter -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wno-missing-field-initializers
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wno-missing-field-initializers -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wformat=2
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wformat=2 -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wcast-align
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wcast-align -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wformat-nonliteral
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wformat-nonliteral -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
cc1: warning: '-Wformat-nonliteral' ignored without '-Wformat' [-Wformat-nonliteral]
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wformat-security
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wformat-security -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
cc1: warning: '-Wformat-security' ignored without '-Wformat' [-Wformat-security]
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wsign-compare
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wsign-compare -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wstrict-aliasing
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wstrict-aliasing -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wshadow
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wshadow -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Winline
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Winline -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wpacked
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wpacked -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wmissing-format-attribute
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wmissing-format-attribute -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wmissing-noreturn
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wmissing-noreturn -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Winit-self
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Winit-self -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wredundant-decls
configure:10534: result: yes
configure:10542: : WARN_CFLAGS already contains $flag
configure:10545: $? = 0
configure:10506: checking whether C compiler accepts -Wmissing-include-dirs
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wmissing-include-dirs -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wunused-but-set-variable
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wunused-but-set-variable -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Warray-bounds
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Warray-bounds -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wreturn-type
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wreturn-type -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wswitch-enum
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wswitch-enum -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wswitch-default
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wswitch-default -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wduplicated-cond
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wduplicated-cond -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wduplicated-branches
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wduplicated-branches -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wlogical-op
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wlogical-op -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wrestrict
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wrestrict -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wnull-dereference
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wnull-dereference -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10506: checking whether C compiler accepts -Wdouble-promotion
configure:10525: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wdouble-promotion -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10525: $? = 0
configure:10534: result: yes
configure:10550: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10553: $? = 0
configure:10711: checking whether C compiler accepts -Wno-error=unused-parameter
configure:10730: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wno-error=unused-parameter -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10730: $? = 0
configure:10739: result: yes
configure:10755: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10758: $? = 0
configure:10711: checking whether C compiler accepts -Wno-error=missing-field-initializers
configure:10730: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wno-error=missing-field-initializers -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:10730: $? = 0
configure:10739: result: yes
configure:10755: : WARN_CFLAGS="$WARN_CFLAGS"
configure:10758: $? = 0
configure:10815: checking whether C compiler accepts -Werror=unknown-warning-option
configure:10842: result: no
configure:10855: checking whether the linker accepts -Wl,--as-needed
configure:10874: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64   -Wl,--as-needed conftest.c  >&5
configure:10874: $? = 0
configure:10883: result: yes
configure:10893: checking whether the linker accepts -Wl,--as-needed
configure:10912: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64   -Wl,--as-needed conftest.c  >&5
configure:10912: $? = 0
configure:10922: result: yes
configure:10949: : AM_LDFLAGS="$AM_LDFLAGS"
configure:10952: $? = 0
configure:10968: checking whether the linker accepts -Wl,-z,relro
configure:10987: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64   -Wl,-z,relro conftest.c  >&5
configure:10987: $? = 0
configure:10996: result: yes
configure:11006: checking whether the linker accepts -Wl,-z,relro
configure:11025: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64   -Wl,-z,relro conftest.c  >&5
configure:11025: $? = 0
configure:11035: result: yes
configure:11051: : AM_LDFLAGS="$AM_LDFLAGS"
configure:11054: $? = 0
configure:11081: checking whether the linker accepts -Wl,-z,now
configure:11100: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64   -Wl,-z,now conftest.c  >&5
configure:11100: $? = 0
configure:11109: result: yes
configure:11119: checking whether the linker accepts -Wl,-z,now
configure:11138: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64   -Wl,-z,now conftest.c  >&5
configure:11138: $? = 0
configure:11148: result: yes
configure:11164: : AM_LDFLAGS="$AM_LDFLAGS"
configure:11167: $? = 0
configure:11194: checking whether the linker accepts -Wl,-z,noexecstack
configure:11213: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64   -Wl,-z,noexecstack conftest.c  >&5
configure:11213: $? = 0
configure:11222: result: yes
configure:11232: checking whether the linker accepts -Wl,-z,noexecstack
configure:11251: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64   -Wl,-z,noexecstack conftest.c  >&5
configure:11251: $? = 0
configure:11261: result: yes
configure:11277: : AM_LDFLAGS="$AM_LDFLAGS"
configure:11280: $? = 0
configure:11310: checking whether the linker accepts -Wl,--no-as-needed
configure:11329: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64   -Wl,--no-as-needed conftest.c  >&5
configure:11329: $? = 0
configure:11338: result: yes
configure:11353: checking whether the linker accepts -Wl,--fatal-warnings
configure:11372: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64   -Wl,--fatal-warnings conftest.c  >&5
configure:11372: $? = 0
configure:11381: result: yes
configure:11391: checking whether the linker accepts -Wl,-fatal_warnings
configure:11410: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64   -Wl,-fatal_warnings conftest.c  >&5
/opt/gcc-arm/bin/../lib/gcc/arm-none-linux-gnueabihf/10.2.1/../../../../arm-none-linux-gnueabihf/bin/ld: -f may not be used without -shared
collect2: error: ld returned 1 exit status
configure:11410: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "ModemManager"
| #define PACKAGE_TARNAME "ModemManager"
| #define PACKAGE_VERSION "1.14.8"
| #define PACKAGE_STRING "ModemManager 1.14.8"
| #define PACKAGE_BUGREPORT "https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues"
| #define PACKAGE_URL ""
| #define PACKAGE "ModemManager"
| #define VERSION "1.14.8"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| /* end confdefs.h.  */
| 
| int
| main ()
| {
| 
|   ;
|   return 0;
| }
configure:11419: result: no
configure:11437: checking whether the linker accepts -Wl,--no-as-needed
configure:11456: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64   -Wl,--no-as-needed conftest.c  >&5
configure:11456: $? = 0
configure:11466: result: yes
configure:11493: : WARN_LDFLAGS="$WARN_LDFLAGS"
configure:11496: $? = 0
configure:11710: : WARN_SCANNERFLAGS="$WARN_SCANNERFLAGS"
configure:11713: $? = 0
configure:11730: : WARN_SCANNERFLAGS="$WARN_SCANNERFLAGS"
configure:11733: $? = 0
configure:11826: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
conftest.c:30:9: error: #error "no C++"
   30 |        #error "no C++"
      |         ^~~~~
configure:11826: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "ModemManager"
| #define PACKAGE_TARNAME "ModemManager"
| #define PACKAGE_VERSION "1.14.8"
| #define PACKAGE_STRING "ModemManager 1.14.8"
| #define PACKAGE_BUGREPORT "https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues"
| #define PACKAGE_URL ""
| #define PACKAGE "ModemManager"
| #define VERSION "1.14.8"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| /* end confdefs.h.  */
| 
|       #ifndef __cplusplus
|        #error "no C++"
|        #endif
| int
| main ()
| {
| 
|   ;
|   return 0;
| }
configure:11838: checking whether C compiler accepts -Werror=unknown-warning-option
configure:11865: result: no
configure:11879: checking whether C compiler accepts -Wno-suggest-attribute=format
configure:11906: result: yes
configure:11926: checking whether C compiler accepts -fno-strict-aliasing
configure:11954: result: yes
configure:11962: : WARN_CFLAGS already contains $flag
configure:11965: $? = 0
configure:12006: checking whether C compiler accepts -Wnested-externs
configure:12034: result: yes
configure:12042: : WARN_CFLAGS already contains $flag
configure:12045: $? = 0
configure:12006: checking whether C compiler accepts -Wmissing-prototypes
configure:12034: result: yes
configure:12042: : WARN_CFLAGS already contains $flag
configure:12045: $? = 0
configure:12006: checking whether C compiler accepts -Wstrict-prototypes
configure:12034: result: yes
configure:12042: : WARN_CFLAGS already contains $flag
configure:12045: $? = 0
configure:12006: checking whether C compiler accepts -Wdeclaration-after-statement
configure:12034: result: yes
configure:12042: : WARN_CFLAGS already contains $flag
configure:12045: $? = 0
configure:12006: checking whether C compiler accepts -Wimplicit-function-declaration
configure:12034: result: yes
configure:12042: : WARN_CFLAGS already contains $flag
configure:12045: $? = 0
configure:12006: checking whether C compiler accepts -Wold-style-definition
configure:12034: result: yes
configure:12042: : WARN_CFLAGS already contains $flag
configure:12045: $? = 0
configure:12006: checking whether C compiler accepts -Wjump-misses-init
configure:12034: result: yes
configure:12042: : WARN_CFLAGS already contains $flag
configure:12045: $? = 0
configure:12084: checking whether C compiler accepts -Wall
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wextra
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wundef
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wwrite-strings
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wpointer-arith
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wmissing-declarations
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wredundant-decls
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wno-unused-parameter
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wno-missing-field-initializers
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wformat=2
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wcast-align
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wformat-nonliteral
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wformat-security
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wsign-compare
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wstrict-aliasing
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wshadow
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Winline
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wpacked
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wmissing-format-attribute
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wmissing-noreturn
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Winit-self
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wredundant-decls
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wmissing-include-dirs
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wunused-but-set-variable
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Warray-bounds
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wreturn-type
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wswitch-enum
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wswitch-default
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wduplicated-cond
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wduplicated-branches
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wlogical-op
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wrestrict
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wnull-dereference
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wdouble-promotion
configure:12112: result: yes
configure:12120: : WARN_CFLAGS already contains $flag
configure:12123: $? = 0
configure:12084: checking whether C compiler accepts -Wno-cast-function-type
configure:12103: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wno-cast-function-type -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:12103: $? = 0
configure:12112: result: yes
configure:12128: : WARN_CFLAGS="$WARN_CFLAGS"
configure:12131: $? = 0
configure:12084: checking whether C compiler accepts -Wno-packed
configure:12103: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wno-packed -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:12103: $? = 0
configure:12112: result: yes
configure:12128: : WARN_CFLAGS="$WARN_CFLAGS"
configure:12131: $? = 0
configure:12289: checking whether C compiler accepts -Wno-error=unused-parameter
configure:12317: result: yes
configure:12325: : WARN_CFLAGS already contains $flag
configure:12328: $? = 0
configure:12289: checking whether C compiler accepts -Wno-error=missing-field-initializers
configure:12317: result: yes
configure:12325: : WARN_CFLAGS already contains $flag
configure:12328: $? = 0
configure:12289: checking whether C compiler accepts -Wno-error=cast-function-type
configure:12308: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wno-error=cast-function-type -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:12308: $? = 0
configure:12317: result: yes
configure:12333: : WARN_CFLAGS="$WARN_CFLAGS"
configure:12336: $? = 0
configure:12289: checking whether C compiler accepts -Wno-error=packed
configure:12308: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -c -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os    -Wno-error=packed -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 conftest.c >&5
configure:12308: $? = 0
configure:12317: result: yes
configure:12333: : WARN_CFLAGS="$WARN_CFLAGS"
configure:12336: $? = 0
configure:12506: checking pkg-config is at least version 0.9.0
configure:12509: result: yes
configure:12521: checking for gtk-doc
configure:12524: $PKG_CONFIG --exists --print-errors "$gtk_doc_requires"
Package gtk-doc was not found in the pkg-config search path.
Perhaps you should add the directory containing `gtk-doc.pc'
to the PKG_CONFIG_PATH environment variable
Package 'gtk-doc', required by 'virtual:world', not found
configure:12527: $? = 1
configure:12533: result: no
configure:12539: WARNING:
  You will not be able to create source packages with 'make dist'
  because gtk-doc >= 1.0 is not found.
configure:12547: checking for gtkdoc-check
configure:12577: result: no
configure:12584: checking for gtkdoc-check
configure:12617: result: no
configure:12626: checking for gtkdoc-rebase
configure:12659: result: no
configure:12670: checking for gtkdoc-mkpdf
configure:12703: result: no
configure:12728: checking whether to build gtk-doc documentation
configure:12730: result: no
configure:12743: checking for GTKDOC_DEPS
configure:12750: $PKG_CONFIG --exists --print-errors "glib-2.0 >= 2.10.0 gobject-2.0  >= 2.10.0"
configure:12753: $? = 0
configure:12767: $PKG_CONFIG --exists --print-errors "glib-2.0 >= 2.10.0 gobject-2.0  >= 2.10.0"
configure:12770: $? = 0
configure:12808: result: yes
configure:12890: checking whether NLS is requested
configure:12899: result: yes
configure:12939: checking for msgfmt
configure: trying /root/buildroot-2021.02/dev_out/host/bin/msgfmt...
0 translated messages.
configure:12971: result: /root/buildroot-2021.02/dev_out/host/bin/msgfmt
configure:12980: checking for gmsgfmt
configure:13011: result: /root/buildroot-2021.02/dev_out/host/bin/msgfmt
configure:13061: checking for xgettext
configure: trying /root/buildroot-2021.02/dev_out/host/bin/xgettext...
/root/buildroot-2021.02/dev_out/host/bin/xgettext: warning: file '/dev/null' extension '' is unknown; will try C
configure:13093: result: /root/buildroot-2021.02/dev_out/host/bin/xgettext
configure:13138: checking for msgmerge
configure: trying /root/buildroot-2021.02/dev_out/host/bin/msgmerge...
configure:13169: result: /root/buildroot-2021.02/dev_out/host/bin/msgmerge
configure:13227: checking for ld used by /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc
configure:13294: result: /opt/gcc-arm/arm-none-linux-gnueabihf/bin/ld
configure:13301: checking if the linker (/opt/gcc-arm/arm-none-linux-gnueabihf/bin/ld) is GNU ld
configure:13316: result: yes
configure:13323: checking for shared library run path origin
configure:13336: result: done
configure:13908: checking for CFPreferencesCopyAppValue
configure:13926: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -std=gnu89 -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  conftest.c  -Wl,-framework -Wl,CoreFoundation >&5
conftest.c:28:10: fatal error: CoreFoundation/CFPreferences.h: No such file or directory
   28 | #include <CoreFoundation/CFPreferences.h>
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
compilation terminated.
configure:13926: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "ModemManager"
| #define PACKAGE_TARNAME "ModemManager"
| #define PACKAGE_VERSION "1.14.8"
| #define PACKAGE_STRING "ModemManager 1.14.8"
| #define PACKAGE_BUGREPORT "https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues"
| #define PACKAGE_URL ""
| #define PACKAGE "ModemManager"
| #define VERSION "1.14.8"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| /* end confdefs.h.  */
| #include <CoreFoundation/CFPreferences.h>
| int
| main ()
| {
| CFPreferencesCopyAppValue(NULL, NULL)
|   ;
|   return 0;
| }
configure:13935: result: no
configure:13942: checking for CFLocaleCopyCurrent
configure:13960: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -std=gnu89 -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  conftest.c  -Wl,-framework -Wl,CoreFoundation >&5
conftest.c:28:10: fatal error: CoreFoundation/CFLocale.h: No such file or directory
   28 | #include <CoreFoundation/CFLocale.h>
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~
compilation terminated.
configure:13960: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "ModemManager"
| #define PACKAGE_TARNAME "ModemManager"
| #define PACKAGE_VERSION "1.14.8"
| #define PACKAGE_STRING "ModemManager 1.14.8"
| #define PACKAGE_BUGREPORT "https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues"
| #define PACKAGE_URL ""
| #define PACKAGE "ModemManager"
| #define VERSION "1.14.8"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define __EXTENSIONS__ 1
| #define _ALL_SOURCE 1
| #define _GNU_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define _TANDEM_SOURCE 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| /* end confdefs.h.  */
| #include <CoreFoundation/CFLocale.h>
| int
| main ()
| {
| CFLocaleCopyCurrent();
|   ;
|   return 0;
| }
configure:13969: result: no
configure:14018: checking for GNU gettext in libc
configure:14047: /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc -o conftest -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -std=gnu89 -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  conftest.c  >&5
configure:14047: $? = 0
configure:14056: result: yes
configure:14903: checking whether to use NLS
configure:14905: result: yes
configure:14908: checking where the gettext function comes from
configure:14919: result: libc
configure:14994: checking for MM
configure:15005: $PKG_CONFIG --exists --print-errors "glib-2.0 >= $GLIB_MIN_VERSION
                  gmodule-2.0
                  gobject-2.0
                  gio-2.0
                  gio-unix-2.0"
configure:15012: $? = 0
configure:15034: $PKG_CONFIG --exists --print-errors "glib-2.0 >= $GLIB_MIN_VERSION
                  gmodule-2.0
                  gobject-2.0
                  gio-2.0
                  gio-unix-2.0"
configure:15041: $? = 0
configure:15115: result: yes
configure:15125: checking for LIBMM_GLIB
configure:15135: $PKG_CONFIG --exists --print-errors "glib-2.0 >= $GLIB_MIN_VERSION
                  gobject-2.0
                  gio-2.0
                  gio-unix-2.0"
configure:15141: $? = 0
configure:15161: $PKG_CONFIG --exists --print-errors "glib-2.0 >= $GLIB_MIN_VERSION
                  gobject-2.0
                  gio-2.0
                  gio-unix-2.0"
configure:15167: $? = 0
configure:15237: result: yes
configure:15247: checking for MMCLI
configure:15256: $PKG_CONFIG --exists --print-errors "glib-2.0 >= $GLIB_MIN_VERSION
                  gobject-2.0
                  gio-2.0"
configure:15261: $? = 0
configure:15279: $PKG_CONFIG --exists --print-errors "glib-2.0 >= $GLIB_MIN_VERSION
                  gobject-2.0
                  gio-2.0"
configure:15284: $? = 0
configure:15350: result: yes
configure:15366: checking for xsltproc
configure:15382: found /root/buildroot-2021.02/dev_out/host/bin/xsltproc
configure:15393: result: yes
configure:15419: checking whether to build with code coverage support
configure:15439: result: no
configure:15771: checking for gobject-introspection
configure:15802: $PKG_CONFIG --exists --print-errors "gobject-introspection-1.0 >= 0.9.6"
Package gobject-introspection-1.0 was not found in the pkg-config search path.
Perhaps you should add the directory containing `gobject-introspection-1.0.pc'
to the PKG_CONFIG_PATH environment variable
Package 'gobject-introspection-1.0', required by 'virtual:world', not found
configure:15805: $? = 1
configure:15817: result: no
configure:16033: checking pkg-config is at least version 0.9.0
configure:16036: result: yes
configure:16046: $PKG_CONFIG --exists --print-errors "$vapigen_pkg"
Package vapigen was not found in the pkg-config search path.
Perhaps you should add the directory containing `vapigen.pc'
to the PKG_CONFIG_PATH environment variable
Package 'vapigen', required by 'virtual:world', not found
configure:16049: $? = 1
configure:16075: checking for vapigen
configure:16097: result: no
configure:16188: checking for GUDEV
configure:16195: $PKG_CONFIG --exists --print-errors "gudev-1.0 >= $GUDEV_VERSION"
configure:16198: $? = 0
configure:16212: $PKG_CONFIG --exists --print-errors "gudev-1.0 >= $GUDEV_VERSION"
configure:16215: $? = 0
configure:16253: result: yes
configure:16275: checking for LIBSYSTEMD
configure:16282: $PKG_CONFIG --exists --print-errors "libsystemd >= 209"
Package libsystemd was not found in the pkg-config search path.
Perhaps you should add the directory containing `libsystemd.pc'
to the PKG_CONFIG_PATH environment variable
Package 'libsystemd', required by 'virtual:world', not found
configure:16285: $? = 1
configure:16299: $PKG_CONFIG --exists --print-errors "libsystemd >= 209"
Package libsystemd was not found in the pkg-config search path.
Perhaps you should add the directory containing `libsystemd.pc'
to the PKG_CONFIG_PATH environment variable
Package 'libsystemd', required by 'virtual:world', not found
configure:16302: $? = 1
configure:16316: result: no
Package 'libsystemd', required by 'virtual:world', not found
configure:16346: checking for LIBSYSTEMD_LOGIN
configure:16353: $PKG_CONFIG --exists --print-errors "libsystemd-login >= 183"
Package libsystemd-login was not found in the pkg-config search path.
Perhaps you should add the directory containing `libsystemd-login.pc'
to the PKG_CONFIG_PATH environment variable
Package 'libsystemd-login', required by 'virtual:world', not found
configure:16356: $? = 1
configure:16370: $PKG_CONFIG --exists --print-errors "libsystemd-login >= 183"
Package libsystemd-login was not found in the pkg-config search path.
Perhaps you should add the directory containing `libsystemd-login.pc'
to the PKG_CONFIG_PATH environment variable
Package 'libsystemd-login', required by 'virtual:world', not found
configure:16373: $? = 1
configure:16387: result: no
Package 'libsystemd-login', required by 'virtual:world', not found
configure:16499: checking for POLKIT
configure:16506: $PKG_CONFIG --exists --print-errors "polkit-gobject-1 >= 0.97"
Package polkit-gobject-1 was not found in the pkg-config search path.
Perhaps you should add the directory containing `polkit-gobject-1.pc'
to the PKG_CONFIG_PATH environment variable
Package 'polkit-gobject-1', required by 'virtual:world', not found
configure:16509: $? = 1
configure:16523: $PKG_CONFIG --exists --print-errors "polkit-gobject-1 >= 0.97"
Package polkit-gobject-1 was not found in the pkg-config search path.
Perhaps you should add the directory containing `polkit-gobject-1.pc'
to the PKG_CONFIG_PATH environment variable
Package 'polkit-gobject-1', required by 'virtual:world', not found
configure:16526: $? = 1
configure:16540: result: no
Package 'polkit-gobject-1', required by 'virtual:world', not found
configure:17901: checking that generated files are newer than configure
configure:17907: result: done
configure:18182: creating ./config.status
configure:20479: WARNING: unrecognized options: --disable-doc, --disable-docs, --disable-documentation, --with-xmlto, --with-fop, --enable-ipv6, --disable-more-warnings

## ---------------- ##
## Cache variables. ##
## ---------------- ##

ac_cv_build=x86_64-pc-linux-gnu
ac_cv_c_compiler_gnu=yes
ac_cv_env_CC_set=set
ac_cv_env_CC_value=/root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc
ac_cv_env_CFLAGS_set=set
ac_cv_env_CFLAGS_value='-D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os  '
ac_cv_env_CPPFLAGS_set=set
ac_cv_env_CPPFLAGS_value='-D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64'
ac_cv_env_CPP_set=set
ac_cv_env_CPP_value=/root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-cpp
ac_cv_env_GTKDOC_DEPS_CFLAGS_set=
ac_cv_env_GTKDOC_DEPS_CFLAGS_value=
ac_cv_env_GTKDOC_DEPS_LIBS_set=
ac_cv_env_GTKDOC_DEPS_LIBS_value=
ac_cv_env_GUDEV_CFLAGS_set=
ac_cv_env_GUDEV_CFLAGS_value=
ac_cv_env_GUDEV_LIBS_set=
ac_cv_env_GUDEV_LIBS_value=
ac_cv_env_LDFLAGS_set=set
ac_cv_env_LDFLAGS_value=
ac_cv_env_LIBMM_GLIB_CFLAGS_set=
ac_cv_env_LIBMM_GLIB_CFLAGS_value=
ac_cv_env_LIBMM_GLIB_LIBS_set=
ac_cv_env_LIBMM_GLIB_LIBS_value=
ac_cv_env_LIBSYSTEMD_CFLAGS_set=
ac_cv_env_LIBSYSTEMD_CFLAGS_value=
ac_cv_env_LIBSYSTEMD_LIBS_set=
ac_cv_env_LIBSYSTEMD_LIBS_value=
ac_cv_env_LIBSYSTEMD_LOGIN_CFLAGS_set=
ac_cv_env_LIBSYSTEMD_LOGIN_CFLAGS_value=
ac_cv_env_LIBSYSTEMD_LOGIN_LIBS_set=
ac_cv_env_LIBSYSTEMD_LOGIN_LIBS_value=
ac_cv_env_LIBS_set=
ac_cv_env_LIBS_value=
ac_cv_env_LT_SYS_LIBRARY_PATH_set=
ac_cv_env_LT_SYS_LIBRARY_PATH_value=
ac_cv_env_MBIM_CFLAGS_set=
ac_cv_env_MBIM_CFLAGS_value=
ac_cv_env_MBIM_LIBS_set=
ac_cv_env_MBIM_LIBS_value=
ac_cv_env_MMCLI_CFLAGS_set=
ac_cv_env_MMCLI_CFLAGS_value=
ac_cv_env_MMCLI_LIBS_set=
ac_cv_env_MMCLI_LIBS_value=
ac_cv_env_MM_CFLAGS_set=
ac_cv_env_MM_CFLAGS_value=
ac_cv_env_MM_LIBS_set=
ac_cv_env_MM_LIBS_value=
ac_cv_env_PKG_CONFIG_LIBDIR_set=
ac_cv_env_PKG_CONFIG_LIBDIR_value=
ac_cv_env_PKG_CONFIG_PATH_set=
ac_cv_env_PKG_CONFIG_PATH_value=
ac_cv_env_PKG_CONFIG_set=set
ac_cv_env_PKG_CONFIG_value=/root/buildroot-2021.02/dev_out/host/bin/pkg-config
ac_cv_env_POLKIT_CFLAGS_set=
ac_cv_env_POLKIT_CFLAGS_value=
ac_cv_env_POLKIT_LIBS_set=
ac_cv_env_POLKIT_LIBS_value=
ac_cv_env_QMI_CFLAGS_set=
ac_cv_env_QMI_CFLAGS_value=
ac_cv_env_QMI_LIBS_set=
ac_cv_env_QMI_LIBS_value=
ac_cv_env_build_alias_set=set
ac_cv_env_build_alias_value=x86_64-pc-linux-gnu
ac_cv_env_host_alias_set=set
ac_cv_env_host_alias_value=arm-buildroot-linux-gnueabihf
ac_cv_env_target_alias_set=set
ac_cv_env_target_alias_value=arm-buildroot-linux-gnueabihf
ac_cv_header_dlfcn_h=yes
ac_cv_header_inttypes_h=yes
ac_cv_header_memory_h=yes
ac_cv_header_minix_config_h=no
ac_cv_header_stdc=yes
ac_cv_header_stdint_h=yes
ac_cv_header_stdlib_h=yes
ac_cv_header_string_h=yes
ac_cv_header_strings_h=yes
ac_cv_header_sys_stat_h=yes
ac_cv_header_sys_types_h=yes
ac_cv_header_unistd_h=yes
ac_cv_host=arm-buildroot-linux-gnueabihf
ac_cv_objext=o
ac_cv_path_EGREP='/usr/bin/grep -E'
ac_cv_path_FGREP='/usr/bin/grep -F'
ac_cv_path_GMSGFMT=/root/buildroot-2021.02/dev_out/host/bin/msgfmt
ac_cv_path_GREP=/usr/bin/grep
ac_cv_path_MSGFMT=/root/buildroot-2021.02/dev_out/host/bin/msgfmt
ac_cv_path_MSGMERGE=/root/buildroot-2021.02/dev_out/host/bin/msgmerge
ac_cv_path_SED=/usr/bin/sed
ac_cv_path_XGETTEXT=/root/buildroot-2021.02/dev_out/host/bin/xgettext
ac_cv_path_install='/usr/bin/install -c'
ac_cv_path_lt_DD=/usr/bin/dd
ac_cv_path_mkdir=/usr/bin/mkdir
ac_cv_prog_AWK=gawk
ac_cv_prog_CC=/root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc
ac_cv_prog_CPP=/root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-cpp
ac_cv_prog_STRIP=strip
ac_cv_prog_XSLTPROC_CHECK=yes
ac_cv_prog_ac_ct_AR=ar
ac_cv_prog_ac_ct_DUMPBIN='link -dump'
ac_cv_prog_ac_ct_MANIFEST_TOOL=mt
ac_cv_prog_ac_ct_OBJDUMP=objdump
ac_cv_prog_ac_ct_RANLIB=ranlib
ac_cv_prog_ac_ct_STRIP=strip
ac_cv_prog_cc_c89=
ac_cv_prog_cc_g=yes
ac_cv_prog_make_make_set=yes
ac_cv_safe_to_define___extensions__=yes
acl_cv_hardcode_direct=no
acl_cv_hardcode_libdir_flag_spec='${wl}-rpath ${wl}$libdir'
acl_cv_hardcode_libdir_separator=
acl_cv_hardcode_minus_L=no
acl_cv_libext=a
acl_cv_libname_spec='lib$name'
acl_cv_library_names_spec='$libname$shrext'
acl_cv_path_LD=/opt/gcc-arm/arm-none-linux-gnueabihf/bin/ld
acl_cv_prog_gnu_ld=yes
acl_cv_rpath=done
acl_cv_shlibext=so
acl_cv_wl=-Wl,
am_cv_CC_dependencies_compiler_type=none
am_cv_make_support_nested_variables=yes
am_cv_prog_cc_c_o=yes
am_cv_prog_tar_ustar=gnutar
ax_cv_check_cflags__Wall=yes
ax_cv_check_cflags__Warray_bounds=yes
ax_cv_check_cflags__Wcast_align=yes
ax_cv_check_cflags__Wdeclaration_after_statement=yes
ax_cv_check_cflags__Wdouble_promotion=yes
ax_cv_check_cflags__Wduplicated_branches=yes
ax_cv_check_cflags__Wduplicated_cond=yes
ax_cv_check_cflags__Wextra=yes
ax_cv_check_cflags__Wformat_2=yes
ax_cv_check_cflags__Wformat_nonliteral=yes
ax_cv_check_cflags__Wformat_security=yes
ax_cv_check_cflags__Wimplicit_function_declaration=yes
ax_cv_check_cflags__Winit_self=yes
ax_cv_check_cflags__Winline=yes
ax_cv_check_cflags__Wjump_misses_init=yes
ax_cv_check_cflags__Wlogical_op=yes
ax_cv_check_cflags__Wmissing_declarations=yes
ax_cv_check_cflags__Wmissing_format_attribute=yes
ax_cv_check_cflags__Wmissing_include_dirs=yes
ax_cv_check_cflags__Wmissing_noreturn=yes
ax_cv_check_cflags__Wmissing_prototypes=yes
ax_cv_check_cflags__Wnested_externs=yes
ax_cv_check_cflags__Wno_cast_function_type=yes
ax_cv_check_cflags__Wno_error_cast_function_type=yes
ax_cv_check_cflags__Wno_error_missing_field_initializers=yes
ax_cv_check_cflags__Wno_error_packed=yes
ax_cv_check_cflags__Wno_error_unused_parameter=yes
ax_cv_check_cflags__Wno_missing_field_initializers=yes
ax_cv_check_cflags__Wno_packed=yes
ax_cv_check_cflags__Wno_unused_parameter=yes
ax_cv_check_cflags__Wnull_dereference=yes
ax_cv_check_cflags__Wold_style_definition=yes
ax_cv_check_cflags__Wpacked=yes
ax_cv_check_cflags__Wpointer_arith=yes
ax_cv_check_cflags__Wredundant_decls=yes
ax_cv_check_cflags__Wrestrict=yes
ax_cv_check_cflags__Wreturn_type=yes
ax_cv_check_cflags__Wshadow=yes
ax_cv_check_cflags__Wsign_compare=yes
ax_cv_check_cflags__Wstrict_aliasing=yes
ax_cv_check_cflags__Wstrict_prototypes=yes
ax_cv_check_cflags__Wswitch_default=yes
ax_cv_check_cflags__Wswitch_enum=yes
ax_cv_check_cflags__Wundef=yes
ax_cv_check_cflags__Wunused_but_set_variable=yes
ax_cv_check_cflags__Wwrite_strings=yes
ax_cv_check_cflags___Werror_unknown_warning_option=no
ax_cv_check_cflags___Wno_suggest_attribute_format=yes
ax_cv_check_cflags__fno_strict_aliasing=yes
ax_cv_check_ldflags__Wl___as_needed=yes
ax_cv_check_ldflags__Wl___no_as_needed=yes
ax_cv_check_ldflags__Wl__z_noexecstack=yes
ax_cv_check_ldflags__Wl__z_now=yes
ax_cv_check_ldflags__Wl__z_relro=yes
ax_cv_check_ldflags___Wl___as_needed=yes
ax_cv_check_ldflags___Wl___fatal_warnings=yes
ax_cv_check_ldflags___Wl___no_as_needed=yes
ax_cv_check_ldflags___Wl__fatal_warnings=no
ax_cv_check_ldflags___Wl__z_noexecstack=yes
ax_cv_check_ldflags___Wl__z_now=yes
ax_cv_check_ldflags___Wl__z_relro=yes
gt_cv_func_CFLocaleCopyCurrent=no
gt_cv_func_CFPreferencesCopyAppValue=no
gt_cv_func_gnugettext1_libc=yes
lt_cv_ar_at_file=@
lt_cv_archive_cmds_need_lc=no
lt_cv_deplibs_check_method=pass_all
lt_cv_file_magic_cmd='$MAGIC_CMD'
lt_cv_file_magic_test_file=
lt_cv_ld_reload_flag=-r
lt_cv_nm_interface='BSD nm'
lt_cv_objdir=.libs
lt_cv_path_LD=/opt/gcc-arm/arm-none-linux-gnueabihf/bin/ld
lt_cv_path_NM=no
lt_cv_path_mainfest_tool=no
lt_cv_prog_compiler_c_o=yes
lt_cv_prog_compiler_pic='-fPIC -DPIC'
lt_cv_prog_compiler_pic_works=yes
lt_cv_prog_compiler_rtti_exceptions=no
lt_cv_prog_compiler_static_works=yes
lt_cv_prog_gnu_ld=yes
lt_cv_sharedlib_from_linklib_cmd='printf %s\n'
lt_cv_shlibpath_overrides_runpath=no
lt_cv_sys_global_symbol_pipe='sed -n -e '\''s/^.*[	 ]\([ABCDGIRSTW][ABCDGIRSTW]*\)[	 ][	 ]*\([_A-Za-z][_A-Za-z0-9]*\)$/\1 \2 \2/p'\'' | sed '\''/ __gnu_lto/d'\'''
lt_cv_sys_global_symbol_to_c_name_address='sed -n -e '\''s/^: \(.*\) .*$/  {"\1", (void *) 0},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/  {"\1", (void *) \&\1},/p'\'''
lt_cv_sys_global_symbol_to_c_name_address_lib_prefix='sed -n -e '\''s/^: \(.*\) .*$/  {"\1", (void *) 0},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(lib.*\)$/  {"\1", (void *) \&\1},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/  {"lib\1", (void *) \&\1},/p'\'''
lt_cv_sys_global_symbol_to_cdecl='sed -n -e '\''s/^T .* \(.*\)$/extern int \1();/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/extern char \1;/p'\'''
lt_cv_sys_global_symbol_to_import=
lt_cv_sys_max_cmd_len=1572864
lt_cv_to_host_file_cmd=func_convert_file_noop
lt_cv_to_tool_file_cmd=func_convert_file_noop
lt_cv_truncate_bin='/usr/bin/dd bs=4096 count=1'
pkg_cv_GTKDOC_DEPS_CFLAGS='-I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include '
pkg_cv_GTKDOC_DEPS_LIBS='-L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgobject-2.0 -lglib-2.0 '
pkg_cv_GUDEV_CFLAGS='-I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gudev-1.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include '
pkg_cv_GUDEV_LIBS='-L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgudev-1.0 -lgobject-2.0 -lglib-2.0 '
pkg_cv_LIBMM_GLIB_CFLAGS='-I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gio-unix-2.0 -pthread '
pkg_cv_LIBMM_GLIB_LIBS='-L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgio-2.0 -lgobject-2.0 -lglib-2.0 '
pkg_cv_MMCLI_CFLAGS='-I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -pthread '
pkg_cv_MMCLI_LIBS='-L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgio-2.0 -lgobject-2.0 -lglib-2.0 '
pkg_cv_MM_CFLAGS='-I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gio-unix-2.0 -pthread '
pkg_cv_MM_LIBS='-L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -Wl,--export-dynamic -lgmodule-2.0 -pthread -lglib-2.0 -lgio-2.0 -lgobject-2.0 -lglib-2.0 '

## ----------------- ##
## Output variables. ##
## ----------------- ##










	$(LCOV) $(code_coverage_quiet) --directory $(CODE_COVERAGE_DIRECTORY) --capture --output-file "$(CODE_COVERAGE_OUTPUT_FILE).tmp" --test-name "$(PACKAGE_NAME)-$(PACKAGE_VERSION)" --no-checksum --compat-libtool $(CODE_COVERAGE_LCOV_OPTIONS)
	$(LCOV) $(code_coverage_quiet) --directory $(CODE_COVERAGE_DIRECTORY) --remove "$(CODE_COVERAGE_OUTPUT_FILE).tmp" "/tmp/*" $(CODE_COVERAGE_IGNORE_PATTERN) --output-file "$(CODE_COVERAGE_OUTPUT_FILE)"
	$(MAKE) $(AM_MAKEFLAGS) code-coverage-capture
	-$(LCOV) --directory $(top_builddir) -z
	-$(MAKE) $(AM_MAKEFLAGS) -k check
	-@rm -f $(CODE_COVERAGE_OUTPUT_FILE).tmp
	-find . -name "*.gcda" -o -name "*.gcov" -delete
	-rm -rf $(CODE_COVERAGE_OUTPUT_FILE) $(CODE_COVERAGE_OUTPUT_FILE).tmp $(CODE_COVERAGE_OUTPUT_DIRECTORY)
	@echo "Need to reconfigure with --enable-code-coverage"
	@echo "Need to reconfigure with --enable-code-coverage"
	@echo "file://$(abs_builddir)/$(CODE_COVERAGE_OUTPUT_DIRECTORY)/index.html"
	LANG=C $(GENHTML) $(code_coverage_quiet) --prefix $(CODE_COVERAGE_DIRECTORY) --output-directory "$(CODE_COVERAGE_OUTPUT_DIRECTORY)" --title "$(PACKAGE_NAME)-$(PACKAGE_VERSION) Code Coverage" --legend --show-details "$(CODE_COVERAGE_OUTPUT_FILE)" $(CODE_COVERAGE_GENHTML_OPTIONS)
#
#
#    $(PACKAGE_NAME)-$(PACKAGE_VERSION)-coverage)
#    $(PACKAGE_NAME)-$(PACKAGE_VERSION)-coverage.info)
#    (Default: $(top_builddir))
#    (Default: $CODE_COVERAGE_LCOV_OPTIONS_DEFAULT)
#    (Default: $CODE_COVERAGE_LCOV_OPTIONS_GCOVPATH)
#    by lcov for code coverage. (Default:
#    instance. (Default: empty)
#    reports to be created. (Default:
#  - CODE_COVERAGE_DIRECTORY: Top-level directory for code coverage reporting.
#  - CODE_COVERAGE_GENHTML_OPTIONS: Extra options to pass to the genhtml
#  - CODE_COVERAGE_IGNORE_PATTERN: Extra glob pattern of files to ignore
#  - CODE_COVERAGE_LCOV_OPTIONS: Extra options to pass to the lcov instance.
#  - CODE_COVERAGE_LCOV_OPTIONS_DEFAULT: Extra options to pass to the lcov instance.
#  - CODE_COVERAGE_LCOV_OPTIONS_GCOVPATH: --gcov-tool pathtogcov
#  - CODE_COVERAGE_OUTPUT_DIRECTORY: Directory for generated code coverage
#  - CODE_COVERAGE_OUTPUT_FILE: Filename and path for the .info file generated
# $(PACKAGE_VERSION). In order to add the current git hash to the title,
# Capture code coverage data
# Code coverage
# Hook rule executed before code-coverage-capture, overridable by the user
# Optional variables
# Optional:
# The generated report will be titled using the $(PACKAGE_NAME) and
# Use recursive makes in order to ignore errors during check
# use the git-version-gen script, available online.
'
.PHONY: check-code-coverage code-coverage-capture code-coverage-capture-hook code-coverage-clean
ACLOCAL='${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing aclocal-1.16'
AMDEPBACKSLASH=''
AMDEP_FALSE=''
AMDEP_TRUE='#'
AMTAR='$${TAR-tar}'
AM_BACKSLASH='\'
AM_DEFAULT_V='$(AM_DEFAULT_VERBOSITY)'
AM_DEFAULT_VERBOSITY='0'
AM_V='$(V)'
AR='ar'
AUTOCONF='${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing autoconf'
AUTOHEADER='${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing autoheader'
AUTOMAKE='${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing automake-1.16'
AWK='gawk'
CC='/root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc'
CCDEPMODE='depmode=none'
CFLAGS='-D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -std=gnu89'
CODE_COVERAGE_CFLAGS=''
CODE_COVERAGE_DIRECTORY ?= $(top_builddir)
CODE_COVERAGE_ENABLED='no'
CODE_COVERAGE_ENABLED_FALSE=''
CODE_COVERAGE_ENABLED_TRUE='#'
CODE_COVERAGE_GENHTML_OPTIONS ?=
CODE_COVERAGE_IGNORE_PATTERN ?=
CODE_COVERAGE_LCOV_OPTIONS ?= $(CODE_COVERAGE_LCOV_OPTIONS_DEFAULT)
CODE_COVERAGE_LCOV_OPTIONS_DEFAULT ?= $(CODE_COVERAGE_LCOV_OPTIONS_GCOVPATH)
CODE_COVERAGE_LCOV_OPTIONS_GCOVPATH ?= --gcov-tool "$(GCOV)"
CODE_COVERAGE_LDFLAGS=''
CODE_COVERAGE_OUTPUT_DIRECTORY ?= $(PACKAGE_NAME)-$(PACKAGE_VERSION)-coverage
CODE_COVERAGE_OUTPUT_FILE ?= $(PACKAGE_NAME)-$(PACKAGE_VERSION)-coverage.info
CODE_COVERAGE_RULES='
CPP='/root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-cpp'
CPPFLAGS='-D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64'
CYGPATH_W='echo'
DBUS_SYS_DIR='/etc/dbus-1/system.d'
DEFS='-DHAVE_CONFIG_H'
DEPDIR='.deps'
DISTCHECK_CONFIGURE_FLAGS += --disable-code-coverage
DISTCHECK_CONFIGURE_FLAGS ?=
DLLTOOL='false'
DSYMUTIL=''
DUMPBIN=':'
ECHO_C=''
ECHO_N='-n'
ECHO_T=''
EGREP='/usr/bin/grep -E'
ENABLE_GTK_DOC_FALSE=''
ENABLE_GTK_DOC_TRUE='#'
ENABLE_PLUGIN_ALTAIR_LTE_FALSE='#'
ENABLE_PLUGIN_ALTAIR_LTE_TRUE=''
ENABLE_PLUGIN_ANYDATA_FALSE='#'
ENABLE_PLUGIN_ANYDATA_TRUE=''
ENABLE_PLUGIN_BROADMOBI_FALSE='#'
ENABLE_PLUGIN_BROADMOBI_TRUE=''
ENABLE_PLUGIN_CINTERION_FALSE='#'
ENABLE_PLUGIN_CINTERION_TRUE=''
ENABLE_PLUGIN_DELL_FALSE='#'
ENABLE_PLUGIN_DELL_TRUE=''
ENABLE_PLUGIN_DLINK_FALSE='#'
ENABLE_PLUGIN_DLINK_TRUE=''
ENABLE_PLUGIN_FOXCONN_FALSE='#'
ENABLE_PLUGIN_FOXCONN_TRUE=''
ENABLE_PLUGIN_GENERIC_FALSE='#'
ENABLE_PLUGIN_GENERIC_TRUE=''
ENABLE_PLUGIN_HAIER_FALSE='#'
ENABLE_PLUGIN_HAIER_TRUE=''
ENABLE_PLUGIN_HUAWEI_FALSE='#'
ENABLE_PLUGIN_HUAWEI_TRUE=''
ENABLE_PLUGIN_IRIDIUM_FALSE='#'
ENABLE_PLUGIN_IRIDIUM_TRUE=''
ENABLE_PLUGIN_LINKTOP_FALSE='#'
ENABLE_PLUGIN_LINKTOP_TRUE=''
ENABLE_PLUGIN_LONGCHEER_FALSE='#'
ENABLE_PLUGIN_LONGCHEER_TRUE=''
ENABLE_PLUGIN_MBM_FALSE='#'
ENABLE_PLUGIN_MBM_TRUE=''
ENABLE_PLUGIN_ME3630_FALSE='#'
ENABLE_PLUGIN_ME3630_TRUE=''
ENABLE_PLUGIN_MOTOROLA_FALSE='#'
ENABLE_PLUGIN_MOTOROLA_TRUE=''
ENABLE_PLUGIN_MTK_FALSE='#'
ENABLE_PLUGIN_MTK_TRUE=''
ENABLE_PLUGIN_NOKIA_FALSE='#'
ENABLE_PLUGIN_NOKIA_ICERA_FALSE='#'
ENABLE_PLUGIN_NOKIA_ICERA_TRUE=''
ENABLE_PLUGIN_NOKIA_TRUE=''
ENABLE_PLUGIN_NOVATEL_FALSE='#'
ENABLE_PLUGIN_NOVATEL_LTE_FALSE='#'
ENABLE_PLUGIN_NOVATEL_LTE_TRUE=''
ENABLE_PLUGIN_NOVATEL_TRUE=''
ENABLE_PLUGIN_OPTION_FALSE='#'
ENABLE_PLUGIN_OPTION_HSO_FALSE='#'
ENABLE_PLUGIN_OPTION_HSO_TRUE=''
ENABLE_PLUGIN_OPTION_TRUE=''
ENABLE_PLUGIN_PANTECH_FALSE='#'
ENABLE_PLUGIN_PANTECH_TRUE=''
ENABLE_PLUGIN_QUECTEL_FALSE='#'
ENABLE_PLUGIN_QUECTEL_TRUE=''
ENABLE_PLUGIN_SAMSUNG_FALSE='#'
ENABLE_PLUGIN_SAMSUNG_TRUE=''
ENABLE_PLUGIN_SIERRA_FALSE='#'
ENABLE_PLUGIN_SIERRA_LEGACY_FALSE='#'
ENABLE_PLUGIN_SIERRA_LEGACY_TRUE=''
ENABLE_PLUGIN_SIERRA_TRUE=''
ENABLE_PLUGIN_SIMTECH_FALSE='#'
ENABLE_PLUGIN_SIMTECH_TRUE=''
ENABLE_PLUGIN_TELIT_FALSE='#'
ENABLE_PLUGIN_TELIT_TRUE=''
ENABLE_PLUGIN_THURAYA_FALSE='#'
ENABLE_PLUGIN_THURAYA_TRUE=''
ENABLE_PLUGIN_TPLINK_FALSE='#'
ENABLE_PLUGIN_TPLINK_TRUE=''
ENABLE_PLUGIN_UBLOX_FALSE='#'
ENABLE_PLUGIN_UBLOX_TRUE=''
ENABLE_PLUGIN_VIA_FALSE='#'
ENABLE_PLUGIN_VIA_TRUE=''
ENABLE_PLUGIN_WAVECOM_FALSE='#'
ENABLE_PLUGIN_WAVECOM_TRUE=''
ENABLE_PLUGIN_X22X_FALSE='#'
ENABLE_PLUGIN_X22X_TRUE=''
ENABLE_PLUGIN_ZTE_FALSE='#'
ENABLE_PLUGIN_ZTE_TRUE=''
ENABLE_VAPIGEN_FALSE=''
ENABLE_VAPIGEN_TRUE='#'
EXEEXT=''
FGREP='/usr/bin/grep -F'
GCOV=''
GDBUS_CODEGEN='gdbus-codegen'
GENHTML=''
GETTEXT_MACRO_VERSION='0.19'
GETTEXT_PACKAGE='ModemManager'
GITIGNOREFILES += $(CODE_COVERAGE_OUTPUT_FILE) $(CODE_COVERAGE_OUTPUT_DIRECTORY)
GITIGNOREFILES ?=
GLIB_MKENUMS='glib-mkenums'
GMSGFMT='/root/buildroot-2021.02/dev_out/host/bin/msgfmt'
GMSGFMT_015='/root/buildroot-2021.02/dev_out/host/bin/msgfmt'
GREP='/usr/bin/grep'
GTKDOC_CHECK=''
GTKDOC_CHECK_PATH=''
GTKDOC_DEPS_CFLAGS='-I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include '
GTKDOC_DEPS_LIBS='-L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgobject-2.0 -lglib-2.0 '
GTKDOC_MKPDF=''
GTKDOC_REBASE='true'
GTK_DOC_BUILD_HTML_FALSE=''
GTK_DOC_BUILD_HTML_TRUE='#'
GTK_DOC_BUILD_PDF_FALSE=''
GTK_DOC_BUILD_PDF_TRUE='#'
GTK_DOC_USE_LIBTOOL_FALSE='#'
GTK_DOC_USE_LIBTOOL_TRUE=''
GTK_DOC_USE_REBASE_FALSE='#'
GTK_DOC_USE_REBASE_TRUE=''
GUDEV_CFLAGS='-I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gudev-1.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include '
GUDEV_LIBS='-L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgudev-1.0 -lgobject-2.0 -lglib-2.0 '
HAVE_GTK_DOC_FALSE=''
HAVE_GTK_DOC_TRUE='#'
HAVE_INTROSPECTION_FALSE=''
HAVE_INTROSPECTION_TRUE='#'
HAVE_SYSTEMD_FALSE=''
HAVE_SYSTEMD_TRUE='#'
HTML_DIR='${datadir}/gtk-doc/html'
INSTALL_DATA='${INSTALL} -m 644'
INSTALL_PROGRAM='${INSTALL}'
INSTALL_SCRIPT='${INSTALL}'
INSTALL_STRIP_PROGRAM='$(install_sh) -c -s'
INTLLIBS=''
INTL_MACOSX_LIBS=''
INTROSPECTION_CFLAGS=''
INTROSPECTION_COMPILER=''
INTROSPECTION_GENERATE=''
INTROSPECTION_GIRDIR=''
INTROSPECTION_LIBS=''
INTROSPECTION_MAKEFILE=''
INTROSPECTION_SCANNER=''
INTROSPECTION_TYPELIBDIR=''
LCOV=''
LD='/opt/gcc-arm/arm-none-linux-gnueabihf/bin/ld'
LDFLAGS=''
LIBICONV='-liconv'
LIBINTL=''
LIBMM_GLIB_CFLAGS='-I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gio-unix-2.0 -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS'
LIBMM_GLIB_LIBS='-L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgio-2.0 -lgobject-2.0 -lglib-2.0 '
LIBOBJS=''
LIBS=''
LIBSYSTEMD_CFLAGS=''
LIBSYSTEMD_LIBS=''
LIBSYSTEMD_LOGIN_CFLAGS=''
LIBSYSTEMD_LOGIN_LIBS=''
LIBTOOL='$(SHELL) $(top_builddir)/libtool'
LIPO=''
LN_S='ln -s'
LTLIBICONV='-liconv'
LTLIBINTL=''
LTLIBOBJS=''
LT_SYS_LIBRARY_PATH=''
MAINT=''
MAINTAINER_MODE_FALSE='#'
MAINTAINER_MODE_TRUE=''
MAKEINFO='${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing makeinfo'
MANIFEST_TOOL=':'
MBIM_CFLAGS=''
MBIM_LIBS=''
MKDIR_P='/usr/bin/mkdir -p'
MMCLI_CFLAGS='-I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS'
MMCLI_LIBS='-L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgio-2.0 -lgobject-2.0 -lglib-2.0 '
MM_CFLAGS='-I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gio-unix-2.0 -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS'
MM_DEFAULT_USER_POLICY=''
MM_GLIB_LT_AGE='6'
MM_GLIB_LT_CURRENT='6'
MM_GLIB_LT_REVISION='0'
MM_LIBS='-L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -Wl,--export-dynamic -lgmodule-2.0 -pthread -lglib-2.0 -lgio-2.0 -lgobject-2.0 -lglib-2.0 '
MM_MAJOR_VERSION='1'
MM_MICRO_VERSION='8'
MM_MINOR_VERSION='14'
MM_POLKIT_SERVICE=''
MM_VERSION='1.14.8'
MSGFMT='/root/buildroot-2021.02/dev_out/host/bin/msgfmt'
MSGFMT_015='/root/buildroot-2021.02/dev_out/host/bin/msgfmt'
MSGMERGE='/root/buildroot-2021.02/dev_out/host/bin/msgmerge'
NM='nm'
NMEDIT=''
OBJDUMP='objdump'
OBJEXT='o'
OTOOL64=''
OTOOL=''
PACKAGE='ModemManager'
PACKAGE_BUGREPORT='https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues'
PACKAGE_NAME='ModemManager'
PACKAGE_STRING='ModemManager 1.14.8'
PACKAGE_TARNAME='ModemManager'
PACKAGE_URL=''
PACKAGE_VERSION='1.14.8'
PATH_SEPARATOR=':'
PKG_CONFIG='/root/buildroot-2021.02/dev_out/host/bin/pkg-config'
PKG_CONFIG_LIBDIR=''
PKG_CONFIG_PATH=''
POLKIT_CFLAGS=''
POLKIT_LIBS=''
POSUB='po'
QCDM_STANDALONE_FALSE=''
QCDM_STANDALONE_TRUE='#'
QMI_CFLAGS=''
QMI_LIBS=''
RANLIB='ranlib'
SED='/usr/bin/sed'
SET_MAKE=''
SHELL='/bin/bash'
STRIP='strip'
SYSTEMD_UNIT_DIR=''
UDEV_BASE_DIR='/lib/udev'
USE_NLS='yes'
VAPIGEN=''
VAPIGEN_MAKEFILE=''
VAPIGEN_VAPIDIR=''
VERSION='1.14.8'
WARN_CFLAGS='-fno-strict-aliasing -Wnested-externs -Wmissing-prototypes -Wstrict-prototypes -Wdeclaration-after-statement -Wimplicit-function-declaration -Wold-style-definition -Wjump-misses-init -Wall -Wextra -Wundef -Wwrite-strings -Wpointer-arith -Wmissing-declarations -Wredundant-decls -Wno-unused-parameter -Wno-missing-field-initializers -Wformat=2 -Wcast-align -Wformat-nonliteral -Wformat-security -Wsign-compare -Wstrict-aliasing -Wshadow -Winline -Wpacked -Wmissing-format-attribute -Wmissing-noreturn -Winit-self -Wmissing-include-dirs -Wunused-but-set-variable -Warray-bounds -Wreturn-type -Wswitch-enum -Wswitch-default -Wduplicated-cond -Wduplicated-branches -Wlogical-op -Wrestrict -Wnull-dereference -Wdouble-promotion -Wno-error=unused-parameter -Wno-error=missing-field-initializers -Wno-cast-function-type -Wno-packed -Wno-error=cast-function-type -Wno-error=packed'
WARN_LDFLAGS='-Wl,--no-as-needed'
WARN_SCANNERFLAGS='              --warn-all                                                             '
WITH_MBIM_FALSE=''
WITH_MBIM_TRUE='#'
WITH_POLKIT_FALSE=''
WITH_POLKIT_TRUE='#'
WITH_QMI_FALSE=''
WITH_QMI_TRUE='#'
WITH_SHARED_FOXCONN_FALSE='#'
WITH_SHARED_FOXCONN_TRUE=''
WITH_SHARED_ICERA_FALSE='#'
WITH_SHARED_ICERA_TRUE=''
WITH_SHARED_NOVATEL_FALSE='#'
WITH_SHARED_NOVATEL_TRUE=''
WITH_SHARED_OPTION_FALSE='#'
WITH_SHARED_OPTION_TRUE=''
WITH_SHARED_SIERRA_FALSE='#'
WITH_SHARED_SIERRA_TRUE=''
WITH_SHARED_TELIT_FALSE='#'
WITH_SHARED_TELIT_TRUE=''
WITH_SHARED_XMM_FALSE='#'
WITH_SHARED_XMM_TRUE=''
WITH_SYSTEMD_JOURNAL_FALSE=''
WITH_SYSTEMD_JOURNAL_TRUE='#'
WITH_SYSTEMD_SUSPEND_RESUME_FALSE=''
WITH_SYSTEMD_SUSPEND_RESUME_TRUE='#'
WITH_UDEV_FALSE='#'
WITH_UDEV_TRUE=''
XGETTEXT='/root/buildroot-2021.02/dev_out/host/bin/xgettext'
XGETTEXT_015='/root/buildroot-2021.02/dev_out/host/bin/xgettext'
XGETTEXT_EXTRA_OPTIONS=''
XSLTPROC_CHECK='yes'
ac_ct_AR='ar'
ac_ct_CC=''
ac_ct_DUMPBIN='link -dump'
am__EXEEXT_FALSE=''
am__EXEEXT_TRUE='#'
am__fastdepCC_FALSE=''
am__fastdepCC_TRUE='#'
am__include='include'
am__isrc=''
am__leading_dot='.'
am__nodep=''
am__quote=''
am__tar='tar --format=ustar -chf - "$$tardir"'
am__untar='tar -xf -'
bindir='${exec_prefix}/bin'
build='x86_64-pc-linux-gnu'
build_alias='x86_64-pc-linux-gnu'
build_cpu='x86_64'
build_os='linux-gnu'
build_vendor='pc'
check-code-coverage:
clean: code-coverage-clean
code-coverage-capture-hook:
code-coverage-capture: code-coverage-capture-hook
code-coverage-clean:
code_coverage_quiet = $(code_coverage_quiet_$(V))
code_coverage_quiet_ = $(code_coverage_quiet_$(AM_DEFAULT_VERBOSITY))
code_coverage_quiet_0 = --quiet
datadir='${datarootdir}'
datarootdir='${prefix}/share'
docdir='${datarootdir}/doc/${PACKAGE_TARNAME}'
dvidir='${docdir}'
else
else
endif
endif
endif
exec_prefix='/usr'
host='arm-buildroot-linux-gnueabihf'
host_alias='arm-buildroot-linux-gnueabihf'
host_cpu='arm'
host_os='linux-gnueabihf'
host_vendor='buildroot'
htmldir='${docdir}'
ifeq ($(CODE_COVERAGE_ENABLED),yes)
ifeq ($(CODE_COVERAGE_ENABLED),yes)
ifeq ($(CODE_COVERAGE_ENABLED),yes)
includedir='${prefix}/include'
infodir='${datarootdir}/info'
install_sh='${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/install-sh'
libdir='${exec_prefix}/lib'
libexecdir='${exec_prefix}/libexec'
localedir='${datarootdir}/locale'
localstatedir='/var'
mandir='${datarootdir}/man'
mkdir_p='$(MKDIR_P)'
oldincludedir='/usr/include'
pdfdir='${docdir}'
prefix='/usr'
program_transform_name='s&^&&'
psdir='${docdir}'
runstatedir='${localstatedir}/run'
sbindir='${exec_prefix}/sbin'
sharedstatedir='${prefix}/com'
sysconfdir='/etc'
target_alias='arm-buildroot-linux-gnueabihf'

## ----------- ##
## confdefs.h. ##
## ----------- ##

/* confdefs.h */
#define PACKAGE_NAME "ModemManager"
#define PACKAGE_TARNAME "ModemManager"
#define PACKAGE_VERSION "1.14.8"
#define PACKAGE_STRING "ModemManager 1.14.8"
#define PACKAGE_BUGREPORT "https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues"
#define PACKAGE_URL ""
#define PACKAGE "ModemManager"
#define VERSION "1.14.8"
#define STDC_HEADERS 1
#define HAVE_SYS_TYPES_H 1
#define HAVE_SYS_STAT_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_MEMORY_H 1
#define HAVE_STRINGS_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_STDINT_H 1
#define HAVE_UNISTD_H 1
#define __EXTENSIONS__ 1
#define _ALL_SOURCE 1
#define _GNU_SOURCE 1
#define _POSIX_PTHREAD_SEMANTICS 1
#define _TANDEM_SOURCE 1
#define HAVE_DLFCN_H 1
#define LT_OBJDIR ".libs/"
#define ENABLE_NLS 1
#define HAVE_GETTEXT 1
#define HAVE_DCGETTEXT 1
#define GETTEXT_PACKAGE "ModemManager"
#define WITH_UDEV 1
#define ENABLE_PLUGIN_GENERIC 1
#define ENABLE_PLUGIN_ALTAIR_LTE 1
#define ENABLE_PLUGIN_ANYDATA 1
#define ENABLE_PLUGIN_BROADMOBI 1
#define ENABLE_PLUGIN_CINTERION 1
#define ENABLE_PLUGIN_DELL 1
#define ENABLE_PLUGIN_DLINK 1
#define ENABLE_PLUGIN_FOXCONN 1
#define ENABLE_PLUGIN_HAIER 1
#define ENABLE_PLUGIN_HUAWEI 1
#define ENABLE_PLUGIN_IRIDIUM 1
#define ENABLE_PLUGIN_LINKTOP 1
#define ENABLE_PLUGIN_LONGCHEER 1
#define ENABLE_PLUGIN_MBM 1
#define ENABLE_PLUGIN_MOTOROLA 1
#define ENABLE_PLUGIN_MTK 1
#define ENABLE_PLUGIN_NOKIA 1
#define ENABLE_PLUGIN_NOKIA_ICERA 1
#define ENABLE_PLUGIN_NOVATEL 1
#define ENABLE_PLUGIN_NOVATEL_LTE 1
#define ENABLE_PLUGIN_OPTION 1
#define ENABLE_PLUGIN_OPTION_HSO 1
#define ENABLE_PLUGIN_PANTECH 1
#define ENABLE_PLUGIN_QUECTEL 1
#define ENABLE_PLUGIN_SAMSUNG 1
#define ENABLE_PLUGIN_SIERRA_LEGACY 1
#define ENABLE_PLUGIN_SIERRA 1
#define ENABLE_PLUGIN_SIMTECH 1
#define ENABLE_PLUGIN_TELIT 1
#define ENABLE_PLUGIN_THURAYA 1
#define ENABLE_PLUGIN_TPLINK 1
#define ENABLE_PLUGIN_UBLOX 1
#define ENABLE_PLUGIN_VIA 1
#define ENABLE_PLUGIN_WAVECOM 1
#define ENABLE_PLUGIN_X22X 1
#define ENABLE_PLUGIN_ZTE 1
#define ENABLE_PLUGIN_ME3630 1
#define WITH_SHARED_ICERA 1
#define WITH_SHARED_SIERRA 1
#define WITH_SHARED_OPTION 1
#define WITH_SHARED_NOVATEL 1
#define WITH_SHARED_XMM 1
#define WITH_SHARED_TELIT 1
#define WITH_SHARED_FOXCONN 1

configure: exit 0

## ---------------------- ##
## Running config.status. ##
## ---------------------- ##

This file was extended by ModemManager config.status 1.14.8, which was
generated by GNU Autoconf 2.69.  Invocation command line was

  CONFIG_FILES    = 
  CONFIG_HEADERS  = 
  CONFIG_LINKS    = 
  CONFIG_COMMANDS = 
  $ ./config.status 

on a2e26749330e

config.status:1483: creating Makefile
config.status:1483: creating data/Makefile
config.status:1483: creating data/ModemManager.pc
config.status:1483: creating data/mm-glib.pc
config.status:1483: creating data/tests/Makefile
config.status:1483: creating data/tests/org.freedesktop.ModemManager1.service
config.status:1483: creating include/Makefile
config.status:1483: creating include/ModemManager-version.h
config.status:1483: creating build-aux/Makefile
config.status:1483: creating libqcdm/Makefile
config.status:1483: creating libqcdm/src/Makefile
config.status:1483: creating libqcdm/tests/Makefile
config.status:1483: creating src/Makefile
config.status:1483: creating src/tests/Makefile
config.status:1483: creating plugins/Makefile
config.status:1483: creating test/Makefile
config.status:1483: creating introspection/Makefile
config.status:1483: creating introspection/tests/Makefile
config.status:1483: creating po/Makefile.in
config.status:1483: creating docs/Makefile
config.status:1483: creating docs/man/Makefile
config.status:1483: creating docs/reference/Makefile
config.status:1483: creating docs/reference/api/Makefile
config.status:1483: creating docs/reference/api/version.xml
config.status:1483: creating docs/reference/libmm-glib/Makefile
config.status:1483: creating docs/reference/libmm-glib/version.xml
config.status:1483: creating libmm-glib/Makefile
config.status:1483: creating libmm-glib/generated/Makefile
config.status:1483: creating libmm-glib/generated/tests/Makefile
config.status:1483: creating libmm-glib/tests/Makefile
config.status:1483: creating vapi/Makefile
config.status:1483: creating cli/Makefile
config.status:1483: creating examples/Makefile
config.status:1483: creating examples/modem-watcher-python/Makefile
config.status:1483: creating examples/modem-watcher-javascript/Makefile
config.status:1483: creating examples/sms-python/Makefile
config.status:1483: creating examples/network-scan-python/Makefile
config.status:1483: creating config.h
config.status:1712: executing depfiles commands
config.status:1712: executing libtool commands
config.status:1712: executing po-directories commands

## ---------------------- ##
## Running config.status. ##
## ---------------------- ##

This file was extended by ModemManager config.status 1.14.8, which was
generated by GNU Autoconf 2.69.  Invocation command line was

  CONFIG_FILES    = 
  CONFIG_HEADERS  = 
  CONFIG_LINKS    = 
  CONFIG_COMMANDS = 
  $ ./config.status plugins/Makefile depfiles

on a2e26749330e

config.status:1483: creating plugins/Makefile
config.status:1712: executing depfiles commands


--------------------------------------------------------------------------------
 * Contacts

ModemManager should implement the Contacts interface if modems allow to query
and manipulate the contacts information stored in SIM or device.


--------------------------------------------------------------------------------
 * Probing time mitigation

Probing time may end up being quite long if we're checking support of a modem
which exposes multiple ports. It is specially bad if the modem exports a port
which is neither AT nor QCDM, as we use all our probing attempts before we can
export the modem in DBus (we do wait to get all ports probed before running the
initialization sequence, as we want to use the primary port for that always).

Therefore, looking for ways to mitigate probing time in the specific bad cases
is a good way of minimizing this problem. Some ideas:

  ** If one AT probing succeeds, don't allow timeouts in remaining ports when
     probing for AT.


--------------------------------------------------------------------------------
 * AT+CMUX & Serial multiplexing

Some modes allow to use virtual channels set up over one single serial
interface, as defined at 3G TS 27.010. This allows devices with one single port
to get a virtual secondary port for AT commands while in connected mode, for
example to update the signal quality value or check registration status.


--------------------------------------------------------------------------------
 * Additional minor enhancements, fixes and general brainstorm

  ** Per-device log function? Something like mm_modem_log() and
     mm_modem_port_log(), so that we include automatically the modem number
     (and port name) in each log line.

  ** Do we really need function name, filename and line in the debug log?

  ** In the default MMBroadbandModem, check how we can know if we're sitting in
     a rev0, revA or revB CDMA network. We need to expose the exact access
     technology in the interface.

  ** Fix object names to show proper inheritance? For example:
     - MMPort, MMPortSerial, MMPortSerialAt, MMPortSerialQcdm
     - MMModem (instead of MMBaseModem), MMModemBroadband, MMModemPots
     - MMBearer, MMBearerBroadband, MMBearerPots

  ** Test cases for CIEV responses.

  ** When a 3GPP modem is disabled, we run AT+CREG=0. That will just disable the
     automatic registration checks and unsolicited messages, the modem will
     still be registered in the network. AT+COPS=2 is the one doing manual
     unregistration from the network, and we should probably include such step
     in the 3GPP disabling sequence.

  ** serial-parsers: convert the v1 parser to a GObject.

  ** MMBroadbandBearer: include additional step for authentication, with
     retries.

  ** MMBroadbandBearer: include additional step for waiting to get connected via
     unsolicited messages.

  ** Huawei plugin: Seems to me that whenever we update the allowed modes OR the
     bands, we're actually also changing the other one. This is because we're
     using hardcoded values in ^SYSCFG write operations; we should instead read
     current mode or band when updating the other.

  ** Huawei plugin: The K4505, at least, doesn't like the default command to
     setup messaging related unsolicited messages:
     > AT+CNMI=2,1,2,1,0
     +CMS ERROR: 303

  ** ZTE plugin: The MF637, at least, doesn't like the default command to setup
     messaging related unsolicited messages:
     > AT+CNMI=2,1,2,1,0
     +CMS ERROR: 303

  ** Pantech plugin: The UMW190 needs some time to settle down after sending the
     PIN, or it will end up stuck if we ask too many PIN-related stuff one after
     the other.

  ** HSO plugin: shouldn't we have the same logic for unsolicited messages
     handling in both connect and disconnect contexts? See Icera plugin for ref.

  ** Icera plugin: retry authentication step in 3gpp dialling to 3 times with 1s
     delay.

  ** QMI: Gobi 2k devices don't like the SYNC command, which is supposed to
     release all previously allocated clients. It gets worse, as if clients are
     not cleanly released by ModemManager (e.g. a segfault), the device reaches
     a point where it doesn't allow allocating more:
       couldn't create client for the 'nas' service:
         QMI protocol error (5): 'client-ids-exhausted'
     This may force us to have something like a state file in /tmp with the IDs
     currently allocated, so that ModemManager can re-use them if needed.

  ** QMI: in NAS >= 1.8 we don't have the operator name given in the
     registration status queries, we'll need to get it with "NAS Get PLMN Name".

  ** QMI: mark the modem as invalid if we lose the QMI and/or WWAN ports. For
     example, we should handle 'sudo rmmod qmi_wwan && sudo modprobe qmi_wwan'

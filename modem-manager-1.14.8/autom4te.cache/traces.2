m4trace:aclocal.m4:1855: -1- AC_SUBST([am__quote])
m4trace:aclocal.m4:1855: -1- AC_SUBST_TRACE([am__quote])
m4trace:aclocal.m4:1855: -1- m4_pattern_allow([^am__quote$])
m4trace:aclocal.m4:2312: -1- m4_include([m4/ax_code_coverage.m4])
m4trace:aclocal.m4:2313: -1- m4_include([m4/gettext.m4])
m4trace:aclocal.m4:2314: -1- m4_include([m4/gtk-doc.m4])
m4trace:aclocal.m4:2315: -1- m4_include([m4/iconv.m4])
m4trace:aclocal.m4:2316: -1- m4_include([m4/intlmacosx.m4])
m4trace:aclocal.m4:2317: -1- m4_include([m4/introspection.m4])
m4trace:aclocal.m4:2318: -1- m4_include([m4/lib-ld.m4])
m4trace:aclocal.m4:2319: -1- m4_include([m4/lib-link.m4])
m4trace:aclocal.m4:2320: -1- m4_include([m4/lib-prefix.m4])
m4trace:aclocal.m4:2321: -1- m4_include([m4/libtool.m4])
m4trace:aclocal.m4:2322: -1- m4_include([m4/ltoptions.m4])
m4trace:aclocal.m4:2323: -1- m4_include([m4/ltsugar.m4])
m4trace:aclocal.m4:2324: -1- m4_include([m4/ltversion.m4])
m4trace:aclocal.m4:2325: -1- m4_include([m4/lt~obsolete.m4])
m4trace:aclocal.m4:2326: -1- m4_include([m4/mm-enable-plugin.m4])
m4trace:aclocal.m4:2327: -1- m4_include([m4/nls.m4])
m4trace:aclocal.m4:2328: -1- m4_include([m4/po.m4])
m4trace:aclocal.m4:2329: -1- m4_include([m4/progtest.m4])
m4trace:aclocal.m4:2330: -1- m4_include([m4/vapigen.m4])
m4trace:configure.ac:28: -1- AC_INIT([ModemManager], [mm_version], [https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues], [ModemManager])
m4trace:configure.ac:28: -1- m4_pattern_forbid([^_?A[CHUM]_])
m4trace:configure.ac:28: -1- m4_pattern_forbid([_AC_])
m4trace:configure.ac:28: -1- m4_pattern_forbid([^LIBOBJS$], [do not use LIBOBJS directly, use AC_LIBOBJ (see section `AC_LIBOBJ vs LIBOBJS'])
m4trace:configure.ac:28: -1- m4_pattern_allow([^AS_FLAGS$])
m4trace:configure.ac:28: -1- m4_pattern_forbid([^_?m4_])
m4trace:configure.ac:28: -1- m4_pattern_forbid([^dnl$])
m4trace:configure.ac:28: -1- m4_pattern_forbid([^_?AS_])
m4trace:configure.ac:28: -1- AC_SUBST([SHELL])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([SHELL])
m4trace:configure.ac:28: -1- m4_pattern_allow([^SHELL$])
m4trace:configure.ac:28: -1- AC_SUBST([PATH_SEPARATOR])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([PATH_SEPARATOR])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PATH_SEPARATOR$])
m4trace:configure.ac:28: -1- AC_SUBST([PACKAGE_NAME], [m4_ifdef([AC_PACKAGE_NAME],      ['AC_PACKAGE_NAME'])])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([PACKAGE_NAME])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.ac:28: -1- AC_SUBST([PACKAGE_TARNAME], [m4_ifdef([AC_PACKAGE_TARNAME],   ['AC_PACKAGE_TARNAME'])])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([PACKAGE_TARNAME])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.ac:28: -1- AC_SUBST([PACKAGE_VERSION], [m4_ifdef([AC_PACKAGE_VERSION],   ['AC_PACKAGE_VERSION'])])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([PACKAGE_VERSION])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.ac:28: -1- AC_SUBST([PACKAGE_STRING], [m4_ifdef([AC_PACKAGE_STRING],    ['AC_PACKAGE_STRING'])])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([PACKAGE_STRING])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.ac:28: -1- AC_SUBST([PACKAGE_BUGREPORT], [m4_ifdef([AC_PACKAGE_BUGREPORT], ['AC_PACKAGE_BUGREPORT'])])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([PACKAGE_BUGREPORT])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.ac:28: -1- AC_SUBST([PACKAGE_URL], [m4_ifdef([AC_PACKAGE_URL],       ['AC_PACKAGE_URL'])])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([PACKAGE_URL])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_URL$])
m4trace:configure.ac:28: -1- AC_SUBST([exec_prefix], [NONE])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([exec_prefix])
m4trace:configure.ac:28: -1- m4_pattern_allow([^exec_prefix$])
m4trace:configure.ac:28: -1- AC_SUBST([prefix], [NONE])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([prefix])
m4trace:configure.ac:28: -1- m4_pattern_allow([^prefix$])
m4trace:configure.ac:28: -1- AC_SUBST([program_transform_name], [s,x,x,])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([program_transform_name])
m4trace:configure.ac:28: -1- m4_pattern_allow([^program_transform_name$])
m4trace:configure.ac:28: -1- AC_SUBST([bindir], ['${exec_prefix}/bin'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([bindir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^bindir$])
m4trace:configure.ac:28: -1- AC_SUBST([sbindir], ['${exec_prefix}/sbin'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([sbindir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^sbindir$])
m4trace:configure.ac:28: -1- AC_SUBST([libexecdir], ['${exec_prefix}/libexec'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([libexecdir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^libexecdir$])
m4trace:configure.ac:28: -1- AC_SUBST([datarootdir], ['${prefix}/share'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([datarootdir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^datarootdir$])
m4trace:configure.ac:28: -1- AC_SUBST([datadir], ['${datarootdir}'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([datadir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^datadir$])
m4trace:configure.ac:28: -1- AC_SUBST([sysconfdir], ['${prefix}/etc'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([sysconfdir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^sysconfdir$])
m4trace:configure.ac:28: -1- AC_SUBST([sharedstatedir], ['${prefix}/com'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([sharedstatedir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^sharedstatedir$])
m4trace:configure.ac:28: -1- AC_SUBST([localstatedir], ['${prefix}/var'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([localstatedir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^localstatedir$])
m4trace:configure.ac:28: -1- AC_SUBST([includedir], ['${prefix}/include'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([includedir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^includedir$])
m4trace:configure.ac:28: -1- AC_SUBST([oldincludedir], ['/usr/include'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([oldincludedir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^oldincludedir$])
m4trace:configure.ac:28: -1- AC_SUBST([docdir], [m4_ifset([AC_PACKAGE_TARNAME],
				     ['${datarootdir}/doc/${PACKAGE_TARNAME}'],
				     ['${datarootdir}/doc/${PACKAGE}'])])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([docdir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^docdir$])
m4trace:configure.ac:28: -1- AC_SUBST([infodir], ['${datarootdir}/info'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([infodir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^infodir$])
m4trace:configure.ac:28: -1- AC_SUBST([htmldir], ['${docdir}'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([htmldir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^htmldir$])
m4trace:configure.ac:28: -1- AC_SUBST([dvidir], ['${docdir}'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([dvidir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^dvidir$])
m4trace:configure.ac:28: -1- AC_SUBST([pdfdir], ['${docdir}'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([pdfdir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^pdfdir$])
m4trace:configure.ac:28: -1- AC_SUBST([psdir], ['${docdir}'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([psdir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^psdir$])
m4trace:configure.ac:28: -1- AC_SUBST([libdir], ['${exec_prefix}/lib'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([libdir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^libdir$])
m4trace:configure.ac:28: -1- AC_SUBST([localedir], ['${datarootdir}/locale'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([localedir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^localedir$])
m4trace:configure.ac:28: -1- AC_SUBST([mandir], ['${datarootdir}/man'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([mandir])
m4trace:configure.ac:28: -1- m4_pattern_allow([^mandir$])
m4trace:configure.ac:28: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_NAME])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.ac:28: -1- AH_OUTPUT([PACKAGE_NAME], [/* Define to the full name of this package. */
@%:@undef PACKAGE_NAME])
m4trace:configure.ac:28: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_TARNAME])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.ac:28: -1- AH_OUTPUT([PACKAGE_TARNAME], [/* Define to the one symbol short name of this package. */
@%:@undef PACKAGE_TARNAME])
m4trace:configure.ac:28: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_VERSION])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.ac:28: -1- AH_OUTPUT([PACKAGE_VERSION], [/* Define to the version of this package. */
@%:@undef PACKAGE_VERSION])
m4trace:configure.ac:28: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_STRING])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.ac:28: -1- AH_OUTPUT([PACKAGE_STRING], [/* Define to the full name and version of this package. */
@%:@undef PACKAGE_STRING])
m4trace:configure.ac:28: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_BUGREPORT])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.ac:28: -1- AH_OUTPUT([PACKAGE_BUGREPORT], [/* Define to the address where bug reports for this package should be sent. */
@%:@undef PACKAGE_BUGREPORT])
m4trace:configure.ac:28: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_URL])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_URL$])
m4trace:configure.ac:28: -1- AH_OUTPUT([PACKAGE_URL], [/* Define to the home page for this package. */
@%:@undef PACKAGE_URL])
m4trace:configure.ac:28: -1- AC_SUBST([DEFS])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([DEFS])
m4trace:configure.ac:28: -1- m4_pattern_allow([^DEFS$])
m4trace:configure.ac:28: -1- AC_SUBST([ECHO_C])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([ECHO_C])
m4trace:configure.ac:28: -1- m4_pattern_allow([^ECHO_C$])
m4trace:configure.ac:28: -1- AC_SUBST([ECHO_N])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([ECHO_N])
m4trace:configure.ac:28: -1- m4_pattern_allow([^ECHO_N$])
m4trace:configure.ac:28: -1- AC_SUBST([ECHO_T])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([ECHO_T])
m4trace:configure.ac:28: -1- m4_pattern_allow([^ECHO_T$])
m4trace:configure.ac:28: -1- AC_SUBST([LIBS])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:28: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:28: -1- AC_SUBST([build_alias])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([build_alias])
m4trace:configure.ac:28: -1- m4_pattern_allow([^build_alias$])
m4trace:configure.ac:28: -1- AC_SUBST([host_alias])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([host_alias])
m4trace:configure.ac:28: -1- m4_pattern_allow([^host_alias$])
m4trace:configure.ac:28: -1- AC_SUBST([target_alias])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([target_alias])
m4trace:configure.ac:28: -1- m4_pattern_allow([^target_alias$])
m4trace:configure.ac:32: -1- AM_INIT_AUTOMAKE([1.11.2 subdir-objects tar-ustar no-dist-gzip dist-xz -Wno-portability])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AM_[A-Z]+FLAGS$])
m4trace:configure.ac:32: -1- AM_AUTOMAKE_VERSION([1.16.1])
m4trace:configure.ac:32: -1- AC_REQUIRE_AUX_FILE([install-sh])
m4trace:configure.ac:32: -1- AC_SUBST([INSTALL_PROGRAM])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([INSTALL_PROGRAM])
m4trace:configure.ac:32: -1- m4_pattern_allow([^INSTALL_PROGRAM$])
m4trace:configure.ac:32: -1- AC_SUBST([INSTALL_SCRIPT])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([INSTALL_SCRIPT])
m4trace:configure.ac:32: -1- m4_pattern_allow([^INSTALL_SCRIPT$])
m4trace:configure.ac:32: -1- AC_SUBST([INSTALL_DATA])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([INSTALL_DATA])
m4trace:configure.ac:32: -1- m4_pattern_allow([^INSTALL_DATA$])
m4trace:configure.ac:32: -1- AC_SUBST([am__isrc], [' -I$(srcdir)'])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([am__isrc])
m4trace:configure.ac:32: -1- m4_pattern_allow([^am__isrc$])
m4trace:configure.ac:32: -1- _AM_SUBST_NOTMAKE([am__isrc])
m4trace:configure.ac:32: -1- AC_SUBST([CYGPATH_W])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([CYGPATH_W])
m4trace:configure.ac:32: -1- m4_pattern_allow([^CYGPATH_W$])
m4trace:configure.ac:32: -1- AC_SUBST([PACKAGE], ['AC_PACKAGE_TARNAME'])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([PACKAGE])
m4trace:configure.ac:32: -1- m4_pattern_allow([^PACKAGE$])
m4trace:configure.ac:32: -1- AC_SUBST([VERSION], ['AC_PACKAGE_VERSION'])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([VERSION])
m4trace:configure.ac:32: -1- m4_pattern_allow([^VERSION$])
m4trace:configure.ac:32: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE])
m4trace:configure.ac:32: -1- m4_pattern_allow([^PACKAGE$])
m4trace:configure.ac:32: -1- AH_OUTPUT([PACKAGE], [/* Name of package */
@%:@undef PACKAGE])
m4trace:configure.ac:32: -1- AC_DEFINE_TRACE_LITERAL([VERSION])
m4trace:configure.ac:32: -1- m4_pattern_allow([^VERSION$])
m4trace:configure.ac:32: -1- AH_OUTPUT([VERSION], [/* Version number of package */
@%:@undef VERSION])
m4trace:configure.ac:32: -1- AC_REQUIRE_AUX_FILE([missing])
m4trace:configure.ac:32: -1- AC_SUBST([ACLOCAL])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([ACLOCAL])
m4trace:configure.ac:32: -1- m4_pattern_allow([^ACLOCAL$])
m4trace:configure.ac:32: -1- AC_SUBST([AUTOCONF])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([AUTOCONF])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AUTOCONF$])
m4trace:configure.ac:32: -1- AC_SUBST([AUTOMAKE])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([AUTOMAKE])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AUTOMAKE$])
m4trace:configure.ac:32: -1- AC_SUBST([AUTOHEADER])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([AUTOHEADER])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AUTOHEADER$])
m4trace:configure.ac:32: -1- AC_SUBST([MAKEINFO])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([MAKEINFO])
m4trace:configure.ac:32: -1- m4_pattern_allow([^MAKEINFO$])
m4trace:configure.ac:32: -1- AC_SUBST([install_sh])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([install_sh])
m4trace:configure.ac:32: -1- m4_pattern_allow([^install_sh$])
m4trace:configure.ac:32: -1- AC_SUBST([STRIP])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([STRIP])
m4trace:configure.ac:32: -1- m4_pattern_allow([^STRIP$])
m4trace:configure.ac:32: -1- AC_SUBST([INSTALL_STRIP_PROGRAM])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([INSTALL_STRIP_PROGRAM])
m4trace:configure.ac:32: -1- m4_pattern_allow([^INSTALL_STRIP_PROGRAM$])
m4trace:configure.ac:32: -1- AC_REQUIRE_AUX_FILE([install-sh])
m4trace:configure.ac:32: -1- AC_SUBST([MKDIR_P])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([MKDIR_P])
m4trace:configure.ac:32: -1- m4_pattern_allow([^MKDIR_P$])
m4trace:configure.ac:32: -1- AC_SUBST([mkdir_p], ['$(MKDIR_P)'])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([mkdir_p])
m4trace:configure.ac:32: -1- m4_pattern_allow([^mkdir_p$])
m4trace:configure.ac:32: -1- AC_SUBST([AWK])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([AWK])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AWK$])
m4trace:configure.ac:32: -1- AC_SUBST([SET_MAKE])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([SET_MAKE])
m4trace:configure.ac:32: -1- m4_pattern_allow([^SET_MAKE$])
m4trace:configure.ac:32: -1- AC_SUBST([am__leading_dot])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([am__leading_dot])
m4trace:configure.ac:32: -1- m4_pattern_allow([^am__leading_dot$])
m4trace:configure.ac:32: -1- AC_SUBST([AMTAR], ['$${TAR-tar}'])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([AMTAR])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AMTAR$])
m4trace:configure.ac:32: -1- AC_SUBST([am__tar])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([am__tar])
m4trace:configure.ac:32: -1- m4_pattern_allow([^am__tar$])
m4trace:configure.ac:32: -1- AC_SUBST([am__untar])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([am__untar])
m4trace:configure.ac:32: -1- m4_pattern_allow([^am__untar$])
m4trace:configure.ac:32: -1- AM_SILENT_RULES
m4trace:configure.ac:32: -1- AC_SUBST([AM_V])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([AM_V])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AM_V$])
m4trace:configure.ac:32: -1- _AM_SUBST_NOTMAKE([AM_V])
m4trace:configure.ac:32: -1- AC_SUBST([AM_DEFAULT_V])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([AM_DEFAULT_V])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AM_DEFAULT_V$])
m4trace:configure.ac:32: -1- _AM_SUBST_NOTMAKE([AM_DEFAULT_V])
m4trace:configure.ac:32: -1- AC_SUBST([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AM_DEFAULT_VERBOSITY$])
m4trace:configure.ac:32: -1- AC_SUBST([AM_BACKSLASH])
m4trace:configure.ac:32: -1- AC_SUBST_TRACE([AM_BACKSLASH])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AM_BACKSLASH$])
m4trace:configure.ac:32: -1- _AM_SUBST_NOTMAKE([AM_BACKSLASH])
m4trace:configure.ac:33: -1- AM_SILENT_RULES([yes])
m4trace:configure.ac:33: -1- AC_SUBST([AM_V])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([AM_V])
m4trace:configure.ac:33: -1- m4_pattern_allow([^AM_V$])
m4trace:configure.ac:33: -1- _AM_SUBST_NOTMAKE([AM_V])
m4trace:configure.ac:33: -1- AC_SUBST([AM_DEFAULT_V])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([AM_DEFAULT_V])
m4trace:configure.ac:33: -1- m4_pattern_allow([^AM_DEFAULT_V$])
m4trace:configure.ac:33: -1- _AM_SUBST_NOTMAKE([AM_DEFAULT_V])
m4trace:configure.ac:33: -1- AC_SUBST([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:33: -1- m4_pattern_allow([^AM_DEFAULT_VERBOSITY$])
m4trace:configure.ac:33: -1- AC_SUBST([AM_BACKSLASH])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([AM_BACKSLASH])
m4trace:configure.ac:33: -1- m4_pattern_allow([^AM_BACKSLASH$])
m4trace:configure.ac:33: -1- _AM_SUBST_NOTMAKE([AM_BACKSLASH])
m4trace:configure.ac:35: -1- AM_MAINTAINER_MODE([enable])
m4trace:configure.ac:35: -1- AM_CONDITIONAL([MAINTAINER_MODE], [test $USE_MAINTAINER_MODE = yes])
m4trace:configure.ac:35: -1- AC_SUBST([MAINTAINER_MODE_TRUE])
m4trace:configure.ac:35: -1- AC_SUBST_TRACE([MAINTAINER_MODE_TRUE])
m4trace:configure.ac:35: -1- m4_pattern_allow([^MAINTAINER_MODE_TRUE$])
m4trace:configure.ac:35: -1- AC_SUBST([MAINTAINER_MODE_FALSE])
m4trace:configure.ac:35: -1- AC_SUBST_TRACE([MAINTAINER_MODE_FALSE])
m4trace:configure.ac:35: -1- m4_pattern_allow([^MAINTAINER_MODE_FALSE$])
m4trace:configure.ac:35: -1- _AM_SUBST_NOTMAKE([MAINTAINER_MODE_TRUE])
m4trace:configure.ac:35: -1- _AM_SUBST_NOTMAKE([MAINTAINER_MODE_FALSE])
m4trace:configure.ac:35: -1- AC_SUBST([MAINT])
m4trace:configure.ac:35: -1- AC_SUBST_TRACE([MAINT])
m4trace:configure.ac:35: -1- m4_pattern_allow([^MAINT$])
m4trace:configure.ac:37: -1- AM_SILENT_RULES([yes])
m4trace:configure.ac:37: -1- AC_SUBST([AM_V])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([AM_V])
m4trace:configure.ac:37: -1- m4_pattern_allow([^AM_V$])
m4trace:configure.ac:37: -1- _AM_SUBST_NOTMAKE([AM_V])
m4trace:configure.ac:37: -1- AC_SUBST([AM_DEFAULT_V])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([AM_DEFAULT_V])
m4trace:configure.ac:37: -1- m4_pattern_allow([^AM_DEFAULT_V$])
m4trace:configure.ac:37: -1- _AM_SUBST_NOTMAKE([AM_DEFAULT_V])
m4trace:configure.ac:37: -1- AC_SUBST([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:37: -1- m4_pattern_allow([^AM_DEFAULT_VERBOSITY$])
m4trace:configure.ac:37: -1- AC_SUBST([AM_BACKSLASH])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([AM_BACKSLASH])
m4trace:configure.ac:37: -1- m4_pattern_allow([^AM_BACKSLASH$])
m4trace:configure.ac:37: -1- _AM_SUBST_NOTMAKE([AM_BACKSLASH])
m4trace:configure.ac:41: -1- AC_CONFIG_HEADERS([config.h])
m4trace:configure.ac:44: -1- AC_SUBST([CC])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:44: -1- AC_SUBST([CFLAGS])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([CFLAGS])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CFLAGS$])
m4trace:configure.ac:44: -1- AC_SUBST([LDFLAGS])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([LDFLAGS])
m4trace:configure.ac:44: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.ac:44: -1- AC_SUBST([LIBS])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:44: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:44: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:44: -1- AC_SUBST([CC])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:44: -1- AC_SUBST([CC])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:44: -1- AC_SUBST([CC])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:44: -1- AC_SUBST([CC])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:44: -1- AC_SUBST([ac_ct_CC])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([ac_ct_CC])
m4trace:configure.ac:44: -1- m4_pattern_allow([^ac_ct_CC$])
m4trace:configure.ac:44: -1- AC_SUBST([EXEEXT], [$ac_cv_exeext])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([EXEEXT])
m4trace:configure.ac:44: -1- m4_pattern_allow([^EXEEXT$])
m4trace:configure.ac:44: -1- AC_SUBST([OBJEXT], [$ac_cv_objext])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([OBJEXT])
m4trace:configure.ac:44: -1- m4_pattern_allow([^OBJEXT$])
m4trace:configure.ac:44: -1- AC_REQUIRE_AUX_FILE([compile])
m4trace:configure.ac:44: -1- AC_SUBST([DEPDIR], ["${am__leading_dot}deps"])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([DEPDIR])
m4trace:configure.ac:44: -1- m4_pattern_allow([^DEPDIR$])
m4trace:configure.ac:44: -1- AC_SUBST([am__include])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([am__include])
m4trace:configure.ac:44: -1- m4_pattern_allow([^am__include$])
m4trace:configure.ac:44: -1- AM_CONDITIONAL([AMDEP], [test "x$enable_dependency_tracking" != xno])
m4trace:configure.ac:44: -1- AC_SUBST([AMDEP_TRUE])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([AMDEP_TRUE])
m4trace:configure.ac:44: -1- m4_pattern_allow([^AMDEP_TRUE$])
m4trace:configure.ac:44: -1- AC_SUBST([AMDEP_FALSE])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([AMDEP_FALSE])
m4trace:configure.ac:44: -1- m4_pattern_allow([^AMDEP_FALSE$])
m4trace:configure.ac:44: -1- _AM_SUBST_NOTMAKE([AMDEP_TRUE])
m4trace:configure.ac:44: -1- _AM_SUBST_NOTMAKE([AMDEP_FALSE])
m4trace:configure.ac:44: -1- AC_SUBST([AMDEPBACKSLASH])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([AMDEPBACKSLASH])
m4trace:configure.ac:44: -1- m4_pattern_allow([^AMDEPBACKSLASH$])
m4trace:configure.ac:44: -1- _AM_SUBST_NOTMAKE([AMDEPBACKSLASH])
m4trace:configure.ac:44: -1- AC_SUBST([am__nodep])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([am__nodep])
m4trace:configure.ac:44: -1- m4_pattern_allow([^am__nodep$])
m4trace:configure.ac:44: -1- _AM_SUBST_NOTMAKE([am__nodep])
m4trace:configure.ac:44: -1- AC_SUBST([CCDEPMODE], [depmode=$am_cv_CC_dependencies_compiler_type])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([CCDEPMODE])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CCDEPMODE$])
m4trace:configure.ac:44: -1- AM_CONDITIONAL([am__fastdepCC], [
  test "x$enable_dependency_tracking" != xno \
  && test "$am_cv_CC_dependencies_compiler_type" = gcc3])
m4trace:configure.ac:44: -1- AC_SUBST([am__fastdepCC_TRUE])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([am__fastdepCC_TRUE])
m4trace:configure.ac:44: -1- m4_pattern_allow([^am__fastdepCC_TRUE$])
m4trace:configure.ac:44: -1- AC_SUBST([am__fastdepCC_FALSE])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([am__fastdepCC_FALSE])
m4trace:configure.ac:44: -1- m4_pattern_allow([^am__fastdepCC_FALSE$])
m4trace:configure.ac:44: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_TRUE])
m4trace:configure.ac:44: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_FALSE])
m4trace:configure.ac:44: -1- AC_SUBST([CPP])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([CPP])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CPP$])
m4trace:configure.ac:44: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:44: -1- AC_SUBST([CPP])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([CPP])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CPP$])
m4trace:configure.ac:44: -1- AC_SUBST([GREP])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([GREP])
m4trace:configure.ac:44: -1- m4_pattern_allow([^GREP$])
m4trace:configure.ac:44: -1- AC_SUBST([EGREP])
m4trace:configure.ac:44: -1- AC_SUBST_TRACE([EGREP])
m4trace:configure.ac:44: -1- m4_pattern_allow([^EGREP$])
m4trace:configure.ac:44: -1- AC_DEFINE_TRACE_LITERAL([STDC_HEADERS])
m4trace:configure.ac:44: -1- m4_pattern_allow([^STDC_HEADERS$])
m4trace:configure.ac:44: -1- AH_OUTPUT([STDC_HEADERS], [/* Define to 1 if you have the ANSI C header files. */
@%:@undef STDC_HEADERS])
m4trace:configure.ac:44: -1- AH_OUTPUT([HAVE_SYS_TYPES_H], [/* Define to 1 if you have the <sys/types.h> header file. */
@%:@undef HAVE_SYS_TYPES_H])
m4trace:configure.ac:44: -1- AH_OUTPUT([HAVE_SYS_STAT_H], [/* Define to 1 if you have the <sys/stat.h> header file. */
@%:@undef HAVE_SYS_STAT_H])
m4trace:configure.ac:44: -1- AH_OUTPUT([HAVE_STDLIB_H], [/* Define to 1 if you have the <stdlib.h> header file. */
@%:@undef HAVE_STDLIB_H])
m4trace:configure.ac:44: -1- AH_OUTPUT([HAVE_STRING_H], [/* Define to 1 if you have the <string.h> header file. */
@%:@undef HAVE_STRING_H])
m4trace:configure.ac:44: -1- AH_OUTPUT([HAVE_MEMORY_H], [/* Define to 1 if you have the <memory.h> header file. */
@%:@undef HAVE_MEMORY_H])
m4trace:configure.ac:44: -1- AH_OUTPUT([HAVE_STRINGS_H], [/* Define to 1 if you have the <strings.h> header file. */
@%:@undef HAVE_STRINGS_H])
m4trace:configure.ac:44: -1- AH_OUTPUT([HAVE_INTTYPES_H], [/* Define to 1 if you have the <inttypes.h> header file. */
@%:@undef HAVE_INTTYPES_H])
m4trace:configure.ac:44: -1- AH_OUTPUT([HAVE_STDINT_H], [/* Define to 1 if you have the <stdint.h> header file. */
@%:@undef HAVE_STDINT_H])
m4trace:configure.ac:44: -1- AH_OUTPUT([HAVE_UNISTD_H], [/* Define to 1 if you have the <unistd.h> header file. */
@%:@undef HAVE_UNISTD_H])
m4trace:configure.ac:44: -1- AC_DEFINE_TRACE_LITERAL([_POSIX_SOURCE])
m4trace:configure.ac:44: -1- m4_pattern_allow([^_POSIX_SOURCE$])
m4trace:configure.ac:44: -1- AH_OUTPUT([_POSIX_SOURCE], [/* Define to 1 if you need to in order for `stat\' and other things to work. */
@%:@undef _POSIX_SOURCE])
m4trace:configure.ac:44: -1- AC_DEFINE_TRACE_LITERAL([_POSIX_1_SOURCE])
m4trace:configure.ac:44: -1- m4_pattern_allow([^_POSIX_1_SOURCE$])
m4trace:configure.ac:44: -1- AH_OUTPUT([_POSIX_1_SOURCE], [/* Define to 2 if the system does not provide POSIX.1 features except with
   this defined. */
@%:@undef _POSIX_1_SOURCE])
m4trace:configure.ac:44: -1- AC_DEFINE_TRACE_LITERAL([_MINIX])
m4trace:configure.ac:44: -1- m4_pattern_allow([^_MINIX$])
m4trace:configure.ac:44: -1- AH_OUTPUT([_MINIX], [/* Define to 1 if on MINIX. */
@%:@undef _MINIX])
m4trace:configure.ac:44: -1- AH_OUTPUT([USE_SYSTEM_EXTENSIONS], [/* Enable extensions on AIX 3, Interix.  */
#ifndef _ALL_SOURCE
# undef _ALL_SOURCE
#endif
/* Enable GNU extensions on systems that have them.  */
#ifndef _GNU_SOURCE
# undef _GNU_SOURCE
#endif
/* Enable threading extensions on Solaris.  */
#ifndef _POSIX_PTHREAD_SEMANTICS
# undef _POSIX_PTHREAD_SEMANTICS
#endif
/* Enable extensions on HP NonStop.  */
#ifndef _TANDEM_SOURCE
# undef _TANDEM_SOURCE
#endif
/* Enable general extensions on Solaris.  */
#ifndef __EXTENSIONS__
# undef __EXTENSIONS__
#endif
])
m4trace:configure.ac:44: -1- AC_DEFINE_TRACE_LITERAL([__EXTENSIONS__])
m4trace:configure.ac:44: -1- m4_pattern_allow([^__EXTENSIONS__$])
m4trace:configure.ac:44: -1- AC_DEFINE_TRACE_LITERAL([_ALL_SOURCE])
m4trace:configure.ac:44: -1- m4_pattern_allow([^_ALL_SOURCE$])
m4trace:configure.ac:44: -1- AC_DEFINE_TRACE_LITERAL([_GNU_SOURCE])
m4trace:configure.ac:44: -1- m4_pattern_allow([^_GNU_SOURCE$])
m4trace:configure.ac:44: -1- AC_DEFINE_TRACE_LITERAL([_POSIX_PTHREAD_SEMANTICS])
m4trace:configure.ac:44: -1- m4_pattern_allow([^_POSIX_PTHREAD_SEMANTICS$])
m4trace:configure.ac:44: -1- AC_DEFINE_TRACE_LITERAL([_TANDEM_SOURCE])
m4trace:configure.ac:44: -1- m4_pattern_allow([^_TANDEM_SOURCE$])
m4trace:configure.ac:47: -1- AC_SUBST([CC])
m4trace:configure.ac:47: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:47: -1- AC_SUBST([CFLAGS])
m4trace:configure.ac:47: -1- AC_SUBST_TRACE([CFLAGS])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CFLAGS$])
m4trace:configure.ac:47: -1- AC_SUBST([LDFLAGS])
m4trace:configure.ac:47: -1- AC_SUBST_TRACE([LDFLAGS])
m4trace:configure.ac:47: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.ac:47: -1- AC_SUBST([LIBS])
m4trace:configure.ac:47: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:47: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:47: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:47: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:47: -1- AC_SUBST([CC])
m4trace:configure.ac:47: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:47: -1- AC_SUBST([CC])
m4trace:configure.ac:47: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:47: -1- AC_SUBST([CC])
m4trace:configure.ac:47: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:47: -1- AC_SUBST([CC])
m4trace:configure.ac:47: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:47: -1- AC_SUBST([ac_ct_CC])
m4trace:configure.ac:47: -1- AC_SUBST_TRACE([ac_ct_CC])
m4trace:configure.ac:47: -1- m4_pattern_allow([^ac_ct_CC$])
m4trace:configure.ac:47: -1- AC_REQUIRE_AUX_FILE([compile])
m4trace:configure.ac:47: -1- AC_SUBST([CCDEPMODE], [depmode=$am_cv_CC_dependencies_compiler_type])
m4trace:configure.ac:47: -1- AC_SUBST_TRACE([CCDEPMODE])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CCDEPMODE$])
m4trace:configure.ac:47: -1- AM_CONDITIONAL([am__fastdepCC], [
  test "x$enable_dependency_tracking" != xno \
  && test "$am_cv_CC_dependencies_compiler_type" = gcc3])
m4trace:configure.ac:47: -1- AC_SUBST([am__fastdepCC_TRUE])
m4trace:configure.ac:47: -1- AC_SUBST_TRACE([am__fastdepCC_TRUE])
m4trace:configure.ac:47: -1- m4_pattern_allow([^am__fastdepCC_TRUE$])
m4trace:configure.ac:47: -1- AC_SUBST([am__fastdepCC_FALSE])
m4trace:configure.ac:47: -1- AC_SUBST_TRACE([am__fastdepCC_FALSE])
m4trace:configure.ac:47: -1- m4_pattern_allow([^am__fastdepCC_FALSE$])
m4trace:configure.ac:47: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_TRUE])
m4trace:configure.ac:47: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_FALSE])
m4trace:configure.ac:48: -1- AM_PROG_CC_C_O
m4trace:configure.ac:54: -1- LT_INIT([disable-static])
m4trace:configure.ac:54: -1- m4_pattern_forbid([^_?LT_[A-Z_]+$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^(_LT_EOF|LT_DLGLOBAL|LT_DLLAZY_OR_NOW|LT_MULTI_MODULE)$])
m4trace:configure.ac:54: -1- AC_REQUIRE_AUX_FILE([ltmain.sh])
m4trace:configure.ac:54: -1- AC_SUBST([LIBTOOL])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([LIBTOOL])
m4trace:configure.ac:54: -1- m4_pattern_allow([^LIBTOOL$])
m4trace:configure.ac:54: -1- AC_CANONICAL_HOST
m4trace:configure.ac:54: -1- AC_CANONICAL_BUILD
m4trace:configure.ac:54: -1- AC_REQUIRE_AUX_FILE([config.sub])
m4trace:configure.ac:54: -1- AC_REQUIRE_AUX_FILE([config.guess])
m4trace:configure.ac:54: -1- AC_SUBST([build], [$ac_cv_build])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([build])
m4trace:configure.ac:54: -1- m4_pattern_allow([^build$])
m4trace:configure.ac:54: -1- AC_SUBST([build_cpu], [$[1]])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([build_cpu])
m4trace:configure.ac:54: -1- m4_pattern_allow([^build_cpu$])
m4trace:configure.ac:54: -1- AC_SUBST([build_vendor], [$[2]])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([build_vendor])
m4trace:configure.ac:54: -1- m4_pattern_allow([^build_vendor$])
m4trace:configure.ac:54: -1- AC_SUBST([build_os])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([build_os])
m4trace:configure.ac:54: -1- m4_pattern_allow([^build_os$])
m4trace:configure.ac:54: -1- AC_SUBST([host], [$ac_cv_host])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([host])
m4trace:configure.ac:54: -1- m4_pattern_allow([^host$])
m4trace:configure.ac:54: -1- AC_SUBST([host_cpu], [$[1]])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([host_cpu])
m4trace:configure.ac:54: -1- m4_pattern_allow([^host_cpu$])
m4trace:configure.ac:54: -1- AC_SUBST([host_vendor], [$[2]])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([host_vendor])
m4trace:configure.ac:54: -1- m4_pattern_allow([^host_vendor$])
m4trace:configure.ac:54: -1- AC_SUBST([host_os])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([host_os])
m4trace:configure.ac:54: -1- m4_pattern_allow([^host_os$])
m4trace:configure.ac:54: -1- AC_SUBST([SED])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([SED])
m4trace:configure.ac:54: -1- m4_pattern_allow([^SED$])
m4trace:configure.ac:54: -1- AC_SUBST([FGREP])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([FGREP])
m4trace:configure.ac:54: -1- m4_pattern_allow([^FGREP$])
m4trace:configure.ac:54: -1- AC_SUBST([GREP])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([GREP])
m4trace:configure.ac:54: -1- m4_pattern_allow([^GREP$])
m4trace:configure.ac:54: -1- AC_SUBST([LD])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([LD])
m4trace:configure.ac:54: -1- m4_pattern_allow([^LD$])
m4trace:configure.ac:54: -1- AC_SUBST([DUMPBIN])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([DUMPBIN])
m4trace:configure.ac:54: -1- m4_pattern_allow([^DUMPBIN$])
m4trace:configure.ac:54: -1- AC_SUBST([ac_ct_DUMPBIN])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([ac_ct_DUMPBIN])
m4trace:configure.ac:54: -1- m4_pattern_allow([^ac_ct_DUMPBIN$])
m4trace:configure.ac:54: -1- AC_SUBST([DUMPBIN])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([DUMPBIN])
m4trace:configure.ac:54: -1- m4_pattern_allow([^DUMPBIN$])
m4trace:configure.ac:54: -1- AC_SUBST([NM])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([NM])
m4trace:configure.ac:54: -1- m4_pattern_allow([^NM$])
m4trace:configure.ac:54: -1- AC_SUBST([LN_S], [$as_ln_s])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([LN_S])
m4trace:configure.ac:54: -1- m4_pattern_allow([^LN_S$])
m4trace:configure.ac:54: -1- AC_SUBST([OBJDUMP])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([OBJDUMP])
m4trace:configure.ac:54: -1- m4_pattern_allow([^OBJDUMP$])
m4trace:configure.ac:54: -1- AC_SUBST([OBJDUMP])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([OBJDUMP])
m4trace:configure.ac:54: -1- m4_pattern_allow([^OBJDUMP$])
m4trace:configure.ac:54: -1- AC_SUBST([DLLTOOL])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([DLLTOOL])
m4trace:configure.ac:54: -1- m4_pattern_allow([^DLLTOOL$])
m4trace:configure.ac:54: -1- AC_SUBST([DLLTOOL])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([DLLTOOL])
m4trace:configure.ac:54: -1- m4_pattern_allow([^DLLTOOL$])
m4trace:configure.ac:54: -1- AC_SUBST([AR])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([AR])
m4trace:configure.ac:54: -1- m4_pattern_allow([^AR$])
m4trace:configure.ac:54: -1- AC_SUBST([ac_ct_AR])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([ac_ct_AR])
m4trace:configure.ac:54: -1- m4_pattern_allow([^ac_ct_AR$])
m4trace:configure.ac:54: -1- AC_SUBST([STRIP])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([STRIP])
m4trace:configure.ac:54: -1- m4_pattern_allow([^STRIP$])
m4trace:configure.ac:54: -1- AC_SUBST([RANLIB])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([RANLIB])
m4trace:configure.ac:54: -1- m4_pattern_allow([^RANLIB$])
m4trace:configure.ac:54: -1- m4_pattern_allow([LT_OBJDIR])
m4trace:configure.ac:54: -1- AC_DEFINE_TRACE_LITERAL([LT_OBJDIR])
m4trace:configure.ac:54: -1- m4_pattern_allow([^LT_OBJDIR$])
m4trace:configure.ac:54: -1- AH_OUTPUT([LT_OBJDIR], [/* Define to the sub-directory where libtool stores uninstalled libraries. */
@%:@undef LT_OBJDIR])
m4trace:configure.ac:54: -1- LT_SUPPORTED_TAG([CC])
m4trace:configure.ac:54: -1- AC_SUBST([MANIFEST_TOOL])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([MANIFEST_TOOL])
m4trace:configure.ac:54: -1- m4_pattern_allow([^MANIFEST_TOOL$])
m4trace:configure.ac:54: -1- AC_SUBST([DSYMUTIL])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([DSYMUTIL])
m4trace:configure.ac:54: -1- m4_pattern_allow([^DSYMUTIL$])
m4trace:configure.ac:54: -1- AC_SUBST([NMEDIT])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([NMEDIT])
m4trace:configure.ac:54: -1- m4_pattern_allow([^NMEDIT$])
m4trace:configure.ac:54: -1- AC_SUBST([LIPO])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([LIPO])
m4trace:configure.ac:54: -1- m4_pattern_allow([^LIPO$])
m4trace:configure.ac:54: -1- AC_SUBST([OTOOL])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([OTOOL])
m4trace:configure.ac:54: -1- m4_pattern_allow([^OTOOL$])
m4trace:configure.ac:54: -1- AC_SUBST([OTOOL64])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([OTOOL64])
m4trace:configure.ac:54: -1- m4_pattern_allow([^OTOOL64$])
m4trace:configure.ac:54: -1- AC_SUBST([LT_SYS_LIBRARY_PATH])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([LT_SYS_LIBRARY_PATH])
m4trace:configure.ac:54: -1- m4_pattern_allow([^LT_SYS_LIBRARY_PATH$])
m4trace:configure.ac:54: -1- AH_OUTPUT([HAVE_DLFCN_H], [/* Define to 1 if you have the <dlfcn.h> header file. */
@%:@undef HAVE_DLFCN_H])
m4trace:configure.ac:54: -1- AC_DEFINE_TRACE_LITERAL([HAVE_DLFCN_H])
m4trace:configure.ac:54: -1- m4_pattern_allow([^HAVE_DLFCN_H$])
m4trace:configure.ac:61: -1- m4_pattern_forbid([^AX_(COMPILER_FLAGS|COMPILER_FLAGS_(CFLAGS|GIR|LDFLAGS))\b], [Unexpanded AX_ macro found. Please install GNU autoconf-archive])
m4trace:configure.ac:75: -1- AC_SUBST([WARN_CFLAGS])
m4trace:configure.ac:75: -1- AC_SUBST_TRACE([WARN_CFLAGS])
m4trace:configure.ac:75: -1- m4_pattern_allow([^WARN_CFLAGS$])
m4trace:configure.ac:75: -1- AC_SUBST([WARN_LDFLAGS])
m4trace:configure.ac:75: -1- AC_SUBST_TRACE([WARN_LDFLAGS])
m4trace:configure.ac:75: -1- m4_pattern_allow([^WARN_LDFLAGS$])
m4trace:configure.ac:75: -1- AC_SUBST([WARN_SCANNERFLAGS])
m4trace:configure.ac:75: -1- AC_SUBST_TRACE([WARN_SCANNERFLAGS])
m4trace:configure.ac:75: -1- m4_pattern_allow([^WARN_SCANNERFLAGS$])
m4trace:configure.ac:76: -1- AC_SUBST([WARN_CFLAGS])
m4trace:configure.ac:76: -1- AC_SUBST_TRACE([WARN_CFLAGS])
m4trace:configure.ac:76: -1- m4_pattern_allow([^WARN_CFLAGS$])
m4trace:configure.ac:92: -1- AC_SUBST([MM_MAJOR_VERSION])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([MM_MAJOR_VERSION])
m4trace:configure.ac:92: -1- m4_pattern_allow([^MM_MAJOR_VERSION$])
m4trace:configure.ac:93: -1- AC_SUBST([MM_MINOR_VERSION])
m4trace:configure.ac:93: -1- AC_SUBST_TRACE([MM_MINOR_VERSION])
m4trace:configure.ac:93: -1- m4_pattern_allow([^MM_MINOR_VERSION$])
m4trace:configure.ac:94: -1- AC_SUBST([MM_MICRO_VERSION])
m4trace:configure.ac:94: -1- AC_SUBST_TRACE([MM_MICRO_VERSION])
m4trace:configure.ac:94: -1- m4_pattern_allow([^MM_MICRO_VERSION$])
m4trace:configure.ac:95: -1- AC_SUBST([MM_VERSION])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([MM_VERSION])
m4trace:configure.ac:95: -1- m4_pattern_allow([^MM_VERSION$])
m4trace:configure.ac:101: -1- AC_SUBST([MM_GLIB_LT_CURRENT])
m4trace:configure.ac:101: -1- AC_SUBST_TRACE([MM_GLIB_LT_CURRENT])
m4trace:configure.ac:101: -1- m4_pattern_allow([^MM_GLIB_LT_CURRENT$])
m4trace:configure.ac:102: -1- AC_SUBST([MM_GLIB_LT_REVISION])
m4trace:configure.ac:102: -1- AC_SUBST_TRACE([MM_GLIB_LT_REVISION])
m4trace:configure.ac:102: -1- m4_pattern_allow([^MM_GLIB_LT_REVISION$])
m4trace:configure.ac:103: -1- AC_SUBST([MM_GLIB_LT_AGE])
m4trace:configure.ac:103: -1- AC_SUBST_TRACE([MM_GLIB_LT_AGE])
m4trace:configure.ac:103: -1- m4_pattern_allow([^MM_GLIB_LT_AGE$])
m4trace:configure.ac:109: -1- m4_pattern_forbid([^_?PKG_[A-Z_]+$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^PKG_CONFIG(_(PATH|LIBDIR|SYSROOT_DIR|ALLOW_SYSTEM_(CFLAGS|LIBS)))?$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^PKG_CONFIG_(DISABLE_UNINSTALLED|TOP_BUILD_DIR|DEBUG_SPEW)$])
m4trace:configure.ac:109: -1- AC_SUBST([PKG_CONFIG])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([PKG_CONFIG])
m4trace:configure.ac:109: -1- m4_pattern_allow([^PKG_CONFIG$])
m4trace:configure.ac:109: -1- AC_SUBST([PKG_CONFIG_PATH])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([PKG_CONFIG_PATH])
m4trace:configure.ac:109: -1- m4_pattern_allow([^PKG_CONFIG_PATH$])
m4trace:configure.ac:109: -1- AC_SUBST([PKG_CONFIG_LIBDIR])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([PKG_CONFIG_LIBDIR])
m4trace:configure.ac:109: -1- m4_pattern_allow([^PKG_CONFIG_LIBDIR$])
m4trace:configure.ac:109: -1- AC_SUBST([PKG_CONFIG])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([PKG_CONFIG])
m4trace:configure.ac:109: -1- m4_pattern_allow([^PKG_CONFIG$])
m4trace:configure.ac:109: -1- AC_SUBST([GTKDOC_CHECK])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([GTKDOC_CHECK])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTKDOC_CHECK$])
m4trace:configure.ac:109: -1- AC_SUBST([GTKDOC_CHECK_PATH])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([GTKDOC_CHECK_PATH])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTKDOC_CHECK_PATH$])
m4trace:configure.ac:109: -1- AC_SUBST([GTKDOC_REBASE])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([GTKDOC_REBASE])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTKDOC_REBASE$])
m4trace:configure.ac:109: -1- AC_SUBST([GTKDOC_MKPDF])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([GTKDOC_MKPDF])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTKDOC_MKPDF$])
m4trace:configure.ac:109: -1- AC_SUBST([HTML_DIR])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([HTML_DIR])
m4trace:configure.ac:109: -1- m4_pattern_allow([^HTML_DIR$])
m4trace:configure.ac:109: -1- AC_SUBST([GTKDOC_DEPS_CFLAGS])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([GTKDOC_DEPS_CFLAGS])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTKDOC_DEPS_CFLAGS$])
m4trace:configure.ac:109: -1- AC_SUBST([GTKDOC_DEPS_LIBS])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([GTKDOC_DEPS_LIBS])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTKDOC_DEPS_LIBS$])
m4trace:configure.ac:109: -1- AC_SUBST([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:109: -1- m4_pattern_allow([^AM_DEFAULT_VERBOSITY$])
m4trace:configure.ac:109: -1- AM_CONDITIONAL([HAVE_GTK_DOC], [test x$have_gtk_doc = xyes])
m4trace:configure.ac:109: -1- AC_SUBST([HAVE_GTK_DOC_TRUE])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([HAVE_GTK_DOC_TRUE])
m4trace:configure.ac:109: -1- m4_pattern_allow([^HAVE_GTK_DOC_TRUE$])
m4trace:configure.ac:109: -1- AC_SUBST([HAVE_GTK_DOC_FALSE])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([HAVE_GTK_DOC_FALSE])
m4trace:configure.ac:109: -1- m4_pattern_allow([^HAVE_GTK_DOC_FALSE$])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([HAVE_GTK_DOC_TRUE])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([HAVE_GTK_DOC_FALSE])
m4trace:configure.ac:109: -1- AM_CONDITIONAL([ENABLE_GTK_DOC], [test x$enable_gtk_doc = xyes])
m4trace:configure.ac:109: -1- AC_SUBST([ENABLE_GTK_DOC_TRUE])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([ENABLE_GTK_DOC_TRUE])
m4trace:configure.ac:109: -1- m4_pattern_allow([^ENABLE_GTK_DOC_TRUE$])
m4trace:configure.ac:109: -1- AC_SUBST([ENABLE_GTK_DOC_FALSE])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([ENABLE_GTK_DOC_FALSE])
m4trace:configure.ac:109: -1- m4_pattern_allow([^ENABLE_GTK_DOC_FALSE$])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([ENABLE_GTK_DOC_TRUE])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([ENABLE_GTK_DOC_FALSE])
m4trace:configure.ac:109: -1- AM_CONDITIONAL([GTK_DOC_BUILD_HTML], [test x$enable_gtk_doc_html = xyes])
m4trace:configure.ac:109: -1- AC_SUBST([GTK_DOC_BUILD_HTML_TRUE])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([GTK_DOC_BUILD_HTML_TRUE])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_BUILD_HTML_TRUE$])
m4trace:configure.ac:109: -1- AC_SUBST([GTK_DOC_BUILD_HTML_FALSE])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([GTK_DOC_BUILD_HTML_FALSE])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_BUILD_HTML_FALSE$])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_BUILD_HTML_TRUE])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_BUILD_HTML_FALSE])
m4trace:configure.ac:109: -1- AM_CONDITIONAL([GTK_DOC_BUILD_PDF], [test x$enable_gtk_doc_pdf = xyes])
m4trace:configure.ac:109: -1- AC_SUBST([GTK_DOC_BUILD_PDF_TRUE])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([GTK_DOC_BUILD_PDF_TRUE])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_BUILD_PDF_TRUE$])
m4trace:configure.ac:109: -1- AC_SUBST([GTK_DOC_BUILD_PDF_FALSE])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([GTK_DOC_BUILD_PDF_FALSE])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_BUILD_PDF_FALSE$])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_BUILD_PDF_TRUE])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_BUILD_PDF_FALSE])
m4trace:configure.ac:109: -1- AM_CONDITIONAL([GTK_DOC_USE_LIBTOOL], [test -n "$LIBTOOL"])
m4trace:configure.ac:109: -1- AC_SUBST([GTK_DOC_USE_LIBTOOL_TRUE])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([GTK_DOC_USE_LIBTOOL_TRUE])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_USE_LIBTOOL_TRUE$])
m4trace:configure.ac:109: -1- AC_SUBST([GTK_DOC_USE_LIBTOOL_FALSE])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([GTK_DOC_USE_LIBTOOL_FALSE])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_USE_LIBTOOL_FALSE$])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_USE_LIBTOOL_TRUE])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_USE_LIBTOOL_FALSE])
m4trace:configure.ac:109: -1- AM_CONDITIONAL([GTK_DOC_USE_REBASE], [test -n "$GTKDOC_REBASE"])
m4trace:configure.ac:109: -1- AC_SUBST([GTK_DOC_USE_REBASE_TRUE])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([GTK_DOC_USE_REBASE_TRUE])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_USE_REBASE_TRUE$])
m4trace:configure.ac:109: -1- AC_SUBST([GTK_DOC_USE_REBASE_FALSE])
m4trace:configure.ac:109: -1- AC_SUBST_TRACE([GTK_DOC_USE_REBASE_FALSE])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_USE_REBASE_FALSE$])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_USE_REBASE_TRUE])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_USE_REBASE_FALSE])
m4trace:configure.ac:115: -1- AM_GNU_GETTEXT([external])
m4trace:configure.ac:115: -1- AM_NLS
m4trace:configure.ac:115: -1- AC_SUBST([USE_NLS])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([USE_NLS])
m4trace:configure.ac:115: -1- m4_pattern_allow([^USE_NLS$])
m4trace:configure.ac:115: -1- AC_SUBST([GETTEXT_MACRO_VERSION], [0.19])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([GETTEXT_MACRO_VERSION])
m4trace:configure.ac:115: -1- m4_pattern_allow([^GETTEXT_MACRO_VERSION$])
m4trace:configure.ac:115: -1- AC_SUBST([MSGFMT])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([MSGFMT])
m4trace:configure.ac:115: -1- m4_pattern_allow([^MSGFMT$])
m4trace:configure.ac:115: -1- AC_SUBST([GMSGFMT])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([GMSGFMT])
m4trace:configure.ac:115: -1- m4_pattern_allow([^GMSGFMT$])
m4trace:configure.ac:115: -1- AC_SUBST([MSGFMT_015])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([MSGFMT_015])
m4trace:configure.ac:115: -1- m4_pattern_allow([^MSGFMT_015$])
m4trace:configure.ac:115: -1- AC_SUBST([GMSGFMT_015])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([GMSGFMT_015])
m4trace:configure.ac:115: -1- m4_pattern_allow([^GMSGFMT_015$])
m4trace:configure.ac:115: -1- AC_SUBST([XGETTEXT])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([XGETTEXT])
m4trace:configure.ac:115: -1- m4_pattern_allow([^XGETTEXT$])
m4trace:configure.ac:115: -1- AC_SUBST([XGETTEXT_015])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([XGETTEXT_015])
m4trace:configure.ac:115: -1- m4_pattern_allow([^XGETTEXT_015$])
m4trace:configure.ac:115: -1- AC_SUBST([MSGMERGE])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([MSGMERGE])
m4trace:configure.ac:115: -1- m4_pattern_allow([^MSGMERGE$])
m4trace:configure.ac:115: -1- AC_SUBST([localedir])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([localedir])
m4trace:configure.ac:115: -1- m4_pattern_allow([^localedir$])
m4trace:configure.ac:115: -1- AC_SUBST([XGETTEXT_EXTRA_OPTIONS])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([XGETTEXT_EXTRA_OPTIONS])
m4trace:configure.ac:115: -1- m4_pattern_allow([^XGETTEXT_EXTRA_OPTIONS$])
m4trace:configure.ac:115: -1- AC_REQUIRE_AUX_FILE([config.rpath])
m4trace:configure.ac:115: -1- AC_DEFINE_TRACE_LITERAL([HAVE_CFPREFERENCESCOPYAPPVALUE])
m4trace:configure.ac:115: -1- m4_pattern_allow([^HAVE_CFPREFERENCESCOPYAPPVALUE$])
m4trace:configure.ac:115: -1- AH_OUTPUT([HAVE_CFPREFERENCESCOPYAPPVALUE], [/* Define to 1 if you have the Mac OS X function CFPreferencesCopyAppValue in
   the CoreFoundation framework. */
@%:@undef HAVE_CFPREFERENCESCOPYAPPVALUE])
m4trace:configure.ac:115: -1- AC_DEFINE_TRACE_LITERAL([HAVE_CFLOCALECOPYCURRENT])
m4trace:configure.ac:115: -1- m4_pattern_allow([^HAVE_CFLOCALECOPYCURRENT$])
m4trace:configure.ac:115: -1- AH_OUTPUT([HAVE_CFLOCALECOPYCURRENT], [/* Define to 1 if you have the Mac OS X function CFLocaleCopyCurrent in the
   CoreFoundation framework. */
@%:@undef HAVE_CFLOCALECOPYCURRENT])
m4trace:configure.ac:115: -1- AC_SUBST([INTL_MACOSX_LIBS])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([INTL_MACOSX_LIBS])
m4trace:configure.ac:115: -1- m4_pattern_allow([^INTL_MACOSX_LIBS$])
m4trace:configure.ac:115: -1- AC_DEFINE_TRACE_LITERAL([HAVE_ICONV])
m4trace:configure.ac:115: -1- m4_pattern_allow([^HAVE_ICONV$])
m4trace:configure.ac:115: -1- AH_OUTPUT([HAVE_ICONV], [/* Define if you have the iconv() function and it works. */
@%:@undef HAVE_ICONV])
m4trace:configure.ac:115: -1- AC_SUBST([LIBICONV])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([LIBICONV])
m4trace:configure.ac:115: -1- m4_pattern_allow([^LIBICONV$])
m4trace:configure.ac:115: -1- AC_SUBST([LTLIBICONV])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([LTLIBICONV])
m4trace:configure.ac:115: -1- m4_pattern_allow([^LTLIBICONV$])
m4trace:configure.ac:115: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_NLS])
m4trace:configure.ac:115: -1- m4_pattern_allow([^ENABLE_NLS$])
m4trace:configure.ac:115: -1- AH_OUTPUT([ENABLE_NLS], [/* Define to 1 if translation of program messages to the user\'s native
   language is requested. */
@%:@undef ENABLE_NLS])
m4trace:configure.ac:115: -1- AC_DEFINE_TRACE_LITERAL([HAVE_GETTEXT])
m4trace:configure.ac:115: -1- m4_pattern_allow([^HAVE_GETTEXT$])
m4trace:configure.ac:115: -1- AH_OUTPUT([HAVE_GETTEXT], [/* Define if the GNU gettext() function is already present or preinstalled. */
@%:@undef HAVE_GETTEXT])
m4trace:configure.ac:115: -1- AC_DEFINE_TRACE_LITERAL([HAVE_DCGETTEXT])
m4trace:configure.ac:115: -1- m4_pattern_allow([^HAVE_DCGETTEXT$])
m4trace:configure.ac:115: -1- AH_OUTPUT([HAVE_DCGETTEXT], [/* Define if the GNU dcgettext() function is already present or preinstalled.
   */
@%:@undef HAVE_DCGETTEXT])
m4trace:configure.ac:115: -1- AC_SUBST([INTLLIBS])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([INTLLIBS])
m4trace:configure.ac:115: -1- m4_pattern_allow([^INTLLIBS$])
m4trace:configure.ac:115: -1- AC_SUBST([LIBINTL])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([LIBINTL])
m4trace:configure.ac:115: -1- m4_pattern_allow([^LIBINTL$])
m4trace:configure.ac:115: -1- AC_SUBST([LTLIBINTL])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([LTLIBINTL])
m4trace:configure.ac:115: -1- m4_pattern_allow([^LTLIBINTL$])
m4trace:configure.ac:115: -1- AC_SUBST([POSUB])
m4trace:configure.ac:115: -1- AC_SUBST_TRACE([POSUB])
m4trace:configure.ac:115: -1- m4_pattern_allow([^POSUB$])
m4trace:configure.ac:119: -1- AC_SUBST([GETTEXT_PACKAGE])
m4trace:configure.ac:119: -1- AC_SUBST_TRACE([GETTEXT_PACKAGE])
m4trace:configure.ac:119: -1- m4_pattern_allow([^GETTEXT_PACKAGE$])
m4trace:configure.ac:120: -1- AC_DEFINE_TRACE_LITERAL([GETTEXT_PACKAGE])
m4trace:configure.ac:120: -1- m4_pattern_allow([^GETTEXT_PACKAGE$])
m4trace:configure.ac:120: -1- AH_OUTPUT([GETTEXT_PACKAGE], [/* Gettext package */
@%:@undef GETTEXT_PACKAGE])
m4trace:configure.ac:131: -1- AC_SUBST([MM_CFLAGS])
m4trace:configure.ac:131: -1- AC_SUBST_TRACE([MM_CFLAGS])
m4trace:configure.ac:131: -1- m4_pattern_allow([^MM_CFLAGS$])
m4trace:configure.ac:131: -1- AC_SUBST([MM_LIBS])
m4trace:configure.ac:131: -1- AC_SUBST_TRACE([MM_LIBS])
m4trace:configure.ac:131: -1- m4_pattern_allow([^MM_LIBS$])
m4trace:configure.ac:138: -1- AC_SUBST([MM_CFLAGS])
m4trace:configure.ac:138: -1- AC_SUBST_TRACE([MM_CFLAGS])
m4trace:configure.ac:138: -1- m4_pattern_allow([^MM_CFLAGS$])
m4trace:configure.ac:139: -1- AC_SUBST([MM_LIBS])
m4trace:configure.ac:139: -1- AC_SUBST_TRACE([MM_LIBS])
m4trace:configure.ac:139: -1- m4_pattern_allow([^MM_LIBS$])
m4trace:configure.ac:141: -1- AC_SUBST([LIBMM_GLIB_CFLAGS])
m4trace:configure.ac:141: -1- AC_SUBST_TRACE([LIBMM_GLIB_CFLAGS])
m4trace:configure.ac:141: -1- m4_pattern_allow([^LIBMM_GLIB_CFLAGS$])
m4trace:configure.ac:141: -1- AC_SUBST([LIBMM_GLIB_LIBS])
m4trace:configure.ac:141: -1- AC_SUBST_TRACE([LIBMM_GLIB_LIBS])
m4trace:configure.ac:141: -1- m4_pattern_allow([^LIBMM_GLIB_LIBS$])
m4trace:configure.ac:147: -1- AC_SUBST([LIBMM_GLIB_CFLAGS])
m4trace:configure.ac:147: -1- AC_SUBST_TRACE([LIBMM_GLIB_CFLAGS])
m4trace:configure.ac:147: -1- m4_pattern_allow([^LIBMM_GLIB_CFLAGS$])
m4trace:configure.ac:148: -1- AC_SUBST([LIBMM_GLIB_LIBS])
m4trace:configure.ac:148: -1- AC_SUBST_TRACE([LIBMM_GLIB_LIBS])
m4trace:configure.ac:148: -1- m4_pattern_allow([^LIBMM_GLIB_LIBS$])
m4trace:configure.ac:150: -1- AC_SUBST([MMCLI_CFLAGS])
m4trace:configure.ac:150: -1- AC_SUBST_TRACE([MMCLI_CFLAGS])
m4trace:configure.ac:150: -1- m4_pattern_allow([^MMCLI_CFLAGS$])
m4trace:configure.ac:150: -1- AC_SUBST([MMCLI_LIBS])
m4trace:configure.ac:150: -1- AC_SUBST_TRACE([MMCLI_LIBS])
m4trace:configure.ac:150: -1- m4_pattern_allow([^MMCLI_LIBS$])
m4trace:configure.ac:155: -1- AC_SUBST([MMCLI_CFLAGS])
m4trace:configure.ac:155: -1- AC_SUBST_TRACE([MMCLI_CFLAGS])
m4trace:configure.ac:155: -1- m4_pattern_allow([^MMCLI_CFLAGS$])
m4trace:configure.ac:156: -1- AC_SUBST([MMCLI_LIBS])
m4trace:configure.ac:156: -1- AC_SUBST_TRACE([MMCLI_LIBS])
m4trace:configure.ac:156: -1- m4_pattern_allow([^MMCLI_LIBS$])
m4trace:configure.ac:160: -1- AC_SUBST([GLIB_MKENUMS])
m4trace:configure.ac:160: -1- AC_SUBST_TRACE([GLIB_MKENUMS])
m4trace:configure.ac:160: -1- m4_pattern_allow([^GLIB_MKENUMS$])
m4trace:configure.ac:163: -1- AC_SUBST([GDBUS_CODEGEN])
m4trace:configure.ac:163: -1- AC_SUBST_TRACE([GDBUS_CODEGEN])
m4trace:configure.ac:163: -1- m4_pattern_allow([^GDBUS_CODEGEN$])
m4trace:configure.ac:166: -1- AC_SUBST([XSLTPROC_CHECK])
m4trace:configure.ac:166: -1- AC_SUBST_TRACE([XSLTPROC_CHECK])
m4trace:configure.ac:166: -1- m4_pattern_allow([^XSLTPROC_CHECK$])
m4trace:configure.ac:176: -1- AM_CONDITIONAL([CODE_COVERAGE_ENABLED], [test x$enable_code_coverage = xyes])
m4trace:configure.ac:176: -1- AC_SUBST([CODE_COVERAGE_ENABLED_TRUE])
m4trace:configure.ac:176: -1- AC_SUBST_TRACE([CODE_COVERAGE_ENABLED_TRUE])
m4trace:configure.ac:176: -1- m4_pattern_allow([^CODE_COVERAGE_ENABLED_TRUE$])
m4trace:configure.ac:176: -1- AC_SUBST([CODE_COVERAGE_ENABLED_FALSE])
m4trace:configure.ac:176: -1- AC_SUBST_TRACE([CODE_COVERAGE_ENABLED_FALSE])
m4trace:configure.ac:176: -1- m4_pattern_allow([^CODE_COVERAGE_ENABLED_FALSE$])
m4trace:configure.ac:176: -1- _AM_SUBST_NOTMAKE([CODE_COVERAGE_ENABLED_TRUE])
m4trace:configure.ac:176: -1- _AM_SUBST_NOTMAKE([CODE_COVERAGE_ENABLED_FALSE])
m4trace:configure.ac:176: -1- AC_SUBST([CODE_COVERAGE_ENABLED], [$enable_code_coverage])
m4trace:configure.ac:176: -1- AC_SUBST_TRACE([CODE_COVERAGE_ENABLED])
m4trace:configure.ac:176: -1- m4_pattern_allow([^CODE_COVERAGE_ENABLED$])
m4trace:configure.ac:176: -1- AC_SUBST([GCOV])
m4trace:configure.ac:176: -1- AC_SUBST_TRACE([GCOV])
m4trace:configure.ac:176: -1- m4_pattern_allow([^GCOV$])
m4trace:configure.ac:176: -1- AC_SUBST([GCOV])
m4trace:configure.ac:176: -1- AC_SUBST_TRACE([GCOV])
m4trace:configure.ac:176: -1- m4_pattern_allow([^GCOV$])
m4trace:configure.ac:176: -1- AC_SUBST([LCOV])
m4trace:configure.ac:176: -1- AC_SUBST_TRACE([LCOV])
m4trace:configure.ac:176: -1- m4_pattern_allow([^LCOV$])
m4trace:configure.ac:176: -1- AC_SUBST([GENHTML])
m4trace:configure.ac:176: -1- AC_SUBST_TRACE([GENHTML])
m4trace:configure.ac:176: -1- m4_pattern_allow([^GENHTML$])
m4trace:configure.ac:176: -1- AC_SUBST([CODE_COVERAGE_CFLAGS])
m4trace:configure.ac:176: -1- AC_SUBST_TRACE([CODE_COVERAGE_CFLAGS])
m4trace:configure.ac:176: -1- m4_pattern_allow([^CODE_COVERAGE_CFLAGS$])
m4trace:configure.ac:176: -1- AC_SUBST([CODE_COVERAGE_LDFLAGS])
m4trace:configure.ac:176: -1- AC_SUBST_TRACE([CODE_COVERAGE_LDFLAGS])
m4trace:configure.ac:176: -1- m4_pattern_allow([^CODE_COVERAGE_LDFLAGS$])
m4trace:configure.ac:176: -1- AC_SUBST([CODE_COVERAGE_RULES])
m4trace:configure.ac:176: -1- AC_SUBST_TRACE([CODE_COVERAGE_RULES])
m4trace:configure.ac:176: -1- m4_pattern_allow([^CODE_COVERAGE_RULES$])
m4trace:configure.ac:176: -1- _AM_SUBST_NOTMAKE([CODE_COVERAGE_RULES])
m4trace:configure.ac:183: -1- AC_SUBST([INTROSPECTION_SCANNER])
m4trace:configure.ac:183: -1- AC_SUBST_TRACE([INTROSPECTION_SCANNER])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_SCANNER$])
m4trace:configure.ac:183: -1- AC_SUBST([INTROSPECTION_COMPILER])
m4trace:configure.ac:183: -1- AC_SUBST_TRACE([INTROSPECTION_COMPILER])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_COMPILER$])
m4trace:configure.ac:183: -1- AC_SUBST([INTROSPECTION_GENERATE])
m4trace:configure.ac:183: -1- AC_SUBST_TRACE([INTROSPECTION_GENERATE])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_GENERATE$])
m4trace:configure.ac:183: -1- AC_SUBST([INTROSPECTION_GIRDIR])
m4trace:configure.ac:183: -1- AC_SUBST_TRACE([INTROSPECTION_GIRDIR])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_GIRDIR$])
m4trace:configure.ac:183: -1- AC_SUBST([INTROSPECTION_TYPELIBDIR])
m4trace:configure.ac:183: -1- AC_SUBST_TRACE([INTROSPECTION_TYPELIBDIR])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_TYPELIBDIR$])
m4trace:configure.ac:183: -1- AC_SUBST([INTROSPECTION_CFLAGS])
m4trace:configure.ac:183: -1- AC_SUBST_TRACE([INTROSPECTION_CFLAGS])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_CFLAGS$])
m4trace:configure.ac:183: -1- AC_SUBST([INTROSPECTION_LIBS])
m4trace:configure.ac:183: -1- AC_SUBST_TRACE([INTROSPECTION_LIBS])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_LIBS$])
m4trace:configure.ac:183: -1- AC_SUBST([INTROSPECTION_MAKEFILE])
m4trace:configure.ac:183: -1- AC_SUBST_TRACE([INTROSPECTION_MAKEFILE])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_MAKEFILE$])
m4trace:configure.ac:183: -1- AM_CONDITIONAL([HAVE_INTROSPECTION], [test "x$found_introspection" = "xyes"])
m4trace:configure.ac:183: -1- AC_SUBST([HAVE_INTROSPECTION_TRUE])
m4trace:configure.ac:183: -1- AC_SUBST_TRACE([HAVE_INTROSPECTION_TRUE])
m4trace:configure.ac:183: -1- m4_pattern_allow([^HAVE_INTROSPECTION_TRUE$])
m4trace:configure.ac:183: -1- AC_SUBST([HAVE_INTROSPECTION_FALSE])
m4trace:configure.ac:183: -1- AC_SUBST_TRACE([HAVE_INTROSPECTION_FALSE])
m4trace:configure.ac:183: -1- m4_pattern_allow([^HAVE_INTROSPECTION_FALSE$])
m4trace:configure.ac:183: -1- _AM_SUBST_NOTMAKE([HAVE_INTROSPECTION_TRUE])
m4trace:configure.ac:183: -1- _AM_SUBST_NOTMAKE([HAVE_INTROSPECTION_FALSE])
m4trace:configure.ac:186: -1- m4_pattern_forbid([^_?PKG_[A-Z_]+$])
m4trace:configure.ac:186: -1- m4_pattern_allow([^PKG_CONFIG(_(PATH|LIBDIR|SYSROOT_DIR|ALLOW_SYSTEM_(CFLAGS|LIBS)))?$])
m4trace:configure.ac:186: -1- m4_pattern_allow([^PKG_CONFIG_(DISABLE_UNINSTALLED|TOP_BUILD_DIR|DEBUG_SPEW)$])
m4trace:configure.ac:186: -1- AC_SUBST([PKG_CONFIG])
m4trace:configure.ac:186: -1- AC_SUBST_TRACE([PKG_CONFIG])
m4trace:configure.ac:186: -1- m4_pattern_allow([^PKG_CONFIG$])
m4trace:configure.ac:186: -1- AC_SUBST([PKG_CONFIG_PATH])
m4trace:configure.ac:186: -1- AC_SUBST_TRACE([PKG_CONFIG_PATH])
m4trace:configure.ac:186: -1- m4_pattern_allow([^PKG_CONFIG_PATH$])
m4trace:configure.ac:186: -1- AC_SUBST([PKG_CONFIG_LIBDIR])
m4trace:configure.ac:186: -1- AC_SUBST_TRACE([PKG_CONFIG_LIBDIR])
m4trace:configure.ac:186: -1- m4_pattern_allow([^PKG_CONFIG_LIBDIR$])
m4trace:configure.ac:186: -1- AC_SUBST([PKG_CONFIG])
m4trace:configure.ac:186: -1- AC_SUBST_TRACE([PKG_CONFIG])
m4trace:configure.ac:186: -1- m4_pattern_allow([^PKG_CONFIG$])
m4trace:configure.ac:186: -1- AC_SUBST([VAPIGEN])
m4trace:configure.ac:186: -1- AC_SUBST_TRACE([VAPIGEN])
m4trace:configure.ac:186: -1- m4_pattern_allow([^VAPIGEN$])
m4trace:configure.ac:186: -1- AC_SUBST([VAPIGEN_VAPIDIR])
m4trace:configure.ac:186: -1- AC_SUBST_TRACE([VAPIGEN_VAPIDIR])
m4trace:configure.ac:186: -1- m4_pattern_allow([^VAPIGEN_VAPIDIR$])
m4trace:configure.ac:186: -1- AC_SUBST([VAPIGEN_MAKEFILE])
m4trace:configure.ac:186: -1- AC_SUBST_TRACE([VAPIGEN_MAKEFILE])
m4trace:configure.ac:186: -1- m4_pattern_allow([^VAPIGEN_MAKEFILE$])
m4trace:configure.ac:186: -1- AM_CONDITIONAL([ENABLE_VAPIGEN], [test "x$enable_vala" = "xyes"])
m4trace:configure.ac:186: -1- AC_SUBST([ENABLE_VAPIGEN_TRUE])
m4trace:configure.ac:186: -1- AC_SUBST_TRACE([ENABLE_VAPIGEN_TRUE])
m4trace:configure.ac:186: -1- m4_pattern_allow([^ENABLE_VAPIGEN_TRUE$])
m4trace:configure.ac:186: -1- AC_SUBST([ENABLE_VAPIGEN_FALSE])
m4trace:configure.ac:186: -1- AC_SUBST_TRACE([ENABLE_VAPIGEN_FALSE])
m4trace:configure.ac:186: -1- m4_pattern_allow([^ENABLE_VAPIGEN_FALSE$])
m4trace:configure.ac:186: -1- _AM_SUBST_NOTMAKE([ENABLE_VAPIGEN_TRUE])
m4trace:configure.ac:186: -1- _AM_SUBST_NOTMAKE([ENABLE_VAPIGEN_FALSE])
m4trace:configure.ac:202: -1- AC_SUBST([DBUS_SYS_DIR])
m4trace:configure.ac:202: -1- AC_SUBST_TRACE([DBUS_SYS_DIR])
m4trace:configure.ac:202: -1- m4_pattern_allow([^DBUS_SYS_DIR$])
m4trace:configure.ac:211: -1- AC_SUBST([UDEV_BASE_DIR])
m4trace:configure.ac:211: -1- AC_SUBST_TRACE([UDEV_BASE_DIR])
m4trace:configure.ac:211: -1- m4_pattern_allow([^UDEV_BASE_DIR$])
m4trace:configure.ac:217: -1- AC_SUBST([SYSTEMD_UNIT_DIR], [$with_systemdsystemunitdir])
m4trace:configure.ac:217: -1- AC_SUBST_TRACE([SYSTEMD_UNIT_DIR])
m4trace:configure.ac:217: -1- m4_pattern_allow([^SYSTEMD_UNIT_DIR$])
m4trace:configure.ac:219: -1- AM_CONDITIONAL([HAVE_SYSTEMD], [test -n "$SYSTEMD_UNIT_DIR" -a "$SYSTEMD_UNIT_DIR" != xno ])
m4trace:configure.ac:219: -1- AC_SUBST([HAVE_SYSTEMD_TRUE])
m4trace:configure.ac:219: -1- AC_SUBST_TRACE([HAVE_SYSTEMD_TRUE])
m4trace:configure.ac:219: -1- m4_pattern_allow([^HAVE_SYSTEMD_TRUE$])
m4trace:configure.ac:219: -1- AC_SUBST([HAVE_SYSTEMD_FALSE])
m4trace:configure.ac:219: -1- AC_SUBST_TRACE([HAVE_SYSTEMD_FALSE])
m4trace:configure.ac:219: -1- m4_pattern_allow([^HAVE_SYSTEMD_FALSE$])
m4trace:configure.ac:219: -1- _AM_SUBST_NOTMAKE([HAVE_SYSTEMD_TRUE])
m4trace:configure.ac:219: -1- _AM_SUBST_NOTMAKE([HAVE_SYSTEMD_FALSE])
m4trace:configure.ac:228: -1- AM_CONDITIONAL([WITH_UDEV], [test "x$with_udev" = "xyes"])
m4trace:configure.ac:228: -1- AC_SUBST([WITH_UDEV_TRUE])
m4trace:configure.ac:228: -1- AC_SUBST_TRACE([WITH_UDEV_TRUE])
m4trace:configure.ac:228: -1- m4_pattern_allow([^WITH_UDEV_TRUE$])
m4trace:configure.ac:228: -1- AC_SUBST([WITH_UDEV_FALSE])
m4trace:configure.ac:228: -1- AC_SUBST_TRACE([WITH_UDEV_FALSE])
m4trace:configure.ac:228: -1- m4_pattern_allow([^WITH_UDEV_FALSE$])
m4trace:configure.ac:228: -1- _AM_SUBST_NOTMAKE([WITH_UDEV_TRUE])
m4trace:configure.ac:228: -1- _AM_SUBST_NOTMAKE([WITH_UDEV_FALSE])
m4trace:configure.ac:231: -1- AC_SUBST([GUDEV_CFLAGS])
m4trace:configure.ac:231: -1- AC_SUBST_TRACE([GUDEV_CFLAGS])
m4trace:configure.ac:231: -1- m4_pattern_allow([^GUDEV_CFLAGS$])
m4trace:configure.ac:231: -1- AC_SUBST([GUDEV_LIBS])
m4trace:configure.ac:231: -1- AC_SUBST_TRACE([GUDEV_LIBS])
m4trace:configure.ac:231: -1- m4_pattern_allow([^GUDEV_LIBS$])
m4trace:configure.ac:235: -1- AC_DEFINE_TRACE_LITERAL([WITH_UDEV])
m4trace:configure.ac:235: -1- m4_pattern_allow([^WITH_UDEV$])
m4trace:configure.ac:235: -1- AH_OUTPUT([WITH_UDEV], [/* Define if you want udev support */
@%:@undef WITH_UDEV])
m4trace:configure.ac:236: -1- AC_SUBST([GUDEV_CFLAGS])
m4trace:configure.ac:236: -1- AC_SUBST_TRACE([GUDEV_CFLAGS])
m4trace:configure.ac:236: -1- m4_pattern_allow([^GUDEV_CFLAGS$])
m4trace:configure.ac:237: -1- AC_SUBST([GUDEV_LIBS])
m4trace:configure.ac:237: -1- AC_SUBST_TRACE([GUDEV_LIBS])
m4trace:configure.ac:237: -1- m4_pattern_allow([^GUDEV_LIBS$])
m4trace:configure.ac:249: -1- AC_SUBST([LIBSYSTEMD_CFLAGS])
m4trace:configure.ac:249: -1- AC_SUBST_TRACE([LIBSYSTEMD_CFLAGS])
m4trace:configure.ac:249: -1- m4_pattern_allow([^LIBSYSTEMD_CFLAGS$])
m4trace:configure.ac:249: -1- AC_SUBST([LIBSYSTEMD_LIBS])
m4trace:configure.ac:249: -1- AC_SUBST_TRACE([LIBSYSTEMD_LIBS])
m4trace:configure.ac:249: -1- m4_pattern_allow([^LIBSYSTEMD_LIBS$])
m4trace:configure.ac:250: -1- AC_SUBST([LIBSYSTEMD_LOGIN_CFLAGS])
m4trace:configure.ac:250: -1- AC_SUBST_TRACE([LIBSYSTEMD_LOGIN_CFLAGS])
m4trace:configure.ac:250: -1- m4_pattern_allow([^LIBSYSTEMD_LOGIN_CFLAGS$])
m4trace:configure.ac:250: -1- AC_SUBST([LIBSYSTEMD_LOGIN_LIBS])
m4trace:configure.ac:250: -1- AC_SUBST_TRACE([LIBSYSTEMD_LOGIN_LIBS])
m4trace:configure.ac:250: -1- m4_pattern_allow([^LIBSYSTEMD_LOGIN_LIBS$])
m4trace:configure.ac:269: -1- AC_DEFINE_TRACE_LITERAL([WITH_SYSTEMD_SUSPEND_RESUME])
m4trace:configure.ac:269: -1- m4_pattern_allow([^WITH_SYSTEMD_SUSPEND_RESUME$])
m4trace:configure.ac:269: -1- AH_OUTPUT([WITH_SYSTEMD_SUSPEND_RESUME], [/* Define if you have systemd suspend-resume support */
@%:@undef WITH_SYSTEMD_SUSPEND_RESUME])
m4trace:configure.ac:276: -1- AM_CONDITIONAL([WITH_SYSTEMD_SUSPEND_RESUME], [test "x$with_systemd_suspend_resume" = "xyes"])
m4trace:configure.ac:276: -1- AC_SUBST([WITH_SYSTEMD_SUSPEND_RESUME_TRUE])
m4trace:configure.ac:276: -1- AC_SUBST_TRACE([WITH_SYSTEMD_SUSPEND_RESUME_TRUE])
m4trace:configure.ac:276: -1- m4_pattern_allow([^WITH_SYSTEMD_SUSPEND_RESUME_TRUE$])
m4trace:configure.ac:276: -1- AC_SUBST([WITH_SYSTEMD_SUSPEND_RESUME_FALSE])
m4trace:configure.ac:276: -1- AC_SUBST_TRACE([WITH_SYSTEMD_SUSPEND_RESUME_FALSE])
m4trace:configure.ac:276: -1- m4_pattern_allow([^WITH_SYSTEMD_SUSPEND_RESUME_FALSE$])
m4trace:configure.ac:276: -1- _AM_SUBST_NOTMAKE([WITH_SYSTEMD_SUSPEND_RESUME_TRUE])
m4trace:configure.ac:276: -1- _AM_SUBST_NOTMAKE([WITH_SYSTEMD_SUSPEND_RESUME_FALSE])
m4trace:configure.ac:300: -1- AC_DEFINE_TRACE_LITERAL([WITH_SYSTEMD_JOURNAL])
m4trace:configure.ac:300: -1- m4_pattern_allow([^WITH_SYSTEMD_JOURNAL$])
m4trace:configure.ac:300: -1- AH_OUTPUT([WITH_SYSTEMD_JOURNAL], [/* Define if you want systemd journal support */
@%:@undef WITH_SYSTEMD_JOURNAL])
m4trace:configure.ac:307: -1- AM_CONDITIONAL([WITH_SYSTEMD_JOURNAL], [test "x$with_systemd_journal" = "xyes"])
m4trace:configure.ac:307: -1- AC_SUBST([WITH_SYSTEMD_JOURNAL_TRUE])
m4trace:configure.ac:307: -1- AC_SUBST_TRACE([WITH_SYSTEMD_JOURNAL_TRUE])
m4trace:configure.ac:307: -1- m4_pattern_allow([^WITH_SYSTEMD_JOURNAL_TRUE$])
m4trace:configure.ac:307: -1- AC_SUBST([WITH_SYSTEMD_JOURNAL_FALSE])
m4trace:configure.ac:307: -1- AC_SUBST_TRACE([WITH_SYSTEMD_JOURNAL_FALSE])
m4trace:configure.ac:307: -1- m4_pattern_allow([^WITH_SYSTEMD_JOURNAL_FALSE$])
m4trace:configure.ac:307: -1- _AM_SUBST_NOTMAKE([WITH_SYSTEMD_JOURNAL_TRUE])
m4trace:configure.ac:307: -1- _AM_SUBST_NOTMAKE([WITH_SYSTEMD_JOURNAL_FALSE])
m4trace:configure.ac:313: -1- AC_SUBST([POLKIT_CFLAGS])
m4trace:configure.ac:313: -1- AC_SUBST_TRACE([POLKIT_CFLAGS])
m4trace:configure.ac:313: -1- m4_pattern_allow([^POLKIT_CFLAGS$])
m4trace:configure.ac:313: -1- AC_SUBST([POLKIT_LIBS])
m4trace:configure.ac:313: -1- AC_SUBST_TRACE([POLKIT_LIBS])
m4trace:configure.ac:313: -1- m4_pattern_allow([^POLKIT_LIBS$])
m4trace:configure.ac:349: -1- AC_DEFINE_TRACE_LITERAL([WITH_POLKIT])
m4trace:configure.ac:349: -1- m4_pattern_allow([^WITH_POLKIT$])
m4trace:configure.ac:349: -1- AH_OUTPUT([WITH_POLKIT], [/* Define if you have PolicyKit support */
@%:@undef WITH_POLKIT])
m4trace:configure.ac:350: -1- AC_SUBST([POLKIT_CFLAGS])
m4trace:configure.ac:350: -1- AC_SUBST_TRACE([POLKIT_CFLAGS])
m4trace:configure.ac:350: -1- m4_pattern_allow([^POLKIT_CFLAGS$])
m4trace:configure.ac:351: -1- AC_SUBST([POLKIT_LIBS])
m4trace:configure.ac:351: -1- AC_SUBST_TRACE([POLKIT_LIBS])
m4trace:configure.ac:351: -1- m4_pattern_allow([^POLKIT_LIBS$])
m4trace:configure.ac:352: -1- AC_SUBST([MM_DEFAULT_USER_POLICY])
m4trace:configure.ac:352: -1- AC_SUBST_TRACE([MM_DEFAULT_USER_POLICY])
m4trace:configure.ac:352: -1- m4_pattern_allow([^MM_DEFAULT_USER_POLICY$])
m4trace:configure.ac:355: -1- AC_SUBST([MM_POLKIT_SERVICE])
m4trace:configure.ac:355: -1- AC_SUBST_TRACE([MM_POLKIT_SERVICE])
m4trace:configure.ac:355: -1- m4_pattern_allow([^MM_POLKIT_SERVICE$])
m4trace:configure.ac:356: -1- AM_CONDITIONAL([WITH_POLKIT], [test "x$with_polkit" != "xno"])
m4trace:configure.ac:356: -1- AC_SUBST([WITH_POLKIT_TRUE])
m4trace:configure.ac:356: -1- AC_SUBST_TRACE([WITH_POLKIT_TRUE])
m4trace:configure.ac:356: -1- m4_pattern_allow([^WITH_POLKIT_TRUE$])
m4trace:configure.ac:356: -1- AC_SUBST([WITH_POLKIT_FALSE])
m4trace:configure.ac:356: -1- AC_SUBST_TRACE([WITH_POLKIT_FALSE])
m4trace:configure.ac:356: -1- m4_pattern_allow([^WITH_POLKIT_FALSE$])
m4trace:configure.ac:356: -1- _AM_SUBST_NOTMAKE([WITH_POLKIT_TRUE])
m4trace:configure.ac:356: -1- _AM_SUBST_NOTMAKE([WITH_POLKIT_FALSE])
m4trace:configure.ac:372: -1- AC_DEFINE_TRACE_LITERAL([WITH_AT_COMMAND_VIA_DBUS])
m4trace:configure.ac:372: -1- m4_pattern_allow([^WITH_AT_COMMAND_VIA_DBUS$])
m4trace:configure.ac:372: -1- AH_OUTPUT([WITH_AT_COMMAND_VIA_DBUS], [/* Define if you want to enable AT commands via DBus */
@%:@undef WITH_AT_COMMAND_VIA_DBUS])
m4trace:configure.ac:382: -1- AM_CONDITIONAL([WITH_MBIM], [test "x$with_mbim" = "xyes"])
m4trace:configure.ac:382: -1- AC_SUBST([WITH_MBIM_TRUE])
m4trace:configure.ac:382: -1- AC_SUBST_TRACE([WITH_MBIM_TRUE])
m4trace:configure.ac:382: -1- m4_pattern_allow([^WITH_MBIM_TRUE$])
m4trace:configure.ac:382: -1- AC_SUBST([WITH_MBIM_FALSE])
m4trace:configure.ac:382: -1- AC_SUBST_TRACE([WITH_MBIM_FALSE])
m4trace:configure.ac:382: -1- m4_pattern_allow([^WITH_MBIM_FALSE$])
m4trace:configure.ac:382: -1- _AM_SUBST_NOTMAKE([WITH_MBIM_TRUE])
m4trace:configure.ac:382: -1- _AM_SUBST_NOTMAKE([WITH_MBIM_FALSE])
m4trace:configure.ac:385: -1- AC_SUBST([MBIM_CFLAGS])
m4trace:configure.ac:385: -1- AC_SUBST_TRACE([MBIM_CFLAGS])
m4trace:configure.ac:385: -1- m4_pattern_allow([^MBIM_CFLAGS$])
m4trace:configure.ac:385: -1- AC_SUBST([MBIM_LIBS])
m4trace:configure.ac:385: -1- AC_SUBST_TRACE([MBIM_LIBS])
m4trace:configure.ac:385: -1- m4_pattern_allow([^MBIM_LIBS$])
m4trace:configure.ac:389: -1- AC_DEFINE_TRACE_LITERAL([WITH_MBIM])
m4trace:configure.ac:389: -1- m4_pattern_allow([^WITH_MBIM$])
m4trace:configure.ac:389: -1- AH_OUTPUT([WITH_MBIM], [/* Define if you want MBIM support */
@%:@undef WITH_MBIM])
m4trace:configure.ac:390: -1- AC_SUBST([MBIM_CFLAGS])
m4trace:configure.ac:390: -1- AC_SUBST_TRACE([MBIM_CFLAGS])
m4trace:configure.ac:390: -1- m4_pattern_allow([^MBIM_CFLAGS$])
m4trace:configure.ac:391: -1- AC_SUBST([MBIM_LIBS])
m4trace:configure.ac:391: -1- AC_SUBST_TRACE([MBIM_LIBS])
m4trace:configure.ac:391: -1- m4_pattern_allow([^MBIM_LIBS$])
m4trace:configure.ac:406: -1- AM_CONDITIONAL([WITH_QMI], [test "x$with_qmi" = "xyes"])
m4trace:configure.ac:406: -1- AC_SUBST([WITH_QMI_TRUE])
m4trace:configure.ac:406: -1- AC_SUBST_TRACE([WITH_QMI_TRUE])
m4trace:configure.ac:406: -1- m4_pattern_allow([^WITH_QMI_TRUE$])
m4trace:configure.ac:406: -1- AC_SUBST([WITH_QMI_FALSE])
m4trace:configure.ac:406: -1- AC_SUBST_TRACE([WITH_QMI_FALSE])
m4trace:configure.ac:406: -1- m4_pattern_allow([^WITH_QMI_FALSE$])
m4trace:configure.ac:406: -1- _AM_SUBST_NOTMAKE([WITH_QMI_TRUE])
m4trace:configure.ac:406: -1- _AM_SUBST_NOTMAKE([WITH_QMI_FALSE])
m4trace:configure.ac:409: -1- AC_SUBST([QMI_CFLAGS])
m4trace:configure.ac:409: -1- AC_SUBST_TRACE([QMI_CFLAGS])
m4trace:configure.ac:409: -1- m4_pattern_allow([^QMI_CFLAGS$])
m4trace:configure.ac:409: -1- AC_SUBST([QMI_LIBS])
m4trace:configure.ac:409: -1- AC_SUBST_TRACE([QMI_LIBS])
m4trace:configure.ac:409: -1- m4_pattern_allow([^QMI_LIBS$])
m4trace:configure.ac:413: -1- AC_DEFINE_TRACE_LITERAL([WITH_QMI])
m4trace:configure.ac:413: -1- m4_pattern_allow([^WITH_QMI$])
m4trace:configure.ac:413: -1- AH_OUTPUT([WITH_QMI], [/* Define if you want QMI support */
@%:@undef WITH_QMI])
m4trace:configure.ac:414: -1- AC_SUBST([QMI_CFLAGS])
m4trace:configure.ac:414: -1- AC_SUBST_TRACE([QMI_CFLAGS])
m4trace:configure.ac:414: -1- m4_pattern_allow([^QMI_CFLAGS$])
m4trace:configure.ac:415: -1- AC_SUBST([QMI_LIBS])
m4trace:configure.ac:415: -1- AC_SUBST_TRACE([QMI_LIBS])
m4trace:configure.ac:415: -1- m4_pattern_allow([^QMI_LIBS$])
m4trace:configure.ac:428: -1- AC_DEFINE_TRACE_LITERAL([MM_DIST_VERSION])
m4trace:configure.ac:428: -1- m4_pattern_allow([^MM_DIST_VERSION$])
m4trace:configure.ac:428: -1- AH_OUTPUT([MM_DIST_VERSION], [/* Define the distribution version string */
@%:@undef MM_DIST_VERSION])
m4trace:configure.ac:435: -1- AM_CONDITIONAL([QCDM_STANDALONE], [test "yes" = "no"])
m4trace:configure.ac:435: -1- AC_SUBST([QCDM_STANDALONE_TRUE])
m4trace:configure.ac:435: -1- AC_SUBST_TRACE([QCDM_STANDALONE_TRUE])
m4trace:configure.ac:435: -1- m4_pattern_allow([^QCDM_STANDALONE_TRUE$])
m4trace:configure.ac:435: -1- AC_SUBST([QCDM_STANDALONE_FALSE])
m4trace:configure.ac:435: -1- AC_SUBST_TRACE([QCDM_STANDALONE_FALSE])
m4trace:configure.ac:435: -1- m4_pattern_allow([^QCDM_STANDALONE_FALSE$])
m4trace:configure.ac:435: -1- _AM_SUBST_NOTMAKE([QCDM_STANDALONE_TRUE])
m4trace:configure.ac:435: -1- _AM_SUBST_NOTMAKE([QCDM_STANDALONE_FALSE])
m4trace:configure.ac:457: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_GENERIC])
m4trace:configure.ac:457: -1- m4_pattern_allow([^ENABLE_PLUGIN_GENERIC$])
m4trace:configure.ac:457: -1- AH_OUTPUT([ENABLE_PLUGIN_GENERIC], [/* Define if generic plugin is enabled */
@%:@undef ENABLE_PLUGIN_GENERIC])
m4trace:configure.ac:457: -1- AM_CONDITIONAL([ENABLE_PLUGIN_GENERIC], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:457: -1- AC_SUBST([ENABLE_PLUGIN_GENERIC_TRUE])
m4trace:configure.ac:457: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_GENERIC_TRUE])
m4trace:configure.ac:457: -1- m4_pattern_allow([^ENABLE_PLUGIN_GENERIC_TRUE$])
m4trace:configure.ac:457: -1- AC_SUBST([ENABLE_PLUGIN_GENERIC_FALSE])
m4trace:configure.ac:457: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_GENERIC_FALSE])
m4trace:configure.ac:457: -1- m4_pattern_allow([^ENABLE_PLUGIN_GENERIC_FALSE$])
m4trace:configure.ac:457: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_GENERIC_TRUE])
m4trace:configure.ac:457: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_GENERIC_FALSE])
m4trace:configure.ac:458: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_ALTAIR_LTE])
m4trace:configure.ac:458: -1- m4_pattern_allow([^ENABLE_PLUGIN_ALTAIR_LTE$])
m4trace:configure.ac:458: -1- AH_OUTPUT([ENABLE_PLUGIN_ALTAIR_LTE], [/* Define if altair-lte plugin is enabled */
@%:@undef ENABLE_PLUGIN_ALTAIR_LTE])
m4trace:configure.ac:458: -1- AM_CONDITIONAL([ENABLE_PLUGIN_ALTAIR_LTE], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:458: -1- AC_SUBST([ENABLE_PLUGIN_ALTAIR_LTE_TRUE])
m4trace:configure.ac:458: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_ALTAIR_LTE_TRUE])
m4trace:configure.ac:458: -1- m4_pattern_allow([^ENABLE_PLUGIN_ALTAIR_LTE_TRUE$])
m4trace:configure.ac:458: -1- AC_SUBST([ENABLE_PLUGIN_ALTAIR_LTE_FALSE])
m4trace:configure.ac:458: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_ALTAIR_LTE_FALSE])
m4trace:configure.ac:458: -1- m4_pattern_allow([^ENABLE_PLUGIN_ALTAIR_LTE_FALSE$])
m4trace:configure.ac:458: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ALTAIR_LTE_TRUE])
m4trace:configure.ac:458: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ALTAIR_LTE_FALSE])
m4trace:configure.ac:459: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_ANYDATA])
m4trace:configure.ac:459: -1- m4_pattern_allow([^ENABLE_PLUGIN_ANYDATA$])
m4trace:configure.ac:459: -1- AH_OUTPUT([ENABLE_PLUGIN_ANYDATA], [/* Define if anydata plugin is enabled */
@%:@undef ENABLE_PLUGIN_ANYDATA])
m4trace:configure.ac:459: -1- AM_CONDITIONAL([ENABLE_PLUGIN_ANYDATA], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:459: -1- AC_SUBST([ENABLE_PLUGIN_ANYDATA_TRUE])
m4trace:configure.ac:459: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_ANYDATA_TRUE])
m4trace:configure.ac:459: -1- m4_pattern_allow([^ENABLE_PLUGIN_ANYDATA_TRUE$])
m4trace:configure.ac:459: -1- AC_SUBST([ENABLE_PLUGIN_ANYDATA_FALSE])
m4trace:configure.ac:459: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_ANYDATA_FALSE])
m4trace:configure.ac:459: -1- m4_pattern_allow([^ENABLE_PLUGIN_ANYDATA_FALSE$])
m4trace:configure.ac:459: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ANYDATA_TRUE])
m4trace:configure.ac:459: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ANYDATA_FALSE])
m4trace:configure.ac:460: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_BROADMOBI])
m4trace:configure.ac:460: -1- m4_pattern_allow([^ENABLE_PLUGIN_BROADMOBI$])
m4trace:configure.ac:460: -1- AH_OUTPUT([ENABLE_PLUGIN_BROADMOBI], [/* Define if broadmobi plugin is enabled */
@%:@undef ENABLE_PLUGIN_BROADMOBI])
m4trace:configure.ac:460: -1- AM_CONDITIONAL([ENABLE_PLUGIN_BROADMOBI], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:460: -1- AC_SUBST([ENABLE_PLUGIN_BROADMOBI_TRUE])
m4trace:configure.ac:460: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_BROADMOBI_TRUE])
m4trace:configure.ac:460: -1- m4_pattern_allow([^ENABLE_PLUGIN_BROADMOBI_TRUE$])
m4trace:configure.ac:460: -1- AC_SUBST([ENABLE_PLUGIN_BROADMOBI_FALSE])
m4trace:configure.ac:460: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_BROADMOBI_FALSE])
m4trace:configure.ac:460: -1- m4_pattern_allow([^ENABLE_PLUGIN_BROADMOBI_FALSE$])
m4trace:configure.ac:460: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_BROADMOBI_TRUE])
m4trace:configure.ac:460: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_BROADMOBI_FALSE])
m4trace:configure.ac:461: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_CINTERION])
m4trace:configure.ac:461: -1- m4_pattern_allow([^ENABLE_PLUGIN_CINTERION$])
m4trace:configure.ac:461: -1- AH_OUTPUT([ENABLE_PLUGIN_CINTERION], [/* Define if cinterion plugin is enabled */
@%:@undef ENABLE_PLUGIN_CINTERION])
m4trace:configure.ac:461: -1- AM_CONDITIONAL([ENABLE_PLUGIN_CINTERION], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:461: -1- AC_SUBST([ENABLE_PLUGIN_CINTERION_TRUE])
m4trace:configure.ac:461: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_CINTERION_TRUE])
m4trace:configure.ac:461: -1- m4_pattern_allow([^ENABLE_PLUGIN_CINTERION_TRUE$])
m4trace:configure.ac:461: -1- AC_SUBST([ENABLE_PLUGIN_CINTERION_FALSE])
m4trace:configure.ac:461: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_CINTERION_FALSE])
m4trace:configure.ac:461: -1- m4_pattern_allow([^ENABLE_PLUGIN_CINTERION_FALSE$])
m4trace:configure.ac:461: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_CINTERION_TRUE])
m4trace:configure.ac:461: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_CINTERION_FALSE])
m4trace:configure.ac:462: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_DELL])
m4trace:configure.ac:462: -1- m4_pattern_allow([^ENABLE_PLUGIN_DELL$])
m4trace:configure.ac:462: -1- AH_OUTPUT([ENABLE_PLUGIN_DELL], [/* Define if dell plugin is enabled */
@%:@undef ENABLE_PLUGIN_DELL])
m4trace:configure.ac:462: -1- AM_CONDITIONAL([ENABLE_PLUGIN_DELL], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:462: -1- AC_SUBST([ENABLE_PLUGIN_DELL_TRUE])
m4trace:configure.ac:462: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_DELL_TRUE])
m4trace:configure.ac:462: -1- m4_pattern_allow([^ENABLE_PLUGIN_DELL_TRUE$])
m4trace:configure.ac:462: -1- AC_SUBST([ENABLE_PLUGIN_DELL_FALSE])
m4trace:configure.ac:462: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_DELL_FALSE])
m4trace:configure.ac:462: -1- m4_pattern_allow([^ENABLE_PLUGIN_DELL_FALSE$])
m4trace:configure.ac:462: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_DELL_TRUE])
m4trace:configure.ac:462: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_DELL_FALSE])
m4trace:configure.ac:468: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_DLINK])
m4trace:configure.ac:468: -1- m4_pattern_allow([^ENABLE_PLUGIN_DLINK$])
m4trace:configure.ac:468: -1- AH_OUTPUT([ENABLE_PLUGIN_DLINK], [/* Define if dlink plugin is enabled */
@%:@undef ENABLE_PLUGIN_DLINK])
m4trace:configure.ac:468: -1- AM_CONDITIONAL([ENABLE_PLUGIN_DLINK], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:468: -1- AC_SUBST([ENABLE_PLUGIN_DLINK_TRUE])
m4trace:configure.ac:468: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_DLINK_TRUE])
m4trace:configure.ac:468: -1- m4_pattern_allow([^ENABLE_PLUGIN_DLINK_TRUE$])
m4trace:configure.ac:468: -1- AC_SUBST([ENABLE_PLUGIN_DLINK_FALSE])
m4trace:configure.ac:468: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_DLINK_FALSE])
m4trace:configure.ac:468: -1- m4_pattern_allow([^ENABLE_PLUGIN_DLINK_FALSE$])
m4trace:configure.ac:468: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_DLINK_TRUE])
m4trace:configure.ac:468: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_DLINK_FALSE])
m4trace:configure.ac:471: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_FOXCONN])
m4trace:configure.ac:471: -1- m4_pattern_allow([^ENABLE_PLUGIN_FOXCONN$])
m4trace:configure.ac:471: -1- AH_OUTPUT([ENABLE_PLUGIN_FOXCONN], [/* Define if foxconn plugin is enabled */
@%:@undef ENABLE_PLUGIN_FOXCONN])
m4trace:configure.ac:471: -1- AM_CONDITIONAL([ENABLE_PLUGIN_FOXCONN], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:471: -1- AC_SUBST([ENABLE_PLUGIN_FOXCONN_TRUE])
m4trace:configure.ac:471: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_FOXCONN_TRUE])
m4trace:configure.ac:471: -1- m4_pattern_allow([^ENABLE_PLUGIN_FOXCONN_TRUE$])
m4trace:configure.ac:471: -1- AC_SUBST([ENABLE_PLUGIN_FOXCONN_FALSE])
m4trace:configure.ac:471: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_FOXCONN_FALSE])
m4trace:configure.ac:471: -1- m4_pattern_allow([^ENABLE_PLUGIN_FOXCONN_FALSE$])
m4trace:configure.ac:471: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_FOXCONN_TRUE])
m4trace:configure.ac:471: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_FOXCONN_FALSE])
m4trace:configure.ac:473: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_HAIER])
m4trace:configure.ac:473: -1- m4_pattern_allow([^ENABLE_PLUGIN_HAIER$])
m4trace:configure.ac:473: -1- AH_OUTPUT([ENABLE_PLUGIN_HAIER], [/* Define if haier plugin is enabled */
@%:@undef ENABLE_PLUGIN_HAIER])
m4trace:configure.ac:473: -1- AM_CONDITIONAL([ENABLE_PLUGIN_HAIER], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:473: -1- AC_SUBST([ENABLE_PLUGIN_HAIER_TRUE])
m4trace:configure.ac:473: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_HAIER_TRUE])
m4trace:configure.ac:473: -1- m4_pattern_allow([^ENABLE_PLUGIN_HAIER_TRUE$])
m4trace:configure.ac:473: -1- AC_SUBST([ENABLE_PLUGIN_HAIER_FALSE])
m4trace:configure.ac:473: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_HAIER_FALSE])
m4trace:configure.ac:473: -1- m4_pattern_allow([^ENABLE_PLUGIN_HAIER_FALSE$])
m4trace:configure.ac:473: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_HAIER_TRUE])
m4trace:configure.ac:473: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_HAIER_FALSE])
m4trace:configure.ac:474: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_HUAWEI])
m4trace:configure.ac:474: -1- m4_pattern_allow([^ENABLE_PLUGIN_HUAWEI$])
m4trace:configure.ac:474: -1- AH_OUTPUT([ENABLE_PLUGIN_HUAWEI], [/* Define if huawei plugin is enabled */
@%:@undef ENABLE_PLUGIN_HUAWEI])
m4trace:configure.ac:474: -1- AM_CONDITIONAL([ENABLE_PLUGIN_HUAWEI], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:474: -1- AC_SUBST([ENABLE_PLUGIN_HUAWEI_TRUE])
m4trace:configure.ac:474: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_HUAWEI_TRUE])
m4trace:configure.ac:474: -1- m4_pattern_allow([^ENABLE_PLUGIN_HUAWEI_TRUE$])
m4trace:configure.ac:474: -1- AC_SUBST([ENABLE_PLUGIN_HUAWEI_FALSE])
m4trace:configure.ac:474: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_HUAWEI_FALSE])
m4trace:configure.ac:474: -1- m4_pattern_allow([^ENABLE_PLUGIN_HUAWEI_FALSE$])
m4trace:configure.ac:474: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_HUAWEI_TRUE])
m4trace:configure.ac:474: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_HUAWEI_FALSE])
m4trace:configure.ac:475: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_IRIDIUM])
m4trace:configure.ac:475: -1- m4_pattern_allow([^ENABLE_PLUGIN_IRIDIUM$])
m4trace:configure.ac:475: -1- AH_OUTPUT([ENABLE_PLUGIN_IRIDIUM], [/* Define if iridium plugin is enabled */
@%:@undef ENABLE_PLUGIN_IRIDIUM])
m4trace:configure.ac:475: -1- AM_CONDITIONAL([ENABLE_PLUGIN_IRIDIUM], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:475: -1- AC_SUBST([ENABLE_PLUGIN_IRIDIUM_TRUE])
m4trace:configure.ac:475: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_IRIDIUM_TRUE])
m4trace:configure.ac:475: -1- m4_pattern_allow([^ENABLE_PLUGIN_IRIDIUM_TRUE$])
m4trace:configure.ac:475: -1- AC_SUBST([ENABLE_PLUGIN_IRIDIUM_FALSE])
m4trace:configure.ac:475: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_IRIDIUM_FALSE])
m4trace:configure.ac:475: -1- m4_pattern_allow([^ENABLE_PLUGIN_IRIDIUM_FALSE$])
m4trace:configure.ac:475: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_IRIDIUM_TRUE])
m4trace:configure.ac:475: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_IRIDIUM_FALSE])
m4trace:configure.ac:476: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_LINKTOP])
m4trace:configure.ac:476: -1- m4_pattern_allow([^ENABLE_PLUGIN_LINKTOP$])
m4trace:configure.ac:476: -1- AH_OUTPUT([ENABLE_PLUGIN_LINKTOP], [/* Define if linktop plugin is enabled */
@%:@undef ENABLE_PLUGIN_LINKTOP])
m4trace:configure.ac:476: -1- AM_CONDITIONAL([ENABLE_PLUGIN_LINKTOP], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:476: -1- AC_SUBST([ENABLE_PLUGIN_LINKTOP_TRUE])
m4trace:configure.ac:476: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_LINKTOP_TRUE])
m4trace:configure.ac:476: -1- m4_pattern_allow([^ENABLE_PLUGIN_LINKTOP_TRUE$])
m4trace:configure.ac:476: -1- AC_SUBST([ENABLE_PLUGIN_LINKTOP_FALSE])
m4trace:configure.ac:476: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_LINKTOP_FALSE])
m4trace:configure.ac:476: -1- m4_pattern_allow([^ENABLE_PLUGIN_LINKTOP_FALSE$])
m4trace:configure.ac:476: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_LINKTOP_TRUE])
m4trace:configure.ac:476: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_LINKTOP_FALSE])
m4trace:configure.ac:477: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_LONGCHEER])
m4trace:configure.ac:477: -1- m4_pattern_allow([^ENABLE_PLUGIN_LONGCHEER$])
m4trace:configure.ac:477: -1- AH_OUTPUT([ENABLE_PLUGIN_LONGCHEER], [/* Define if longcheer plugin is enabled */
@%:@undef ENABLE_PLUGIN_LONGCHEER])
m4trace:configure.ac:477: -1- AM_CONDITIONAL([ENABLE_PLUGIN_LONGCHEER], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:477: -1- AC_SUBST([ENABLE_PLUGIN_LONGCHEER_TRUE])
m4trace:configure.ac:477: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_LONGCHEER_TRUE])
m4trace:configure.ac:477: -1- m4_pattern_allow([^ENABLE_PLUGIN_LONGCHEER_TRUE$])
m4trace:configure.ac:477: -1- AC_SUBST([ENABLE_PLUGIN_LONGCHEER_FALSE])
m4trace:configure.ac:477: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_LONGCHEER_FALSE])
m4trace:configure.ac:477: -1- m4_pattern_allow([^ENABLE_PLUGIN_LONGCHEER_FALSE$])
m4trace:configure.ac:477: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_LONGCHEER_TRUE])
m4trace:configure.ac:477: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_LONGCHEER_FALSE])
m4trace:configure.ac:478: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_MBM])
m4trace:configure.ac:478: -1- m4_pattern_allow([^ENABLE_PLUGIN_MBM$])
m4trace:configure.ac:478: -1- AH_OUTPUT([ENABLE_PLUGIN_MBM], [/* Define if mbm plugin is enabled */
@%:@undef ENABLE_PLUGIN_MBM])
m4trace:configure.ac:478: -1- AM_CONDITIONAL([ENABLE_PLUGIN_MBM], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:478: -1- AC_SUBST([ENABLE_PLUGIN_MBM_TRUE])
m4trace:configure.ac:478: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_MBM_TRUE])
m4trace:configure.ac:478: -1- m4_pattern_allow([^ENABLE_PLUGIN_MBM_TRUE$])
m4trace:configure.ac:478: -1- AC_SUBST([ENABLE_PLUGIN_MBM_FALSE])
m4trace:configure.ac:478: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_MBM_FALSE])
m4trace:configure.ac:478: -1- m4_pattern_allow([^ENABLE_PLUGIN_MBM_FALSE$])
m4trace:configure.ac:478: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_MBM_TRUE])
m4trace:configure.ac:478: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_MBM_FALSE])
m4trace:configure.ac:479: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_MOTOROLA])
m4trace:configure.ac:479: -1- m4_pattern_allow([^ENABLE_PLUGIN_MOTOROLA$])
m4trace:configure.ac:479: -1- AH_OUTPUT([ENABLE_PLUGIN_MOTOROLA], [/* Define if motorola plugin is enabled */
@%:@undef ENABLE_PLUGIN_MOTOROLA])
m4trace:configure.ac:479: -1- AM_CONDITIONAL([ENABLE_PLUGIN_MOTOROLA], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:479: -1- AC_SUBST([ENABLE_PLUGIN_MOTOROLA_TRUE])
m4trace:configure.ac:479: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_MOTOROLA_TRUE])
m4trace:configure.ac:479: -1- m4_pattern_allow([^ENABLE_PLUGIN_MOTOROLA_TRUE$])
m4trace:configure.ac:479: -1- AC_SUBST([ENABLE_PLUGIN_MOTOROLA_FALSE])
m4trace:configure.ac:479: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_MOTOROLA_FALSE])
m4trace:configure.ac:479: -1- m4_pattern_allow([^ENABLE_PLUGIN_MOTOROLA_FALSE$])
m4trace:configure.ac:479: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_MOTOROLA_TRUE])
m4trace:configure.ac:479: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_MOTOROLA_FALSE])
m4trace:configure.ac:480: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_MTK])
m4trace:configure.ac:480: -1- m4_pattern_allow([^ENABLE_PLUGIN_MTK$])
m4trace:configure.ac:480: -1- AH_OUTPUT([ENABLE_PLUGIN_MTK], [/* Define if mtk plugin is enabled */
@%:@undef ENABLE_PLUGIN_MTK])
m4trace:configure.ac:480: -1- AM_CONDITIONAL([ENABLE_PLUGIN_MTK], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:480: -1- AC_SUBST([ENABLE_PLUGIN_MTK_TRUE])
m4trace:configure.ac:480: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_MTK_TRUE])
m4trace:configure.ac:480: -1- m4_pattern_allow([^ENABLE_PLUGIN_MTK_TRUE$])
m4trace:configure.ac:480: -1- AC_SUBST([ENABLE_PLUGIN_MTK_FALSE])
m4trace:configure.ac:480: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_MTK_FALSE])
m4trace:configure.ac:480: -1- m4_pattern_allow([^ENABLE_PLUGIN_MTK_FALSE$])
m4trace:configure.ac:480: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_MTK_TRUE])
m4trace:configure.ac:480: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_MTK_FALSE])
m4trace:configure.ac:481: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_NOKIA])
m4trace:configure.ac:481: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOKIA$])
m4trace:configure.ac:481: -1- AH_OUTPUT([ENABLE_PLUGIN_NOKIA], [/* Define if nokia plugin is enabled */
@%:@undef ENABLE_PLUGIN_NOKIA])
m4trace:configure.ac:481: -1- AM_CONDITIONAL([ENABLE_PLUGIN_NOKIA], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:481: -1- AC_SUBST([ENABLE_PLUGIN_NOKIA_TRUE])
m4trace:configure.ac:481: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_NOKIA_TRUE])
m4trace:configure.ac:481: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOKIA_TRUE$])
m4trace:configure.ac:481: -1- AC_SUBST([ENABLE_PLUGIN_NOKIA_FALSE])
m4trace:configure.ac:481: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_NOKIA_FALSE])
m4trace:configure.ac:481: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOKIA_FALSE$])
m4trace:configure.ac:481: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOKIA_TRUE])
m4trace:configure.ac:481: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOKIA_FALSE])
m4trace:configure.ac:482: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_NOKIA_ICERA])
m4trace:configure.ac:482: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOKIA_ICERA$])
m4trace:configure.ac:482: -1- AH_OUTPUT([ENABLE_PLUGIN_NOKIA_ICERA], [/* Define if nokia-icera plugin is enabled */
@%:@undef ENABLE_PLUGIN_NOKIA_ICERA])
m4trace:configure.ac:482: -1- AM_CONDITIONAL([ENABLE_PLUGIN_NOKIA_ICERA], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:482: -1- AC_SUBST([ENABLE_PLUGIN_NOKIA_ICERA_TRUE])
m4trace:configure.ac:482: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_NOKIA_ICERA_TRUE])
m4trace:configure.ac:482: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOKIA_ICERA_TRUE$])
m4trace:configure.ac:482: -1- AC_SUBST([ENABLE_PLUGIN_NOKIA_ICERA_FALSE])
m4trace:configure.ac:482: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_NOKIA_ICERA_FALSE])
m4trace:configure.ac:482: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOKIA_ICERA_FALSE$])
m4trace:configure.ac:482: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOKIA_ICERA_TRUE])
m4trace:configure.ac:482: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOKIA_ICERA_FALSE])
m4trace:configure.ac:484: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_NOVATEL])
m4trace:configure.ac:484: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOVATEL$])
m4trace:configure.ac:484: -1- AH_OUTPUT([ENABLE_PLUGIN_NOVATEL], [/* Define if novatel plugin is enabled */
@%:@undef ENABLE_PLUGIN_NOVATEL])
m4trace:configure.ac:484: -1- AM_CONDITIONAL([ENABLE_PLUGIN_NOVATEL], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:484: -1- AC_SUBST([ENABLE_PLUGIN_NOVATEL_TRUE])
m4trace:configure.ac:484: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_NOVATEL_TRUE])
m4trace:configure.ac:484: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOVATEL_TRUE$])
m4trace:configure.ac:484: -1- AC_SUBST([ENABLE_PLUGIN_NOVATEL_FALSE])
m4trace:configure.ac:484: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_NOVATEL_FALSE])
m4trace:configure.ac:484: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOVATEL_FALSE$])
m4trace:configure.ac:484: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOVATEL_TRUE])
m4trace:configure.ac:484: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOVATEL_FALSE])
m4trace:configure.ac:486: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_NOVATEL_LTE])
m4trace:configure.ac:486: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOVATEL_LTE$])
m4trace:configure.ac:486: -1- AH_OUTPUT([ENABLE_PLUGIN_NOVATEL_LTE], [/* Define if novatel-lte plugin is enabled */
@%:@undef ENABLE_PLUGIN_NOVATEL_LTE])
m4trace:configure.ac:486: -1- AM_CONDITIONAL([ENABLE_PLUGIN_NOVATEL_LTE], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:486: -1- AC_SUBST([ENABLE_PLUGIN_NOVATEL_LTE_TRUE])
m4trace:configure.ac:486: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_NOVATEL_LTE_TRUE])
m4trace:configure.ac:486: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOVATEL_LTE_TRUE$])
m4trace:configure.ac:486: -1- AC_SUBST([ENABLE_PLUGIN_NOVATEL_LTE_FALSE])
m4trace:configure.ac:486: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_NOVATEL_LTE_FALSE])
m4trace:configure.ac:486: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOVATEL_LTE_FALSE$])
m4trace:configure.ac:486: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOVATEL_LTE_TRUE])
m4trace:configure.ac:486: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOVATEL_LTE_FALSE])
m4trace:configure.ac:487: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_OPTION])
m4trace:configure.ac:487: -1- m4_pattern_allow([^ENABLE_PLUGIN_OPTION$])
m4trace:configure.ac:487: -1- AH_OUTPUT([ENABLE_PLUGIN_OPTION], [/* Define if option plugin is enabled */
@%:@undef ENABLE_PLUGIN_OPTION])
m4trace:configure.ac:487: -1- AM_CONDITIONAL([ENABLE_PLUGIN_OPTION], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:487: -1- AC_SUBST([ENABLE_PLUGIN_OPTION_TRUE])
m4trace:configure.ac:487: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_OPTION_TRUE])
m4trace:configure.ac:487: -1- m4_pattern_allow([^ENABLE_PLUGIN_OPTION_TRUE$])
m4trace:configure.ac:487: -1- AC_SUBST([ENABLE_PLUGIN_OPTION_FALSE])
m4trace:configure.ac:487: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_OPTION_FALSE])
m4trace:configure.ac:487: -1- m4_pattern_allow([^ENABLE_PLUGIN_OPTION_FALSE$])
m4trace:configure.ac:487: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_OPTION_TRUE])
m4trace:configure.ac:487: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_OPTION_FALSE])
m4trace:configure.ac:489: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_OPTION_HSO])
m4trace:configure.ac:489: -1- m4_pattern_allow([^ENABLE_PLUGIN_OPTION_HSO$])
m4trace:configure.ac:489: -1- AH_OUTPUT([ENABLE_PLUGIN_OPTION_HSO], [/* Define if option-hso plugin is enabled */
@%:@undef ENABLE_PLUGIN_OPTION_HSO])
m4trace:configure.ac:489: -1- AM_CONDITIONAL([ENABLE_PLUGIN_OPTION_HSO], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:489: -1- AC_SUBST([ENABLE_PLUGIN_OPTION_HSO_TRUE])
m4trace:configure.ac:489: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_OPTION_HSO_TRUE])
m4trace:configure.ac:489: -1- m4_pattern_allow([^ENABLE_PLUGIN_OPTION_HSO_TRUE$])
m4trace:configure.ac:489: -1- AC_SUBST([ENABLE_PLUGIN_OPTION_HSO_FALSE])
m4trace:configure.ac:489: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_OPTION_HSO_FALSE])
m4trace:configure.ac:489: -1- m4_pattern_allow([^ENABLE_PLUGIN_OPTION_HSO_FALSE$])
m4trace:configure.ac:489: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_OPTION_HSO_TRUE])
m4trace:configure.ac:489: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_OPTION_HSO_FALSE])
m4trace:configure.ac:491: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_PANTECH])
m4trace:configure.ac:491: -1- m4_pattern_allow([^ENABLE_PLUGIN_PANTECH$])
m4trace:configure.ac:491: -1- AH_OUTPUT([ENABLE_PLUGIN_PANTECH], [/* Define if pantech plugin is enabled */
@%:@undef ENABLE_PLUGIN_PANTECH])
m4trace:configure.ac:491: -1- AM_CONDITIONAL([ENABLE_PLUGIN_PANTECH], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:491: -1- AC_SUBST([ENABLE_PLUGIN_PANTECH_TRUE])
m4trace:configure.ac:491: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_PANTECH_TRUE])
m4trace:configure.ac:491: -1- m4_pattern_allow([^ENABLE_PLUGIN_PANTECH_TRUE$])
m4trace:configure.ac:491: -1- AC_SUBST([ENABLE_PLUGIN_PANTECH_FALSE])
m4trace:configure.ac:491: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_PANTECH_FALSE])
m4trace:configure.ac:491: -1- m4_pattern_allow([^ENABLE_PLUGIN_PANTECH_FALSE$])
m4trace:configure.ac:491: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_PANTECH_TRUE])
m4trace:configure.ac:491: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_PANTECH_FALSE])
m4trace:configure.ac:492: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_QUECTEL])
m4trace:configure.ac:492: -1- m4_pattern_allow([^ENABLE_PLUGIN_QUECTEL$])
m4trace:configure.ac:492: -1- AH_OUTPUT([ENABLE_PLUGIN_QUECTEL], [/* Define if quectel plugin is enabled */
@%:@undef ENABLE_PLUGIN_QUECTEL])
m4trace:configure.ac:492: -1- AM_CONDITIONAL([ENABLE_PLUGIN_QUECTEL], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:492: -1- AC_SUBST([ENABLE_PLUGIN_QUECTEL_TRUE])
m4trace:configure.ac:492: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_QUECTEL_TRUE])
m4trace:configure.ac:492: -1- m4_pattern_allow([^ENABLE_PLUGIN_QUECTEL_TRUE$])
m4trace:configure.ac:492: -1- AC_SUBST([ENABLE_PLUGIN_QUECTEL_FALSE])
m4trace:configure.ac:492: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_QUECTEL_FALSE])
m4trace:configure.ac:492: -1- m4_pattern_allow([^ENABLE_PLUGIN_QUECTEL_FALSE$])
m4trace:configure.ac:492: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_QUECTEL_TRUE])
m4trace:configure.ac:492: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_QUECTEL_FALSE])
m4trace:configure.ac:493: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_SAMSUNG])
m4trace:configure.ac:493: -1- m4_pattern_allow([^ENABLE_PLUGIN_SAMSUNG$])
m4trace:configure.ac:493: -1- AH_OUTPUT([ENABLE_PLUGIN_SAMSUNG], [/* Define if samsung plugin is enabled */
@%:@undef ENABLE_PLUGIN_SAMSUNG])
m4trace:configure.ac:493: -1- AM_CONDITIONAL([ENABLE_PLUGIN_SAMSUNG], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:493: -1- AC_SUBST([ENABLE_PLUGIN_SAMSUNG_TRUE])
m4trace:configure.ac:493: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_SAMSUNG_TRUE])
m4trace:configure.ac:493: -1- m4_pattern_allow([^ENABLE_PLUGIN_SAMSUNG_TRUE$])
m4trace:configure.ac:493: -1- AC_SUBST([ENABLE_PLUGIN_SAMSUNG_FALSE])
m4trace:configure.ac:493: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_SAMSUNG_FALSE])
m4trace:configure.ac:493: -1- m4_pattern_allow([^ENABLE_PLUGIN_SAMSUNG_FALSE$])
m4trace:configure.ac:493: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SAMSUNG_TRUE])
m4trace:configure.ac:493: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SAMSUNG_FALSE])
m4trace:configure.ac:495: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_SIERRA_LEGACY])
m4trace:configure.ac:495: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIERRA_LEGACY$])
m4trace:configure.ac:495: -1- AH_OUTPUT([ENABLE_PLUGIN_SIERRA_LEGACY], [/* Define if sierra-legacy plugin is enabled */
@%:@undef ENABLE_PLUGIN_SIERRA_LEGACY])
m4trace:configure.ac:495: -1- AM_CONDITIONAL([ENABLE_PLUGIN_SIERRA_LEGACY], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:495: -1- AC_SUBST([ENABLE_PLUGIN_SIERRA_LEGACY_TRUE])
m4trace:configure.ac:495: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_SIERRA_LEGACY_TRUE])
m4trace:configure.ac:495: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIERRA_LEGACY_TRUE$])
m4trace:configure.ac:495: -1- AC_SUBST([ENABLE_PLUGIN_SIERRA_LEGACY_FALSE])
m4trace:configure.ac:495: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_SIERRA_LEGACY_FALSE])
m4trace:configure.ac:495: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIERRA_LEGACY_FALSE$])
m4trace:configure.ac:495: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SIERRA_LEGACY_TRUE])
m4trace:configure.ac:495: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SIERRA_LEGACY_FALSE])
m4trace:configure.ac:498: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_SIERRA])
m4trace:configure.ac:498: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIERRA$])
m4trace:configure.ac:498: -1- AH_OUTPUT([ENABLE_PLUGIN_SIERRA], [/* Define if sierra plugin is enabled */
@%:@undef ENABLE_PLUGIN_SIERRA])
m4trace:configure.ac:498: -1- AM_CONDITIONAL([ENABLE_PLUGIN_SIERRA], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:498: -1- AC_SUBST([ENABLE_PLUGIN_SIERRA_TRUE])
m4trace:configure.ac:498: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_SIERRA_TRUE])
m4trace:configure.ac:498: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIERRA_TRUE$])
m4trace:configure.ac:498: -1- AC_SUBST([ENABLE_PLUGIN_SIERRA_FALSE])
m4trace:configure.ac:498: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_SIERRA_FALSE])
m4trace:configure.ac:498: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIERRA_FALSE$])
m4trace:configure.ac:498: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SIERRA_TRUE])
m4trace:configure.ac:498: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SIERRA_FALSE])
m4trace:configure.ac:499: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_SIMTECH])
m4trace:configure.ac:499: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIMTECH$])
m4trace:configure.ac:499: -1- AH_OUTPUT([ENABLE_PLUGIN_SIMTECH], [/* Define if simtech plugin is enabled */
@%:@undef ENABLE_PLUGIN_SIMTECH])
m4trace:configure.ac:499: -1- AM_CONDITIONAL([ENABLE_PLUGIN_SIMTECH], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:499: -1- AC_SUBST([ENABLE_PLUGIN_SIMTECH_TRUE])
m4trace:configure.ac:499: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_SIMTECH_TRUE])
m4trace:configure.ac:499: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIMTECH_TRUE$])
m4trace:configure.ac:499: -1- AC_SUBST([ENABLE_PLUGIN_SIMTECH_FALSE])
m4trace:configure.ac:499: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_SIMTECH_FALSE])
m4trace:configure.ac:499: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIMTECH_FALSE$])
m4trace:configure.ac:499: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SIMTECH_TRUE])
m4trace:configure.ac:499: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SIMTECH_FALSE])
m4trace:configure.ac:500: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_TELIT])
m4trace:configure.ac:500: -1- m4_pattern_allow([^ENABLE_PLUGIN_TELIT$])
m4trace:configure.ac:500: -1- AH_OUTPUT([ENABLE_PLUGIN_TELIT], [/* Define if telit plugin is enabled */
@%:@undef ENABLE_PLUGIN_TELIT])
m4trace:configure.ac:500: -1- AM_CONDITIONAL([ENABLE_PLUGIN_TELIT], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:500: -1- AC_SUBST([ENABLE_PLUGIN_TELIT_TRUE])
m4trace:configure.ac:500: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_TELIT_TRUE])
m4trace:configure.ac:500: -1- m4_pattern_allow([^ENABLE_PLUGIN_TELIT_TRUE$])
m4trace:configure.ac:500: -1- AC_SUBST([ENABLE_PLUGIN_TELIT_FALSE])
m4trace:configure.ac:500: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_TELIT_FALSE])
m4trace:configure.ac:500: -1- m4_pattern_allow([^ENABLE_PLUGIN_TELIT_FALSE$])
m4trace:configure.ac:500: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_TELIT_TRUE])
m4trace:configure.ac:500: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_TELIT_FALSE])
m4trace:configure.ac:502: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_THURAYA])
m4trace:configure.ac:502: -1- m4_pattern_allow([^ENABLE_PLUGIN_THURAYA$])
m4trace:configure.ac:502: -1- AH_OUTPUT([ENABLE_PLUGIN_THURAYA], [/* Define if thuraya plugin is enabled */
@%:@undef ENABLE_PLUGIN_THURAYA])
m4trace:configure.ac:502: -1- AM_CONDITIONAL([ENABLE_PLUGIN_THURAYA], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:502: -1- AC_SUBST([ENABLE_PLUGIN_THURAYA_TRUE])
m4trace:configure.ac:502: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_THURAYA_TRUE])
m4trace:configure.ac:502: -1- m4_pattern_allow([^ENABLE_PLUGIN_THURAYA_TRUE$])
m4trace:configure.ac:502: -1- AC_SUBST([ENABLE_PLUGIN_THURAYA_FALSE])
m4trace:configure.ac:502: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_THURAYA_FALSE])
m4trace:configure.ac:502: -1- m4_pattern_allow([^ENABLE_PLUGIN_THURAYA_FALSE$])
m4trace:configure.ac:502: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_THURAYA_TRUE])
m4trace:configure.ac:502: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_THURAYA_FALSE])
m4trace:configure.ac:503: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_TPLINK])
m4trace:configure.ac:503: -1- m4_pattern_allow([^ENABLE_PLUGIN_TPLINK$])
m4trace:configure.ac:503: -1- AH_OUTPUT([ENABLE_PLUGIN_TPLINK], [/* Define if tplink plugin is enabled */
@%:@undef ENABLE_PLUGIN_TPLINK])
m4trace:configure.ac:503: -1- AM_CONDITIONAL([ENABLE_PLUGIN_TPLINK], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:503: -1- AC_SUBST([ENABLE_PLUGIN_TPLINK_TRUE])
m4trace:configure.ac:503: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_TPLINK_TRUE])
m4trace:configure.ac:503: -1- m4_pattern_allow([^ENABLE_PLUGIN_TPLINK_TRUE$])
m4trace:configure.ac:503: -1- AC_SUBST([ENABLE_PLUGIN_TPLINK_FALSE])
m4trace:configure.ac:503: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_TPLINK_FALSE])
m4trace:configure.ac:503: -1- m4_pattern_allow([^ENABLE_PLUGIN_TPLINK_FALSE$])
m4trace:configure.ac:503: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_TPLINK_TRUE])
m4trace:configure.ac:503: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_TPLINK_FALSE])
m4trace:configure.ac:504: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_UBLOX])
m4trace:configure.ac:504: -1- m4_pattern_allow([^ENABLE_PLUGIN_UBLOX$])
m4trace:configure.ac:504: -1- AH_OUTPUT([ENABLE_PLUGIN_UBLOX], [/* Define if ublox plugin is enabled */
@%:@undef ENABLE_PLUGIN_UBLOX])
m4trace:configure.ac:504: -1- AM_CONDITIONAL([ENABLE_PLUGIN_UBLOX], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:504: -1- AC_SUBST([ENABLE_PLUGIN_UBLOX_TRUE])
m4trace:configure.ac:504: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_UBLOX_TRUE])
m4trace:configure.ac:504: -1- m4_pattern_allow([^ENABLE_PLUGIN_UBLOX_TRUE$])
m4trace:configure.ac:504: -1- AC_SUBST([ENABLE_PLUGIN_UBLOX_FALSE])
m4trace:configure.ac:504: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_UBLOX_FALSE])
m4trace:configure.ac:504: -1- m4_pattern_allow([^ENABLE_PLUGIN_UBLOX_FALSE$])
m4trace:configure.ac:504: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_UBLOX_TRUE])
m4trace:configure.ac:504: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_UBLOX_FALSE])
m4trace:configure.ac:505: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_VIA])
m4trace:configure.ac:505: -1- m4_pattern_allow([^ENABLE_PLUGIN_VIA$])
m4trace:configure.ac:505: -1- AH_OUTPUT([ENABLE_PLUGIN_VIA], [/* Define if via plugin is enabled */
@%:@undef ENABLE_PLUGIN_VIA])
m4trace:configure.ac:505: -1- AM_CONDITIONAL([ENABLE_PLUGIN_VIA], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:505: -1- AC_SUBST([ENABLE_PLUGIN_VIA_TRUE])
m4trace:configure.ac:505: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_VIA_TRUE])
m4trace:configure.ac:505: -1- m4_pattern_allow([^ENABLE_PLUGIN_VIA_TRUE$])
m4trace:configure.ac:505: -1- AC_SUBST([ENABLE_PLUGIN_VIA_FALSE])
m4trace:configure.ac:505: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_VIA_FALSE])
m4trace:configure.ac:505: -1- m4_pattern_allow([^ENABLE_PLUGIN_VIA_FALSE$])
m4trace:configure.ac:505: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_VIA_TRUE])
m4trace:configure.ac:505: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_VIA_FALSE])
m4trace:configure.ac:506: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_WAVECOM])
m4trace:configure.ac:506: -1- m4_pattern_allow([^ENABLE_PLUGIN_WAVECOM$])
m4trace:configure.ac:506: -1- AH_OUTPUT([ENABLE_PLUGIN_WAVECOM], [/* Define if wavecom plugin is enabled */
@%:@undef ENABLE_PLUGIN_WAVECOM])
m4trace:configure.ac:506: -1- AM_CONDITIONAL([ENABLE_PLUGIN_WAVECOM], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:506: -1- AC_SUBST([ENABLE_PLUGIN_WAVECOM_TRUE])
m4trace:configure.ac:506: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_WAVECOM_TRUE])
m4trace:configure.ac:506: -1- m4_pattern_allow([^ENABLE_PLUGIN_WAVECOM_TRUE$])
m4trace:configure.ac:506: -1- AC_SUBST([ENABLE_PLUGIN_WAVECOM_FALSE])
m4trace:configure.ac:506: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_WAVECOM_FALSE])
m4trace:configure.ac:506: -1- m4_pattern_allow([^ENABLE_PLUGIN_WAVECOM_FALSE$])
m4trace:configure.ac:506: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_WAVECOM_TRUE])
m4trace:configure.ac:506: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_WAVECOM_FALSE])
m4trace:configure.ac:507: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_X22X])
m4trace:configure.ac:507: -1- m4_pattern_allow([^ENABLE_PLUGIN_X22X$])
m4trace:configure.ac:507: -1- AH_OUTPUT([ENABLE_PLUGIN_X22X], [/* Define if x22x plugin is enabled */
@%:@undef ENABLE_PLUGIN_X22X])
m4trace:configure.ac:507: -1- AM_CONDITIONAL([ENABLE_PLUGIN_X22X], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:507: -1- AC_SUBST([ENABLE_PLUGIN_X22X_TRUE])
m4trace:configure.ac:507: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_X22X_TRUE])
m4trace:configure.ac:507: -1- m4_pattern_allow([^ENABLE_PLUGIN_X22X_TRUE$])
m4trace:configure.ac:507: -1- AC_SUBST([ENABLE_PLUGIN_X22X_FALSE])
m4trace:configure.ac:507: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_X22X_FALSE])
m4trace:configure.ac:507: -1- m4_pattern_allow([^ENABLE_PLUGIN_X22X_FALSE$])
m4trace:configure.ac:507: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_X22X_TRUE])
m4trace:configure.ac:507: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_X22X_FALSE])
m4trace:configure.ac:508: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_ZTE])
m4trace:configure.ac:508: -1- m4_pattern_allow([^ENABLE_PLUGIN_ZTE$])
m4trace:configure.ac:508: -1- AH_OUTPUT([ENABLE_PLUGIN_ZTE], [/* Define if zte plugin is enabled */
@%:@undef ENABLE_PLUGIN_ZTE])
m4trace:configure.ac:508: -1- AM_CONDITIONAL([ENABLE_PLUGIN_ZTE], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:508: -1- AC_SUBST([ENABLE_PLUGIN_ZTE_TRUE])
m4trace:configure.ac:508: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_ZTE_TRUE])
m4trace:configure.ac:508: -1- m4_pattern_allow([^ENABLE_PLUGIN_ZTE_TRUE$])
m4trace:configure.ac:508: -1- AC_SUBST([ENABLE_PLUGIN_ZTE_FALSE])
m4trace:configure.ac:508: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_ZTE_FALSE])
m4trace:configure.ac:508: -1- m4_pattern_allow([^ENABLE_PLUGIN_ZTE_FALSE$])
m4trace:configure.ac:508: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ZTE_TRUE])
m4trace:configure.ac:508: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ZTE_FALSE])
m4trace:configure.ac:510: -1- AC_DEFINE_TRACE_LITERAL([ENABLE_PLUGIN_ME3630])
m4trace:configure.ac:510: -1- m4_pattern_allow([^ENABLE_PLUGIN_ME3630$])
m4trace:configure.ac:510: -1- AH_OUTPUT([ENABLE_PLUGIN_ME3630], [/* Define if me3630 plugin is enabled */
@%:@undef ENABLE_PLUGIN_ME3630])
m4trace:configure.ac:510: -1- AM_CONDITIONAL([ENABLE_PLUGIN_ME3630], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:510: -1- AC_SUBST([ENABLE_PLUGIN_ME3630_TRUE])
m4trace:configure.ac:510: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_ME3630_TRUE])
m4trace:configure.ac:510: -1- m4_pattern_allow([^ENABLE_PLUGIN_ME3630_TRUE$])
m4trace:configure.ac:510: -1- AC_SUBST([ENABLE_PLUGIN_ME3630_FALSE])
m4trace:configure.ac:510: -1- AC_SUBST_TRACE([ENABLE_PLUGIN_ME3630_FALSE])
m4trace:configure.ac:510: -1- m4_pattern_allow([^ENABLE_PLUGIN_ME3630_FALSE$])
m4trace:configure.ac:510: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ME3630_TRUE])
m4trace:configure.ac:510: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ME3630_FALSE])
m4trace:configure.ac:512: -1- AM_CONDITIONAL([WITH_SHARED_ICERA], [test "x$with_shared_icera" = "xyes"])
m4trace:configure.ac:512: -1- AC_SUBST([WITH_SHARED_ICERA_TRUE])
m4trace:configure.ac:512: -1- AC_SUBST_TRACE([WITH_SHARED_ICERA_TRUE])
m4trace:configure.ac:512: -1- m4_pattern_allow([^WITH_SHARED_ICERA_TRUE$])
m4trace:configure.ac:512: -1- AC_SUBST([WITH_SHARED_ICERA_FALSE])
m4trace:configure.ac:512: -1- AC_SUBST_TRACE([WITH_SHARED_ICERA_FALSE])
m4trace:configure.ac:512: -1- m4_pattern_allow([^WITH_SHARED_ICERA_FALSE$])
m4trace:configure.ac:512: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_ICERA_TRUE])
m4trace:configure.ac:512: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_ICERA_FALSE])
m4trace:configure.ac:512: -1- AC_DEFINE_TRACE_LITERAL([WITH_SHARED_ICERA])
m4trace:configure.ac:512: -1- m4_pattern_allow([^WITH_SHARED_ICERA$])
m4trace:configure.ac:512: -1- AH_OUTPUT([WITH_SHARED_ICERA], [/* Define if icera utils are built */
@%:@undef WITH_SHARED_ICERA])
m4trace:configure.ac:513: -1- AM_CONDITIONAL([WITH_SHARED_SIERRA], [test "x$with_shared_sierra" = "xyes"])
m4trace:configure.ac:513: -1- AC_SUBST([WITH_SHARED_SIERRA_TRUE])
m4trace:configure.ac:513: -1- AC_SUBST_TRACE([WITH_SHARED_SIERRA_TRUE])
m4trace:configure.ac:513: -1- m4_pattern_allow([^WITH_SHARED_SIERRA_TRUE$])
m4trace:configure.ac:513: -1- AC_SUBST([WITH_SHARED_SIERRA_FALSE])
m4trace:configure.ac:513: -1- AC_SUBST_TRACE([WITH_SHARED_SIERRA_FALSE])
m4trace:configure.ac:513: -1- m4_pattern_allow([^WITH_SHARED_SIERRA_FALSE$])
m4trace:configure.ac:513: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_SIERRA_TRUE])
m4trace:configure.ac:513: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_SIERRA_FALSE])
m4trace:configure.ac:513: -1- AC_DEFINE_TRACE_LITERAL([WITH_SHARED_SIERRA])
m4trace:configure.ac:513: -1- m4_pattern_allow([^WITH_SHARED_SIERRA$])
m4trace:configure.ac:513: -1- AH_OUTPUT([WITH_SHARED_SIERRA], [/* Define if sierra utils are built */
@%:@undef WITH_SHARED_SIERRA])
m4trace:configure.ac:514: -1- AM_CONDITIONAL([WITH_SHARED_OPTION], [test "x$with_shared_option" = "xyes"])
m4trace:configure.ac:514: -1- AC_SUBST([WITH_SHARED_OPTION_TRUE])
m4trace:configure.ac:514: -1- AC_SUBST_TRACE([WITH_SHARED_OPTION_TRUE])
m4trace:configure.ac:514: -1- m4_pattern_allow([^WITH_SHARED_OPTION_TRUE$])
m4trace:configure.ac:514: -1- AC_SUBST([WITH_SHARED_OPTION_FALSE])
m4trace:configure.ac:514: -1- AC_SUBST_TRACE([WITH_SHARED_OPTION_FALSE])
m4trace:configure.ac:514: -1- m4_pattern_allow([^WITH_SHARED_OPTION_FALSE$])
m4trace:configure.ac:514: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_OPTION_TRUE])
m4trace:configure.ac:514: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_OPTION_FALSE])
m4trace:configure.ac:514: -1- AC_DEFINE_TRACE_LITERAL([WITH_SHARED_OPTION])
m4trace:configure.ac:514: -1- m4_pattern_allow([^WITH_SHARED_OPTION$])
m4trace:configure.ac:514: -1- AH_OUTPUT([WITH_SHARED_OPTION], [/* Define if option utils are built */
@%:@undef WITH_SHARED_OPTION])
m4trace:configure.ac:515: -1- AM_CONDITIONAL([WITH_SHARED_NOVATEL], [test "x$with_shared_novatel" = "xyes"])
m4trace:configure.ac:515: -1- AC_SUBST([WITH_SHARED_NOVATEL_TRUE])
m4trace:configure.ac:515: -1- AC_SUBST_TRACE([WITH_SHARED_NOVATEL_TRUE])
m4trace:configure.ac:515: -1- m4_pattern_allow([^WITH_SHARED_NOVATEL_TRUE$])
m4trace:configure.ac:515: -1- AC_SUBST([WITH_SHARED_NOVATEL_FALSE])
m4trace:configure.ac:515: -1- AC_SUBST_TRACE([WITH_SHARED_NOVATEL_FALSE])
m4trace:configure.ac:515: -1- m4_pattern_allow([^WITH_SHARED_NOVATEL_FALSE$])
m4trace:configure.ac:515: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_NOVATEL_TRUE])
m4trace:configure.ac:515: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_NOVATEL_FALSE])
m4trace:configure.ac:515: -1- AC_DEFINE_TRACE_LITERAL([WITH_SHARED_NOVATEL])
m4trace:configure.ac:515: -1- m4_pattern_allow([^WITH_SHARED_NOVATEL$])
m4trace:configure.ac:515: -1- AH_OUTPUT([WITH_SHARED_NOVATEL], [/* Define if novatel utils are built */
@%:@undef WITH_SHARED_NOVATEL])
m4trace:configure.ac:516: -1- AM_CONDITIONAL([WITH_SHARED_XMM], [test "x$with_shared_xmm" = "xyes"])
m4trace:configure.ac:516: -1- AC_SUBST([WITH_SHARED_XMM_TRUE])
m4trace:configure.ac:516: -1- AC_SUBST_TRACE([WITH_SHARED_XMM_TRUE])
m4trace:configure.ac:516: -1- m4_pattern_allow([^WITH_SHARED_XMM_TRUE$])
m4trace:configure.ac:516: -1- AC_SUBST([WITH_SHARED_XMM_FALSE])
m4trace:configure.ac:516: -1- AC_SUBST_TRACE([WITH_SHARED_XMM_FALSE])
m4trace:configure.ac:516: -1- m4_pattern_allow([^WITH_SHARED_XMM_FALSE$])
m4trace:configure.ac:516: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_XMM_TRUE])
m4trace:configure.ac:516: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_XMM_FALSE])
m4trace:configure.ac:516: -1- AC_DEFINE_TRACE_LITERAL([WITH_SHARED_XMM])
m4trace:configure.ac:516: -1- m4_pattern_allow([^WITH_SHARED_XMM$])
m4trace:configure.ac:516: -1- AH_OUTPUT([WITH_SHARED_XMM], [/* Define if xmm utils are built */
@%:@undef WITH_SHARED_XMM])
m4trace:configure.ac:517: -1- AM_CONDITIONAL([WITH_SHARED_TELIT], [test "x$with_shared_telit" = "xyes"])
m4trace:configure.ac:517: -1- AC_SUBST([WITH_SHARED_TELIT_TRUE])
m4trace:configure.ac:517: -1- AC_SUBST_TRACE([WITH_SHARED_TELIT_TRUE])
m4trace:configure.ac:517: -1- m4_pattern_allow([^WITH_SHARED_TELIT_TRUE$])
m4trace:configure.ac:517: -1- AC_SUBST([WITH_SHARED_TELIT_FALSE])
m4trace:configure.ac:517: -1- AC_SUBST_TRACE([WITH_SHARED_TELIT_FALSE])
m4trace:configure.ac:517: -1- m4_pattern_allow([^WITH_SHARED_TELIT_FALSE$])
m4trace:configure.ac:517: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_TELIT_TRUE])
m4trace:configure.ac:517: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_TELIT_FALSE])
m4trace:configure.ac:517: -1- AC_DEFINE_TRACE_LITERAL([WITH_SHARED_TELIT])
m4trace:configure.ac:517: -1- m4_pattern_allow([^WITH_SHARED_TELIT$])
m4trace:configure.ac:517: -1- AH_OUTPUT([WITH_SHARED_TELIT], [/* Define if telit utils are built */
@%:@undef WITH_SHARED_TELIT])
m4trace:configure.ac:518: -1- AM_CONDITIONAL([WITH_SHARED_FOXCONN], [test "x$with_shared_foxconn" = "xyes"])
m4trace:configure.ac:518: -1- AC_SUBST([WITH_SHARED_FOXCONN_TRUE])
m4trace:configure.ac:518: -1- AC_SUBST_TRACE([WITH_SHARED_FOXCONN_TRUE])
m4trace:configure.ac:518: -1- m4_pattern_allow([^WITH_SHARED_FOXCONN_TRUE$])
m4trace:configure.ac:518: -1- AC_SUBST([WITH_SHARED_FOXCONN_FALSE])
m4trace:configure.ac:518: -1- AC_SUBST_TRACE([WITH_SHARED_FOXCONN_FALSE])
m4trace:configure.ac:518: -1- m4_pattern_allow([^WITH_SHARED_FOXCONN_FALSE$])
m4trace:configure.ac:518: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_FOXCONN_TRUE])
m4trace:configure.ac:518: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_FOXCONN_FALSE])
m4trace:configure.ac:518: -1- AC_DEFINE_TRACE_LITERAL([WITH_SHARED_FOXCONN])
m4trace:configure.ac:518: -1- m4_pattern_allow([^WITH_SHARED_FOXCONN$])
m4trace:configure.ac:518: -1- AH_OUTPUT([WITH_SHARED_FOXCONN], [/* Define if foxconn utils are built */
@%:@undef WITH_SHARED_FOXCONN])
m4trace:configure.ac:524: -1- AC_CONFIG_FILES([
Makefile
data/Makefile
data/ModemManager.pc
data/mm-glib.pc
data/tests/Makefile
data/tests/org.freedesktop.ModemManager1.service
include/Makefile
include/ModemManager-version.h
build-aux/Makefile
libqcdm/Makefile
libqcdm/src/Makefile
libqcdm/tests/Makefile
src/Makefile
src/tests/Makefile
plugins/Makefile
test/Makefile
introspection/Makefile
introspection/tests/Makefile
po/Makefile.in
docs/Makefile
docs/man/Makefile
docs/reference/Makefile
docs/reference/api/Makefile
docs/reference/api/version.xml
docs/reference/libmm-glib/Makefile
docs/reference/libmm-glib/version.xml
libmm-glib/Makefile
libmm-glib/generated/Makefile
libmm-glib/generated/tests/Makefile
libmm-glib/tests/Makefile
vapi/Makefile
cli/Makefile
examples/Makefile
examples/modem-watcher-python/Makefile
examples/modem-watcher-javascript/Makefile
examples/sms-python/Makefile
examples/network-scan-python/Makefile
])
m4trace:configure.ac:563: -1- AC_SUBST([LIB@&t@OBJS], [$ac_libobjs])
m4trace:configure.ac:563: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:563: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:563: -1- AC_SUBST([LTLIBOBJS], [$ac_ltlibobjs])
m4trace:configure.ac:563: -1- AC_SUBST_TRACE([LTLIBOBJS])
m4trace:configure.ac:563: -1- m4_pattern_allow([^LTLIBOBJS$])
m4trace:configure.ac:563: -1- AM_CONDITIONAL([am__EXEEXT], [test -n "$EXEEXT"])
m4trace:configure.ac:563: -1- AC_SUBST([am__EXEEXT_TRUE])
m4trace:configure.ac:563: -1- AC_SUBST_TRACE([am__EXEEXT_TRUE])
m4trace:configure.ac:563: -1- m4_pattern_allow([^am__EXEEXT_TRUE$])
m4trace:configure.ac:563: -1- AC_SUBST([am__EXEEXT_FALSE])
m4trace:configure.ac:563: -1- AC_SUBST_TRACE([am__EXEEXT_FALSE])
m4trace:configure.ac:563: -1- m4_pattern_allow([^am__EXEEXT_FALSE$])
m4trace:configure.ac:563: -1- _AM_SUBST_NOTMAKE([am__EXEEXT_TRUE])
m4trace:configure.ac:563: -1- _AM_SUBST_NOTMAKE([am__EXEEXT_FALSE])
m4trace:configure.ac:563: -1- AC_SUBST_TRACE([top_builddir])
m4trace:configure.ac:563: -1- AC_SUBST_TRACE([top_build_prefix])
m4trace:configure.ac:563: -1- AC_SUBST_TRACE([srcdir])
m4trace:configure.ac:563: -1- AC_SUBST_TRACE([abs_srcdir])
m4trace:configure.ac:563: -1- AC_SUBST_TRACE([top_srcdir])
m4trace:configure.ac:563: -1- AC_SUBST_TRACE([abs_top_srcdir])
m4trace:configure.ac:563: -1- AC_SUBST_TRACE([builddir])
m4trace:configure.ac:563: -1- AC_SUBST_TRACE([abs_builddir])
m4trace:configure.ac:563: -1- AC_SUBST_TRACE([abs_top_builddir])
m4trace:configure.ac:563: -1- AC_SUBST_TRACE([INSTALL])
m4trace:configure.ac:563: -1- AC_SUBST_TRACE([MKDIR_P])
m4trace:configure.ac:563: -1- AC_REQUIRE_AUX_FILE([ltmain.sh])

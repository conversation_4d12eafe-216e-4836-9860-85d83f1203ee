m4trace:/usr/share/aclocal/ax_append_compile_flags.m4:40: -1- AC_DEFUN([AX_APPEND_COMPILE_FLAGS], [AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])
AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
for flag in $1; do
  AX_CHECK_COMPILE_FLAG([$flag], [AX_APPEND_FLAG([$flag], [$2])], [], [$3], [$4])
done
])
m4trace:/usr/share/aclocal/ax_append_flag.m4:33: -1- AC_DEFUN([AX_APPEND_FLAG], [dnl
AC_PREREQ(2.64)dnl for _AC_LANG_PREFIX and AS_VAR_SET_IF
AS_VAR_PUSHDEF([FLAGS], [m4_default($2,_AC_LANG_PREFIX[FLAGS])])
AS_VAR_SET_IF(FLAGS,[
  AS_CASE([" AS_VAR_GET(FLAGS) "],
    [*" $1 "*], [AC_RUN_LOG([: FLAGS already contains $1])],
    [
     AS_VAR_APPEND(FLAGS,[" $1"])
     AC_RUN_LOG([: FLAGS="$FLAGS"])
    ])
  ],
  [
  AS_VAR_SET(FLAGS,[$1])
  AC_RUN_LOG([: FLAGS="$FLAGS"])
  ])
AS_VAR_POPDEF([FLAGS])dnl
])
m4trace:/usr/share/aclocal/ax_append_link_flags.m4:38: -1- AC_DEFUN([AX_APPEND_LINK_FLAGS], [AX_REQUIRE_DEFINED([AX_CHECK_LINK_FLAG])
AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
for flag in $1; do
  AX_CHECK_LINK_FLAG([$flag], [AX_APPEND_FLAG([$flag], [m4_default([$2], [LDFLAGS])])], [], [$3], [$4])
done
])
m4trace:/usr/share/aclocal/ax_check_compile_flag.m4:39: -1- AC_DEFUN([AX_CHECK_COMPILE_FLAG], [AC_PREREQ(2.64)dnl for _AC_LANG_PREFIX and AS_VAR_IF
AS_VAR_PUSHDEF([CACHEVAR],[ax_cv_check_[]_AC_LANG_ABBREV[]flags_$4_$1])dnl
AC_CACHE_CHECK([whether _AC_LANG compiler accepts $1], CACHEVAR, [
  ax_check_save_flags=$[]_AC_LANG_PREFIX[]FLAGS
  _AC_LANG_PREFIX[]FLAGS="$[]_AC_LANG_PREFIX[]FLAGS $4 $1"
  AC_COMPILE_IFELSE([m4_default([$5],[AC_LANG_PROGRAM()])],
    [AS_VAR_SET(CACHEVAR,[yes])],
    [AS_VAR_SET(CACHEVAR,[no])])
  _AC_LANG_PREFIX[]FLAGS=$ax_check_save_flags])
AS_VAR_IF(CACHEVAR,yes,
  [m4_default([$2], :)],
  [m4_default([$3], :)])
AS_VAR_POPDEF([CACHEVAR])dnl
])
m4trace:/usr/share/aclocal/ax_check_link_flag.m4:39: -1- AC_DEFUN([AX_CHECK_LINK_FLAG], [AC_PREREQ(2.64)dnl for _AC_LANG_PREFIX and AS_VAR_IF
AS_VAR_PUSHDEF([CACHEVAR],[ax_cv_check_ldflags_$4_$1])dnl
AC_CACHE_CHECK([whether the linker accepts $1], CACHEVAR, [
  ax_check_save_flags=$LDFLAGS
  LDFLAGS="$LDFLAGS $4 $1"
  AC_LINK_IFELSE([m4_default([$5],[AC_LANG_PROGRAM()])],
    [AS_VAR_SET(CACHEVAR,[yes])],
    [AS_VAR_SET(CACHEVAR,[no])])
  LDFLAGS=$ax_check_save_flags])
AS_VAR_IF(CACHEVAR,yes,
  [m4_default([$2], :)],
  [m4_default([$3], :)])
AS_VAR_POPDEF([CACHEVAR])dnl
])
m4trace:/usr/share/aclocal/ax_compiler_flags.m4:113: -1- AC_DEFUN([AX_COMPILER_FLAGS], [
    # C support is enabled by default.
    _AX_COMPILER_FLAGS_LANG([C])
    # Only enable C++ support if AC_PROG_CXX is called. The redefinition of
    # AC_PROG_CXX is so that a fatal error is emitted if this macro is called
    # before AC_PROG_CXX, which would otherwise cause no C++ warnings to be
    # checked.
    AC_PROVIDE_IFELSE([AC_PROG_CXX],
                      [_AX_COMPILER_FLAGS_LANG([CXX])],
                      [m4_define([AC_PROG_CXX], defn([AC_PROG_CXX])[_AX_COMPILER_FLAGS_LANG([CXX])])])
    AX_REQUIRE_DEFINED([AX_COMPILER_FLAGS_LDFLAGS])

    # Default value for IS-RELEASE is $ax_is_release
    ax_compiler_flags_is_release=m4_tolower(m4_normalize(ifelse([$3],,
                                                                [$ax_is_release],
                                                                [$3])))

    AC_ARG_ENABLE([compile-warnings],
                  AS_HELP_STRING([--enable-compile-warnings=@<:@no/yes/error@:>@],
                                 [Enable compiler warnings and errors]),,
                  [AS_IF([test "$ax_compiler_flags_is_release" = "yes"],
                         [enable_compile_warnings="yes"],
                         [enable_compile_warnings="error"])])
    AC_ARG_ENABLE([Werror],
                  AS_HELP_STRING([--disable-Werror],
                                 [Unconditionally make all compiler warnings non-fatal]),,
                  [enable_Werror=maybe])

    # Return the user's chosen warning level
    AS_IF([test "$enable_Werror" = "no" -a \
                "$enable_compile_warnings" = "error"],[
        enable_compile_warnings="yes"
    ])

    ax_enable_compile_warnings=$enable_compile_warnings

    AX_COMPILER_FLAGS_CFLAGS([$1],[$ax_compiler_flags_is_release],
                             [$4],[$5 $6 $7 $8])
    m4_ifdef([_AX_COMPILER_FLAGS_LANG_CXX_enabled],
             [AX_COMPILER_FLAGS_CXXFLAGS([WARN_CXXFLAGS],
                                         [$ax_compiler_flags_is_release],
                                         [$4],[$5 $6 $7 $8])])
    AX_COMPILER_FLAGS_LDFLAGS([$2],[$ax_compiler_flags_is_release],
                              [$9],[$10 $11 $12 $13])
    AX_COMPILER_FLAGS_GIR([WARN_SCANNERFLAGS],[$ax_compiler_flags_is_release])
])
m4trace:/usr/share/aclocal/ax_compiler_flags_cflags.m4:31: -1- AC_DEFUN([AX_COMPILER_FLAGS_CFLAGS], [
    AC_REQUIRE([AC_PROG_SED])
    AX_REQUIRE_DEFINED([AX_APPEND_COMPILE_FLAGS])
    AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
    AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])

    # Variable names
    m4_define([ax_warn_cflags_variable],
              [m4_normalize(ifelse([$1],,[WARN_CFLAGS],[$1]))])

    AC_LANG_PUSH([C])

    AC_COMPILE_IFELSE([AC_LANG_PROGRAM([
      [#ifndef __cplusplus
       #error "no C++"
       #endif]])],
      [ax_compiler_cxx=yes;],
      [ax_compiler_cxx=no;])

    # Always pass -Werror=unknown-warning-option to get Clang to fail on bad
    # flags, otherwise they are always appended to the warn_cflags variable, and
    # Clang warns on them for every compilation unit.
    # If this is passed to GCC, it will explode, so the flag must be enabled
    # conditionally.
    AX_CHECK_COMPILE_FLAG([-Werror=unknown-warning-option],[
        ax_compiler_flags_test="-Werror=unknown-warning-option"
    ],[
        ax_compiler_flags_test=""
    ])

    # Check that -Wno-suggest-attribute=format is supported
    AX_CHECK_COMPILE_FLAG([-Wno-suggest-attribute=format],[
        ax_compiler_no_suggest_attribute_flags="-Wno-suggest-attribute=format"
    ],[
        ax_compiler_no_suggest_attribute_flags=""
    ])

    # Base flags
    AX_APPEND_COMPILE_FLAGS([ dnl
        -fno-strict-aliasing dnl
        $3 dnl
    ],ax_warn_cflags_variable,[$ax_compiler_flags_test])

    AS_IF([test "$ax_enable_compile_warnings" != "no"],[
        if test "$ax_compiler_cxx" = "no" ; then
            # C-only flags. Warn in C++
            AX_APPEND_COMPILE_FLAGS([ dnl
                -Wnested-externs dnl
                -Wmissing-prototypes dnl
                -Wstrict-prototypes dnl
                -Wdeclaration-after-statement dnl
                -Wimplicit-function-declaration dnl
                -Wold-style-definition dnl
                -Wjump-misses-init dnl
            ],ax_warn_cflags_variable,[$ax_compiler_flags_test])
        fi

        # "yes" flags
        AX_APPEND_COMPILE_FLAGS([ dnl
            -Wall dnl
            -Wextra dnl
            -Wundef dnl
            -Wwrite-strings dnl
            -Wpointer-arith dnl
            -Wmissing-declarations dnl
            -Wredundant-decls dnl
            -Wno-unused-parameter dnl
            -Wno-missing-field-initializers dnl
            -Wformat=2 dnl
            -Wcast-align dnl
            -Wformat-nonliteral dnl
            -Wformat-security dnl
            -Wsign-compare dnl
            -Wstrict-aliasing dnl
            -Wshadow dnl
            -Winline dnl
            -Wpacked dnl
            -Wmissing-format-attribute dnl
            -Wmissing-noreturn dnl
            -Winit-self dnl
            -Wredundant-decls dnl
            -Wmissing-include-dirs dnl
            -Wunused-but-set-variable dnl
            -Warray-bounds dnl
            -Wreturn-type dnl
            -Wswitch-enum dnl
            -Wswitch-default dnl
            -Wduplicated-cond dnl
            -Wduplicated-branches dnl
            -Wlogical-op dnl
            -Wrestrict dnl
            -Wnull-dereference dnl
            -Wdouble-promotion dnl
            $4 dnl
            $5 dnl
            $6 dnl
            $7 dnl
        ],ax_warn_cflags_variable,[$ax_compiler_flags_test])
    ])
    AS_IF([test "$ax_enable_compile_warnings" = "error"],[
        # "error" flags; -Werror has to be appended unconditionally because
        # it's not possible to test for
        #
        # suggest-attribute=format is disabled because it gives too many false
        # positives
        AX_APPEND_FLAG([-Werror],ax_warn_cflags_variable)

        AX_APPEND_COMPILE_FLAGS([ dnl
            [$ax_compiler_no_suggest_attribute_flags] dnl
        ],ax_warn_cflags_variable,[$ax_compiler_flags_test])
    ])

    # In the flags below, when disabling specific flags, always add *both*
    # -Wno-foo and -Wno-error=foo. This fixes the situation where (for example)
    # we enable -Werror, disable a flag, and a build bot passes CFLAGS=-Wall,
    # which effectively turns that flag back on again as an error.
    for flag in $ax_warn_cflags_variable; do
        AS_CASE([$flag],
                [-Wno-*=*],[],
                [-Wno-*],[
                    AX_APPEND_COMPILE_FLAGS([-Wno-error=$(AS_ECHO([$flag]) | $SED 's/^-Wno-//')],
                                            ax_warn_cflags_variable,
                                            [$ax_compiler_flags_test])
                ])
    done

    AC_LANG_POP([C])

    # Substitute the variables
    AC_SUBST(ax_warn_cflags_variable)
])
m4trace:/usr/share/aclocal/ax_compiler_flags_cxxflags.m4:31: -1- AC_DEFUN([AX_COMPILER_FLAGS_CXXFLAGS], [
    AC_REQUIRE([AC_PROG_SED])
    AX_REQUIRE_DEFINED([AX_APPEND_COMPILE_FLAGS])
    AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
    AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])

    # Variable names
    m4_define([ax_warn_cxxflags_variable],
              [m4_normalize(ifelse([$1],,[WARN_CXXFLAGS],[$1]))])

    AC_LANG_PUSH([C++])

    # Always pass -Werror=unknown-warning-option to get Clang to fail on bad
    # flags, otherwise they are always appended to the warn_cxxflags variable,
    # and Clang warns on them for every compilation unit.
    # If this is passed to GCC, it will explode, so the flag must be enabled
    # conditionally.
    AX_CHECK_COMPILE_FLAG([-Werror=unknown-warning-option],[
        ax_compiler_flags_test="-Werror=unknown-warning-option"
    ],[
        ax_compiler_flags_test=""
    ])

    # Check that -Wno-suggest-attribute=format is supported
    AX_CHECK_COMPILE_FLAG([-Wno-suggest-attribute=format],[
        ax_compiler_no_suggest_attribute_flags="-Wno-suggest-attribute=format"
    ],[
        ax_compiler_no_suggest_attribute_flags=""
    ])

    # Base flags
    AX_APPEND_COMPILE_FLAGS([ dnl
        -fno-strict-aliasing dnl
        $3 dnl
    ],ax_warn_cxxflags_variable,[$ax_compiler_flags_test])

    AS_IF([test "$ax_enable_compile_warnings" != "no"],[
        # "yes" flags
        AX_APPEND_COMPILE_FLAGS([ dnl
            -Wall dnl
            -Wextra dnl
            -Wundef dnl
            -Wwrite-strings dnl
            -Wpointer-arith dnl
            -Wmissing-declarations dnl
            -Wredundant-decls dnl
            -Wno-unused-parameter dnl
            -Wno-missing-field-initializers dnl
            -Wformat=2 dnl
            -Wcast-align dnl
            -Wformat-nonliteral dnl
            -Wformat-security dnl
            -Wsign-compare dnl
            -Wstrict-aliasing dnl
            -Wshadow dnl
            -Winline dnl
            -Wpacked dnl
            -Wmissing-format-attribute dnl
            -Wmissing-noreturn dnl
            -Winit-self dnl
            -Wredundant-decls dnl
            -Wmissing-include-dirs dnl
            -Wunused-but-set-variable dnl
            -Warray-bounds dnl
            -Wreturn-type dnl
            -Wno-overloaded-virtual dnl
            -Wswitch-enum dnl
            -Wswitch-default dnl
            $4 dnl
            $5 dnl
            $6 dnl
            $7 dnl
        ],ax_warn_cxxflags_variable,[$ax_compiler_flags_test])
    ])
    AS_IF([test "$ax_enable_compile_warnings" = "error"],[
        # "error" flags; -Werror has to be appended unconditionally because
        # it's not possible to test for
        #
        # suggest-attribute=format is disabled because it gives too many false
        # positives
        AX_APPEND_FLAG([-Werror],ax_warn_cxxflags_variable)

        AX_APPEND_COMPILE_FLAGS([ dnl
            [$ax_compiler_no_suggest_attribute_flags] dnl
        ],ax_warn_cxxflags_variable,[$ax_compiler_flags_test])
    ])

    # In the flags below, when disabling specific flags, always add *both*
    # -Wno-foo and -Wno-error=foo. This fixes the situation where (for example)
    # we enable -Werror, disable a flag, and a build bot passes CXXFLAGS=-Wall,
    # which effectively turns that flag back on again as an error.
    for flag in $ax_warn_cxxflags_variable; do
        AS_CASE([$flag],
                [-Wno-*=*],[],
                [-Wno-*],[
                    AX_APPEND_COMPILE_FLAGS([-Wno-error=$(AS_ECHO([$flag]) | $SED 's/^-Wno-//')],
                                            ax_warn_cxxflags_variable,
                                            [$ax_compiler_flags_test])
                ])
    done

    AC_LANG_POP([C++])

    # Substitute the variables
    AC_SUBST(ax_warn_cxxflags_variable)
])
m4trace:/usr/share/aclocal/ax_compiler_flags_gir.m4:31: -1- AC_DEFUN([AX_COMPILER_FLAGS_GIR], [
    AX_REQUIRE_DEFINED([AX_APPEND_FLAG])

    # Variable names
    m4_define([ax_warn_scannerflags_variable],
              [m4_normalize(ifelse([$1],,[WARN_SCANNERFLAGS],[$1]))])

    # Base flags
    AX_APPEND_FLAG([$3],ax_warn_scannerflags_variable)

    AS_IF([test "$ax_enable_compile_warnings" != "no"],[
        # "yes" flags
        AX_APPEND_FLAG([ dnl
            --warn-all dnl
            $4 dnl
            $5 dnl
            $6 dnl
            $7 dnl
        ],ax_warn_scannerflags_variable)
    ])
    AS_IF([test "$ax_enable_compile_warnings" = "error"],[
        # "error" flags
        AX_APPEND_FLAG([ dnl
            --warn-error dnl
        ],ax_warn_scannerflags_variable)
    ])

    # Substitute the variables
    AC_SUBST(ax_warn_scannerflags_variable)
])
m4trace:/usr/share/aclocal/ax_compiler_flags_ldflags.m4:31: -1- AC_DEFUN([AX_COMPILER_FLAGS_LDFLAGS], [
    AX_REQUIRE_DEFINED([AX_APPEND_LINK_FLAGS])
    AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
    AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])
    AX_REQUIRE_DEFINED([AX_CHECK_LINK_FLAG])

    # Variable names
    m4_define([ax_warn_ldflags_variable],
              [m4_normalize(ifelse([$1],,[WARN_LDFLAGS],[$1]))])

    # Always pass -Werror=unknown-warning-option to get Clang to fail on bad
    # flags, otherwise they are always appended to the warn_ldflags variable,
    # and Clang warns on them for every compilation unit.
    # If this is passed to GCC, it will explode, so the flag must be enabled
    # conditionally.
    AX_CHECK_COMPILE_FLAG([-Werror=unknown-warning-option],[
        ax_compiler_flags_test="-Werror=unknown-warning-option"
    ],[
        ax_compiler_flags_test=""
    ])

    AX_CHECK_LINK_FLAG([-Wl,--as-needed], [
        AX_APPEND_LINK_FLAGS([-Wl,--as-needed],
          [AM_LDFLAGS],[$ax_compiler_flags_test])
    ])
    AX_CHECK_LINK_FLAG([-Wl,-z,relro], [
        AX_APPEND_LINK_FLAGS([-Wl,-z,relro],
          [AM_LDFLAGS],[$ax_compiler_flags_test])
    ])
    AX_CHECK_LINK_FLAG([-Wl,-z,now], [
        AX_APPEND_LINK_FLAGS([-Wl,-z,now],
          [AM_LDFLAGS],[$ax_compiler_flags_test])
    ])
    AX_CHECK_LINK_FLAG([-Wl,-z,noexecstack], [
        AX_APPEND_LINK_FLAGS([-Wl,-z,noexecstack],
          [AM_LDFLAGS],[$ax_compiler_flags_test])
    ])
    # textonly, retpolineplt not yet

    # macOS and cygwin linker do not have --as-needed
    AX_CHECK_LINK_FLAG([-Wl,--no-as-needed], [
        ax_compiler_flags_as_needed_option="-Wl,--no-as-needed"
    ], [
        ax_compiler_flags_as_needed_option=""
    ])

    # macOS linker speaks with a different accent
    ax_compiler_flags_fatal_warnings_option=""
    AX_CHECK_LINK_FLAG([-Wl,--fatal-warnings], [
        ax_compiler_flags_fatal_warnings_option="-Wl,--fatal-warnings"
    ])
    AX_CHECK_LINK_FLAG([-Wl,-fatal_warnings], [
        ax_compiler_flags_fatal_warnings_option="-Wl,-fatal_warnings"
    ])

    # Base flags
    AX_APPEND_LINK_FLAGS([ dnl
        $ax_compiler_flags_as_needed_option dnl
        $3 dnl
    ],ax_warn_ldflags_variable,[$ax_compiler_flags_test])

    AS_IF([test "$ax_enable_compile_warnings" != "no"],[
        # "yes" flags
        AX_APPEND_LINK_FLAGS([$4 $5 $6 $7],
                                ax_warn_ldflags_variable,
                                [$ax_compiler_flags_test])
    ])
    AS_IF([test "$ax_enable_compile_warnings" = "error"],[
        # "error" flags; -Werror has to be appended unconditionally because
        # it's not possible to test for
        #
        # suggest-attribute=format is disabled because it gives too many false
        # positives
        AX_APPEND_LINK_FLAGS([ dnl
            $ax_compiler_flags_fatal_warnings_option dnl
        ],ax_warn_ldflags_variable,[$ax_compiler_flags_test])
    ])

    # Substitute the variables
    AC_SUBST(ax_warn_ldflags_variable)
])
m4trace:/usr/share/aclocal/ax_is_release.m4:49: -1- AC_DEFUN([AX_IS_RELEASE], [
    AC_BEFORE([AC_INIT],[$0])

    m4_case([$1],
      [git-directory],[
        # $is_release = (.git directory does not exist)
        AS_IF([test -d ${srcdir}/.git],[ax_is_release=no],[ax_is_release=yes])
      ],
      [minor-version],[
        # $is_release = ($minor_version is even)
        minor_version=`echo "$PACKAGE_VERSION" | sed 's/[[^.]][[^.]]*.\([[^.]][[^.]]*\).*/\1/'`
        AS_IF([test "$(( $minor_version % 2 ))" -ne 0],
              [ax_is_release=no],[ax_is_release=yes])
      ],
      [micro-version],[
        # $is_release = ($micro_version is even)
        micro_version=`echo "$PACKAGE_VERSION" | sed 's/[[^.]]*\.[[^.]]*\.\([[^.]]*\).*/\1/'`
        AS_IF([test "$(( $micro_version % 2 ))" -ne 0],
              [ax_is_release=no],[ax_is_release=yes])
      ],
      [dash-version],[
        # $is_release = ($PACKAGE_VERSION has a dash)
        AS_CASE([$PACKAGE_VERSION],
                [*-*], [ax_is_release=no],
                [*], [ax_is_release=yes])
      ],
      [always],[ax_is_release=yes],
      [never],[ax_is_release=no],
      [
        AC_MSG_ERROR([Invalid policy. Valid policies: git-directory, minor-version, micro-version, dash-version, always, never.])
      ])
])
m4trace:/usr/share/aclocal/ax_require_defined.m4:35: -1- AC_DEFUN([AX_REQUIRE_DEFINED], [dnl
  m4_ifndef([$1], [m4_fatal([macro ]$1[ is not defined; is a m4 file missing?])])
])
m4trace:/usr/share/aclocal/ltargz.m4:12: -1- AC_DEFUN([LT_FUNC_ARGZ], [
AC_CHECK_HEADERS([argz.h], [], [], [AC_INCLUDES_DEFAULT])

AC_CHECK_TYPES([error_t],
  [],
  [AC_DEFINE([error_t], [int],
   [Define to a type to use for 'error_t' if it is not otherwise available.])
   AC_DEFINE([__error_t_defined], [1], [Define so that glibc/gnulib argp.h
    does not typedef error_t.])],
  [#if defined(HAVE_ARGZ_H)
#  include <argz.h>
#endif])

LT_ARGZ_H=
AC_CHECK_FUNCS([argz_add argz_append argz_count argz_create_sep argz_insert \
	argz_next argz_stringify], [], [LT_ARGZ_H=lt__argz.h; AC_LIBOBJ([lt__argz])])

dnl if have system argz functions, allow forced use of
dnl libltdl-supplied implementation (and default to do so
dnl on "known bad" systems). Could use a runtime check, but
dnl (a) detecting malloc issues is notoriously unreliable
dnl (b) only known system that declares argz functions,
dnl     provides them, yet they are broken, is cygwin
dnl     releases prior to 16-Mar-2007 (1.5.24 and earlier)
dnl So, it's more straightforward simply to special case
dnl this for known bad systems.
AS_IF([test -z "$LT_ARGZ_H"],
    [AC_CACHE_CHECK(
        [if argz actually works],
        [lt_cv_sys_argz_works],
        [[case $host_os in #(
	 *cygwin*)
	   lt_cv_sys_argz_works=no
	   if test no != "$cross_compiling"; then
	     lt_cv_sys_argz_works="guessing no"
	   else
	     lt_sed_extract_leading_digits='s/^\([0-9\.]*\).*/\1/'
	     save_IFS=$IFS
	     IFS=-.
	     set x `uname -r | sed -e "$lt_sed_extract_leading_digits"`
	     IFS=$save_IFS
	     lt_os_major=${2-0}
	     lt_os_minor=${3-0}
	     lt_os_micro=${4-0}
	     if test 1 -lt "$lt_os_major" \
		|| { test 1 -eq "$lt_os_major" \
		  && { test 5 -lt "$lt_os_minor" \
		    || { test 5 -eq "$lt_os_minor" \
		      && test 24 -lt "$lt_os_micro"; }; }; }; then
	       lt_cv_sys_argz_works=yes
	     fi
	   fi
	   ;; #(
	 *) lt_cv_sys_argz_works=yes ;;
	 esac]])
     AS_IF([test yes = "$lt_cv_sys_argz_works"],
        [AC_DEFINE([HAVE_WORKING_ARGZ], 1,
                   [This value is set to 1 to indicate that the system argz facility works])],
        [LT_ARGZ_H=lt__argz.h
        AC_LIBOBJ([lt__argz])])])

AC_SUBST([LT_ARGZ_H])
])
m4trace:/usr/share/aclocal/ltdl.m4:16: -1- AC_DEFUN([LT_CONFIG_LTDL_DIR], [AC_BEFORE([$0], [LTDL_INIT])
_$0($*)
])
m4trace:/usr/share/aclocal/ltdl.m4:68: -1- AC_DEFUN([LTDL_CONVENIENCE], [AC_BEFORE([$0], [LTDL_INIT])dnl
dnl Although the argument is deprecated and no longer documented,
dnl LTDL_CONVENIENCE used to take a DIRECTORY orgument, if we have one
dnl here make sure it is the same as any other declaration of libltdl's
dnl location!  This also ensures lt_ltdl_dir is set when configure.ac is
dnl not yet using an explicit LT_CONFIG_LTDL_DIR.
m4_ifval([$1], [_LT_CONFIG_LTDL_DIR([$1])])dnl
_$0()
])
m4trace:/usr/share/aclocal/ltdl.m4:81: -1- AU_DEFUN([AC_LIBLTDL_CONVENIENCE], [_LT_CONFIG_LTDL_DIR([m4_default([$1], [libltdl])])
_LTDL_CONVENIENCE])
m4trace:/usr/share/aclocal/ltdl.m4:81: -1- AC_DEFUN([AC_LIBLTDL_CONVENIENCE], [AC_DIAGNOSE([obsolete], [The macro `AC_LIBLTDL_CONVENIENCE' is obsolete.
You should run autoupdate.])dnl
_LT_CONFIG_LTDL_DIR([m4_default([$1], [libltdl])])
_LTDL_CONVENIENCE])
m4trace:/usr/share/aclocal/ltdl.m4:124: -1- AC_DEFUN([LTDL_INSTALLABLE], [AC_BEFORE([$0], [LTDL_INIT])dnl
dnl Although the argument is deprecated and no longer documented,
dnl LTDL_INSTALLABLE used to take a DIRECTORY orgument, if we have one
dnl here make sure it is the same as any other declaration of libltdl's
dnl location!  This also ensures lt_ltdl_dir is set when configure.ac is
dnl not yet using an explicit LT_CONFIG_LTDL_DIR.
m4_ifval([$1], [_LT_CONFIG_LTDL_DIR([$1])])dnl
_$0()
])
m4trace:/usr/share/aclocal/ltdl.m4:137: -1- AU_DEFUN([AC_LIBLTDL_INSTALLABLE], [_LT_CONFIG_LTDL_DIR([m4_default([$1], [libltdl])])
_LTDL_INSTALLABLE])
m4trace:/usr/share/aclocal/ltdl.m4:137: -1- AC_DEFUN([AC_LIBLTDL_INSTALLABLE], [AC_DIAGNOSE([obsolete], [The macro `AC_LIBLTDL_INSTALLABLE' is obsolete.
You should run autoupdate.])dnl
_LT_CONFIG_LTDL_DIR([m4_default([$1], [libltdl])])
_LTDL_INSTALLABLE])
m4trace:/usr/share/aclocal/ltdl.m4:213: -1- AC_DEFUN([_LT_LIBOBJ], [
  m4_pattern_allow([^_LT_LIBOBJS$])
  _LT_LIBOBJS="$_LT_LIBOBJS $1.$ac_objext"
])
m4trace:/usr/share/aclocal/ltdl.m4:226: -1- AC_DEFUN([LTDL_INIT], [dnl Parse OPTIONS
_LT_SET_OPTIONS([$0], [$1])

dnl We need to keep our own list of libobjs separate from our parent project,
dnl and the easiest way to do that is redefine the AC_LIBOBJs macro while
dnl we look for our own LIBOBJs.
m4_pushdef([AC_LIBOBJ], m4_defn([_LT_LIBOBJ]))
m4_pushdef([AC_LIBSOURCES])

dnl If not otherwise defined, default to the 1.5.x compatible subproject mode:
m4_if(_LTDL_MODE, [],
        [m4_define([_LTDL_MODE], m4_default([$2], [subproject]))
        m4_if([-1], [m4_bregexp(_LTDL_MODE, [\(subproject\|\(non\)?recursive\)])],
                [m4_fatal([unknown libltdl mode: ]_LTDL_MODE)])])

AC_ARG_WITH([included_ltdl],
    [AS_HELP_STRING([--with-included-ltdl],
                    [use the GNU ltdl sources included here])])

if test yes != "$with_included_ltdl"; then
  # We are not being forced to use the included libltdl sources, so
  # decide whether there is a useful installed version we can use.
  AC_CHECK_HEADER([ltdl.h],
      [AC_CHECK_DECL([lt_dlinterface_register],
	   [AC_CHECK_LIB([ltdl], [lt_dladvise_preload],
	       [with_included_ltdl=no],
	       [with_included_ltdl=yes])],
	   [with_included_ltdl=yes],
	   [AC_INCLUDES_DEFAULT
	    #include <ltdl.h>])],
      [with_included_ltdl=yes],
      [AC_INCLUDES_DEFAULT]
  )
fi

dnl If neither LT_CONFIG_LTDL_DIR, LTDL_CONVENIENCE nor LTDL_INSTALLABLE
dnl was called yet, then for old times' sake, we assume libltdl is in an
dnl eponymous directory:
AC_PROVIDE_IFELSE([LT_CONFIG_LTDL_DIR], [], [_LT_CONFIG_LTDL_DIR([libltdl])])

AC_ARG_WITH([ltdl_include],
    [AS_HELP_STRING([--with-ltdl-include=DIR],
                    [use the ltdl headers installed in DIR])])

if test -n "$with_ltdl_include"; then
  if test -f "$with_ltdl_include/ltdl.h"; then :
  else
    AC_MSG_ERROR([invalid ltdl include directory: '$with_ltdl_include'])
  fi
else
  with_ltdl_include=no
fi

AC_ARG_WITH([ltdl_lib],
    [AS_HELP_STRING([--with-ltdl-lib=DIR],
                    [use the libltdl.la installed in DIR])])

if test -n "$with_ltdl_lib"; then
  if test -f "$with_ltdl_lib/libltdl.la"; then :
  else
    AC_MSG_ERROR([invalid ltdl library directory: '$with_ltdl_lib'])
  fi
else
  with_ltdl_lib=no
fi

case ,$with_included_ltdl,$with_ltdl_include,$with_ltdl_lib, in
  ,yes,no,no,)
	m4_case(m4_default(_LTDL_TYPE, [convenience]),
	    [convenience], [_LTDL_CONVENIENCE],
	    [installable], [_LTDL_INSTALLABLE],
	  [m4_fatal([unknown libltdl build type: ]_LTDL_TYPE)])
	;;
  ,no,no,no,)
	# If the included ltdl is not to be used, then use the
	# preinstalled libltdl we found.
	AC_DEFINE([HAVE_LTDL], [1],
	  [Define this if a modern libltdl is already installed])
	LIBLTDL=-lltdl
	LTDLDEPS=
	LTDLINCL=
	;;
  ,no*,no,*)
	AC_MSG_ERROR(['--with-ltdl-include' and '--with-ltdl-lib' options must be used together])
	;;
  *)	with_included_ltdl=no
	LIBLTDL="-L$with_ltdl_lib -lltdl"
	LTDLDEPS=
	LTDLINCL=-I$with_ltdl_include
	;;
esac
INCLTDL=$LTDLINCL

# Report our decision...
AC_MSG_CHECKING([where to find libltdl headers])
AC_MSG_RESULT([$LTDLINCL])
AC_MSG_CHECKING([where to find libltdl library])
AC_MSG_RESULT([$LIBLTDL])

_LTDL_SETUP

dnl restore autoconf definition.
m4_popdef([AC_LIBOBJ])
m4_popdef([AC_LIBSOURCES])

AC_CONFIG_COMMANDS_PRE([
    _ltdl_libobjs=
    _ltdl_ltlibobjs=
    if test -n "$_LT_LIBOBJS"; then
      # Remove the extension.
      _lt_sed_drop_objext='s/\.o$//;s/\.obj$//'
      for i in `for i in $_LT_LIBOBJS; do echo "$i"; done | sed "$_lt_sed_drop_objext" | sort -u`; do
        _ltdl_libobjs="$_ltdl_libobjs $lt_libobj_prefix$i.$ac_objext"
        _ltdl_ltlibobjs="$_ltdl_ltlibobjs $lt_libobj_prefix$i.lo"
      done
    fi
    AC_SUBST([ltdl_LIBOBJS], [$_ltdl_libobjs])
    AC_SUBST([ltdl_LTLIBOBJS], [$_ltdl_ltlibobjs])
])

# Only expand once:
m4_define([LTDL_INIT])
])
m4trace:/usr/share/aclocal/ltdl.m4:352: -1- AU_DEFUN([AC_LIB_LTDL], [LTDL_INIT($@)])
m4trace:/usr/share/aclocal/ltdl.m4:352: -1- AC_DEFUN([AC_LIB_LTDL], [AC_DIAGNOSE([obsolete], [The macro `AC_LIB_LTDL' is obsolete.
You should run autoupdate.])dnl
LTDL_INIT($@)])
m4trace:/usr/share/aclocal/ltdl.m4:353: -1- AU_DEFUN([AC_WITH_LTDL], [LTDL_INIT($@)])
m4trace:/usr/share/aclocal/ltdl.m4:353: -1- AC_DEFUN([AC_WITH_LTDL], [AC_DIAGNOSE([obsolete], [The macro `AC_WITH_LTDL' is obsolete.
You should run autoupdate.])dnl
LTDL_INIT($@)])
m4trace:/usr/share/aclocal/ltdl.m4:354: -1- AU_DEFUN([LT_WITH_LTDL], [LTDL_INIT($@)])
m4trace:/usr/share/aclocal/ltdl.m4:354: -1- AC_DEFUN([LT_WITH_LTDL], [AC_DIAGNOSE([obsolete], [The macro `LT_WITH_LTDL' is obsolete.
You should run autoupdate.])dnl
LTDL_INIT($@)])
m4trace:/usr/share/aclocal/ltdl.m4:367: -1- AC_DEFUN([_LTDL_SETUP], [AC_REQUIRE([AC_PROG_CC])dnl
AC_REQUIRE([LT_SYS_MODULE_EXT])dnl
AC_REQUIRE([LT_SYS_MODULE_PATH])dnl
AC_REQUIRE([LT_SYS_DLSEARCH_PATH])dnl
AC_REQUIRE([LT_LIB_DLLOAD])dnl
AC_REQUIRE([LT_SYS_SYMBOL_USCORE])dnl
AC_REQUIRE([LT_FUNC_DLSYM_USCORE])dnl
AC_REQUIRE([LT_SYS_DLOPEN_DEPLIBS])dnl
AC_REQUIRE([LT_FUNC_ARGZ])dnl

m4_require([_LT_CHECK_OBJDIR])dnl
m4_require([_LT_HEADER_DLFCN])dnl
m4_require([_LT_CHECK_DLPREOPEN])dnl
m4_require([_LT_DECL_SED])dnl

dnl Don't require this, or it will be expanded earlier than the code
dnl that sets the variables it relies on:
_LT_ENABLE_INSTALL

dnl _LTDL_MODE specific code must be called at least once:
_LTDL_MODE_DISPATCH

# In order that ltdl.c can compile, find out the first AC_CONFIG_HEADERS
# the user used.  This is so that ltdl.h can pick up the parent projects
# config.h file, The first file in AC_CONFIG_HEADERS must contain the
# definitions required by ltdl.c.
# FIXME: Remove use of undocumented AC_LIST_HEADERS (2.59 compatibility).
AC_CONFIG_COMMANDS_PRE([dnl
m4_pattern_allow([^LT_CONFIG_H$])dnl
m4_ifset([AH_HEADER],
    [LT_CONFIG_H=AH_HEADER],
    [m4_ifset([AC_LIST_HEADERS],
	    [LT_CONFIG_H=`echo "AC_LIST_HEADERS" | $SED 's|^[[      ]]*||;s|[[ :]].*$||'`],
	[])])])
AC_SUBST([LT_CONFIG_H])

AC_CHECK_HEADERS([unistd.h dl.h sys/dl.h dld.h mach-o/dyld.h dirent.h],
	[], [], [AC_INCLUDES_DEFAULT])

AC_CHECK_FUNCS([closedir opendir readdir], [], [AC_LIBOBJ([lt__dirent])])
AC_CHECK_FUNCS([strlcat strlcpy], [], [AC_LIBOBJ([lt__strl])])

m4_pattern_allow([LT_LIBEXT])dnl
AC_DEFINE_UNQUOTED([LT_LIBEXT],["$libext"],[The archive extension])

name=
eval "lt_libprefix=\"$libname_spec\""
m4_pattern_allow([LT_LIBPREFIX])dnl
AC_DEFINE_UNQUOTED([LT_LIBPREFIX],["$lt_libprefix"],[The archive prefix])

name=ltdl
eval "LTDLOPEN=\"$libname_spec\""
AC_SUBST([LTDLOPEN])
])
m4trace:/usr/share/aclocal/ltdl.m4:443: -1- AC_DEFUN([LT_SYS_DLOPEN_DEPLIBS], [AC_REQUIRE([AC_CANONICAL_HOST])dnl
AC_CACHE_CHECK([whether deplibs are loaded by dlopen],
  [lt_cv_sys_dlopen_deplibs],
  [# PORTME does your system automatically load deplibs for dlopen?
  # or its logical equivalent (e.g. shl_load for HP-UX < 11)
  # For now, we just catch OSes we know something about -- in the
  # future, we'll try test this programmatically.
  lt_cv_sys_dlopen_deplibs=unknown
  case $host_os in
  aix3*|aix4.1.*|aix4.2.*)
    # Unknown whether this is true for these versions of AIX, but
    # we want this 'case' here to explicitly catch those versions.
    lt_cv_sys_dlopen_deplibs=unknown
    ;;
  aix[[4-9]]*)
    lt_cv_sys_dlopen_deplibs=yes
    ;;
  amigaos*)
    case $host_cpu in
    powerpc)
      lt_cv_sys_dlopen_deplibs=no
      ;;
    esac
    ;;
  bitrig*)
    lt_cv_sys_dlopen_deplibs=yes
    ;;
  darwin*)
    # Assuming the user has installed a libdl from somewhere, this is true
    # If you are looking for one http://www.opendarwin.org/projects/dlcompat
    lt_cv_sys_dlopen_deplibs=yes
    ;;
  freebsd* | dragonfly*)
    lt_cv_sys_dlopen_deplibs=yes
    ;;
  gnu* | linux* | k*bsd*-gnu | kopensolaris*-gnu)
    # GNU and its variants, using gnu ld.so (Glibc)
    lt_cv_sys_dlopen_deplibs=yes
    ;;
  hpux10*|hpux11*)
    lt_cv_sys_dlopen_deplibs=yes
    ;;
  interix*)
    lt_cv_sys_dlopen_deplibs=yes
    ;;
  irix[[12345]]*|irix6.[[01]]*)
    # Catch all versions of IRIX before 6.2, and indicate that we don't
    # know how it worked for any of those versions.
    lt_cv_sys_dlopen_deplibs=unknown
    ;;
  irix*)
    # The case above catches anything before 6.2, and it's known that
    # at 6.2 and later dlopen does load deplibs.
    lt_cv_sys_dlopen_deplibs=yes
    ;;
  netbsd* | netbsdelf*-gnu)
    lt_cv_sys_dlopen_deplibs=yes
    ;;
  openbsd*)
    lt_cv_sys_dlopen_deplibs=yes
    ;;
  osf[[1234]]*)
    # dlopen did load deplibs (at least at 4.x), but until the 5.x series,
    # it did *not* use an RPATH in a shared library to find objects the
    # library depends on, so we explicitly say 'no'.
    lt_cv_sys_dlopen_deplibs=no
    ;;
  osf5.0|osf5.0a|osf5.1)
    # dlopen *does* load deplibs and with the right loader patch applied
    # it even uses RPATH in a shared library to search for shared objects
    # that the library depends on, but there's no easy way to know if that
    # patch is installed.  Since this is the case, all we can really
    # say is unknown -- it depends on the patch being installed.  If
    # it is, this changes to 'yes'.  Without it, it would be 'no'.
    lt_cv_sys_dlopen_deplibs=unknown
    ;;
  osf*)
    # the two cases above should catch all versions of osf <= 5.1.  Read
    # the comments above for what we know about them.
    # At > 5.1, deplibs are loaded *and* any RPATH in a shared library
    # is used to find them so we can finally say 'yes'.
    lt_cv_sys_dlopen_deplibs=yes
    ;;
  qnx*)
    lt_cv_sys_dlopen_deplibs=yes
    ;;
  solaris*)
    lt_cv_sys_dlopen_deplibs=yes
    ;;
  sysv5* | sco3.2v5* | sco5v6* | unixware* | OpenUNIX* | sysv4*uw2*)
    libltdl_cv_sys_dlopen_deplibs=yes
    ;;
  esac
  ])
if test yes != "$lt_cv_sys_dlopen_deplibs"; then
 AC_DEFINE([LTDL_DLOPEN_DEPLIBS], [1],
    [Define if the OS needs help to load dependent libraries for dlopen().])
fi
])
m4trace:/usr/share/aclocal/ltdl.m4:545: -1- AU_DEFUN([AC_LTDL_SYS_DLOPEN_DEPLIBS], [m4_if($#, 0, [LT_SYS_DLOPEN_DEPLIBS], [LT_SYS_DLOPEN_DEPLIBS($@)])])
m4trace:/usr/share/aclocal/ltdl.m4:545: -1- AC_DEFUN([AC_LTDL_SYS_DLOPEN_DEPLIBS], [AC_DIAGNOSE([obsolete], [The macro `AC_LTDL_SYS_DLOPEN_DEPLIBS' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_SYS_DLOPEN_DEPLIBS], [LT_SYS_DLOPEN_DEPLIBS($@)])])
m4trace:/usr/share/aclocal/ltdl.m4:552: -1- AC_DEFUN([LT_SYS_MODULE_EXT], [m4_require([_LT_SYS_DYNAMIC_LINKER])dnl
AC_CACHE_CHECK([what extension is used for runtime loadable modules],
  [libltdl_cv_shlibext],
[
module=yes
eval libltdl_cv_shlibext=$shrext_cmds
module=no
eval libltdl_cv_shrext=$shrext_cmds
  ])
if test -n "$libltdl_cv_shlibext"; then
  m4_pattern_allow([LT_MODULE_EXT])dnl
  AC_DEFINE_UNQUOTED([LT_MODULE_EXT], ["$libltdl_cv_shlibext"],
    [Define to the extension used for runtime loadable modules, say, ".so".])
fi
if test "$libltdl_cv_shrext" != "$libltdl_cv_shlibext"; then
  m4_pattern_allow([LT_SHARED_EXT])dnl
  AC_DEFINE_UNQUOTED([LT_SHARED_EXT], ["$libltdl_cv_shrext"],
    [Define to the shared library suffix, say, ".dylib".])
fi
if test -n "$shared_archive_member_spec"; then
  m4_pattern_allow([LT_SHARED_LIB_MEMBER])dnl
  AC_DEFINE_UNQUOTED([LT_SHARED_LIB_MEMBER], ["($shared_archive_member_spec.o)"],
    [Define to the shared archive member specification, say "(shr.o)".])
fi
])
m4trace:/usr/share/aclocal/ltdl.m4:580: -1- AU_DEFUN([AC_LTDL_SHLIBEXT], [m4_if($#, 0, [LT_SYS_MODULE_EXT], [LT_SYS_MODULE_EXT($@)])])
m4trace:/usr/share/aclocal/ltdl.m4:580: -1- AC_DEFUN([AC_LTDL_SHLIBEXT], [AC_DIAGNOSE([obsolete], [The macro `AC_LTDL_SHLIBEXT' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_SYS_MODULE_EXT], [LT_SYS_MODULE_EXT($@)])])
m4trace:/usr/share/aclocal/ltdl.m4:587: -1- AC_DEFUN([LT_SYS_MODULE_PATH], [m4_require([_LT_SYS_DYNAMIC_LINKER])dnl
AC_CACHE_CHECK([what variable specifies run-time module search path],
  [lt_cv_module_path_var], [lt_cv_module_path_var=$shlibpath_var])
if test -n "$lt_cv_module_path_var"; then
  m4_pattern_allow([LT_MODULE_PATH_VAR])dnl
  AC_DEFINE_UNQUOTED([LT_MODULE_PATH_VAR], ["$lt_cv_module_path_var"],
    [Define to the name of the environment variable that determines the run-time module search path.])
fi
])
m4trace:/usr/share/aclocal/ltdl.m4:599: -1- AU_DEFUN([AC_LTDL_SHLIBPATH], [m4_if($#, 0, [LT_SYS_MODULE_PATH], [LT_SYS_MODULE_PATH($@)])])
m4trace:/usr/share/aclocal/ltdl.m4:599: -1- AC_DEFUN([AC_LTDL_SHLIBPATH], [AC_DIAGNOSE([obsolete], [The macro `AC_LTDL_SHLIBPATH' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_SYS_MODULE_PATH], [LT_SYS_MODULE_PATH($@)])])
m4trace:/usr/share/aclocal/ltdl.m4:606: -1- AC_DEFUN([LT_SYS_DLSEARCH_PATH], [m4_require([_LT_SYS_DYNAMIC_LINKER])dnl
AC_CACHE_CHECK([for the default library search path],
  [lt_cv_sys_dlsearch_path],
  [lt_cv_sys_dlsearch_path=$sys_lib_dlsearch_path_spec])
if test -n "$lt_cv_sys_dlsearch_path"; then
  sys_dlsearch_path=
  for dir in $lt_cv_sys_dlsearch_path; do
    if test -z "$sys_dlsearch_path"; then
      sys_dlsearch_path=$dir
    else
      sys_dlsearch_path=$sys_dlsearch_path$PATH_SEPARATOR$dir
    fi
  done
  m4_pattern_allow([LT_DLSEARCH_PATH])dnl
  AC_DEFINE_UNQUOTED([LT_DLSEARCH_PATH], ["$sys_dlsearch_path"],
    [Define to the system default library search path.])
fi
])
m4trace:/usr/share/aclocal/ltdl.m4:627: -1- AU_DEFUN([AC_LTDL_SYSSEARCHPATH], [m4_if($#, 0, [LT_SYS_DLSEARCH_PATH], [LT_SYS_DLSEARCH_PATH($@)])])
m4trace:/usr/share/aclocal/ltdl.m4:627: -1- AC_DEFUN([AC_LTDL_SYSSEARCHPATH], [AC_DIAGNOSE([obsolete], [The macro `AC_LTDL_SYSSEARCHPATH' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_SYS_DLSEARCH_PATH], [LT_SYS_DLSEARCH_PATH($@)])])
m4trace:/usr/share/aclocal/ltdl.m4:653: -1- AC_DEFUN([LT_LIB_DLLOAD], [m4_pattern_allow([^LT_DLLOADERS$])
LT_DLLOADERS=
AC_SUBST([LT_DLLOADERS])

AC_LANG_PUSH([C])
lt_dlload_save_LIBS=$LIBS

LIBADD_DLOPEN=
AC_SEARCH_LIBS([dlopen], [dl],
	[AC_DEFINE([HAVE_LIBDL], [1],
		   [Define if you have the libdl library or equivalent.])
	if test "$ac_cv_search_dlopen" != "none required"; then
	  LIBADD_DLOPEN=-ldl
	fi
	libltdl_cv_lib_dl_dlopen=yes
	LT_DLLOADERS="$LT_DLLOADERS ${lt_dlopen_dir+$lt_dlopen_dir/}dlopen.la"],
    [AC_LINK_IFELSE([AC_LANG_PROGRAM([[#if HAVE_DLFCN_H
#  include <dlfcn.h>
#endif
    ]], [[dlopen(0, 0);]])],
	    [AC_DEFINE([HAVE_LIBDL], [1],
		       [Define if you have the libdl library or equivalent.])
	    libltdl_cv_func_dlopen=yes
	    LT_DLLOADERS="$LT_DLLOADERS ${lt_dlopen_dir+$lt_dlopen_dir/}dlopen.la"],
	[AC_CHECK_LIB([svld], [dlopen],
		[AC_DEFINE([HAVE_LIBDL], [1],
			 [Define if you have the libdl library or equivalent.])
	        LIBADD_DLOPEN=-lsvld libltdl_cv_func_dlopen=yes
		LT_DLLOADERS="$LT_DLLOADERS ${lt_dlopen_dir+$lt_dlopen_dir/}dlopen.la"])])])
if test yes = "$libltdl_cv_func_dlopen" || test yes = "$libltdl_cv_lib_dl_dlopen"
then
  lt_save_LIBS=$LIBS
  LIBS="$LIBS $LIBADD_DLOPEN"
  AC_CHECK_FUNCS([dlerror])
  LIBS=$lt_save_LIBS
fi
AC_SUBST([LIBADD_DLOPEN])

LIBADD_SHL_LOAD=
AC_CHECK_FUNC([shl_load],
	[AC_DEFINE([HAVE_SHL_LOAD], [1],
		   [Define if you have the shl_load function.])
	LT_DLLOADERS="$LT_DLLOADERS ${lt_dlopen_dir+$lt_dlopen_dir/}shl_load.la"],
    [AC_CHECK_LIB([dld], [shl_load],
	    [AC_DEFINE([HAVE_SHL_LOAD], [1],
		       [Define if you have the shl_load function.])
	    LT_DLLOADERS="$LT_DLLOADERS ${lt_dlopen_dir+$lt_dlopen_dir/}shl_load.la"
	    LIBADD_SHL_LOAD=-ldld])])
AC_SUBST([LIBADD_SHL_LOAD])

case $host_os in
darwin[[1567]].*)
# We only want this for pre-Mac OS X 10.4.
  AC_CHECK_FUNC([_dyld_func_lookup],
	[AC_DEFINE([HAVE_DYLD], [1],
		   [Define if you have the _dyld_func_lookup function.])
	LT_DLLOADERS="$LT_DLLOADERS ${lt_dlopen_dir+$lt_dlopen_dir/}dyld.la"])
  ;;
beos*)
  LT_DLLOADERS="$LT_DLLOADERS ${lt_dlopen_dir+$lt_dlopen_dir/}load_add_on.la"
  ;;
cygwin* | mingw* | pw32*)
  AC_CHECK_DECLS([cygwin_conv_path], [], [], [[#include <sys/cygwin.h>]])
  LT_DLLOADERS="$LT_DLLOADERS ${lt_dlopen_dir+$lt_dlopen_dir/}loadlibrary.la"
  ;;
esac

AC_CHECK_LIB([dld], [dld_link],
	[AC_DEFINE([HAVE_DLD], [1],
		   [Define if you have the GNU dld library.])
		LT_DLLOADERS="$LT_DLLOADERS ${lt_dlopen_dir+$lt_dlopen_dir/}dld_link.la"])
AC_SUBST([LIBADD_DLD_LINK])

m4_pattern_allow([^LT_DLPREOPEN$])
LT_DLPREOPEN=
if test -n "$LT_DLLOADERS"
then
  for lt_loader in $LT_DLLOADERS; do
    LT_DLPREOPEN="$LT_DLPREOPEN-dlpreopen $lt_loader "
  done
  AC_DEFINE([HAVE_LIBDLLOADER], [1],
            [Define if libdlloader will be built on this platform])
fi
AC_SUBST([LT_DLPREOPEN])

dnl This isn't used anymore, but set it for backwards compatibility
LIBADD_DL="$LIBADD_DLOPEN $LIBADD_SHL_LOAD"
AC_SUBST([LIBADD_DL])

LIBS=$lt_dlload_save_LIBS
AC_LANG_POP
])
m4trace:/usr/share/aclocal/ltdl.m4:748: -1- AU_DEFUN([AC_LTDL_DLLIB], [m4_if($#, 0, [LT_LIB_DLLOAD], [LT_LIB_DLLOAD($@)])])
m4trace:/usr/share/aclocal/ltdl.m4:748: -1- AC_DEFUN([AC_LTDL_DLLIB], [AC_DIAGNOSE([obsolete], [The macro `AC_LTDL_DLLIB' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_LIB_DLLOAD], [LT_LIB_DLLOAD($@)])])
m4trace:/usr/share/aclocal/ltdl.m4:756: -1- AC_DEFUN([LT_SYS_SYMBOL_USCORE], [m4_require([_LT_CMD_GLOBAL_SYMBOLS])dnl
AC_CACHE_CHECK([for _ prefix in compiled symbols],
  [lt_cv_sys_symbol_underscore],
  [lt_cv_sys_symbol_underscore=no
  cat > conftest.$ac_ext <<_LT_EOF
void nm_test_func(){}
int main(){nm_test_func;return 0;}
_LT_EOF
  if AC_TRY_EVAL(ac_compile); then
    # Now try to grab the symbols.
    ac_nlist=conftest.nm
    if AC_TRY_EVAL(NM conftest.$ac_objext \| $lt_cv_sys_global_symbol_pipe \> $ac_nlist) && test -s "$ac_nlist"; then
      # See whether the symbols have a leading underscore.
      if grep '^. _nm_test_func' "$ac_nlist" >/dev/null; then
        lt_cv_sys_symbol_underscore=yes
      else
        if grep '^. nm_test_func ' "$ac_nlist" >/dev/null; then
	  :
        else
	  echo "configure: cannot find nm_test_func in $ac_nlist" >&AS_MESSAGE_LOG_FD
        fi
      fi
    else
      echo "configure: cannot run $lt_cv_sys_global_symbol_pipe" >&AS_MESSAGE_LOG_FD
    fi
  else
    echo "configure: failed program was:" >&AS_MESSAGE_LOG_FD
    cat conftest.c >&AS_MESSAGE_LOG_FD
  fi
  rm -rf conftest*
  ])
  sys_symbol_underscore=$lt_cv_sys_symbol_underscore
  AC_SUBST([sys_symbol_underscore])
])
m4trace:/usr/share/aclocal/ltdl.m4:793: -1- AU_DEFUN([AC_LTDL_SYMBOL_USCORE], [m4_if($#, 0, [LT_SYS_SYMBOL_USCORE], [LT_SYS_SYMBOL_USCORE($@)])])
m4trace:/usr/share/aclocal/ltdl.m4:793: -1- AC_DEFUN([AC_LTDL_SYMBOL_USCORE], [AC_DIAGNOSE([obsolete], [The macro `AC_LTDL_SYMBOL_USCORE' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_SYS_SYMBOL_USCORE], [LT_SYS_SYMBOL_USCORE($@)])])
m4trace:/usr/share/aclocal/ltdl.m4:800: -1- AC_DEFUN([LT_FUNC_DLSYM_USCORE], [AC_REQUIRE([_LT_COMPILER_PIC])dnl	for lt_prog_compiler_wl
AC_REQUIRE([LT_SYS_SYMBOL_USCORE])dnl	for lt_cv_sys_symbol_underscore
AC_REQUIRE([LT_SYS_MODULE_EXT])dnl	for libltdl_cv_shlibext
if test yes = "$lt_cv_sys_symbol_underscore"; then
  if test yes = "$libltdl_cv_func_dlopen" || test yes = "$libltdl_cv_lib_dl_dlopen"; then
    AC_CACHE_CHECK([whether we have to add an underscore for dlsym],
      [libltdl_cv_need_uscore],
      [libltdl_cv_need_uscore=unknown
      dlsym_uscore_save_LIBS=$LIBS
      LIBS="$LIBS $LIBADD_DLOPEN"
      libname=conftmod # stay within 8.3 filename limits!
      cat >$libname.$ac_ext <<_LT_EOF
[#line $LINENO "configure"
#include "confdefs.h"
/* When -fvisibility=hidden is used, assume the code has been annotated
   correspondingly for the symbols needed.  */
#if defined __GNUC__ && (((__GNUC__ == 3) && (__GNUC_MINOR__ >= 3)) || (__GNUC__ > 3))
int fnord () __attribute__((visibility("default")));
#endif
int fnord () { return 42; }]
_LT_EOF

      # ltfn_module_cmds module_cmds
      # Execute tilde-delimited MODULE_CMDS with environment primed for
      # $module_cmds or $archive_cmds type content.
      ltfn_module_cmds ()
      {( # subshell avoids polluting parent global environment
          module_cmds_save_ifs=$IFS; IFS='~'
          for cmd in @S|@1; do
            IFS=$module_cmds_save_ifs
            libobjs=$libname.$ac_objext; lib=$libname$libltdl_cv_shlibext
            rpath=/not-exists; soname=$libname$libltdl_cv_shlibext; output_objdir=.
            major=; versuffix=; verstring=; deplibs=
            ECHO=echo; wl=$lt_prog_compiler_wl; allow_undefined_flag=
            eval $cmd
          done
          IFS=$module_cmds_save_ifs
      )}

      # Compile a loadable module using libtool macro expansion results.
      $CC $pic_flag -c $libname.$ac_ext
      ltfn_module_cmds "${module_cmds:-$archive_cmds}"

      # Try to fetch fnord with dlsym().
      libltdl_dlunknown=0; libltdl_dlnouscore=1; libltdl_dluscore=2
      cat >conftest.$ac_ext <<_LT_EOF
[#line $LINENO "configure"
#include "confdefs.h"
#if HAVE_DLFCN_H
#include <dlfcn.h>
#endif
#include <stdio.h>
#ifndef RTLD_GLOBAL
#  ifdef DL_GLOBAL
#    define RTLD_GLOBAL DL_GLOBAL
#  else
#    define RTLD_GLOBAL 0
#  endif
#endif
#ifndef RTLD_NOW
#  ifdef DL_NOW
#    define RTLD_NOW DL_NOW
#  else
#    define RTLD_NOW 0
#  endif
#endif
int main () {
  void *handle = dlopen ("`pwd`/$libname$libltdl_cv_shlibext", RTLD_GLOBAL|RTLD_NOW);
  int status = $libltdl_dlunknown;
  if (handle) {
    if (dlsym (handle, "fnord"))
      status = $libltdl_dlnouscore;
    else {
      if (dlsym (handle, "_fnord"))
        status = $libltdl_dluscore;
      else
	puts (dlerror ());
    }
    dlclose (handle);
  } else
    puts (dlerror ());
  return status;
}]
_LT_EOF
      if AC_TRY_EVAL(ac_link) && test -s "conftest$ac_exeext" 2>/dev/null; then
        (./conftest; exit; ) >&AS_MESSAGE_LOG_FD 2>/dev/null
        libltdl_status=$?
        case x$libltdl_status in
          x$libltdl_dlnouscore) libltdl_cv_need_uscore=no ;;
	  x$libltdl_dluscore) libltdl_cv_need_uscore=yes ;;
	  x*) libltdl_cv_need_uscore=unknown ;;
        esac
      fi
      rm -rf conftest* $libname*
      LIBS=$dlsym_uscore_save_LIBS
    ])
  fi
fi

if test yes = "$libltdl_cv_need_uscore"; then
  AC_DEFINE([NEED_USCORE], [1],
    [Define if dlsym() requires a leading underscore in symbol names.])
fi
])
m4trace:/usr/share/aclocal/ltdl.m4:907: -1- AU_DEFUN([AC_LTDL_DLSYM_USCORE], [m4_if($#, 0, [LT_FUNC_DLSYM_USCORE], [LT_FUNC_DLSYM_USCORE($@)])])
m4trace:/usr/share/aclocal/ltdl.m4:907: -1- AC_DEFUN([AC_LTDL_DLSYM_USCORE], [AC_DIAGNOSE([obsolete], [The macro `AC_LTDL_DLSYM_USCORE' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_FUNC_DLSYM_USCORE], [LT_FUNC_DLSYM_USCORE($@)])])
m4trace:/usr/share/aclocal/pkg.m4:58: -1- AC_DEFUN([PKG_PROG_PKG_CONFIG], [m4_pattern_forbid([^_?PKG_[A-Z_]+$])
m4_pattern_allow([^PKG_CONFIG(_(PATH|LIBDIR|SYSROOT_DIR|ALLOW_SYSTEM_(CFLAGS|LIBS)))?$])
m4_pattern_allow([^PKG_CONFIG_(DISABLE_UNINSTALLED|TOP_BUILD_DIR|DEBUG_SPEW)$])
AC_ARG_VAR([PKG_CONFIG], [path to pkg-config utility])
AC_ARG_VAR([PKG_CONFIG_PATH], [directories to add to pkg-config's search path])
AC_ARG_VAR([PKG_CONFIG_LIBDIR], [path overriding pkg-config's built-in search path])

if test "x$ac_cv_env_PKG_CONFIG_set" != "xset"; then
	AC_PATH_TOOL([PKG_CONFIG], [pkg-config])
fi
if test -n "$PKG_CONFIG"; then
	_pkg_min_version=m4_default([$1], [0.9.0])
	AC_MSG_CHECKING([pkg-config is at least version $_pkg_min_version])
	if $PKG_CONFIG --atleast-pkgconfig-version $_pkg_min_version; then
		AC_MSG_RESULT([yes])
	else
		AC_MSG_RESULT([no])
		PKG_CONFIG=""
	fi
fi[]dnl
])
m4trace:/usr/share/aclocal/pkg.m4:92: -1- AC_DEFUN([PKG_CHECK_EXISTS], [AC_REQUIRE([PKG_PROG_PKG_CONFIG])dnl
if test -n "$PKG_CONFIG" && \
    AC_RUN_LOG([$PKG_CONFIG --exists --print-errors "$1"]); then
  m4_default([$2], [:])
m4_ifvaln([$3], [else
  $3])dnl
fi])
m4trace:/usr/share/aclocal/pkg.m4:121: -1- AC_DEFUN([_PKG_SHORT_ERRORS_SUPPORTED], [AC_REQUIRE([PKG_PROG_PKG_CONFIG])
if $PKG_CONFIG --atleast-pkgconfig-version 0.20; then
        _pkg_short_errors_supported=yes
else
        _pkg_short_errors_supported=no
fi[]dnl
])
m4trace:/usr/share/aclocal/pkg.m4:139: -1- AC_DEFUN([PKG_CHECK_MODULES], [AC_REQUIRE([PKG_PROG_PKG_CONFIG])dnl
AC_ARG_VAR([$1][_CFLAGS], [C compiler flags for $1, overriding pkg-config])dnl
AC_ARG_VAR([$1][_LIBS], [linker flags for $1, overriding pkg-config])dnl

pkg_failed=no
AC_MSG_CHECKING([for $1])

_PKG_CONFIG([$1][_CFLAGS], [cflags], [$2])
_PKG_CONFIG([$1][_LIBS], [libs], [$2])

m4_define([_PKG_TEXT], [Alternatively, you may set the environment variables $1[]_CFLAGS
and $1[]_LIBS to avoid the need to call pkg-config.
See the pkg-config man page for more details.])

if test $pkg_failed = yes; then
   	AC_MSG_RESULT([no])
        _PKG_SHORT_ERRORS_SUPPORTED
        if test $_pkg_short_errors_supported = yes; then
	        $1[]_PKG_ERRORS=`$PKG_CONFIG --short-errors --print-errors --cflags --libs "$2" 2>&1`
        else 
	        $1[]_PKG_ERRORS=`$PKG_CONFIG --print-errors --cflags --libs "$2" 2>&1`
        fi
	# Put the nasty error message in config.log where it belongs
	echo "$$1[]_PKG_ERRORS" >&AS_MESSAGE_LOG_FD

	m4_default([$4], [AC_MSG_ERROR(
[Package requirements ($2) were not met:

$$1_PKG_ERRORS

Consider adjusting the PKG_CONFIG_PATH environment variable if you
installed software in a non-standard prefix.

_PKG_TEXT])[]dnl
        ])
elif test $pkg_failed = untried; then
     	AC_MSG_RESULT([no])
	m4_default([$4], [AC_MSG_FAILURE(
[The pkg-config script could not be found or is too old.  Make sure it
is in your PATH or set the PKG_CONFIG environment variable to the full
path to pkg-config.

_PKG_TEXT

To get pkg-config, see <http://pkg-config.freedesktop.org/>.])[]dnl
        ])
else
	$1[]_CFLAGS=$pkg_cv_[]$1[]_CFLAGS
	$1[]_LIBS=$pkg_cv_[]$1[]_LIBS
        AC_MSG_RESULT([yes])
	$3
fi[]dnl
])
m4trace:/usr/share/aclocal/pkg.m4:208: -1- AC_DEFUN([PKG_CHECK_MODULES_STATIC], [AC_REQUIRE([PKG_PROG_PKG_CONFIG])dnl
_save_PKG_CONFIG=$PKG_CONFIG
PKG_CONFIG="$PKG_CONFIG --static"
PKG_CHECK_MODULES($@)
PKG_CONFIG=$_save_PKG_CONFIG[]dnl
])
m4trace:/usr/share/aclocal/pkg.m4:226: -1- AC_DEFUN([PKG_INSTALLDIR], [m4_pushdef([pkg_default], [m4_default([$1], ['${libdir}/pkgconfig'])])
m4_pushdef([pkg_description],
    [pkg-config installation directory @<:@]pkg_default[@:>@])
AC_ARG_WITH([pkgconfigdir],
    [AS_HELP_STRING([--with-pkgconfigdir], pkg_description)],,
    [with_pkgconfigdir=]pkg_default)
AC_SUBST([pkgconfigdir], [$with_pkgconfigdir])
m4_popdef([pkg_default])
m4_popdef([pkg_description])
])
m4trace:/usr/share/aclocal/pkg.m4:248: -1- AC_DEFUN([PKG_NOARCH_INSTALLDIR], [m4_pushdef([pkg_default], [m4_default([$1], ['${datadir}/pkgconfig'])])
m4_pushdef([pkg_description],
    [pkg-config arch-independent installation directory @<:@]pkg_default[@:>@])
AC_ARG_WITH([noarch-pkgconfigdir],
    [AS_HELP_STRING([--with-noarch-pkgconfigdir], pkg_description)],,
    [with_noarch_pkgconfigdir=]pkg_default)
AC_SUBST([noarch_pkgconfigdir], [$with_noarch_pkgconfigdir])
m4_popdef([pkg_default])
m4_popdef([pkg_description])
])
m4trace:/usr/share/aclocal/pkg.m4:267: -1- AC_DEFUN([PKG_CHECK_VAR], [AC_REQUIRE([PKG_PROG_PKG_CONFIG])dnl
AC_ARG_VAR([$1], [value of $3 for $2, overriding pkg-config])dnl

_PKG_CONFIG([$1], [variable="][$3]["], [$2])
AS_VAR_COPY([$1], [pkg_cv_][$1])

AS_VAR_IF([$1], [""], [$5], [$4])dnl
])
m4trace:/usr/share/aclocal-1.16/amversion.m4:14: -1- AC_DEFUN([AM_AUTOMAKE_VERSION], [am__api_version='1.16'
dnl Some users find AM_AUTOMAKE_VERSION and mistake it for a way to
dnl require some minimum version.  Point them to the right macro.
m4_if([$1], [1.16.1], [],
      [AC_FATAL([Do not call $0, use AM_INIT_AUTOMAKE([$1]).])])dnl
])
m4trace:/usr/share/aclocal-1.16/amversion.m4:33: -1- AC_DEFUN([AM_SET_CURRENT_AUTOMAKE_VERSION], [AM_AUTOMAKE_VERSION([1.16.1])dnl
m4_ifndef([AC_AUTOCONF_VERSION],
  [m4_copy([m4_PACKAGE_VERSION], [AC_AUTOCONF_VERSION])])dnl
_AM_AUTOCONF_VERSION(m4_defn([AC_AUTOCONF_VERSION]))])
m4trace:/usr/share/aclocal-1.16/auxdir.m4:47: -1- AC_DEFUN([AM_AUX_DIR_EXPAND], [AC_REQUIRE([AC_CONFIG_AUX_DIR_DEFAULT])dnl
# Expand $ac_aux_dir to an absolute path.
am_aux_dir=`cd "$ac_aux_dir" && pwd`
])
m4trace:/usr/share/aclocal-1.16/cond.m4:12: -1- AC_DEFUN([AM_CONDITIONAL], [AC_PREREQ([2.52])dnl
 m4_if([$1], [TRUE],  [AC_FATAL([$0: invalid condition: $1])],
       [$1], [FALSE], [AC_FATAL([$0: invalid condition: $1])])dnl
AC_SUBST([$1_TRUE])dnl
AC_SUBST([$1_FALSE])dnl
_AM_SUBST_NOTMAKE([$1_TRUE])dnl
_AM_SUBST_NOTMAKE([$1_FALSE])dnl
m4_define([_AM_COND_VALUE_$1], [$2])dnl
if $2; then
  $1_TRUE=
  $1_FALSE='#'
else
  $1_TRUE='#'
  $1_FALSE=
fi
AC_CONFIG_COMMANDS_PRE(
[if test -z "${$1_TRUE}" && test -z "${$1_FALSE}"; then
  AC_MSG_ERROR([[conditional "$1" was never defined.
Usually this means the macro was only invoked conditionally.]])
fi])])
m4trace:/usr/share/aclocal-1.16/depend.m4:26: -1- AC_DEFUN([_AM_DEPENDENCIES], [AC_REQUIRE([AM_SET_DEPDIR])dnl
AC_REQUIRE([AM_OUTPUT_DEPENDENCY_COMMANDS])dnl
AC_REQUIRE([AM_MAKE_INCLUDE])dnl
AC_REQUIRE([AM_DEP_TRACK])dnl

m4_if([$1], [CC],   [depcc="$CC"   am_compiler_list=],
      [$1], [CXX],  [depcc="$CXX"  am_compiler_list=],
      [$1], [OBJC], [depcc="$OBJC" am_compiler_list='gcc3 gcc'],
      [$1], [OBJCXX], [depcc="$OBJCXX" am_compiler_list='gcc3 gcc'],
      [$1], [UPC],  [depcc="$UPC"  am_compiler_list=],
      [$1], [GCJ],  [depcc="$GCJ"  am_compiler_list='gcc3 gcc'],
                    [depcc="$$1"   am_compiler_list=])

AC_CACHE_CHECK([dependency style of $depcc],
               [am_cv_$1_dependencies_compiler_type],
[if test -z "$AMDEP_TRUE" && test -f "$am_depcomp"; then
  # We make a subdir and do the tests there.  Otherwise we can end up
  # making bogus files that we don't know about and never remove.  For
  # instance it was reported that on HP-UX the gcc test will end up
  # making a dummy file named 'D' -- because '-MD' means "put the output
  # in D".
  rm -rf conftest.dir
  mkdir conftest.dir
  # Copy depcomp to subdir because otherwise we won't find it if we're
  # using a relative directory.
  cp "$am_depcomp" conftest.dir
  cd conftest.dir
  # We will build objects and dependencies in a subdirectory because
  # it helps to detect inapplicable dependency modes.  For instance
  # both Tru64's cc and ICC support -MD to output dependencies as a
  # side effect of compilation, but ICC will put the dependencies in
  # the current directory while Tru64 will put them in the object
  # directory.
  mkdir sub

  am_cv_$1_dependencies_compiler_type=none
  if test "$am_compiler_list" = ""; then
     am_compiler_list=`sed -n ['s/^#*\([a-zA-Z0-9]*\))$/\1/p'] < ./depcomp`
  fi
  am__universal=false
  m4_case([$1], [CC],
    [case " $depcc " in #(
     *\ -arch\ *\ -arch\ *) am__universal=true ;;
     esac],
    [CXX],
    [case " $depcc " in #(
     *\ -arch\ *\ -arch\ *) am__universal=true ;;
     esac])

  for depmode in $am_compiler_list; do
    # Setup a source with many dependencies, because some compilers
    # like to wrap large dependency lists on column 80 (with \), and
    # we should not choose a depcomp mode which is confused by this.
    #
    # We need to recreate these files for each test, as the compiler may
    # overwrite some of them when testing with obscure command lines.
    # This happens at least with the AIX C compiler.
    : > sub/conftest.c
    for i in 1 2 3 4 5 6; do
      echo '#include "conftst'$i'.h"' >> sub/conftest.c
      # Using ": > sub/conftst$i.h" creates only sub/conftst1.h with
      # Solaris 10 /bin/sh.
      echo '/* dummy */' > sub/conftst$i.h
    done
    echo "${am__include} ${am__quote}sub/conftest.Po${am__quote}" > confmf

    # We check with '-c' and '-o' for the sake of the "dashmstdout"
    # mode.  It turns out that the SunPro C++ compiler does not properly
    # handle '-M -o', and we need to detect this.  Also, some Intel
    # versions had trouble with output in subdirs.
    am__obj=sub/conftest.${OBJEXT-o}
    am__minus_obj="-o $am__obj"
    case $depmode in
    gcc)
      # This depmode causes a compiler race in universal mode.
      test "$am__universal" = false || continue
      ;;
    nosideeffect)
      # After this tag, mechanisms are not by side-effect, so they'll
      # only be used when explicitly requested.
      if test "x$enable_dependency_tracking" = xyes; then
	continue
      else
	break
      fi
      ;;
    msvc7 | msvc7msys | msvisualcpp | msvcmsys)
      # This compiler won't grok '-c -o', but also, the minuso test has
      # not run yet.  These depmodes are late enough in the game, and
      # so weak that their functioning should not be impacted.
      am__obj=conftest.${OBJEXT-o}
      am__minus_obj=
      ;;
    none) break ;;
    esac
    if depmode=$depmode \
       source=sub/conftest.c object=$am__obj \
       depfile=sub/conftest.Po tmpdepfile=sub/conftest.TPo \
       $SHELL ./depcomp $depcc -c $am__minus_obj sub/conftest.c \
         >/dev/null 2>conftest.err &&
       grep sub/conftst1.h sub/conftest.Po > /dev/null 2>&1 &&
       grep sub/conftst6.h sub/conftest.Po > /dev/null 2>&1 &&
       grep $am__obj sub/conftest.Po > /dev/null 2>&1 &&
       ${MAKE-make} -s -f confmf > /dev/null 2>&1; then
      # icc doesn't choke on unknown options, it will just issue warnings
      # or remarks (even with -Werror).  So we grep stderr for any message
      # that says an option was ignored or not supported.
      # When given -MP, icc 7.0 and 7.1 complain thusly:
      #   icc: Command line warning: ignoring option '-M'; no argument required
      # The diagnosis changed in icc 8.0:
      #   icc: Command line remark: option '-MP' not supported
      if (grep 'ignoring option' conftest.err ||
          grep 'not supported' conftest.err) >/dev/null 2>&1; then :; else
        am_cv_$1_dependencies_compiler_type=$depmode
        break
      fi
    fi
  done

  cd ..
  rm -rf conftest.dir
else
  am_cv_$1_dependencies_compiler_type=none
fi
])
AC_SUBST([$1DEPMODE], [depmode=$am_cv_$1_dependencies_compiler_type])
AM_CONDITIONAL([am__fastdep$1], [
  test "x$enable_dependency_tracking" != xno \
  && test "$am_cv_$1_dependencies_compiler_type" = gcc3])
])
m4trace:/usr/share/aclocal-1.16/depend.m4:163: -1- AC_DEFUN([AM_SET_DEPDIR], [AC_REQUIRE([AM_SET_LEADING_DOT])dnl
AC_SUBST([DEPDIR], ["${am__leading_dot}deps"])dnl
])
m4trace:/usr/share/aclocal-1.16/depend.m4:171: -1- AC_DEFUN([AM_DEP_TRACK], [AC_ARG_ENABLE([dependency-tracking], [dnl
AS_HELP_STRING(
  [--enable-dependency-tracking],
  [do not reject slow dependency extractors])
AS_HELP_STRING(
  [--disable-dependency-tracking],
  [speeds up one-time build])])
if test "x$enable_dependency_tracking" != xno; then
  am_depcomp="$ac_aux_dir/depcomp"
  AMDEPBACKSLASH='\'
  am__nodep='_no'
fi
AM_CONDITIONAL([AMDEP], [test "x$enable_dependency_tracking" != xno])
AC_SUBST([AMDEPBACKSLASH])dnl
_AM_SUBST_NOTMAKE([AMDEPBACKSLASH])dnl
AC_SUBST([am__nodep])dnl
_AM_SUBST_NOTMAKE([am__nodep])dnl
])
m4trace:/usr/share/aclocal-1.16/depout.m4:11: -1- AC_DEFUN([_AM_OUTPUT_DEPENDENCY_COMMANDS], [{
  # Older Autoconf quotes --file arguments for eval, but not when files
  # are listed without --file.  Let's play safe and only enable the eval
  # if we detect the quoting.
  # TODO: see whether this extra hack can be removed once we start
  # requiring Autoconf 2.70 or later.
  AS_CASE([$CONFIG_FILES],
          [*\'*], [eval set x "$CONFIG_FILES"],
          [*], [set x $CONFIG_FILES])
  shift
  # Used to flag and report bootstrapping failures.
  am_rc=0
  for am_mf
  do
    # Strip MF so we end up with the name of the file.
    am_mf=`AS_ECHO(["$am_mf"]) | sed -e 's/:.*$//'`
    # Check whether this is an Automake generated Makefile which includes
    # dependency-tracking related rules and includes.
    # Grep'ing the whole file directly is not great: AIX grep has a line
    # limit of 2048, but all sed's we know have understand at least 4000.
    sed -n 's,^am--depfiles:.*,X,p' "$am_mf" | grep X >/dev/null 2>&1 \
      || continue
    am_dirpart=`AS_DIRNAME(["$am_mf"])`
    am_filepart=`AS_BASENAME(["$am_mf"])`
    AM_RUN_LOG([cd "$am_dirpart" \
      && sed -e '/# am--include-marker/d' "$am_filepart" \
        | $MAKE -f - am--depfiles]) || am_rc=$?
  done
  if test $am_rc -ne 0; then
    AC_MSG_FAILURE([Something went wrong bootstrapping makefile fragments
    for automatic dependency tracking.  Try re-running configure with the
    '--disable-dependency-tracking' option to at least be able to build
    the package (albeit without support for automatic dependency tracking).])
  fi
  AS_UNSET([am_dirpart])
  AS_UNSET([am_filepart])
  AS_UNSET([am_mf])
  AS_UNSET([am_rc])
  rm -f conftest-deps.mk
}
])
m4trace:/usr/share/aclocal-1.16/depout.m4:62: -1- AC_DEFUN([AM_OUTPUT_DEPENDENCY_COMMANDS], [AC_CONFIG_COMMANDS([depfiles],
     [test x"$AMDEP_TRUE" != x"" || _AM_OUTPUT_DEPENDENCY_COMMANDS],
     [AMDEP_TRUE="$AMDEP_TRUE" MAKE="${MAKE-make}"])])
m4trace:/usr/share/aclocal-1.16/init.m4:29: -1- AC_DEFUN([AM_INIT_AUTOMAKE], [AC_PREREQ([2.65])dnl
dnl Autoconf wants to disallow AM_ names.  We explicitly allow
dnl the ones we care about.
m4_pattern_allow([^AM_[A-Z]+FLAGS$])dnl
AC_REQUIRE([AM_SET_CURRENT_AUTOMAKE_VERSION])dnl
AC_REQUIRE([AC_PROG_INSTALL])dnl
if test "`cd $srcdir && pwd`" != "`pwd`"; then
  # Use -I$(srcdir) only when $(srcdir) != ., so that make's output
  # is not polluted with repeated "-I."
  AC_SUBST([am__isrc], [' -I$(srcdir)'])_AM_SUBST_NOTMAKE([am__isrc])dnl
  # test to see if srcdir already configured
  if test -f $srcdir/config.status; then
    AC_MSG_ERROR([source directory already configured; run "make distclean" there first])
  fi
fi

# test whether we have cygpath
if test -z "$CYGPATH_W"; then
  if (cygpath --version) >/dev/null 2>/dev/null; then
    CYGPATH_W='cygpath -w'
  else
    CYGPATH_W=echo
  fi
fi
AC_SUBST([CYGPATH_W])

# Define the identity of the package.
dnl Distinguish between old-style and new-style calls.
m4_ifval([$2],
[AC_DIAGNOSE([obsolete],
             [$0: two- and three-arguments forms are deprecated.])
m4_ifval([$3], [_AM_SET_OPTION([no-define])])dnl
 AC_SUBST([PACKAGE], [$1])dnl
 AC_SUBST([VERSION], [$2])],
[_AM_SET_OPTIONS([$1])dnl
dnl Diagnose old-style AC_INIT with new-style AM_AUTOMAKE_INIT.
m4_if(
  m4_ifdef([AC_PACKAGE_NAME], [ok]):m4_ifdef([AC_PACKAGE_VERSION], [ok]),
  [ok:ok],,
  [m4_fatal([AC_INIT should be called with package and version arguments])])dnl
 AC_SUBST([PACKAGE], ['AC_PACKAGE_TARNAME'])dnl
 AC_SUBST([VERSION], ['AC_PACKAGE_VERSION'])])dnl

_AM_IF_OPTION([no-define],,
[AC_DEFINE_UNQUOTED([PACKAGE], ["$PACKAGE"], [Name of package])
 AC_DEFINE_UNQUOTED([VERSION], ["$VERSION"], [Version number of package])])dnl

# Some tools Automake needs.
AC_REQUIRE([AM_SANITY_CHECK])dnl
AC_REQUIRE([AC_ARG_PROGRAM])dnl
AM_MISSING_PROG([ACLOCAL], [aclocal-${am__api_version}])
AM_MISSING_PROG([AUTOCONF], [autoconf])
AM_MISSING_PROG([AUTOMAKE], [automake-${am__api_version}])
AM_MISSING_PROG([AUTOHEADER], [autoheader])
AM_MISSING_PROG([MAKEINFO], [makeinfo])
AC_REQUIRE([AM_PROG_INSTALL_SH])dnl
AC_REQUIRE([AM_PROG_INSTALL_STRIP])dnl
AC_REQUIRE([AC_PROG_MKDIR_P])dnl
# For better backward compatibility.  To be removed once Automake 1.9.x
# dies out for good.  For more background, see:
# <https://lists.gnu.org/archive/html/automake/2012-07/msg00001.html>
# <https://lists.gnu.org/archive/html/automake/2012-07/msg00014.html>
AC_SUBST([mkdir_p], ['$(MKDIR_P)'])
# We need awk for the "check" target (and possibly the TAP driver).  The
# system "awk" is bad on some platforms.
AC_REQUIRE([AC_PROG_AWK])dnl
AC_REQUIRE([AC_PROG_MAKE_SET])dnl
AC_REQUIRE([AM_SET_LEADING_DOT])dnl
_AM_IF_OPTION([tar-ustar], [_AM_PROG_TAR([ustar])],
	      [_AM_IF_OPTION([tar-pax], [_AM_PROG_TAR([pax])],
			     [_AM_PROG_TAR([v7])])])
_AM_IF_OPTION([no-dependencies],,
[AC_PROVIDE_IFELSE([AC_PROG_CC],
		  [_AM_DEPENDENCIES([CC])],
		  [m4_define([AC_PROG_CC],
			     m4_defn([AC_PROG_CC])[_AM_DEPENDENCIES([CC])])])dnl
AC_PROVIDE_IFELSE([AC_PROG_CXX],
		  [_AM_DEPENDENCIES([CXX])],
		  [m4_define([AC_PROG_CXX],
			     m4_defn([AC_PROG_CXX])[_AM_DEPENDENCIES([CXX])])])dnl
AC_PROVIDE_IFELSE([AC_PROG_OBJC],
		  [_AM_DEPENDENCIES([OBJC])],
		  [m4_define([AC_PROG_OBJC],
			     m4_defn([AC_PROG_OBJC])[_AM_DEPENDENCIES([OBJC])])])dnl
AC_PROVIDE_IFELSE([AC_PROG_OBJCXX],
		  [_AM_DEPENDENCIES([OBJCXX])],
		  [m4_define([AC_PROG_OBJCXX],
			     m4_defn([AC_PROG_OBJCXX])[_AM_DEPENDENCIES([OBJCXX])])])dnl
])
AC_REQUIRE([AM_SILENT_RULES])dnl
dnl The testsuite driver may need to know about EXEEXT, so add the
dnl 'am__EXEEXT' conditional if _AM_COMPILER_EXEEXT was seen.  This
dnl macro is hooked onto _AC_COMPILER_EXEEXT early, see below.
AC_CONFIG_COMMANDS_PRE(dnl
[m4_provide_if([_AM_COMPILER_EXEEXT],
  [AM_CONDITIONAL([am__EXEEXT], [test -n "$EXEEXT"])])])dnl

# POSIX will say in a future version that running "rm -f" with no argument
# is OK; and we want to be able to make that assumption in our Makefile
# recipes.  So use an aggressive probe to check that the usage we want is
# actually supported "in the wild" to an acceptable degree.
# See automake bug#10828.
# To make any issue more visible, cause the running configure to be aborted
# by default if the 'rm' program in use doesn't match our expectations; the
# user can still override this though.
if rm -f && rm -fr && rm -rf; then : OK; else
  cat >&2 <<'END'
Oops!

Your 'rm' program seems unable to run without file operands specified
on the command line, even when the '-f' option is present.  This is contrary
to the behaviour of most rm programs out there, and not conforming with
the upcoming POSIX standard: <http://austingroupbugs.net/view.php?id=542>

<NAME_EMAIL> about your system, including the value
of your $PATH and any error possibly output before this message.  This
can help us improve future automake versions.

END
  if test x"$ACCEPT_INFERIOR_RM_PROGRAM" = x"yes"; then
    echo 'Configuration will proceed anyway, since you have set the' >&2
    echo 'ACCEPT_INFERIOR_RM_PROGRAM variable to "yes"' >&2
    echo >&2
  else
    cat >&2 <<'END'
Aborting the configuration process, to ensure you take notice of the issue.

You can download and install GNU coreutils to get an 'rm' implementation
that behaves properly: <https://www.gnu.org/software/coreutils/>.

If you want to complete the configuration process using your problematic
'rm' anyway, export the environment variable ACCEPT_INFERIOR_RM_PROGRAM
to "yes", and re-run configure.

END
    AC_MSG_ERROR([Your 'rm' program is bad, sorry.])
  fi
fi
dnl The trailing newline in this macro's definition is deliberate, for
dnl backward compatibility and to allow trailing 'dnl'-style comments
dnl after the AM_INIT_AUTOMAKE invocation. See automake bug#16841.
])
m4trace:/usr/share/aclocal-1.16/init.m4:186: -1- AC_DEFUN([_AC_AM_CONFIG_HEADER_HOOK], [# Compute $1's index in $config_headers.
_am_arg=$1
_am_stamp_count=1
for _am_header in $config_headers :; do
  case $_am_header in
    $_am_arg | $_am_arg:* )
      break ;;
    * )
      _am_stamp_count=`expr $_am_stamp_count + 1` ;;
  esac
done
echo "timestamp for $_am_arg" >`AS_DIRNAME(["$_am_arg"])`/stamp-h[]$_am_stamp_count])
m4trace:/usr/share/aclocal-1.16/install-sh.m4:11: -1- AC_DEFUN([AM_PROG_INSTALL_SH], [AC_REQUIRE([AM_AUX_DIR_EXPAND])dnl
if test x"${install_sh+set}" != xset; then
  case $am_aux_dir in
  *\ * | *\	*)
    install_sh="\${SHELL} '$am_aux_dir/install-sh'" ;;
  *)
    install_sh="\${SHELL} $am_aux_dir/install-sh"
  esac
fi
AC_SUBST([install_sh])])
m4trace:/usr/share/aclocal-1.16/lead-dot.m4:10: -1- AC_DEFUN([AM_SET_LEADING_DOT], [rm -rf .tst 2>/dev/null
mkdir .tst 2>/dev/null
if test -d .tst; then
  am__leading_dot=.
else
  am__leading_dot=_
fi
rmdir .tst 2>/dev/null
AC_SUBST([am__leading_dot])])
m4trace:/usr/share/aclocal-1.16/maintainer.m4:16: -1- AC_DEFUN([AM_MAINTAINER_MODE], [m4_case(m4_default([$1], [disable]),
       [enable], [m4_define([am_maintainer_other], [disable])],
       [disable], [m4_define([am_maintainer_other], [enable])],
       [m4_define([am_maintainer_other], [enable])
        m4_warn([syntax], [unexpected argument to AM@&t@_MAINTAINER_MODE: $1])])
AC_MSG_CHECKING([whether to enable maintainer-specific portions of Makefiles])
  dnl maintainer-mode's default is 'disable' unless 'enable' is passed
  AC_ARG_ENABLE([maintainer-mode],
    [AS_HELP_STRING([--]am_maintainer_other[-maintainer-mode],
      am_maintainer_other[ make rules and dependencies not useful
      (and sometimes confusing) to the casual installer])],
    [USE_MAINTAINER_MODE=$enableval],
    [USE_MAINTAINER_MODE=]m4_if(am_maintainer_other, [enable], [no], [yes]))
  AC_MSG_RESULT([$USE_MAINTAINER_MODE])
  AM_CONDITIONAL([MAINTAINER_MODE], [test $USE_MAINTAINER_MODE = yes])
  MAINT=$MAINTAINER_MODE_TRUE
  AC_SUBST([MAINT])dnl

])
m4trace:/usr/share/aclocal-1.16/make.m4:13: -1- AC_DEFUN([AM_MAKE_INCLUDE], [AC_MSG_CHECKING([whether ${MAKE-make} supports the include directive])
cat > confinc.mk << 'END'
am__doit:
	@echo this is the am__doit target >confinc.out
.PHONY: am__doit
END
am__include="#"
am__quote=
# BSD make does it like this.
echo '.include "confinc.mk" # ignored' > confmf.BSD
# Other make implementations (GNU, Solaris 10, AIX) do it like this.
echo 'include confinc.mk # ignored' > confmf.GNU
_am_result=no
for s in GNU BSD; do
  AM_RUN_LOG([${MAKE-make} -f confmf.$s && cat confinc.out])
  AS_CASE([$?:`cat confinc.out 2>/dev/null`],
      ['0:this is the am__doit target'],
      [AS_CASE([$s],
          [BSD], [am__include='.include' am__quote='"'],
          [am__include='include' am__quote=''])])
  if test "$am__include" != "#"; then
    _am_result="yes ($s style)"
    break
  fi
done
rm -f confinc.* confmf.*
AC_MSG_RESULT([${_am_result}])
AC_SUBST([am__include])])
m4trace:/usr/share/aclocal-1.16/make.m4:42: -1- m4_pattern_allow([^am__quote$])
m4trace:/usr/share/aclocal-1.16/missing.m4:11: -1- AC_DEFUN([AM_MISSING_PROG], [AC_REQUIRE([AM_MISSING_HAS_RUN])
$1=${$1-"${am_missing_run}$2"}
AC_SUBST($1)])
m4trace:/usr/share/aclocal-1.16/missing.m4:20: -1- AC_DEFUN([AM_MISSING_HAS_RUN], [AC_REQUIRE([AM_AUX_DIR_EXPAND])dnl
AC_REQUIRE_AUX_FILE([missing])dnl
if test x"${MISSING+set}" != xset; then
  case $am_aux_dir in
  *\ * | *\	*)
    MISSING="\${SHELL} \"$am_aux_dir/missing\"" ;;
  *)
    MISSING="\${SHELL} $am_aux_dir/missing" ;;
  esac
fi
# Use eval to expand $SHELL
if eval "$MISSING --is-lightweight"; then
  am_missing_run="$MISSING "
else
  am_missing_run=
  AC_MSG_WARN(['missing' script is too old or missing])
fi
])
m4trace:/usr/share/aclocal-1.16/options.m4:11: -1- AC_DEFUN([_AM_MANGLE_OPTION], [[_AM_OPTION_]m4_bpatsubst($1, [[^a-zA-Z0-9_]], [_])])
m4trace:/usr/share/aclocal-1.16/options.m4:17: -1- AC_DEFUN([_AM_SET_OPTION], [m4_define(_AM_MANGLE_OPTION([$1]), [1])])
m4trace:/usr/share/aclocal-1.16/options.m4:23: -1- AC_DEFUN([_AM_SET_OPTIONS], [m4_foreach_w([_AM_Option], [$1], [_AM_SET_OPTION(_AM_Option)])])
m4trace:/usr/share/aclocal-1.16/options.m4:29: -1- AC_DEFUN([_AM_IF_OPTION], [m4_ifset(_AM_MANGLE_OPTION([$1]), [$2], [$3])])
m4trace:/usr/share/aclocal-1.16/prog-cc-c-o.m4:12: -1- AC_DEFUN([_AM_PROG_CC_C_O], [AC_REQUIRE([AM_AUX_DIR_EXPAND])dnl
AC_REQUIRE_AUX_FILE([compile])dnl
AC_LANG_PUSH([C])dnl
AC_CACHE_CHECK(
  [whether $CC understands -c and -o together],
  [am_cv_prog_cc_c_o],
  [AC_LANG_CONFTEST([AC_LANG_PROGRAM([])])
  # Make sure it works both with $CC and with simple cc.
  # Following AC_PROG_CC_C_O, we do the test twice because some
  # compilers refuse to overwrite an existing .o file with -o,
  # though they will create one.
  am_cv_prog_cc_c_o=yes
  for am_i in 1 2; do
    if AM_RUN_LOG([$CC -c conftest.$ac_ext -o conftest2.$ac_objext]) \
         && test -f conftest2.$ac_objext; then
      : OK
    else
      am_cv_prog_cc_c_o=no
      break
    fi
  done
  rm -f core conftest*
  unset am_i])
if test "$am_cv_prog_cc_c_o" != yes; then
   # Losing compiler, so override with the script.
   # FIXME: It is wrong to rewrite CC.
   # But if we don't then we get into trouble of one sort or another.
   # A longer-term fix would be to have automake use am__CC in this case,
   # and then we could set am__CC="\$(top_srcdir)/compile \$(CC)"
   CC="$am_aux_dir/compile $CC"
fi
AC_LANG_POP([C])])
m4trace:/usr/share/aclocal-1.16/prog-cc-c-o.m4:47: -1- AC_DEFUN_ONCE([AM_PROG_CC_C_O], [AC_REQUIRE([AC_PROG_CC])])
m4trace:/usr/share/aclocal-1.16/runlog.m4:12: -1- AC_DEFUN([AM_RUN_LOG], [{ echo "$as_me:$LINENO: $1" >&AS_MESSAGE_LOG_FD
   ($1) >&AS_MESSAGE_LOG_FD 2>&AS_MESSAGE_LOG_FD
   ac_status=$?
   echo "$as_me:$LINENO: \$? = $ac_status" >&AS_MESSAGE_LOG_FD
   (exit $ac_status); }])
m4trace:/usr/share/aclocal-1.16/sanity.m4:11: -1- AC_DEFUN([AM_SANITY_CHECK], [AC_MSG_CHECKING([whether build environment is sane])
# Reject unsafe characters in $srcdir or the absolute working directory
# name.  Accept space and tab only in the latter.
am_lf='
'
case `pwd` in
  *[[\\\"\#\$\&\'\`$am_lf]]*)
    AC_MSG_ERROR([unsafe absolute working directory name]);;
esac
case $srcdir in
  *[[\\\"\#\$\&\'\`$am_lf\ \	]]*)
    AC_MSG_ERROR([unsafe srcdir value: '$srcdir']);;
esac

# Do 'set' in a subshell so we don't clobber the current shell's
# arguments.  Must try -L first in case configure is actually a
# symlink; some systems play weird games with the mod time of symlinks
# (eg FreeBSD returns the mod time of the symlink's containing
# directory).
if (
   am_has_slept=no
   for am_try in 1 2; do
     echo "timestamp, slept: $am_has_slept" > conftest.file
     set X `ls -Lt "$srcdir/configure" conftest.file 2> /dev/null`
     if test "$[*]" = "X"; then
	# -L didn't work.
	set X `ls -t "$srcdir/configure" conftest.file`
     fi
     if test "$[*]" != "X $srcdir/configure conftest.file" \
	&& test "$[*]" != "X conftest.file $srcdir/configure"; then

	# If neither matched, then we have a broken ls.  This can happen
	# if, for instance, CONFIG_SHELL is bash and it inherits a
	# broken ls alias from the environment.  This has actually
	# happened.  Such a system could not be considered "sane".
	AC_MSG_ERROR([ls -t appears to fail.  Make sure there is not a broken
  alias in your environment])
     fi
     if test "$[2]" = conftest.file || test $am_try -eq 2; then
       break
     fi
     # Just in case.
     sleep 1
     am_has_slept=yes
   done
   test "$[2]" = conftest.file
   )
then
   # Ok.
   :
else
   AC_MSG_ERROR([newly created file is older than distributed files!
Check your system clock])
fi
AC_MSG_RESULT([yes])
# If we didn't sleep, we still need to ensure time stamps of config.status and
# generated files are strictly newer.
am_sleep_pid=
if grep 'slept: no' conftest.file >/dev/null 2>&1; then
  ( sleep 1 ) &
  am_sleep_pid=$!
fi
AC_CONFIG_COMMANDS_PRE(
  [AC_MSG_CHECKING([that generated files are newer than configure])
   if test -n "$am_sleep_pid"; then
     # Hide warnings about reused PIDs.
     wait $am_sleep_pid 2>/dev/null
   fi
   AC_MSG_RESULT([done])])
rm -f conftest.file
])
m4trace:/usr/share/aclocal-1.16/silent.m4:12: -1- AC_DEFUN([AM_SILENT_RULES], [AC_ARG_ENABLE([silent-rules], [dnl
AS_HELP_STRING(
  [--enable-silent-rules],
  [less verbose build output (undo: "make V=1")])
AS_HELP_STRING(
  [--disable-silent-rules],
  [verbose build output (undo: "make V=0")])dnl
])
case $enable_silent_rules in @%:@ (((
  yes) AM_DEFAULT_VERBOSITY=0;;
   no) AM_DEFAULT_VERBOSITY=1;;
    *) AM_DEFAULT_VERBOSITY=m4_if([$1], [yes], [0], [1]);;
esac
dnl
dnl A few 'make' implementations (e.g., NonStop OS and NextStep)
dnl do not support nested variable expansions.
dnl See automake bug#9928 and bug#10237.
am_make=${MAKE-make}
AC_CACHE_CHECK([whether $am_make supports nested variables],
   [am_cv_make_support_nested_variables],
   [if AS_ECHO([['TRUE=$(BAR$(V))
BAR0=false
BAR1=true
V=1
am__doit:
	@$(TRUE)
.PHONY: am__doit']]) | $am_make -f - >/dev/null 2>&1; then
  am_cv_make_support_nested_variables=yes
else
  am_cv_make_support_nested_variables=no
fi])
if test $am_cv_make_support_nested_variables = yes; then
  dnl Using '$V' instead of '$(V)' breaks IRIX make.
  AM_V='$(V)'
  AM_DEFAULT_V='$(AM_DEFAULT_VERBOSITY)'
else
  AM_V=$AM_DEFAULT_VERBOSITY
  AM_DEFAULT_V=$AM_DEFAULT_VERBOSITY
fi
AC_SUBST([AM_V])dnl
AM_SUBST_NOTMAKE([AM_V])dnl
AC_SUBST([AM_DEFAULT_V])dnl
AM_SUBST_NOTMAKE([AM_DEFAULT_V])dnl
AC_SUBST([AM_DEFAULT_VERBOSITY])dnl
AM_BACKSLASH='\'
AC_SUBST([AM_BACKSLASH])dnl
_AM_SUBST_NOTMAKE([AM_BACKSLASH])dnl
])
m4trace:/usr/share/aclocal-1.16/strip.m4:17: -1- AC_DEFUN([AM_PROG_INSTALL_STRIP], [AC_REQUIRE([AM_PROG_INSTALL_SH])dnl
# Installed binaries are usually stripped using 'strip' when the user
# run "make install-strip".  However 'strip' might not be the right
# tool to use in cross-compilation environments, therefore Automake
# will honor the 'STRIP' environment variable to overrule this program.
dnl Don't test for $cross_compiling = yes, because it might be 'maybe'.
if test "$cross_compiling" != no; then
  AC_CHECK_TOOL([STRIP], [strip], :)
fi
INSTALL_STRIP_PROGRAM="\$(install_sh) -c -s"
AC_SUBST([INSTALL_STRIP_PROGRAM])])
m4trace:/usr/share/aclocal-1.16/substnot.m4:12: -1- AC_DEFUN([_AM_SUBST_NOTMAKE])
m4trace:/usr/share/aclocal-1.16/substnot.m4:17: -1- AC_DEFUN([AM_SUBST_NOTMAKE], [_AM_SUBST_NOTMAKE($@)])
m4trace:/usr/share/aclocal-1.16/tar.m4:23: -1- AC_DEFUN([_AM_PROG_TAR], [# Always define AMTAR for backward compatibility.  Yes, it's still used
# in the wild :-(  We should find a proper way to deprecate it ...
AC_SUBST([AMTAR], ['$${TAR-tar}'])

# We'll loop over all known methods to create a tar archive until one works.
_am_tools='gnutar m4_if([$1], [ustar], [plaintar]) pax cpio none'

m4_if([$1], [v7],
  [am__tar='$${TAR-tar} chof - "$$tardir"' am__untar='$${TAR-tar} xf -'],

  [m4_case([$1],
    [ustar],
     [# The POSIX 1988 'ustar' format is defined with fixed-size fields.
      # There is notably a 21 bits limit for the UID and the GID.  In fact,
      # the 'pax' utility can hang on bigger UID/GID (see automake bug#8343
      # and bug#13588).
      am_max_uid=2097151 # 2^21 - 1
      am_max_gid=$am_max_uid
      # The $UID and $GID variables are not portable, so we need to resort
      # to the POSIX-mandated id(1) utility.  Errors in the 'id' calls
      # below are definitely unexpected, so allow the users to see them
      # (that is, avoid stderr redirection).
      am_uid=`id -u || echo unknown`
      am_gid=`id -g || echo unknown`
      AC_MSG_CHECKING([whether UID '$am_uid' is supported by ustar format])
      if test $am_uid -le $am_max_uid; then
         AC_MSG_RESULT([yes])
      else
         AC_MSG_RESULT([no])
         _am_tools=none
      fi
      AC_MSG_CHECKING([whether GID '$am_gid' is supported by ustar format])
      if test $am_gid -le $am_max_gid; then
         AC_MSG_RESULT([yes])
      else
        AC_MSG_RESULT([no])
        _am_tools=none
      fi],

  [pax],
    [],

  [m4_fatal([Unknown tar format])])

  AC_MSG_CHECKING([how to create a $1 tar archive])

  # Go ahead even if we have the value already cached.  We do so because we
  # need to set the values for the 'am__tar' and 'am__untar' variables.
  _am_tools=${am_cv_prog_tar_$1-$_am_tools}

  for _am_tool in $_am_tools; do
    case $_am_tool in
    gnutar)
      for _am_tar in tar gnutar gtar; do
        AM_RUN_LOG([$_am_tar --version]) && break
      done
      am__tar="$_am_tar --format=m4_if([$1], [pax], [posix], [$1]) -chf - "'"$$tardir"'
      am__tar_="$_am_tar --format=m4_if([$1], [pax], [posix], [$1]) -chf - "'"$tardir"'
      am__untar="$_am_tar -xf -"
      ;;
    plaintar)
      # Must skip GNU tar: if it does not support --format= it doesn't create
      # ustar tarball either.
      (tar --version) >/dev/null 2>&1 && continue
      am__tar='tar chf - "$$tardir"'
      am__tar_='tar chf - "$tardir"'
      am__untar='tar xf -'
      ;;
    pax)
      am__tar='pax -L -x $1 -w "$$tardir"'
      am__tar_='pax -L -x $1 -w "$tardir"'
      am__untar='pax -r'
      ;;
    cpio)
      am__tar='find "$$tardir" -print | cpio -o -H $1 -L'
      am__tar_='find "$tardir" -print | cpio -o -H $1 -L'
      am__untar='cpio -i -H $1 -d'
      ;;
    none)
      am__tar=false
      am__tar_=false
      am__untar=false
      ;;
    esac

    # If the value was cached, stop now.  We just wanted to have am__tar
    # and am__untar set.
    test -n "${am_cv_prog_tar_$1}" && break

    # tar/untar a dummy directory, and stop if the command works.
    rm -rf conftest.dir
    mkdir conftest.dir
    echo GrepMe > conftest.dir/file
    AM_RUN_LOG([tardir=conftest.dir && eval $am__tar_ >conftest.tar])
    rm -rf conftest.dir
    if test -s conftest.tar; then
      AM_RUN_LOG([$am__untar <conftest.tar])
      AM_RUN_LOG([cat conftest.dir/file])
      grep GrepMe conftest.dir/file >/dev/null 2>&1 && break
    fi
  done
  rm -rf conftest.dir

  AC_CACHE_VAL([am_cv_prog_tar_$1], [am_cv_prog_tar_$1=$_am_tool])
  AC_MSG_RESULT([$am_cv_prog_tar_$1])])

AC_SUBST([am__tar])
AC_SUBST([am__untar])
])
m4trace:m4/ax_code_coverage.m4:72: -1- AC_DEFUN([AX_CODE_COVERAGE], [
	dnl Check for --enable-code-coverage
	AC_REQUIRE([AC_PROG_SED])

	# allow to override gcov location
	AC_ARG_WITH([gcov],
	  [AS_HELP_STRING([--with-gcov[=GCOV]], [use given GCOV for coverage (GCOV=gcov).])],
	  [_AX_CODE_COVERAGE_GCOV_PROG_WITH=$with_gcov],
	  [_AX_CODE_COVERAGE_GCOV_PROG_WITH=gcov])

	AC_MSG_CHECKING([whether to build with code coverage support])
	AC_ARG_ENABLE([code-coverage],
	  AS_HELP_STRING([--enable-code-coverage],
	  [Whether to enable code coverage support]),,
	  enable_code_coverage=no)

	AM_CONDITIONAL([CODE_COVERAGE_ENABLED], [test x$enable_code_coverage = xyes])
	AC_SUBST([CODE_COVERAGE_ENABLED], [$enable_code_coverage])
	AC_MSG_RESULT($enable_code_coverage)

	AS_IF([ test "$enable_code_coverage" = "yes" ], [
		# check for gcov
		AC_CHECK_TOOL([GCOV],
		  [$_AX_CODE_COVERAGE_GCOV_PROG_WITH],
		  [:])
		AS_IF([test "X$GCOV" = "X:"],
		  [AC_MSG_ERROR([gcov is needed to do coverage])])
		AC_SUBST([GCOV])

		dnl Check if gcc is being used
		AS_IF([ test "$GCC" = "no" ], [
			AC_MSG_ERROR([not compiling with gcc, which is required for gcov code coverage])
		])

		# List of supported lcov versions.
		lcov_version_list="1.6 1.7 1.8 1.9 1.10 1.11"

		AC_CHECK_PROG([LCOV], [lcov], [lcov])
		AC_CHECK_PROG([GENHTML], [genhtml], [genhtml])

		AS_IF([ test "$LCOV" ], [
			AC_CACHE_CHECK([for lcov version], ax_cv_lcov_version, [
				ax_cv_lcov_version=invalid
				lcov_version=`$LCOV -v 2>/dev/null | $SED -e 's/^.* //'`
				for lcov_check_version in $lcov_version_list; do
					if test "$lcov_version" = "$lcov_check_version"; then
						ax_cv_lcov_version="$lcov_check_version (ok)"
					fi
				done
			])
		], [
			lcov_msg="To enable code coverage reporting you must have one of the following lcov versions installed: $lcov_version_list"
			AC_MSG_ERROR([$lcov_msg])
		])

		case $ax_cv_lcov_version in
			""|invalid[)]
				lcov_msg="You must have one of the following versions of lcov: $lcov_version_list (found: $lcov_version)."
				AC_MSG_ERROR([$lcov_msg])
				LCOV="exit 0;"
			;;
		esac

		AS_IF([ test -z "$GENHTML" ], [
			AC_MSG_ERROR([Could not find genhtml from the lcov package])
		])

		dnl Build the code coverage flags
		CODE_COVERAGE_CFLAGS="-O0 -g -fprofile-arcs -ftest-coverage"
		CODE_COVERAGE_LDFLAGS="-lgcov"

		AC_SUBST([CODE_COVERAGE_CFLAGS])
		AC_SUBST([CODE_COVERAGE_LDFLAGS])
	])

CODE_COVERAGE_RULES='
# Code coverage
#
# Optional:
#  - CODE_COVERAGE_DIRECTORY: Top-level directory for code coverage reporting.
#    (Default: $(top_builddir))
#  - CODE_COVERAGE_OUTPUT_FILE: Filename and path for the .info file generated
#    by lcov for code coverage. (Default:
#    $(PACKAGE_NAME)-$(PACKAGE_VERSION)-coverage.info)
#  - CODE_COVERAGE_OUTPUT_DIRECTORY: Directory for generated code coverage
#    reports to be created. (Default:
#    $(PACKAGE_NAME)-$(PACKAGE_VERSION)-coverage)
#  - CODE_COVERAGE_LCOV_OPTIONS_GCOVPATH: --gcov-tool pathtogcov
#  - CODE_COVERAGE_LCOV_OPTIONS_DEFAULT: Extra options to pass to the lcov instance.
#    (Default: $CODE_COVERAGE_LCOV_OPTIONS_GCOVPATH)
#  - CODE_COVERAGE_LCOV_OPTIONS: Extra options to pass to the lcov instance.
#    (Default: $CODE_COVERAGE_LCOV_OPTIONS_DEFAULT)
#  - CODE_COVERAGE_GENHTML_OPTIONS: Extra options to pass to the genhtml
#    instance. (Default: empty)
#  - CODE_COVERAGE_IGNORE_PATTERN: Extra glob pattern of files to ignore
#
# The generated report will be titled using the $(PACKAGE_NAME) and
# $(PACKAGE_VERSION). In order to add the current git hash to the title,
# use the git-version-gen script, available online.

# Optional variables
CODE_COVERAGE_DIRECTORY ?= $(top_builddir)
CODE_COVERAGE_OUTPUT_FILE ?= $(PACKAGE_NAME)-$(PACKAGE_VERSION)-coverage.info
CODE_COVERAGE_OUTPUT_DIRECTORY ?= $(PACKAGE_NAME)-$(PACKAGE_VERSION)-coverage
CODE_COVERAGE_LCOV_OPTIONS_GCOVPATH ?= --gcov-tool "$(GCOV)"
CODE_COVERAGE_LCOV_OPTIONS_DEFAULT ?= $(CODE_COVERAGE_LCOV_OPTIONS_GCOVPATH)
CODE_COVERAGE_LCOV_OPTIONS ?= $(CODE_COVERAGE_LCOV_OPTIONS_DEFAULT)
CODE_COVERAGE_GENHTML_OPTIONS ?=
CODE_COVERAGE_IGNORE_PATTERN ?=

code_coverage_quiet = $(code_coverage_quiet_$(V))
code_coverage_quiet_ = $(code_coverage_quiet_$(AM_DEFAULT_VERBOSITY))
code_coverage_quiet_0 = --quiet

# Use recursive makes in order to ignore errors during check
check-code-coverage:
ifeq ($(CODE_COVERAGE_ENABLED),yes)
	-$(MAKE) $(AM_MAKEFLAGS) -k check
	$(MAKE) $(AM_MAKEFLAGS) code-coverage-capture
else
	@echo "Need to reconfigure with --enable-code-coverage"
endif

# Capture code coverage data
code-coverage-capture: code-coverage-capture-hook
ifeq ($(CODE_COVERAGE_ENABLED),yes)
	$(LCOV) $(code_coverage_quiet) --directory $(CODE_COVERAGE_DIRECTORY) --capture --output-file "$(CODE_COVERAGE_OUTPUT_FILE).tmp" --test-name "$(PACKAGE_NAME)-$(PACKAGE_VERSION)" --no-checksum --compat-libtool $(CODE_COVERAGE_LCOV_OPTIONS)
	$(LCOV) $(code_coverage_quiet) --directory $(CODE_COVERAGE_DIRECTORY) --remove "$(CODE_COVERAGE_OUTPUT_FILE).tmp" "/tmp/*" $(CODE_COVERAGE_IGNORE_PATTERN) --output-file "$(CODE_COVERAGE_OUTPUT_FILE)"
	-@rm -f $(CODE_COVERAGE_OUTPUT_FILE).tmp
	LANG=C $(GENHTML) $(code_coverage_quiet) --prefix $(CODE_COVERAGE_DIRECTORY) --output-directory "$(CODE_COVERAGE_OUTPUT_DIRECTORY)" --title "$(PACKAGE_NAME)-$(PACKAGE_VERSION) Code Coverage" --legend --show-details "$(CODE_COVERAGE_OUTPUT_FILE)" $(CODE_COVERAGE_GENHTML_OPTIONS)
	@echo "file://$(abs_builddir)/$(CODE_COVERAGE_OUTPUT_DIRECTORY)/index.html"
else
	@echo "Need to reconfigure with --enable-code-coverage"
endif

# Hook rule executed before code-coverage-capture, overridable by the user
code-coverage-capture-hook:

ifeq ($(CODE_COVERAGE_ENABLED),yes)
clean: code-coverage-clean
code-coverage-clean:
	-$(LCOV) --directory $(top_builddir) -z
	-rm -rf $(CODE_COVERAGE_OUTPUT_FILE) $(CODE_COVERAGE_OUTPUT_FILE).tmp $(CODE_COVERAGE_OUTPUT_DIRECTORY)
	-find . -name "*.gcda" -o -name "*.gcov" -delete
endif

GITIGNOREFILES ?=
GITIGNOREFILES += $(CODE_COVERAGE_OUTPUT_FILE) $(CODE_COVERAGE_OUTPUT_DIRECTORY)

DISTCHECK_CONFIGURE_FLAGS ?=
DISTCHECK_CONFIGURE_FLAGS += --disable-code-coverage

.PHONY: check-code-coverage code-coverage-capture code-coverage-capture-hook code-coverage-clean
'

	AC_SUBST([CODE_COVERAGE_RULES])
	m4_ifdef([_AM_SUBST_NOTMAKE], [_AM_SUBST_NOTMAKE([CODE_COVERAGE_RULES])])
])
m4trace:m4/codeset.m4:10: -1- AC_DEFUN([AM_LANGINFO_CODESET], [
  AC_CACHE_CHECK([for nl_langinfo and CODESET], [am_cv_langinfo_codeset],
    [AC_LINK_IFELSE(
       [AC_LANG_PROGRAM(
          [[#include <langinfo.h>]],
          [[char* cs = nl_langinfo(CODESET); return !cs;]])],
       [am_cv_langinfo_codeset=yes],
       [am_cv_langinfo_codeset=no])
    ])
  if test $am_cv_langinfo_codeset = yes; then
    AC_DEFINE([HAVE_LANGINFO_CODESET], [1],
      [Define if you have <langinfo.h> and nl_langinfo(CODESET).])
  fi
])
m4trace:m4/extern-inline.m4:8: -1- AC_DEFUN([gl_EXTERN_INLINE], [
  AH_VERBATIM([extern_inline],
[/* Please see the Gnulib manual for how to use these macros.

   Suppress extern inline with HP-UX cc, as it appears to be broken; see
   <http://lists.gnu.org/archive/html/bug-texinfo/2013-02/msg00030.html>.

   Suppress extern inline with Sun C in standards-conformance mode, as it
   mishandles inline functions that call each other.  E.g., for 'inline void f
   (void) { } inline void g (void) { f (); }', c99 incorrectly complains
   'reference to static identifier "f" in extern inline function'.
   This bug was observed with Sun C 5.12 SunOS_i386 2011/11/16.

   Suppress extern inline (with or without __attribute__ ((__gnu_inline__)))
   on configurations that mistakenly use 'static inline' to implement
   functions or macros in standard C headers like <ctype.h>.  For example,
   if isdigit is mistakenly implemented via a static inline function,
   a program containing an extern inline function that calls isdigit
   may not work since the C standard prohibits extern inline functions
   from calling static functions.  This bug is known to occur on:

     OS X 10.8 and earlier; see:
     http://lists.gnu.org/archive/html/bug-gnulib/2012-12/msg00023.html

     DragonFly; see
     http://muscles.dragonflybsd.org/bulk/bleeding-edge-potential/latest-per-pkg/ah-tty-0.3.12.log

     FreeBSD; see:
     http://lists.gnu.org/archive/html/bug-gnulib/2014-07/msg00104.html

   OS X 10.9 has a macro __header_inline indicating the bug is fixed for C and
   for clang but remains for g++; see <http://trac.macports.org/ticket/41033>.
   Assume DragonFly and FreeBSD will be similar.  */
#if (((defined __APPLE__ && defined __MACH__) \
      || defined __DragonFly__ || defined __FreeBSD__) \
     && (defined __header_inline \
         ? (defined __cplusplus && defined __GNUC_STDC_INLINE__ \
            && ! defined __clang__) \
         : ((! defined _DONT_USE_CTYPE_INLINE_ \
             && (defined __GNUC__ || defined __cplusplus)) \
            || (defined _FORTIFY_SOURCE && 0 < _FORTIFY_SOURCE \
                && defined __GNUC__ && ! defined __cplusplus))))
# define _GL_EXTERN_INLINE_STDHEADER_BUG
#endif
#if ((__GNUC__ \
      ? defined __GNUC_STDC_INLINE__ && __GNUC_STDC_INLINE__ \
      : (199901L <= __STDC_VERSION__ \
         && !defined __HP_cc \
         && !defined __PGI \
         && !(defined __SUNPRO_C && __STDC__))) \
     && !defined _GL_EXTERN_INLINE_STDHEADER_BUG)
# define _GL_INLINE inline
# define _GL_EXTERN_INLINE extern inline
# define _GL_EXTERN_INLINE_IN_USE
#elif (2 < __GNUC__ + (7 <= __GNUC_MINOR__) && !defined __STRICT_ANSI__ \
       && !defined _GL_EXTERN_INLINE_STDHEADER_BUG)
# if defined __GNUC_GNU_INLINE__ && __GNUC_GNU_INLINE__
   /* __gnu_inline__ suppresses a GCC 4.2 diagnostic.  */
#  define _GL_INLINE extern inline __attribute__ ((__gnu_inline__))
# else
#  define _GL_INLINE extern inline
# endif
# define _GL_EXTERN_INLINE extern
# define _GL_EXTERN_INLINE_IN_USE
#else
# define _GL_INLINE static _GL_UNUSED
# define _GL_EXTERN_INLINE static _GL_UNUSED
#endif

/* In GCC 4.6 (inclusive) to 5.1 (exclusive),
   suppress bogus "no previous prototype for 'FOO'"
   and "no previous declaration for 'FOO'" diagnostics,
   when FOO is an inline function in the header; see
   <https://gcc.gnu.org/bugzilla/show_bug.cgi?id=54113> and
   <https://gcc.gnu.org/bugzilla/show_bug.cgi?id=63877>.  */
#if __GNUC__ == 4 && 6 <= __GNUC_MINOR__
# if defined __GNUC_STDC_INLINE__ && __GNUC_STDC_INLINE__
#  define _GL_INLINE_HEADER_CONST_PRAGMA
# else
#  define _GL_INLINE_HEADER_CONST_PRAGMA \
     _Pragma ("GCC diagnostic ignored \"-Wsuggest-attribute=const\"")
# endif
# define _GL_INLINE_HEADER_BEGIN \
    _Pragma ("GCC diagnostic push") \
    _Pragma ("GCC diagnostic ignored \"-Wmissing-prototypes\"") \
    _Pragma ("GCC diagnostic ignored \"-Wmissing-declarations\"") \
    _GL_INLINE_HEADER_CONST_PRAGMA
# define _GL_INLINE_HEADER_END \
    _Pragma ("GCC diagnostic pop")
#else
# define _GL_INLINE_HEADER_BEGIN
# define _GL_INLINE_HEADER_END
#endif])
])
m4trace:m4/fcntl-o.m4:12: -1- AC_DEFUN([gl_FCNTL_O_FLAGS], [
  dnl Persuade glibc <fcntl.h> to define O_NOATIME and O_NOFOLLOW.
  dnl AC_USE_SYSTEM_EXTENSIONS was introduced in autoconf 2.60 and obsoletes
  dnl AC_GNU_SOURCE.
  m4_ifdef([AC_USE_SYSTEM_EXTENSIONS],
    [AC_REQUIRE([AC_USE_SYSTEM_EXTENSIONS])],
    [AC_REQUIRE([AC_GNU_SOURCE])])

  AC_CHECK_HEADERS_ONCE([unistd.h])
  AC_CHECK_FUNCS_ONCE([symlink])
  AC_CACHE_CHECK([for working fcntl.h], [gl_cv_header_working_fcntl_h],
    [AC_RUN_IFELSE(
       [AC_LANG_PROGRAM(
          [[#include <sys/types.h>
           #include <sys/stat.h>
           #if HAVE_UNISTD_H
           # include <unistd.h>
           #else /* on Windows with MSVC */
           # include <io.h>
           # include <stdlib.h>
           # defined sleep(n) _sleep ((n) * 1000)
           #endif
           #include <fcntl.h>
           #ifndef O_NOATIME
            #define O_NOATIME 0
           #endif
           #ifndef O_NOFOLLOW
            #define O_NOFOLLOW 0
           #endif
           static int const constants[] =
            {
              O_CREAT, O_EXCL, O_NOCTTY, O_TRUNC, O_APPEND,
              O_NONBLOCK, O_SYNC, O_ACCMODE, O_RDONLY, O_RDWR, O_WRONLY
            };
          ]],
          [[
            int result = !constants;
            #if HAVE_SYMLINK
            {
              static char const sym[] = "conftest.sym";
              if (symlink ("/dev/null", sym) != 0)
                result |= 2;
              else
                {
                  int fd = open (sym, O_WRONLY | O_NOFOLLOW | O_CREAT, 0);
                  if (fd >= 0)
                    {
                      close (fd);
                      result |= 4;
                    }
                }
              if (unlink (sym) != 0 || symlink (".", sym) != 0)
                result |= 2;
              else
                {
                  int fd = open (sym, O_RDONLY | O_NOFOLLOW);
                  if (fd >= 0)
                    {
                      close (fd);
                      result |= 4;
                    }
                }
              unlink (sym);
            }
            #endif
            {
              static char const file[] = "confdefs.h";
              int fd = open (file, O_RDONLY | O_NOATIME);
              if (fd < 0)
                result |= 8;
              else
                {
                  struct stat st0;
                  if (fstat (fd, &st0) != 0)
                    result |= 16;
                  else
                    {
                      char c;
                      sleep (1);
                      if (read (fd, &c, 1) != 1)
                        result |= 24;
                      else
                        {
                          if (close (fd) != 0)
                            result |= 32;
                          else
                            {
                              struct stat st1;
                              if (stat (file, &st1) != 0)
                                result |= 40;
                              else
                                if (st0.st_atime != st1.st_atime)
                                  result |= 64;
                            }
                        }
                    }
                }
            }
            return result;]])],
       [gl_cv_header_working_fcntl_h=yes],
       [case $? in #(
        4) gl_cv_header_working_fcntl_h='no (bad O_NOFOLLOW)';; #(
        64) gl_cv_header_working_fcntl_h='no (bad O_NOATIME)';; #(
        68) gl_cv_header_working_fcntl_h='no (bad O_NOATIME, O_NOFOLLOW)';; #(
         *) gl_cv_header_working_fcntl_h='no';;
        esac],
       [gl_cv_header_working_fcntl_h=cross-compiling])])

  case $gl_cv_header_working_fcntl_h in #(
  *O_NOATIME* | no | cross-compiling) ac_val=0;; #(
  *) ac_val=1;;
  esac
  AC_DEFINE_UNQUOTED([HAVE_WORKING_O_NOATIME], [$ac_val],
    [Define to 1 if O_NOATIME works.])

  case $gl_cv_header_working_fcntl_h in #(
  *O_NOFOLLOW* | no | cross-compiling) ac_val=0;; #(
  *) ac_val=1;;
  esac
  AC_DEFINE_UNQUOTED([HAVE_WORKING_O_NOFOLLOW], [$ac_val],
    [Define to 1 if O_NOFOLLOW works.])
])
m4trace:m4/gettext.m4:57: -1- AC_DEFUN([AM_GNU_GETTEXT], [
  dnl Argument checking.
  ifelse([$1], [], , [ifelse([$1], [external], , [ifelse([$1], [no-libtool], , [ifelse([$1], [use-libtool], ,
    [errprint([ERROR: invalid first argument to AM_GNU_GETTEXT
])])])])])
  ifelse(ifelse([$1], [], [old])[]ifelse([$1], [no-libtool], [old]), [old],
    [AC_DIAGNOSE([obsolete], [Use of AM_GNU_GETTEXT without [external] argument is deprecated.])])
  ifelse([$2], [], , [ifelse([$2], [need-ngettext], , [ifelse([$2], [need-formatstring-macros], ,
    [errprint([ERROR: invalid second argument to AM_GNU_GETTEXT
])])])])
  define([gt_included_intl],
    ifelse([$1], [external],
      ifdef([AM_GNU_GETTEXT_][INTL_SUBDIR], [yes], [no]),
      [yes]))
  define([gt_libtool_suffix_prefix], ifelse([$1], [use-libtool], [l], []))
  gt_NEEDS_INIT
  AM_GNU_GETTEXT_NEED([$2])

  AC_REQUIRE([AM_PO_SUBDIRS])dnl
  ifelse(gt_included_intl, yes, [
    AC_REQUIRE([AM_INTL_SUBDIR])dnl
  ])

  dnl Prerequisites of AC_LIB_LINKFLAGS_BODY.
  AC_REQUIRE([AC_LIB_PREPARE_PREFIX])
  AC_REQUIRE([AC_LIB_RPATH])

  dnl Sometimes libintl requires libiconv, so first search for libiconv.
  dnl Ideally we would do this search only after the
  dnl      if test "$USE_NLS" = "yes"; then
  dnl        if { eval "gt_val=\$$gt_func_gnugettext_libc"; test "$gt_val" != "yes"; }; then
  dnl tests. But if configure.in invokes AM_ICONV after AM_GNU_GETTEXT
  dnl the configure script would need to contain the same shell code
  dnl again, outside any 'if'. There are two solutions:
  dnl - Invoke AM_ICONV_LINKFLAGS_BODY here, outside any 'if'.
  dnl - Control the expansions in more detail using AC_PROVIDE_IFELSE.
  dnl Since AC_PROVIDE_IFELSE is only in autoconf >= 2.52 and not
  dnl documented, we avoid it.
  ifelse(gt_included_intl, yes, , [
    AC_REQUIRE([AM_ICONV_LINKFLAGS_BODY])
  ])

  dnl Sometimes, on Mac OS X, libintl requires linking with CoreFoundation.
  gt_INTL_MACOSX

  dnl Set USE_NLS.
  AC_REQUIRE([AM_NLS])

  ifelse(gt_included_intl, yes, [
    BUILD_INCLUDED_LIBINTL=no
    USE_INCLUDED_LIBINTL=no
  ])
  LIBINTL=
  LTLIBINTL=
  POSUB=

  dnl Add a version number to the cache macros.
  case " $gt_needs " in
    *" need-formatstring-macros "*) gt_api_version=3 ;;
    *" need-ngettext "*) gt_api_version=2 ;;
    *) gt_api_version=1 ;;
  esac
  gt_func_gnugettext_libc="gt_cv_func_gnugettext${gt_api_version}_libc"
  gt_func_gnugettext_libintl="gt_cv_func_gnugettext${gt_api_version}_libintl"

  dnl If we use NLS figure out what method
  if test "$USE_NLS" = "yes"; then
    gt_use_preinstalled_gnugettext=no
    ifelse(gt_included_intl, yes, [
      AC_MSG_CHECKING([whether included gettext is requested])
      AC_ARG_WITH([included-gettext],
        [  --with-included-gettext use the GNU gettext library included here],
        nls_cv_force_use_gnu_gettext=$withval,
        nls_cv_force_use_gnu_gettext=no)
      AC_MSG_RESULT([$nls_cv_force_use_gnu_gettext])

      nls_cv_use_gnu_gettext="$nls_cv_force_use_gnu_gettext"
      if test "$nls_cv_force_use_gnu_gettext" != "yes"; then
    ])
        dnl User does not insist on using GNU NLS library.  Figure out what
        dnl to use.  If GNU gettext is available we use this.  Else we have
        dnl to fall back to GNU NLS library.

        if test $gt_api_version -ge 3; then
          gt_revision_test_code='
#ifndef __GNU_GETTEXT_SUPPORTED_REVISION
#define __GNU_GETTEXT_SUPPORTED_REVISION(major) ((major) == 0 ? 0 : -1)
#endif
changequote(,)dnl
typedef int array [2 * (__GNU_GETTEXT_SUPPORTED_REVISION(0) >= 1) - 1];
changequote([,])dnl
'
        else
          gt_revision_test_code=
        fi
        if test $gt_api_version -ge 2; then
          gt_expression_test_code=' + * ngettext ("", "", 0)'
        else
          gt_expression_test_code=
        fi

        AC_CACHE_CHECK([for GNU gettext in libc], [$gt_func_gnugettext_libc],
         [AC_LINK_IFELSE(
            [AC_LANG_PROGRAM(
               [[
#include <libintl.h>
#ifndef __GNU_GETTEXT_SUPPORTED_REVISION
extern int _nl_msg_cat_cntr;
extern int *_nl_domain_bindings;
#define __GNU_GETTEXT_SYMBOL_EXPRESSION (_nl_msg_cat_cntr + *_nl_domain_bindings)
#else
#define __GNU_GETTEXT_SYMBOL_EXPRESSION 0
#endif
$gt_revision_test_code
               ]],
               [[
bindtextdomain ("", "");
return * gettext ("")$gt_expression_test_code + __GNU_GETTEXT_SYMBOL_EXPRESSION
               ]])],
            [eval "$gt_func_gnugettext_libc=yes"],
            [eval "$gt_func_gnugettext_libc=no"])])

        if { eval "gt_val=\$$gt_func_gnugettext_libc"; test "$gt_val" != "yes"; }; then
          dnl Sometimes libintl requires libiconv, so first search for libiconv.
          ifelse(gt_included_intl, yes, , [
            AM_ICONV_LINK
          ])
          dnl Search for libintl and define LIBINTL, LTLIBINTL and INCINTL
          dnl accordingly. Don't use AC_LIB_LINKFLAGS_BODY([intl],[iconv])
          dnl because that would add "-liconv" to LIBINTL and LTLIBINTL
          dnl even if libiconv doesn't exist.
          AC_LIB_LINKFLAGS_BODY([intl])
          AC_CACHE_CHECK([for GNU gettext in libintl],
            [$gt_func_gnugettext_libintl],
           [gt_save_CPPFLAGS="$CPPFLAGS"
            CPPFLAGS="$CPPFLAGS $INCINTL"
            gt_save_LIBS="$LIBS"
            LIBS="$LIBS $LIBINTL"
            dnl Now see whether libintl exists and does not depend on libiconv.
            AC_LINK_IFELSE(
              [AC_LANG_PROGRAM(
                 [[
#include <libintl.h>
#ifndef __GNU_GETTEXT_SUPPORTED_REVISION
extern int _nl_msg_cat_cntr;
extern
#ifdef __cplusplus
"C"
#endif
const char *_nl_expand_alias (const char *);
#define __GNU_GETTEXT_SYMBOL_EXPRESSION (_nl_msg_cat_cntr + *_nl_expand_alias (""))
#else
#define __GNU_GETTEXT_SYMBOL_EXPRESSION 0
#endif
$gt_revision_test_code
                 ]],
                 [[
bindtextdomain ("", "");
return * gettext ("")$gt_expression_test_code + __GNU_GETTEXT_SYMBOL_EXPRESSION
                 ]])],
              [eval "$gt_func_gnugettext_libintl=yes"],
              [eval "$gt_func_gnugettext_libintl=no"])
            dnl Now see whether libintl exists and depends on libiconv.
            if { eval "gt_val=\$$gt_func_gnugettext_libintl"; test "$gt_val" != yes; } && test -n "$LIBICONV"; then
              LIBS="$LIBS $LIBICONV"
              AC_LINK_IFELSE(
                [AC_LANG_PROGRAM(
                   [[
#include <libintl.h>
#ifndef __GNU_GETTEXT_SUPPORTED_REVISION
extern int _nl_msg_cat_cntr;
extern
#ifdef __cplusplus
"C"
#endif
const char *_nl_expand_alias (const char *);
#define __GNU_GETTEXT_SYMBOL_EXPRESSION (_nl_msg_cat_cntr + *_nl_expand_alias (""))
#else
#define __GNU_GETTEXT_SYMBOL_EXPRESSION 0
#endif
$gt_revision_test_code
                   ]],
                   [[
bindtextdomain ("", "");
return * gettext ("")$gt_expression_test_code + __GNU_GETTEXT_SYMBOL_EXPRESSION
                   ]])],
                [LIBINTL="$LIBINTL $LIBICONV"
                 LTLIBINTL="$LTLIBINTL $LTLIBICONV"
                 eval "$gt_func_gnugettext_libintl=yes"
                ])
            fi
            CPPFLAGS="$gt_save_CPPFLAGS"
            LIBS="$gt_save_LIBS"])
        fi

        dnl If an already present or preinstalled GNU gettext() is found,
        dnl use it.  But if this macro is used in GNU gettext, and GNU
        dnl gettext is already preinstalled in libintl, we update this
        dnl libintl.  (Cf. the install rule in intl/Makefile.in.)
        if { eval "gt_val=\$$gt_func_gnugettext_libc"; test "$gt_val" = "yes"; } \
           || { { eval "gt_val=\$$gt_func_gnugettext_libintl"; test "$gt_val" = "yes"; } \
                && test "$PACKAGE" != gettext-runtime \
                && test "$PACKAGE" != gettext-tools; }; then
          gt_use_preinstalled_gnugettext=yes
        else
          dnl Reset the values set by searching for libintl.
          LIBINTL=
          LTLIBINTL=
          INCINTL=
        fi

    ifelse(gt_included_intl, yes, [
        if test "$gt_use_preinstalled_gnugettext" != "yes"; then
          dnl GNU gettext is not found in the C library.
          dnl Fall back on included GNU gettext library.
          nls_cv_use_gnu_gettext=yes
        fi
      fi

      if test "$nls_cv_use_gnu_gettext" = "yes"; then
        dnl Mark actions used to generate GNU NLS library.
        BUILD_INCLUDED_LIBINTL=yes
        USE_INCLUDED_LIBINTL=yes
        LIBINTL="ifelse([$3],[],\${top_builddir}/intl,[$3])/libintl.[]gt_libtool_suffix_prefix[]a $LIBICONV $LIBTHREAD"
        LTLIBINTL="ifelse([$3],[],\${top_builddir}/intl,[$3])/libintl.[]gt_libtool_suffix_prefix[]a $LTLIBICONV $LTLIBTHREAD"
        LIBS=`echo " $LIBS " | sed -e 's/ -lintl / /' -e 's/^ //' -e 's/ $//'`
      fi

      CATOBJEXT=
      if test "$gt_use_preinstalled_gnugettext" = "yes" \
         || test "$nls_cv_use_gnu_gettext" = "yes"; then
        dnl Mark actions to use GNU gettext tools.
        CATOBJEXT=.gmo
      fi
    ])

    if test -n "$INTL_MACOSX_LIBS"; then
      if test "$gt_use_preinstalled_gnugettext" = "yes" \
         || test "$nls_cv_use_gnu_gettext" = "yes"; then
        dnl Some extra flags are needed during linking.
        LIBINTL="$LIBINTL $INTL_MACOSX_LIBS"
        LTLIBINTL="$LTLIBINTL $INTL_MACOSX_LIBS"
      fi
    fi

    if test "$gt_use_preinstalled_gnugettext" = "yes" \
       || test "$nls_cv_use_gnu_gettext" = "yes"; then
      AC_DEFINE([ENABLE_NLS], [1],
        [Define to 1 if translation of program messages to the user's native language
   is requested.])
    else
      USE_NLS=no
    fi
  fi

  AC_MSG_CHECKING([whether to use NLS])
  AC_MSG_RESULT([$USE_NLS])
  if test "$USE_NLS" = "yes"; then
    AC_MSG_CHECKING([where the gettext function comes from])
    if test "$gt_use_preinstalled_gnugettext" = "yes"; then
      if { eval "gt_val=\$$gt_func_gnugettext_libintl"; test "$gt_val" = "yes"; }; then
        gt_source="external libintl"
      else
        gt_source="libc"
      fi
    else
      gt_source="included intl directory"
    fi
    AC_MSG_RESULT([$gt_source])
  fi

  if test "$USE_NLS" = "yes"; then

    if test "$gt_use_preinstalled_gnugettext" = "yes"; then
      if { eval "gt_val=\$$gt_func_gnugettext_libintl"; test "$gt_val" = "yes"; }; then
        AC_MSG_CHECKING([how to link with libintl])
        AC_MSG_RESULT([$LIBINTL])
        AC_LIB_APPENDTOVAR([CPPFLAGS], [$INCINTL])
      fi

      dnl For backward compatibility. Some packages may be using this.
      AC_DEFINE([HAVE_GETTEXT], [1],
       [Define if the GNU gettext() function is already present or preinstalled.])
      AC_DEFINE([HAVE_DCGETTEXT], [1],
       [Define if the GNU dcgettext() function is already present or preinstalled.])
    fi

    dnl We need to process the po/ directory.
    POSUB=po
  fi

  ifelse(gt_included_intl, yes, [
    dnl If this is used in GNU gettext we have to set BUILD_INCLUDED_LIBINTL
    dnl to 'yes' because some of the testsuite requires it.
    if test "$PACKAGE" = gettext-runtime || test "$PACKAGE" = gettext-tools; then
      BUILD_INCLUDED_LIBINTL=yes
    fi

    dnl Make all variables we use known to autoconf.
    AC_SUBST([BUILD_INCLUDED_LIBINTL])
    AC_SUBST([USE_INCLUDED_LIBINTL])
    AC_SUBST([CATOBJEXT])

    dnl For backward compatibility. Some configure.ins may be using this.
    nls_cv_header_intl=
    nls_cv_header_libgt=

    dnl For backward compatibility. Some Makefiles may be using this.
    DATADIRNAME=share
    AC_SUBST([DATADIRNAME])

    dnl For backward compatibility. Some Makefiles may be using this.
    INSTOBJEXT=.mo
    AC_SUBST([INSTOBJEXT])

    dnl For backward compatibility. Some Makefiles may be using this.
    GENCAT=gencat
    AC_SUBST([GENCAT])

    dnl For backward compatibility. Some Makefiles may be using this.
    INTLOBJS=
    if test "$USE_INCLUDED_LIBINTL" = yes; then
      INTLOBJS="\$(GETTOBJS)"
    fi
    AC_SUBST([INTLOBJS])

    dnl Enable libtool support if the surrounding package wishes it.
    INTL_LIBTOOL_SUFFIX_PREFIX=gt_libtool_suffix_prefix
    AC_SUBST([INTL_LIBTOOL_SUFFIX_PREFIX])
  ])

  dnl For backward compatibility. Some Makefiles may be using this.
  INTLLIBS="$LIBINTL"
  AC_SUBST([INTLLIBS])

  dnl Make all documented variables known to autoconf.
  AC_SUBST([LIBINTL])
  AC_SUBST([LTLIBINTL])
  AC_SUBST([POSUB])
])
m4trace:m4/gettext.m4:409: -1- AC_DEFUN([AM_GNU_GETTEXT_NEED], [
  m4_divert_text([INIT_PREPARE], [gt_needs="$gt_needs $1"])
])
m4trace:m4/gettext.m4:416: -1- AC_DEFUN([AM_GNU_GETTEXT_VERSION], [])
m4trace:m4/gettext.m4:420: -1- AC_DEFUN([AM_GNU_GETTEXT_REQUIRE_VERSION], [])
m4trace:m4/glibc2.m4:11: -1- AC_DEFUN([gt_GLIBC2], [
    AC_CACHE_CHECK([whether we are using the GNU C Library 2 or newer],
      [ac_cv_gnu_library_2],
      [AC_EGREP_CPP([Lucky GNU user],
        [
#include <features.h>
#ifdef __GNU_LIBRARY__
 #if (__GLIBC__ >= 2) && !defined __UCLIBC__
  Lucky GNU user
 #endif
#endif
        ],
        [ac_cv_gnu_library_2=yes],
        [ac_cv_gnu_library_2=no])
      ]
    )
    AC_SUBST([GLIBC2])
    GLIBC2="$ac_cv_gnu_library_2"
  
])
m4trace:m4/glibc21.m4:11: -1- AC_DEFUN([gl_GLIBC21], [
    AC_CACHE_CHECK([whether we are using the GNU C Library >= 2.1 or uClibc],
      [ac_cv_gnu_library_2_1],
      [AC_EGREP_CPP([Lucky],
        [
#include <features.h>
#ifdef __GNU_LIBRARY__
 #if (__GLIBC__ == 2 && __GLIBC_MINOR__ >= 1) || (__GLIBC__ > 2)
  Lucky GNU user
 #endif
#endif
#ifdef __UCLIBC__
 Lucky user
#endif
        ],
        [ac_cv_gnu_library_2_1=yes],
        [ac_cv_gnu_library_2_1=no])
      ]
    )
    AC_SUBST([GLIBC21])
    GLIBC21="$ac_cv_gnu_library_2_1"
  
])
m4trace:m4/gtk-doc.m4:7: -1- AC_DEFUN([GTK_DOC_CHECK], [
  AC_REQUIRE([PKG_PROG_PKG_CONFIG])
  AC_BEFORE([AC_PROG_LIBTOOL],[$0])dnl setup libtool first
  AC_BEFORE([AM_PROG_LIBTOOL],[$0])dnl setup libtool first

  ifelse([$1],[],[gtk_doc_requires="gtk-doc"],[gtk_doc_requires="gtk-doc >= $1"])
  AC_MSG_CHECKING([for gtk-doc])
  PKG_CHECK_EXISTS([$gtk_doc_requires],[have_gtk_doc=yes],[have_gtk_doc=no])
  AC_MSG_RESULT($have_gtk_doc)

  if test "$have_gtk_doc" = "no"; then
      AC_MSG_WARN([
  You will not be able to create source packages with 'make dist'
  because $gtk_doc_requires is not found.])
  fi

  dnl check for tools we added during development
  dnl Use AC_CHECK_PROG to avoid the check target using an absolute path that
  dnl may not be writable by the user. Currently, automake requires that the
  dnl test name must end in '.test'.
  dnl https://bugzilla.gnome.org/show_bug.cgi?id=701638
  AC_CHECK_PROG([GTKDOC_CHECK],[gtkdoc-check],[gtkdoc-check.test])
  AC_PATH_PROG([GTKDOC_CHECK_PATH],[gtkdoc-check])
  AC_PATH_PROGS([GTKDOC_REBASE],[gtkdoc-rebase],[true])
  AC_PATH_PROG([GTKDOC_MKPDF],[gtkdoc-mkpdf])

  dnl for overriding the documentation installation directory
  AC_ARG_WITH([html-dir],
    AS_HELP_STRING([--with-html-dir=PATH], [path to installed docs]),,
    [with_html_dir='${datadir}/gtk-doc/html'])
  HTML_DIR="$with_html_dir"
  AC_SUBST([HTML_DIR])

  dnl enable/disable documentation building
  AC_ARG_ENABLE([gtk-doc],
    AS_HELP_STRING([--enable-gtk-doc],
                   [use gtk-doc to build documentation [[default=no]]]),,
    [enable_gtk_doc=no])

  AC_MSG_CHECKING([whether to build gtk-doc documentation])
  AC_MSG_RESULT($enable_gtk_doc)

  if test "x$enable_gtk_doc" = "xyes" && test "$have_gtk_doc" = "no"; then
    AC_MSG_ERROR([
  You must have $gtk_doc_requires installed to build documentation for
  $PACKAGE_NAME. Please install gtk-doc or disable building the
  documentation by adding '--disable-gtk-doc' to '[$]0'.])
  fi

  dnl don't check for glib if we build glib
  if test "x$PACKAGE_NAME" != "xglib"; then
    dnl don't fail if someone does not have glib
    PKG_CHECK_MODULES(GTKDOC_DEPS, glib-2.0 >= 2.10.0 gobject-2.0  >= 2.10.0,,[:])
  fi

  dnl enable/disable output formats
  AC_ARG_ENABLE([gtk-doc-html],
    AS_HELP_STRING([--enable-gtk-doc-html],
                   [build documentation in html format [[default=yes]]]),,
    [enable_gtk_doc_html=yes])
    AC_ARG_ENABLE([gtk-doc-pdf],
      AS_HELP_STRING([--enable-gtk-doc-pdf],
                     [build documentation in pdf format [[default=no]]]),,
      [enable_gtk_doc_pdf=no])

  if test -z "$GTKDOC_MKPDF"; then
    enable_gtk_doc_pdf=no
  fi

  if test -z "$AM_DEFAULT_VERBOSITY"; then
    AM_DEFAULT_VERBOSITY=1
  fi
  AC_SUBST([AM_DEFAULT_VERBOSITY])

  AM_CONDITIONAL([HAVE_GTK_DOC], [test x$have_gtk_doc = xyes])
  AM_CONDITIONAL([ENABLE_GTK_DOC], [test x$enable_gtk_doc = xyes])
  AM_CONDITIONAL([GTK_DOC_BUILD_HTML], [test x$enable_gtk_doc_html = xyes])
  AM_CONDITIONAL([GTK_DOC_BUILD_PDF], [test x$enable_gtk_doc_pdf = xyes])
  AM_CONDITIONAL([GTK_DOC_USE_LIBTOOL], [test -n "$LIBTOOL"])
  AM_CONDITIONAL([GTK_DOC_USE_REBASE], [test -n "$GTKDOC_REBASE"])
])
m4trace:m4/iconv.m4:9: -1- AC_DEFUN([AM_ICONV_LINKFLAGS_BODY], [
  dnl Prerequisites of AC_LIB_LINKFLAGS_BODY.
  AC_REQUIRE([AC_LIB_PREPARE_PREFIX])
  AC_REQUIRE([AC_LIB_RPATH])

  dnl Search for libiconv and define LIBICONV, LTLIBICONV and INCICONV
  dnl accordingly.
  AC_LIB_LINKFLAGS_BODY([iconv])
])
m4trace:m4/iconv.m4:20: -1- AC_DEFUN([AM_ICONV_LINK], [
  dnl Some systems have iconv in libc, some have it in libiconv (OSF/1 and
  dnl those with the standalone portable GNU libiconv installed).
  AC_REQUIRE([AC_CANONICAL_HOST]) dnl for cross-compiles

  dnl Search for libiconv and define LIBICONV, LTLIBICONV and INCICONV
  dnl accordingly.
  AC_REQUIRE([AM_ICONV_LINKFLAGS_BODY])

  dnl Add $INCICONV to CPPFLAGS before performing the following checks,
  dnl because if the user has installed libiconv and not disabled its use
  dnl via --without-libiconv-prefix, he wants to use it. The first
  dnl AC_LINK_IFELSE will then fail, the second AC_LINK_IFELSE will succeed.
  am_save_CPPFLAGS="$CPPFLAGS"
  AC_LIB_APPENDTOVAR([CPPFLAGS], [$INCICONV])

  AC_CACHE_CHECK([for iconv], [am_cv_func_iconv], [
    am_cv_func_iconv="no, consider installing GNU libiconv"
    am_cv_lib_iconv=no
    AC_LINK_IFELSE(
      [AC_LANG_PROGRAM(
         [[
#include <stdlib.h>
#include <iconv.h>
         ]],
         [[iconv_t cd = iconv_open("","");
           iconv(cd,NULL,NULL,NULL,NULL);
           iconv_close(cd);]])],
      [am_cv_func_iconv=yes])
    if test "$am_cv_func_iconv" != yes; then
      am_save_LIBS="$LIBS"
      LIBS="$LIBS $LIBICONV"
      AC_LINK_IFELSE(
        [AC_LANG_PROGRAM(
           [[
#include <stdlib.h>
#include <iconv.h>
           ]],
           [[iconv_t cd = iconv_open("","");
             iconv(cd,NULL,NULL,NULL,NULL);
             iconv_close(cd);]])],
        [am_cv_lib_iconv=yes]
        [am_cv_func_iconv=yes])
      LIBS="$am_save_LIBS"
    fi
  ])
  if test "$am_cv_func_iconv" = yes; then
    AC_CACHE_CHECK([for working iconv], [am_cv_func_iconv_works], [
      dnl This tests against bugs in AIX 5.1, AIX 6.1..7.1, HP-UX 11.11,
      dnl Solaris 10.
      am_save_LIBS="$LIBS"
      if test $am_cv_lib_iconv = yes; then
        LIBS="$LIBS $LIBICONV"
      fi
      am_cv_func_iconv_works=no
      for ac_iconv_const in '' 'const'; do
        AC_RUN_IFELSE(
          [AC_LANG_PROGRAM(
             [[
#include <iconv.h>
#include <string.h>

#ifndef ICONV_CONST
# define ICONV_CONST $ac_iconv_const
#endif
             ]],
             [[int result = 0;
  /* Test against AIX 5.1 bug: Failures are not distinguishable from successful
     returns.  */
  {
    iconv_t cd_utf8_to_88591 = iconv_open ("ISO8859-1", "UTF-8");
    if (cd_utf8_to_88591 != (iconv_t)(-1))
      {
        static ICONV_CONST char input[] = "\342\202\254"; /* EURO SIGN */
        char buf[10];
        ICONV_CONST char *inptr = input;
        size_t inbytesleft = strlen (input);
        char *outptr = buf;
        size_t outbytesleft = sizeof (buf);
        size_t res = iconv (cd_utf8_to_88591,
                            &inptr, &inbytesleft,
                            &outptr, &outbytesleft);
        if (res == 0)
          result |= 1;
        iconv_close (cd_utf8_to_88591);
      }
  }
  /* Test against Solaris 10 bug: Failures are not distinguishable from
     successful returns.  */
  {
    iconv_t cd_ascii_to_88591 = iconv_open ("ISO8859-1", "646");
    if (cd_ascii_to_88591 != (iconv_t)(-1))
      {
        static ICONV_CONST char input[] = "\263";
        char buf[10];
        ICONV_CONST char *inptr = input;
        size_t inbytesleft = strlen (input);
        char *outptr = buf;
        size_t outbytesleft = sizeof (buf);
        size_t res = iconv (cd_ascii_to_88591,
                            &inptr, &inbytesleft,
                            &outptr, &outbytesleft);
        if (res == 0)
          result |= 2;
        iconv_close (cd_ascii_to_88591);
      }
  }
  /* Test against AIX 6.1..7.1 bug: Buffer overrun.  */
  {
    iconv_t cd_88591_to_utf8 = iconv_open ("UTF-8", "ISO-8859-1");
    if (cd_88591_to_utf8 != (iconv_t)(-1))
      {
        static ICONV_CONST char input[] = "\304";
        static char buf[2] = { (char)0xDE, (char)0xAD };
        ICONV_CONST char *inptr = input;
        size_t inbytesleft = 1;
        char *outptr = buf;
        size_t outbytesleft = 1;
        size_t res = iconv (cd_88591_to_utf8,
                            &inptr, &inbytesleft,
                            &outptr, &outbytesleft);
        if (res != (size_t)(-1) || outptr - buf > 1 || buf[1] != (char)0xAD)
          result |= 4;
        iconv_close (cd_88591_to_utf8);
      }
  }
#if 0 /* This bug could be worked around by the caller.  */
  /* Test against HP-UX 11.11 bug: Positive return value instead of 0.  */
  {
    iconv_t cd_88591_to_utf8 = iconv_open ("utf8", "iso88591");
    if (cd_88591_to_utf8 != (iconv_t)(-1))
      {
        static ICONV_CONST char input[] = "\304rger mit b\366sen B\374bchen ohne Augenma\337";
        char buf[50];
        ICONV_CONST char *inptr = input;
        size_t inbytesleft = strlen (input);
        char *outptr = buf;
        size_t outbytesleft = sizeof (buf);
        size_t res = iconv (cd_88591_to_utf8,
                            &inptr, &inbytesleft,
                            &outptr, &outbytesleft);
        if ((int)res > 0)
          result |= 8;
        iconv_close (cd_88591_to_utf8);
      }
  }
#endif
  /* Test against HP-UX 11.11 bug: No converter from EUC-JP to UTF-8 is
     provided.  */
  if (/* Try standardized names.  */
      iconv_open ("UTF-8", "EUC-JP") == (iconv_t)(-1)
      /* Try IRIX, OSF/1 names.  */
      && iconv_open ("UTF-8", "eucJP") == (iconv_t)(-1)
      /* Try AIX names.  */
      && iconv_open ("UTF-8", "IBM-eucJP") == (iconv_t)(-1)
      /* Try HP-UX names.  */
      && iconv_open ("utf8", "eucJP") == (iconv_t)(-1))
    result |= 16;
  return result;
]])],
          [am_cv_func_iconv_works=yes], ,
          [case "$host_os" in
             aix* | hpux*) am_cv_func_iconv_works="guessing no" ;;
             *)            am_cv_func_iconv_works="guessing yes" ;;
           esac])
        test "$am_cv_func_iconv_works" = no || break
      done
      LIBS="$am_save_LIBS"
    ])
    case "$am_cv_func_iconv_works" in
      *no) am_func_iconv=no am_cv_lib_iconv=no ;;
      *)   am_func_iconv=yes ;;
    esac
  else
    am_func_iconv=no am_cv_lib_iconv=no
  fi
  if test "$am_func_iconv" = yes; then
    AC_DEFINE([HAVE_ICONV], [1],
      [Define if you have the iconv() function and it works.])
  fi
  if test "$am_cv_lib_iconv" = yes; then
    AC_MSG_CHECKING([how to link with libiconv])
    AC_MSG_RESULT([$LIBICONV])
  else
    dnl If $LIBICONV didn't lead to a usable library, we don't need $INCICONV
    dnl either.
    CPPFLAGS="$am_save_CPPFLAGS"
    LIBICONV=
    LTLIBICONV=
  fi
  AC_SUBST([LIBICONV])
  AC_SUBST([LTLIBICONV])
])
m4trace:m4/iconv.m4:233: -1- AC_DEFUN_ONCE([AM_ICONV], [
  AM_ICONV_LINK
  if test "$am_cv_func_iconv" = yes; then
    AC_MSG_CHECKING([for iconv declaration])
    AC_CACHE_VAL([am_cv_proto_iconv], [
      AC_COMPILE_IFELSE(
        [AC_LANG_PROGRAM(
           [[
#include <stdlib.h>
#include <iconv.h>
extern
#ifdef __cplusplus
"C"
#endif
#if defined(__STDC__) || defined(_MSC_VER) || defined(__cplusplus)
size_t iconv (iconv_t cd, char * *inbuf, size_t *inbytesleft, char * *outbuf, size_t *outbytesleft);
#else
size_t iconv();
#endif
           ]],
           [[]])],
        [am_cv_proto_iconv_arg1=""],
        [am_cv_proto_iconv_arg1="const"])
      am_cv_proto_iconv="extern size_t iconv (iconv_t cd, $am_cv_proto_iconv_arg1 char * *inbuf, size_t *inbytesleft, char * *outbuf, size_t *outbytesleft);"])
    am_cv_proto_iconv=`echo "[$]am_cv_proto_iconv" | tr -s ' ' | sed -e 's/( /(/'`
    AC_MSG_RESULT([
         $am_cv_proto_iconv])
    AC_DEFINE_UNQUOTED([ICONV_CONST], [$am_cv_proto_iconv_arg1],
      [Define as const if the declaration of iconv() needs const.])
    dnl Also substitute ICONV_CONST in the gnulib generated <iconv.h>.
    m4_ifdef([gl_ICONV_H_DEFAULTS],
      [AC_REQUIRE([gl_ICONV_H_DEFAULTS])
       if test -n "$am_cv_proto_iconv_arg1"; then
         ICONV_CONST="const"
       fi
      ])
  fi
])
m4trace:m4/intdiv0.m4:9: -1- AC_DEFUN([gt_INTDIV0], [
  AC_REQUIRE([AC_PROG_CC])dnl
  AC_REQUIRE([AC_CANONICAL_HOST])dnl

  AC_CACHE_CHECK([whether integer division by zero raises SIGFPE],
    gt_cv_int_divbyzero_sigfpe,
    [
      gt_cv_int_divbyzero_sigfpe=
changequote(,)dnl
      case "$host_os" in
        macos* | darwin[6-9]* | darwin[1-9][0-9]*)
          # On Mac OS X 10.2 or newer, just assume the same as when cross-
          # compiling. If we were to perform the real test, 1 Crash Report
          # dialog window would pop up.
          case "$host_cpu" in
            i[34567]86 | x86_64)
              gt_cv_int_divbyzero_sigfpe="guessing yes" ;;
          esac
          ;;
      esac
changequote([,])dnl
      if test -z "$gt_cv_int_divbyzero_sigfpe"; then
        AC_RUN_IFELSE(
          [AC_LANG_SOURCE([[
#include <stdlib.h>
#include <signal.h>

static void
sigfpe_handler (int sig)
{
  /* Exit with code 0 if SIGFPE, with code 1 if any other signal.  */
  _exit (sig != SIGFPE);
}

int x = 1;
int y = 0;
int z;
int nan;

int main ()
{
  signal (SIGFPE, sigfpe_handler);
/* IRIX and AIX (when "xlc -qcheck" is used) yield signal SIGTRAP.  */
#if (defined (__sgi) || defined (_AIX)) && defined (SIGTRAP)
  signal (SIGTRAP, sigfpe_handler);
#endif
/* Linux/SPARC yields signal SIGILL.  */
#if defined (__sparc__) && defined (__linux__)
  signal (SIGILL, sigfpe_handler);
#endif

  z = x / y;
  nan = y / y;
  exit (2);
}
]])],
          [gt_cv_int_divbyzero_sigfpe=yes],
          [gt_cv_int_divbyzero_sigfpe=no],
          [
            # Guess based on the CPU.
changequote(,)dnl
            case "$host_cpu" in
              alpha* | i[34567]86 | x86_64 | m68k | s390*)
                gt_cv_int_divbyzero_sigfpe="guessing yes";;
              *)
                gt_cv_int_divbyzero_sigfpe="guessing no";;
            esac
changequote([,])dnl
          ])
      fi
    ])
  case "$gt_cv_int_divbyzero_sigfpe" in
    *yes) value=1;;
    *) value=0;;
  esac
  AC_DEFINE_UNQUOTED([INTDIV0_RAISES_SIGFPE], [$value],
    [Define if integer division by zero raises signal SIGFPE.])
])
m4trace:m4/intl.m4:25: -1- AC_DEFUN([AM_INTL_SUBDIR], [
  AC_REQUIRE([AC_PROG_INSTALL])dnl
  AC_REQUIRE([AC_PROG_MKDIR_P])dnl
  AC_REQUIRE([AC_PROG_CC])dnl
  AC_REQUIRE([AC_CANONICAL_HOST])dnl
  AC_REQUIRE([gt_GLIBC2])dnl
  AC_REQUIRE([AC_PROG_RANLIB])dnl
  AC_REQUIRE([gl_VISIBILITY])dnl
  AC_REQUIRE([gt_INTL_SUBDIR_CORE])dnl
  AC_REQUIRE([AC_TYPE_LONG_LONG_INT])dnl
  AC_REQUIRE([gt_TYPE_WCHAR_T])dnl
  AC_REQUIRE([gt_TYPE_WINT_T])dnl
  AC_REQUIRE([gl_AC_HEADER_INTTYPES_H])
  AC_REQUIRE([gt_TYPE_INTMAX_T])
  AC_REQUIRE([gt_PRINTF_POSIX])
  AC_REQUIRE([gl_GLIBC21])dnl
  AC_REQUIRE([gl_XSIZE])dnl
  AC_REQUIRE([gl_FCNTL_O_FLAGS])dnl
  AC_REQUIRE([gt_INTL_MACOSX])dnl
  AC_REQUIRE([gl_EXTERN_INLINE])dnl
  AC_REQUIRE([gt_GL_ATTRIBUTE])dnl

  dnl Support for automake's --enable-silent-rules.
  case "$enable_silent_rules" in
    yes) INTL_DEFAULT_VERBOSITY=0;;
    no)  INTL_DEFAULT_VERBOSITY=1;;
    *)   INTL_DEFAULT_VERBOSITY=1;;
  esac
  AC_SUBST([INTL_DEFAULT_VERBOSITY])

  AC_CHECK_TYPE([ptrdiff_t], ,
    [AC_DEFINE([ptrdiff_t], [long],
       [Define as the type of the result of subtracting two pointers, if the system doesn't define it.])
    ])
  AC_CHECK_HEADERS([features.h stddef.h stdlib.h string.h])
  AC_CHECK_FUNCS([asprintf fwprintf newlocale putenv setenv setlocale \
    snprintf strnlen wcslen wcsnlen mbrtowc wcrtomb])

  dnl Use the _snprintf function only if it is declared (because on NetBSD it
  dnl is defined as a weak alias of snprintf; we prefer to use the latter).
  AC_CHECK_DECLS([_snprintf, _snwprintf], , , [#include <stdio.h>])

  dnl Use the *_unlocked functions only if they are declared.
  dnl (because some of them were defined without being declared in Solaris
  dnl 2.5.1 but were removed in Solaris 2.6, whereas we want binaries built
  dnl on Solaris 2.5.1 to run on Solaris 2.6).
  AC_CHECK_DECLS([getc_unlocked], , , [#include <stdio.h>])

  case $gt_cv_func_printf_posix in
    *yes) HAVE_POSIX_PRINTF=1 ;;
    *) HAVE_POSIX_PRINTF=0 ;;
  esac
  AC_SUBST([HAVE_POSIX_PRINTF])
  if test "$ac_cv_func_asprintf" = yes; then
    HAVE_ASPRINTF=1
  else
    HAVE_ASPRINTF=0
  fi
  AC_SUBST([HAVE_ASPRINTF])
  if test "$ac_cv_func_snprintf" = yes; then
    HAVE_SNPRINTF=1
  else
    HAVE_SNPRINTF=0
  fi
  AC_SUBST([HAVE_SNPRINTF])
  if test "$ac_cv_func_newlocale" = yes; then
    HAVE_NEWLOCALE=1
  else
    HAVE_NEWLOCALE=0
  fi
  AC_SUBST([HAVE_NEWLOCALE])
  if test "$ac_cv_func_wprintf" = yes; then
    HAVE_WPRINTF=1
  else
    HAVE_WPRINTF=0
  fi
  AC_SUBST([HAVE_WPRINTF])

  AM_LANGINFO_CODESET
  gt_LC_MESSAGES

  dnl Compilation on mingw and Cygwin needs special Makefile rules, because
  dnl 1. when we install a shared library, we must arrange to export
  dnl    auxiliary pointer variables for every exported variable,
  dnl 2. when we install a shared library and a static library simultaneously,
  dnl    the include file specifies __declspec(dllimport) and therefore we
  dnl    must arrange to define the auxiliary pointer variables for the
  dnl    exported variables _also_ in the static library.
  if test "$enable_shared" = yes; then
    case "$host_os" in
      mingw* | cygwin*) is_woe32dll=yes ;;
      *) is_woe32dll=no ;;
    esac
  else
    is_woe32dll=no
  fi
  WOE32DLL=$is_woe32dll
  AC_SUBST([WOE32DLL])

  dnl On mingw and Cygwin, we can activate special Makefile rules which add
  dnl version information to the shared libraries and executables.
  case "$host_os" in
    mingw* | cygwin*) is_woe32=yes ;;
    *) is_woe32=no ;;
  esac
  WOE32=$is_woe32
  AC_SUBST([WOE32])
  if test $WOE32 = yes; then
    dnl Check for a program that compiles Windows resource files.
    AC_CHECK_TOOL([WINDRES], [windres])
  fi

  dnl Determine whether when creating a library, "-lc" should be passed to
  dnl libtool or not. On many platforms, it is required for the libtool option
  dnl -no-undefined to work. On HP-UX, however, the -lc - stored by libtool
  dnl in the *.la files - makes it impossible to create multithreaded programs,
  dnl because libtool also reorders the -lc to come before the -pthread, and
  dnl this disables pthread_create() <http://docs.hp.com/en/1896/pthreads.html>.
  case "$host_os" in
    hpux*) LTLIBC="" ;;
    *)     LTLIBC="-lc" ;;
  esac
  AC_SUBST([LTLIBC])

  dnl Rename some macros and functions used for locking.
  AH_BOTTOM([
#define __libc_lock_t                   gl_lock_t
#define __libc_lock_define              gl_lock_define
#define __libc_lock_define_initialized  gl_lock_define_initialized
#define __libc_lock_init                gl_lock_init
#define __libc_lock_lock                gl_lock_lock
#define __libc_lock_unlock              gl_lock_unlock
#define __libc_lock_recursive_t                   gl_recursive_lock_t
#define __libc_lock_define_recursive              gl_recursive_lock_define
#define __libc_lock_define_initialized_recursive  gl_recursive_lock_define_initialized
#define __libc_lock_init_recursive                gl_recursive_lock_init
#define __libc_lock_lock_recursive                gl_recursive_lock_lock
#define __libc_lock_unlock_recursive              gl_recursive_lock_unlock
#define glthread_in_use  libintl_thread_in_use
#define glthread_lock_init_func     libintl_lock_init_func
#define glthread_lock_lock_func     libintl_lock_lock_func
#define glthread_lock_unlock_func   libintl_lock_unlock_func
#define glthread_lock_destroy_func  libintl_lock_destroy_func
#define glthread_rwlock_init_multithreaded     libintl_rwlock_init_multithreaded
#define glthread_rwlock_init_func              libintl_rwlock_init_func
#define glthread_rwlock_rdlock_multithreaded   libintl_rwlock_rdlock_multithreaded
#define glthread_rwlock_rdlock_func            libintl_rwlock_rdlock_func
#define glthread_rwlock_wrlock_multithreaded   libintl_rwlock_wrlock_multithreaded
#define glthread_rwlock_wrlock_func            libintl_rwlock_wrlock_func
#define glthread_rwlock_unlock_multithreaded   libintl_rwlock_unlock_multithreaded
#define glthread_rwlock_unlock_func            libintl_rwlock_unlock_func
#define glthread_rwlock_destroy_multithreaded  libintl_rwlock_destroy_multithreaded
#define glthread_rwlock_destroy_func           libintl_rwlock_destroy_func
#define glthread_recursive_lock_init_multithreaded     libintl_recursive_lock_init_multithreaded
#define glthread_recursive_lock_init_func              libintl_recursive_lock_init_func
#define glthread_recursive_lock_lock_multithreaded     libintl_recursive_lock_lock_multithreaded
#define glthread_recursive_lock_lock_func              libintl_recursive_lock_lock_func
#define glthread_recursive_lock_unlock_multithreaded   libintl_recursive_lock_unlock_multithreaded
#define glthread_recursive_lock_unlock_func            libintl_recursive_lock_unlock_func
#define glthread_recursive_lock_destroy_multithreaded  libintl_recursive_lock_destroy_multithreaded
#define glthread_recursive_lock_destroy_func           libintl_recursive_lock_destroy_func
#define glthread_once_func            libintl_once_func
#define glthread_once_singlethreaded  libintl_once_singlethreaded
#define glthread_once_multithreaded   libintl_once_multithreaded
])
])
m4trace:m4/intl.m4:211: -1- AC_DEFUN([gt_INTL_SUBDIR_CORE], [
  AC_REQUIRE([AC_C_INLINE])dnl
  AC_REQUIRE([AC_TYPE_SIZE_T])dnl
  AC_REQUIRE([gl_AC_HEADER_STDINT_H])
  AC_REQUIRE([AC_FUNC_ALLOCA])dnl
  AC_REQUIRE([AC_FUNC_MMAP])dnl
  AC_REQUIRE([gt_INTDIV0])dnl
  AC_REQUIRE([gl_AC_TYPE_UINTMAX_T])dnl
  AC_REQUIRE([gt_INTTYPES_PRI])dnl
  AC_REQUIRE([gl_LOCK])dnl

  AC_LINK_IFELSE(
    [AC_LANG_PROGRAM(
       [[int foo (int a) { a = __builtin_expect (a, 10); return a == 10 ? 0 : 1; }]],
       [[]])],
    [AC_DEFINE([HAVE_BUILTIN_EXPECT], [1],
       [Define to 1 if the compiler understands __builtin_expect.])])

  AC_CHECK_HEADERS([argz.h inttypes.h limits.h unistd.h sys/param.h])
  AC_CHECK_FUNCS([getcwd getegid geteuid getgid getuid mempcpy munmap \
    stpcpy strcasecmp strdup strtoul tsearch uselocale argz_count \
    argz_stringify argz_next __fsetlocking])

  dnl Solaris 12 provides getlocalename_l, while Illumos doesn't have
  dnl it nor the equivalent.
  if test $ac_cv_func_uselocale = yes; then
    AC_CHECK_FUNCS([getlocalename_l])
  fi

  dnl Use the *_unlocked functions only if they are declared.
  dnl (because some of them were defined without being declared in Solaris
  dnl 2.5.1 but were removed in Solaris 2.6, whereas we want binaries built
  dnl on Solaris 2.5.1 to run on Solaris 2.6).
  AC_CHECK_DECLS([feof_unlocked, fgets_unlocked], , , [#include <stdio.h>])

  AM_ICONV

  dnl intl/plural.c is generated from intl/plural.y. It requires bison,
  dnl because plural.y uses bison specific features. It requires at least
  dnl bison-2.7 for %define api.pure.
  dnl bison is only needed for the maintainer (who touches plural.y). But in
  dnl order to avoid separate Makefiles or --enable-maintainer-mode, we put
  dnl the rule in general Makefile. Now, some people carelessly touch the
  dnl files or have a broken "make" program, hence the plural.c rule will
  dnl sometimes fire. To avoid an error, defines BISON to ":" if it is not
  dnl present or too old.
  AC_CHECK_PROGS([INTLBISON], [bison])
  if test -z "$INTLBISON"; then
    ac_verc_fail=yes
  else
    dnl Found it, now check the version.
    AC_MSG_CHECKING([version of bison])
changequote(<<,>>)dnl
    ac_prog_version=`$INTLBISON --version 2>&1 | sed -n 's/^.*GNU Bison.* \([0-9]*\.[0-9.]*\).*$/\1/p'`
    case $ac_prog_version in
      '') ac_prog_version="v. ?.??, bad"; ac_verc_fail=yes;;
      2.[7-9]* | [3-9].*)
changequote([,])dnl
         ac_prog_version="$ac_prog_version, ok"; ac_verc_fail=no;;
      *) ac_prog_version="$ac_prog_version, bad"; ac_verc_fail=yes;;
    esac
    AC_MSG_RESULT([$ac_prog_version])
  fi
  if test $ac_verc_fail = yes; then
    INTLBISON=:
  fi
])
m4trace:m4/intl.m4:282: -1- AC_DEFUN([gt_GL_ATTRIBUTE], [
  m4_ifndef([gl_[]COMMON],
    AH_VERBATIM([gt_gl_attribute],
[/* Define as a marker that can be attached to declarations that might not
    be used.  This helps to reduce warnings, such as from
    GCC -Wunused-parameter.  */
#ifndef _GL_UNUSED
# if __GNUC__ >= 3 || (__GNUC__ == 2 && __GNUC_MINOR__ >= 7)
#  define _GL_UNUSED __attribute__ ((__unused__))
# else
#  define _GL_UNUSED
# endif
#endif

/* The __pure__ attribute was added in gcc 2.96.  */
#ifndef _GL_ATTRIBUTE_PURE
# if __GNUC__ > 2 || (__GNUC__ == 2 && __GNUC_MINOR__ >= 96)
#  define _GL_ATTRIBUTE_PURE __attribute__ ((__pure__))
# else
#  define _GL_ATTRIBUTE_PURE /* empty */
# endif
#endif
]))])
m4trace:m4/intlmacosx.m4:18: -1- AC_DEFUN([gt_INTL_MACOSX], [
  dnl Check for API introduced in Mac OS X 10.2.
  AC_CACHE_CHECK([for CFPreferencesCopyAppValue],
    [gt_cv_func_CFPreferencesCopyAppValue],
    [gt_save_LIBS="$LIBS"
     LIBS="$LIBS -Wl,-framework -Wl,CoreFoundation"
     AC_LINK_IFELSE(
       [AC_LANG_PROGRAM(
          [[#include <CoreFoundation/CFPreferences.h>]],
          [[CFPreferencesCopyAppValue(NULL, NULL)]])],
       [gt_cv_func_CFPreferencesCopyAppValue=yes],
       [gt_cv_func_CFPreferencesCopyAppValue=no])
     LIBS="$gt_save_LIBS"])
  if test $gt_cv_func_CFPreferencesCopyAppValue = yes; then
    AC_DEFINE([HAVE_CFPREFERENCESCOPYAPPVALUE], [1],
      [Define to 1 if you have the Mac OS X function CFPreferencesCopyAppValue in the CoreFoundation framework.])
  fi
  dnl Check for API introduced in Mac OS X 10.3.
  AC_CACHE_CHECK([for CFLocaleCopyCurrent], [gt_cv_func_CFLocaleCopyCurrent],
    [gt_save_LIBS="$LIBS"
     LIBS="$LIBS -Wl,-framework -Wl,CoreFoundation"
     AC_LINK_IFELSE(
       [AC_LANG_PROGRAM(
          [[#include <CoreFoundation/CFLocale.h>]],
          [[CFLocaleCopyCurrent();]])],
       [gt_cv_func_CFLocaleCopyCurrent=yes],
       [gt_cv_func_CFLocaleCopyCurrent=no])
     LIBS="$gt_save_LIBS"])
  if test $gt_cv_func_CFLocaleCopyCurrent = yes; then
    AC_DEFINE([HAVE_CFLOCALECOPYCURRENT], [1],
      [Define to 1 if you have the Mac OS X function CFLocaleCopyCurrent in the CoreFoundation framework.])
  fi
  INTL_MACOSX_LIBS=
  if test $gt_cv_func_CFPreferencesCopyAppValue = yes || test $gt_cv_func_CFLocaleCopyCurrent = yes; then
    INTL_MACOSX_LIBS="-Wl,-framework -Wl,CoreFoundation"
  fi
  AC_SUBST([INTL_MACOSX_LIBS])
])
m4trace:m4/intmax.m4:11: -1- AC_DEFUN([gt_TYPE_INTMAX_T], [
  AC_REQUIRE([gl_AC_HEADER_INTTYPES_H])
  AC_REQUIRE([gl_AC_HEADER_STDINT_H])
  AC_CACHE_CHECK([for intmax_t], [gt_cv_c_intmax_t],
    [AC_COMPILE_IFELSE(
       [AC_LANG_PROGRAM(
          [[
#include <stddef.h>
#include <stdlib.h>
#if HAVE_STDINT_H_WITH_UINTMAX
#include <stdint.h>
#endif
#if HAVE_INTTYPES_H_WITH_UINTMAX
#include <inttypes.h>
#endif
          ]],
          [[intmax_t x = -1;
            return !x;]])],
       [gt_cv_c_intmax_t=yes],
       [gt_cv_c_intmax_t=no])])
  if test $gt_cv_c_intmax_t = yes; then
    AC_DEFINE([HAVE_INTMAX_T], [1],
      [Define if you have the 'intmax_t' type in <stdint.h> or <inttypes.h>.])
  fi
])
m4trace:m4/introspection.m4:84: -1- AC_DEFUN([GOBJECT_INTROSPECTION_CHECK], [
  _GOBJECT_INTROSPECTION_CHECK_INTERNAL([$1])
])
m4trace:m4/introspection.m4:93: -1- AC_DEFUN([GOBJECT_INTROSPECTION_REQUIRE], [
  _GOBJECT_INTROSPECTION_CHECK_INTERNAL([$1], [require])
])
m4trace:m4/inttypes-pri.m4:14: -1- AC_DEFUN([gt_INTTYPES_PRI], [
  AC_CHECK_HEADERS([inttypes.h])
  if test $ac_cv_header_inttypes_h = yes; then
    AC_CACHE_CHECK([whether the inttypes.h PRIxNN macros are broken],
      [gt_cv_inttypes_pri_broken],
      [
        AC_COMPILE_IFELSE(
          [AC_LANG_PROGRAM(
             [[
#include <inttypes.h>
#ifdef PRId32
char *p = PRId32;
#endif
             ]],
             [[]])],
          [gt_cv_inttypes_pri_broken=no],
          [gt_cv_inttypes_pri_broken=yes])
      ])
  fi
  if test "$gt_cv_inttypes_pri_broken" = yes; then
    AC_DEFINE_UNQUOTED([PRI_MACROS_BROKEN], [1],
      [Define if <inttypes.h> exists and defines unusable PRI* macros.])
    PRI_MACROS_BROKEN=1
  else
    PRI_MACROS_BROKEN=0
  fi
  AC_SUBST([PRI_MACROS_BROKEN])
])
m4trace:m4/inttypes_h.m4:12: -1- AC_DEFUN([gl_AC_HEADER_INTTYPES_H], [
  AC_CACHE_CHECK([for inttypes.h], [gl_cv_header_inttypes_h],
    [AC_COMPILE_IFELSE(
       [AC_LANG_PROGRAM(
          [[
#include <sys/types.h>
#include <inttypes.h>
          ]],
          [[uintmax_t i = (uintmax_t) -1; return !i;]])],
       [gl_cv_header_inttypes_h=yes],
       [gl_cv_header_inttypes_h=no])])
  if test $gl_cv_header_inttypes_h = yes; then
    AC_DEFINE_UNQUOTED([HAVE_INTTYPES_H_WITH_UINTMAX], [1],
      [Define if <inttypes.h> exists, doesn't clash with <sys/types.h>,
       and declares uintmax_t. ])
  fi
])
m4trace:m4/lcmessage.m4:22: -1- AC_DEFUN([gt_LC_MESSAGES], [
  AC_CACHE_CHECK([for LC_MESSAGES], [gt_cv_val_LC_MESSAGES],
    [AC_LINK_IFELSE(
       [AC_LANG_PROGRAM(
          [[#include <locale.h>]],
          [[return LC_MESSAGES]])],
       [gt_cv_val_LC_MESSAGES=yes],
       [gt_cv_val_LC_MESSAGES=no])])
  if test $gt_cv_val_LC_MESSAGES = yes; then
    AC_DEFINE([HAVE_LC_MESSAGES], [1],
      [Define if your <locale.h> file defines LC_MESSAGES.])
  fi
])
m4trace:m4/lib-ld.m4:12: -1- AC_DEFUN([AC_LIB_PROG_LD_GNU], [AC_CACHE_CHECK([if the linker ($LD) is GNU ld], [acl_cv_prog_gnu_ld],
[# I'd rather use --version here, but apparently some GNU lds only accept -v.
case `$LD -v 2>&1 </dev/null` in
*GNU* | *'with BFD'*)
  acl_cv_prog_gnu_ld=yes
  ;;
*)
  acl_cv_prog_gnu_ld=no
  ;;
esac])
with_gnu_ld=$acl_cv_prog_gnu_ld
])
m4trace:m4/lib-ld.m4:27: -1- AC_DEFUN([AC_LIB_PROG_LD], [AC_REQUIRE([AC_PROG_CC])dnl
AC_REQUIRE([AC_CANONICAL_HOST])dnl

AC_ARG_WITH([gnu-ld],
    [AS_HELP_STRING([--with-gnu-ld],
        [assume the C compiler uses GNU ld [default=no]])],
    [test "$withval" = no || with_gnu_ld=yes],
    [with_gnu_ld=no])dnl

# Prepare PATH_SEPARATOR.
# The user is always right.
if test "${PATH_SEPARATOR+set}" != set; then
  # Determine PATH_SEPARATOR by trying to find /bin/sh in a PATH which
  # contains only /bin. Note that ksh looks also at the FPATH variable,
  # so we have to set that as well for the test.
  PATH_SEPARATOR=:
  (PATH='/bin;/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 \
    && { (PATH='/bin:/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 \
           || PATH_SEPARATOR=';'
       }
fi

ac_prog=ld
if test "$GCC" = yes; then
  # Check if gcc -print-prog-name=ld gives a path.
  AC_MSG_CHECKING([for ld used by $CC])
  case $host in
  *-*-mingw*)
    # gcc leaves a trailing carriage return which upsets mingw
    ac_prog=`($CC -print-prog-name=ld) 2>&5 | tr -d '\015'` ;;
  *)
    ac_prog=`($CC -print-prog-name=ld) 2>&5` ;;
  esac
  case $ac_prog in
    # Accept absolute paths.
    [[\\/]]* | ?:[[\\/]]*)
      re_direlt='/[[^/]][[^/]]*/\.\./'
      # Canonicalize the pathname of ld
      ac_prog=`echo "$ac_prog"| sed 's%\\\\%/%g'`
      while echo "$ac_prog" | grep "$re_direlt" > /dev/null 2>&1; do
        ac_prog=`echo $ac_prog| sed "s%$re_direlt%/%"`
      done
      test -z "$LD" && LD="$ac_prog"
      ;;
  "")
    # If it fails, then pretend we aren't using GCC.
    ac_prog=ld
    ;;
  *)
    # If it is relative, then search for the first ld in PATH.
    with_gnu_ld=unknown
    ;;
  esac
elif test "$with_gnu_ld" = yes; then
  AC_MSG_CHECKING([for GNU ld])
else
  AC_MSG_CHECKING([for non-GNU ld])
fi
AC_CACHE_VAL([acl_cv_path_LD],
[if test -z "$LD"; then
  acl_save_ifs="$IFS"; IFS=$PATH_SEPARATOR
  for ac_dir in $PATH; do
    IFS="$acl_save_ifs"
    test -z "$ac_dir" && ac_dir=.
    if test -f "$ac_dir/$ac_prog" || test -f "$ac_dir/$ac_prog$ac_exeext"; then
      acl_cv_path_LD="$ac_dir/$ac_prog"
      # Check to see if the program is GNU ld.  I'd rather use --version,
      # but apparently some variants of GNU ld only accept -v.
      # Break only if it was the GNU/non-GNU ld that we prefer.
      case `"$acl_cv_path_LD" -v 2>&1 </dev/null` in
      *GNU* | *'with BFD'*)
        test "$with_gnu_ld" != no && break
        ;;
      *)
        test "$with_gnu_ld" != yes && break
        ;;
      esac
    fi
  done
  IFS="$acl_save_ifs"
else
  acl_cv_path_LD="$LD" # Let the user override the test with a path.
fi])
LD="$acl_cv_path_LD"
if test -n "$LD"; then
  AC_MSG_RESULT([$LD])
else
  AC_MSG_RESULT([no])
fi
test -z "$LD" && AC_MSG_ERROR([no acceptable ld found in \$PATH])
AC_LIB_PROG_LD_GNU
])
m4trace:m4/lib-link.m4:17: -1- AC_DEFUN([AC_LIB_LINKFLAGS], [
  AC_REQUIRE([AC_LIB_PREPARE_PREFIX])
  AC_REQUIRE([AC_LIB_RPATH])
  pushdef([Name],[m4_translit([$1],[./+-], [____])])
  pushdef([NAME],[m4_translit([$1],[abcdefghijklmnopqrstuvwxyz./+-],
                                   [ABCDEFGHIJKLMNOPQRSTUVWXYZ____])])
  AC_CACHE_CHECK([how to link with lib[]$1], [ac_cv_lib[]Name[]_libs], [
    AC_LIB_LINKFLAGS_BODY([$1], [$2])
    ac_cv_lib[]Name[]_libs="$LIB[]NAME"
    ac_cv_lib[]Name[]_ltlibs="$LTLIB[]NAME"
    ac_cv_lib[]Name[]_cppflags="$INC[]NAME"
    ac_cv_lib[]Name[]_prefix="$LIB[]NAME[]_PREFIX"
  ])
  LIB[]NAME="$ac_cv_lib[]Name[]_libs"
  LTLIB[]NAME="$ac_cv_lib[]Name[]_ltlibs"
  INC[]NAME="$ac_cv_lib[]Name[]_cppflags"
  LIB[]NAME[]_PREFIX="$ac_cv_lib[]Name[]_prefix"
  AC_LIB_APPENDTOVAR([CPPFLAGS], [$INC]NAME)
  AC_SUBST([LIB]NAME)
  AC_SUBST([LTLIB]NAME)
  AC_SUBST([LIB]NAME[_PREFIX])
  dnl Also set HAVE_LIB[]NAME so that AC_LIB_HAVE_LINKFLAGS can reuse the
  dnl results of this search when this library appears as a dependency.
  HAVE_LIB[]NAME=yes
  popdef([NAME])
  popdef([Name])
])
m4trace:m4/lib-link.m4:57: -1- AC_DEFUN([AC_LIB_HAVE_LINKFLAGS], [
  AC_REQUIRE([AC_LIB_PREPARE_PREFIX])
  AC_REQUIRE([AC_LIB_RPATH])
  pushdef([Name],[m4_translit([$1],[./+-], [____])])
  pushdef([NAME],[m4_translit([$1],[abcdefghijklmnopqrstuvwxyz./+-],
                                   [ABCDEFGHIJKLMNOPQRSTUVWXYZ____])])

  dnl Search for lib[]Name and define LIB[]NAME, LTLIB[]NAME and INC[]NAME
  dnl accordingly.
  AC_LIB_LINKFLAGS_BODY([$1], [$2])

  dnl Add $INC[]NAME to CPPFLAGS before performing the following checks,
  dnl because if the user has installed lib[]Name and not disabled its use
  dnl via --without-lib[]Name-prefix, he wants to use it.
  ac_save_CPPFLAGS="$CPPFLAGS"
  AC_LIB_APPENDTOVAR([CPPFLAGS], [$INC]NAME)

  AC_CACHE_CHECK([for lib[]$1], [ac_cv_lib[]Name], [
    ac_save_LIBS="$LIBS"
    dnl If $LIB[]NAME contains some -l options, add it to the end of LIBS,
    dnl because these -l options might require -L options that are present in
    dnl LIBS. -l options benefit only from the -L options listed before it.
    dnl Otherwise, add it to the front of LIBS, because it may be a static
    dnl library that depends on another static library that is present in LIBS.
    dnl Static libraries benefit only from the static libraries listed after
    dnl it.
    case " $LIB[]NAME" in
      *" -l"*) LIBS="$LIBS $LIB[]NAME" ;;
      *)       LIBS="$LIB[]NAME $LIBS" ;;
    esac
    AC_LINK_IFELSE(
      [AC_LANG_PROGRAM([[$3]], [[$4]])],
      [ac_cv_lib[]Name=yes],
      [ac_cv_lib[]Name='m4_if([$5], [], [no], [[$5]])'])
    LIBS="$ac_save_LIBS"
  ])
  if test "$ac_cv_lib[]Name" = yes; then
    HAVE_LIB[]NAME=yes
    AC_DEFINE([HAVE_LIB]NAME, 1, [Define if you have the lib][$1 library.])
    AC_MSG_CHECKING([how to link with lib[]$1])
    AC_MSG_RESULT([$LIB[]NAME])
  else
    HAVE_LIB[]NAME=no
    dnl If $LIB[]NAME didn't lead to a usable library, we don't need
    dnl $INC[]NAME either.
    CPPFLAGS="$ac_save_CPPFLAGS"
    LIB[]NAME=
    LTLIB[]NAME=
    LIB[]NAME[]_PREFIX=
  fi
  AC_SUBST([HAVE_LIB]NAME)
  AC_SUBST([LIB]NAME)
  AC_SUBST([LTLIB]NAME)
  AC_SUBST([LIB]NAME[_PREFIX])
  popdef([NAME])
  popdef([Name])
])
m4trace:m4/lib-link.m4:125: -1- AC_DEFUN([AC_LIB_RPATH], [
  dnl Tell automake >= 1.10 to complain if config.rpath is missing.
  m4_ifdef([AC_REQUIRE_AUX_FILE], [AC_REQUIRE_AUX_FILE([config.rpath])])
  AC_REQUIRE([AC_PROG_CC])                dnl we use $CC, $GCC, $LDFLAGS
  AC_REQUIRE([AC_LIB_PROG_LD])            dnl we use $LD, $with_gnu_ld
  AC_REQUIRE([AC_CANONICAL_HOST])         dnl we use $host
  AC_REQUIRE([AC_CONFIG_AUX_DIR_DEFAULT]) dnl we use $ac_aux_dir
  AC_CACHE_CHECK([for shared library run path origin], [acl_cv_rpath], [
    CC="$CC" GCC="$GCC" LDFLAGS="$LDFLAGS" LD="$LD" with_gnu_ld="$with_gnu_ld" \
    ${CONFIG_SHELL-/bin/sh} "$ac_aux_dir/config.rpath" "$host" > conftest.sh
    . ./conftest.sh
    rm -f ./conftest.sh
    acl_cv_rpath=done
  ])
  wl="$acl_cv_wl"
  acl_libext="$acl_cv_libext"
  acl_shlibext="$acl_cv_shlibext"
  acl_libname_spec="$acl_cv_libname_spec"
  acl_library_names_spec="$acl_cv_library_names_spec"
  acl_hardcode_libdir_flag_spec="$acl_cv_hardcode_libdir_flag_spec"
  acl_hardcode_libdir_separator="$acl_cv_hardcode_libdir_separator"
  acl_hardcode_direct="$acl_cv_hardcode_direct"
  acl_hardcode_minus_L="$acl_cv_hardcode_minus_L"
  dnl Determine whether the user wants rpath handling at all.
  AC_ARG_ENABLE([rpath],
    [  --disable-rpath         do not hardcode runtime library paths],
    :, enable_rpath=yes)
])
m4trace:m4/lib-link.m4:161: -1- AC_DEFUN([AC_LIB_FROMPACKAGE], [
  pushdef([NAME],[m4_translit([$1],[abcdefghijklmnopqrstuvwxyz./+-],
                                   [ABCDEFGHIJKLMNOPQRSTUVWXYZ____])])
  define([acl_frompackage_]NAME, [$2])
  popdef([NAME])
  pushdef([PACK],[$2])
  pushdef([PACKUP],[m4_translit(PACK,[abcdefghijklmnopqrstuvwxyz./+-],
                                     [ABCDEFGHIJKLMNOPQRSTUVWXYZ____])])
  define([acl_libsinpackage_]PACKUP,
    m4_ifdef([acl_libsinpackage_]PACKUP, [m4_defn([acl_libsinpackage_]PACKUP)[, ]],)[lib$1])
  popdef([PACKUP])
  popdef([PACK])
])
m4trace:m4/lib-link.m4:181: -1- AC_DEFUN([AC_LIB_LINKFLAGS_BODY], [
  AC_REQUIRE([AC_LIB_PREPARE_MULTILIB])
  pushdef([NAME],[m4_translit([$1],[abcdefghijklmnopqrstuvwxyz./+-],
                                   [ABCDEFGHIJKLMNOPQRSTUVWXYZ____])])
  pushdef([PACK],[m4_ifdef([acl_frompackage_]NAME, [acl_frompackage_]NAME, lib[$1])])
  pushdef([PACKUP],[m4_translit(PACK,[abcdefghijklmnopqrstuvwxyz./+-],
                                     [ABCDEFGHIJKLMNOPQRSTUVWXYZ____])])
  pushdef([PACKLIBS],[m4_ifdef([acl_frompackage_]NAME, [acl_libsinpackage_]PACKUP, lib[$1])])
  dnl Autoconf >= 2.61 supports dots in --with options.
  pushdef([P_A_C_K],[m4_if(m4_version_compare(m4_defn([m4_PACKAGE_VERSION]),[2.61]),[-1],[m4_translit(PACK,[.],[_])],PACK)])
  dnl By default, look in $includedir and $libdir.
  use_additional=yes
  AC_LIB_WITH_FINAL_PREFIX([
    eval additional_includedir=\"$includedir\"
    eval additional_libdir=\"$libdir\"
  ])
  AC_ARG_WITH(P_A_C_K[-prefix],
[[  --with-]]P_A_C_K[[-prefix[=DIR]  search for ]PACKLIBS[ in DIR/include and DIR/lib
  --without-]]P_A_C_K[[-prefix     don't search for ]PACKLIBS[ in includedir and libdir]],
[
    if test "X$withval" = "Xno"; then
      use_additional=no
    else
      if test "X$withval" = "X"; then
        AC_LIB_WITH_FINAL_PREFIX([
          eval additional_includedir=\"$includedir\"
          eval additional_libdir=\"$libdir\"
        ])
      else
        additional_includedir="$withval/include"
        additional_libdir="$withval/$acl_libdirstem"
        if test "$acl_libdirstem2" != "$acl_libdirstem" \
           && ! test -d "$withval/$acl_libdirstem"; then
          additional_libdir="$withval/$acl_libdirstem2"
        fi
      fi
    fi
])
  dnl Search the library and its dependencies in $additional_libdir and
  dnl $LDFLAGS. Using breadth-first-seach.
  LIB[]NAME=
  LTLIB[]NAME=
  INC[]NAME=
  LIB[]NAME[]_PREFIX=
  dnl HAVE_LIB${NAME} is an indicator that LIB${NAME}, LTLIB${NAME} have been
  dnl computed. So it has to be reset here.
  HAVE_LIB[]NAME=
  rpathdirs=
  ltrpathdirs=
  names_already_handled=
  names_next_round='$1 $2'
  while test -n "$names_next_round"; do
    names_this_round="$names_next_round"
    names_next_round=
    for name in $names_this_round; do
      already_handled=
      for n in $names_already_handled; do
        if test "$n" = "$name"; then
          already_handled=yes
          break
        fi
      done
      if test -z "$already_handled"; then
        names_already_handled="$names_already_handled $name"
        dnl See if it was already located by an earlier AC_LIB_LINKFLAGS
        dnl or AC_LIB_HAVE_LINKFLAGS call.
        uppername=`echo "$name" | sed -e 'y|abcdefghijklmnopqrstuvwxyz./+-|ABCDEFGHIJKLMNOPQRSTUVWXYZ____|'`
        eval value=\"\$HAVE_LIB$uppername\"
        if test -n "$value"; then
          if test "$value" = yes; then
            eval value=\"\$LIB$uppername\"
            test -z "$value" || LIB[]NAME="${LIB[]NAME}${LIB[]NAME:+ }$value"
            eval value=\"\$LTLIB$uppername\"
            test -z "$value" || LTLIB[]NAME="${LTLIB[]NAME}${LTLIB[]NAME:+ }$value"
          else
            dnl An earlier call to AC_LIB_HAVE_LINKFLAGS has determined
            dnl that this library doesn't exist. So just drop it.
            :
          fi
        else
          dnl Search the library lib$name in $additional_libdir and $LDFLAGS
          dnl and the already constructed $LIBNAME/$LTLIBNAME.
          found_dir=
          found_la=
          found_so=
          found_a=
          eval libname=\"$acl_libname_spec\"    # typically: libname=lib$name
          if test -n "$acl_shlibext"; then
            shrext=".$acl_shlibext"             # typically: shrext=.so
          else
            shrext=
          fi
          if test $use_additional = yes; then
            dir="$additional_libdir"
            dnl The same code as in the loop below:
            dnl First look for a shared library.
            if test -n "$acl_shlibext"; then
              if test -f "$dir/$libname$shrext"; then
                found_dir="$dir"
                found_so="$dir/$libname$shrext"
              else
                if test "$acl_library_names_spec" = '$libname$shrext$versuffix'; then
                  ver=`(cd "$dir" && \
                        for f in "$libname$shrext".*; do echo "$f"; done \
                        | sed -e "s,^$libname$shrext\\\\.,," \
                        | sort -t '.' -n -r -k1,1 -k2,2 -k3,3 -k4,4 -k5,5 \
                        | sed 1q ) 2>/dev/null`
                  if test -n "$ver" && test -f "$dir/$libname$shrext.$ver"; then
                    found_dir="$dir"
                    found_so="$dir/$libname$shrext.$ver"
                  fi
                else
                  eval library_names=\"$acl_library_names_spec\"
                  for f in $library_names; do
                    if test -f "$dir/$f"; then
                      found_dir="$dir"
                      found_so="$dir/$f"
                      break
                    fi
                  done
                fi
              fi
            fi
            dnl Then look for a static library.
            if test "X$found_dir" = "X"; then
              if test -f "$dir/$libname.$acl_libext"; then
                found_dir="$dir"
                found_a="$dir/$libname.$acl_libext"
              fi
            fi
            if test "X$found_dir" != "X"; then
              if test -f "$dir/$libname.la"; then
                found_la="$dir/$libname.la"
              fi
            fi
          fi
          if test "X$found_dir" = "X"; then
            for x in $LDFLAGS $LTLIB[]NAME; do
              AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
              case "$x" in
                -L*)
                  dir=`echo "X$x" | sed -e 's/^X-L//'`
                  dnl First look for a shared library.
                  if test -n "$acl_shlibext"; then
                    if test -f "$dir/$libname$shrext"; then
                      found_dir="$dir"
                      found_so="$dir/$libname$shrext"
                    else
                      if test "$acl_library_names_spec" = '$libname$shrext$versuffix'; then
                        ver=`(cd "$dir" && \
                              for f in "$libname$shrext".*; do echo "$f"; done \
                              | sed -e "s,^$libname$shrext\\\\.,," \
                              | sort -t '.' -n -r -k1,1 -k2,2 -k3,3 -k4,4 -k5,5 \
                              | sed 1q ) 2>/dev/null`
                        if test -n "$ver" && test -f "$dir/$libname$shrext.$ver"; then
                          found_dir="$dir"
                          found_so="$dir/$libname$shrext.$ver"
                        fi
                      else
                        eval library_names=\"$acl_library_names_spec\"
                        for f in $library_names; do
                          if test -f "$dir/$f"; then
                            found_dir="$dir"
                            found_so="$dir/$f"
                            break
                          fi
                        done
                      fi
                    fi
                  fi
                  dnl Then look for a static library.
                  if test "X$found_dir" = "X"; then
                    if test -f "$dir/$libname.$acl_libext"; then
                      found_dir="$dir"
                      found_a="$dir/$libname.$acl_libext"
                    fi
                  fi
                  if test "X$found_dir" != "X"; then
                    if test -f "$dir/$libname.la"; then
                      found_la="$dir/$libname.la"
                    fi
                  fi
                  ;;
              esac
              if test "X$found_dir" != "X"; then
                break
              fi
            done
          fi
          if test "X$found_dir" != "X"; then
            dnl Found the library.
            LTLIB[]NAME="${LTLIB[]NAME}${LTLIB[]NAME:+ }-L$found_dir -l$name"
            if test "X$found_so" != "X"; then
              dnl Linking with a shared library. We attempt to hardcode its
              dnl directory into the executable's runpath, unless it's the
              dnl standard /usr/lib.
              if test "$enable_rpath" = no \
                 || test "X$found_dir" = "X/usr/$acl_libdirstem" \
                 || test "X$found_dir" = "X/usr/$acl_libdirstem2"; then
                dnl No hardcoding is needed.
                LIB[]NAME="${LIB[]NAME}${LIB[]NAME:+ }$found_so"
              else
                dnl Use an explicit option to hardcode DIR into the resulting
                dnl binary.
                dnl Potentially add DIR to ltrpathdirs.
                dnl The ltrpathdirs will be appended to $LTLIBNAME at the end.
                haveit=
                for x in $ltrpathdirs; do
                  if test "X$x" = "X$found_dir"; then
                    haveit=yes
                    break
                  fi
                done
                if test -z "$haveit"; then
                  ltrpathdirs="$ltrpathdirs $found_dir"
                fi
                dnl The hardcoding into $LIBNAME is system dependent.
                if test "$acl_hardcode_direct" = yes; then
                  dnl Using DIR/libNAME.so during linking hardcodes DIR into the
                  dnl resulting binary.
                  LIB[]NAME="${LIB[]NAME}${LIB[]NAME:+ }$found_so"
                else
                  if test -n "$acl_hardcode_libdir_flag_spec" && test "$acl_hardcode_minus_L" = no; then
                    dnl Use an explicit option to hardcode DIR into the resulting
                    dnl binary.
                    LIB[]NAME="${LIB[]NAME}${LIB[]NAME:+ }$found_so"
                    dnl Potentially add DIR to rpathdirs.
                    dnl The rpathdirs will be appended to $LIBNAME at the end.
                    haveit=
                    for x in $rpathdirs; do
                      if test "X$x" = "X$found_dir"; then
                        haveit=yes
                        break
                      fi
                    done
                    if test -z "$haveit"; then
                      rpathdirs="$rpathdirs $found_dir"
                    fi
                  else
                    dnl Rely on "-L$found_dir".
                    dnl But don't add it if it's already contained in the LDFLAGS
                    dnl or the already constructed $LIBNAME
                    haveit=
                    for x in $LDFLAGS $LIB[]NAME; do
                      AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
                      if test "X$x" = "X-L$found_dir"; then
                        haveit=yes
                        break
                      fi
                    done
                    if test -z "$haveit"; then
                      LIB[]NAME="${LIB[]NAME}${LIB[]NAME:+ }-L$found_dir"
                    fi
                    if test "$acl_hardcode_minus_L" != no; then
                      dnl FIXME: Not sure whether we should use
                      dnl "-L$found_dir -l$name" or "-L$found_dir $found_so"
                      dnl here.
                      LIB[]NAME="${LIB[]NAME}${LIB[]NAME:+ }$found_so"
                    else
                      dnl We cannot use $acl_hardcode_runpath_var and LD_RUN_PATH
                      dnl here, because this doesn't fit in flags passed to the
                      dnl compiler. So give up. No hardcoding. This affects only
                      dnl very old systems.
                      dnl FIXME: Not sure whether we should use
                      dnl "-L$found_dir -l$name" or "-L$found_dir $found_so"
                      dnl here.
                      LIB[]NAME="${LIB[]NAME}${LIB[]NAME:+ }-l$name"
                    fi
                  fi
                fi
              fi
            else
              if test "X$found_a" != "X"; then
                dnl Linking with a static library.
                LIB[]NAME="${LIB[]NAME}${LIB[]NAME:+ }$found_a"
              else
                dnl We shouldn't come here, but anyway it's good to have a
                dnl fallback.
                LIB[]NAME="${LIB[]NAME}${LIB[]NAME:+ }-L$found_dir -l$name"
              fi
            fi
            dnl Assume the include files are nearby.
            additional_includedir=
            case "$found_dir" in
              */$acl_libdirstem | */$acl_libdirstem/)
                basedir=`echo "X$found_dir" | sed -e 's,^X,,' -e "s,/$acl_libdirstem/"'*$,,'`
                if test "$name" = '$1'; then
                  LIB[]NAME[]_PREFIX="$basedir"
                fi
                additional_includedir="$basedir/include"
                ;;
              */$acl_libdirstem2 | */$acl_libdirstem2/)
                basedir=`echo "X$found_dir" | sed -e 's,^X,,' -e "s,/$acl_libdirstem2/"'*$,,'`
                if test "$name" = '$1'; then
                  LIB[]NAME[]_PREFIX="$basedir"
                fi
                additional_includedir="$basedir/include"
                ;;
            esac
            if test "X$additional_includedir" != "X"; then
              dnl Potentially add $additional_includedir to $INCNAME.
              dnl But don't add it
              dnl   1. if it's the standard /usr/include,
              dnl   2. if it's /usr/local/include and we are using GCC on Linux,
              dnl   3. if it's already present in $CPPFLAGS or the already
              dnl      constructed $INCNAME,
              dnl   4. if it doesn't exist as a directory.
              if test "X$additional_includedir" != "X/usr/include"; then
                haveit=
                if test "X$additional_includedir" = "X/usr/local/include"; then
                  if test -n "$GCC"; then
                    case $host_os in
                      linux* | gnu* | k*bsd*-gnu) haveit=yes;;
                    esac
                  fi
                fi
                if test -z "$haveit"; then
                  for x in $CPPFLAGS $INC[]NAME; do
                    AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
                    if test "X$x" = "X-I$additional_includedir"; then
                      haveit=yes
                      break
                    fi
                  done
                  if test -z "$haveit"; then
                    if test -d "$additional_includedir"; then
                      dnl Really add $additional_includedir to $INCNAME.
                      INC[]NAME="${INC[]NAME}${INC[]NAME:+ }-I$additional_includedir"
                    fi
                  fi
                fi
              fi
            fi
            dnl Look for dependencies.
            if test -n "$found_la"; then
              dnl Read the .la file. It defines the variables
              dnl dlname, library_names, old_library, dependency_libs, current,
              dnl age, revision, installed, dlopen, dlpreopen, libdir.
              save_libdir="$libdir"
              case "$found_la" in
                */* | *\\*) . "$found_la" ;;
                *) . "./$found_la" ;;
              esac
              libdir="$save_libdir"
              dnl We use only dependency_libs.
              for dep in $dependency_libs; do
                case "$dep" in
                  -L*)
                    additional_libdir=`echo "X$dep" | sed -e 's/^X-L//'`
                    dnl Potentially add $additional_libdir to $LIBNAME and $LTLIBNAME.
                    dnl But don't add it
                    dnl   1. if it's the standard /usr/lib,
                    dnl   2. if it's /usr/local/lib and we are using GCC on Linux,
                    dnl   3. if it's already present in $LDFLAGS or the already
                    dnl      constructed $LIBNAME,
                    dnl   4. if it doesn't exist as a directory.
                    if test "X$additional_libdir" != "X/usr/$acl_libdirstem" \
                       && test "X$additional_libdir" != "X/usr/$acl_libdirstem2"; then
                      haveit=
                      if test "X$additional_libdir" = "X/usr/local/$acl_libdirstem" \
                         || test "X$additional_libdir" = "X/usr/local/$acl_libdirstem2"; then
                        if test -n "$GCC"; then
                          case $host_os in
                            linux* | gnu* | k*bsd*-gnu) haveit=yes;;
                          esac
                        fi
                      fi
                      if test -z "$haveit"; then
                        haveit=
                        for x in $LDFLAGS $LIB[]NAME; do
                          AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
                          if test "X$x" = "X-L$additional_libdir"; then
                            haveit=yes
                            break
                          fi
                        done
                        if test -z "$haveit"; then
                          if test -d "$additional_libdir"; then
                            dnl Really add $additional_libdir to $LIBNAME.
                            LIB[]NAME="${LIB[]NAME}${LIB[]NAME:+ }-L$additional_libdir"
                          fi
                        fi
                        haveit=
                        for x in $LDFLAGS $LTLIB[]NAME; do
                          AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
                          if test "X$x" = "X-L$additional_libdir"; then
                            haveit=yes
                            break
                          fi
                        done
                        if test -z "$haveit"; then
                          if test -d "$additional_libdir"; then
                            dnl Really add $additional_libdir to $LTLIBNAME.
                            LTLIB[]NAME="${LTLIB[]NAME}${LTLIB[]NAME:+ }-L$additional_libdir"
                          fi
                        fi
                      fi
                    fi
                    ;;
                  -R*)
                    dir=`echo "X$dep" | sed -e 's/^X-R//'`
                    if test "$enable_rpath" != no; then
                      dnl Potentially add DIR to rpathdirs.
                      dnl The rpathdirs will be appended to $LIBNAME at the end.
                      haveit=
                      for x in $rpathdirs; do
                        if test "X$x" = "X$dir"; then
                          haveit=yes
                          break
                        fi
                      done
                      if test -z "$haveit"; then
                        rpathdirs="$rpathdirs $dir"
                      fi
                      dnl Potentially add DIR to ltrpathdirs.
                      dnl The ltrpathdirs will be appended to $LTLIBNAME at the end.
                      haveit=
                      for x in $ltrpathdirs; do
                        if test "X$x" = "X$dir"; then
                          haveit=yes
                          break
                        fi
                      done
                      if test -z "$haveit"; then
                        ltrpathdirs="$ltrpathdirs $dir"
                      fi
                    fi
                    ;;
                  -l*)
                    dnl Handle this in the next round.
                    names_next_round="$names_next_round "`echo "X$dep" | sed -e 's/^X-l//'`
                    ;;
                  *.la)
                    dnl Handle this in the next round. Throw away the .la's
                    dnl directory; it is already contained in a preceding -L
                    dnl option.
                    names_next_round="$names_next_round "`echo "X$dep" | sed -e 's,^X.*/,,' -e 's,^lib,,' -e 's,\.la$,,'`
                    ;;
                  *)
                    dnl Most likely an immediate library name.
                    LIB[]NAME="${LIB[]NAME}${LIB[]NAME:+ }$dep"
                    LTLIB[]NAME="${LTLIB[]NAME}${LTLIB[]NAME:+ }$dep"
                    ;;
                esac
              done
            fi
          else
            dnl Didn't find the library; assume it is in the system directories
            dnl known to the linker and runtime loader. (All the system
            dnl directories known to the linker should also be known to the
            dnl runtime loader, otherwise the system is severely misconfigured.)
            LIB[]NAME="${LIB[]NAME}${LIB[]NAME:+ }-l$name"
            LTLIB[]NAME="${LTLIB[]NAME}${LTLIB[]NAME:+ }-l$name"
          fi
        fi
      fi
    done
  done
  if test "X$rpathdirs" != "X"; then
    if test -n "$acl_hardcode_libdir_separator"; then
      dnl Weird platform: only the last -rpath option counts, the user must
      dnl pass all path elements in one option. We can arrange that for a
      dnl single library, but not when more than one $LIBNAMEs are used.
      alldirs=
      for found_dir in $rpathdirs; do
        alldirs="${alldirs}${alldirs:+$acl_hardcode_libdir_separator}$found_dir"
      done
      dnl Note: acl_hardcode_libdir_flag_spec uses $libdir and $wl.
      acl_save_libdir="$libdir"
      libdir="$alldirs"
      eval flag=\"$acl_hardcode_libdir_flag_spec\"
      libdir="$acl_save_libdir"
      LIB[]NAME="${LIB[]NAME}${LIB[]NAME:+ }$flag"
    else
      dnl The -rpath options are cumulative.
      for found_dir in $rpathdirs; do
        acl_save_libdir="$libdir"
        libdir="$found_dir"
        eval flag=\"$acl_hardcode_libdir_flag_spec\"
        libdir="$acl_save_libdir"
        LIB[]NAME="${LIB[]NAME}${LIB[]NAME:+ }$flag"
      done
    fi
  fi
  if test "X$ltrpathdirs" != "X"; then
    dnl When using libtool, the option that works for both libraries and
    dnl executables is -R. The -R options are cumulative.
    for found_dir in $ltrpathdirs; do
      LTLIB[]NAME="${LTLIB[]NAME}${LTLIB[]NAME:+ }-R$found_dir"
    done
  fi
  popdef([P_A_C_K])
  popdef([PACKLIBS])
  popdef([PACKUP])
  popdef([PACK])
  popdef([NAME])
])
m4trace:m4/lib-link.m4:684: -1- AC_DEFUN([AC_LIB_APPENDTOVAR], [
  for element in [$2]; do
    haveit=
    for x in $[$1]; do
      AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
      if test "X$x" = "X$element"; then
        haveit=yes
        break
      fi
    done
    if test -z "$haveit"; then
      [$1]="${[$1]}${[$1]:+ }$element"
    fi
  done
])
m4trace:m4/lib-link.m4:708: -1- AC_DEFUN([AC_LIB_LINKFLAGS_FROM_LIBS], [
  AC_REQUIRE([AC_LIB_RPATH])
  AC_REQUIRE([AC_LIB_PREPARE_MULTILIB])
  $1=
  if test "$enable_rpath" != no; then
    if test -n "$acl_hardcode_libdir_flag_spec" && test "$acl_hardcode_minus_L" = no; then
      dnl Use an explicit option to hardcode directories into the resulting
      dnl binary.
      rpathdirs=
      next=
      for opt in $2; do
        if test -n "$next"; then
          dir="$next"
          dnl No need to hardcode the standard /usr/lib.
          if test "X$dir" != "X/usr/$acl_libdirstem" \
             && test "X$dir" != "X/usr/$acl_libdirstem2"; then
            rpathdirs="$rpathdirs $dir"
          fi
          next=
        else
          case $opt in
            -L) next=yes ;;
            -L*) dir=`echo "X$opt" | sed -e 's,^X-L,,'`
                 dnl No need to hardcode the standard /usr/lib.
                 if test "X$dir" != "X/usr/$acl_libdirstem" \
                    && test "X$dir" != "X/usr/$acl_libdirstem2"; then
                   rpathdirs="$rpathdirs $dir"
                 fi
                 next= ;;
            *) next= ;;
          esac
        fi
      done
      if test "X$rpathdirs" != "X"; then
        if test -n ""$3""; then
          dnl libtool is used for linking. Use -R options.
          for dir in $rpathdirs; do
            $1="${$1}${$1:+ }-R$dir"
          done
        else
          dnl The linker is used for linking directly.
          if test -n "$acl_hardcode_libdir_separator"; then
            dnl Weird platform: only the last -rpath option counts, the user
            dnl must pass all path elements in one option.
            alldirs=
            for dir in $rpathdirs; do
              alldirs="${alldirs}${alldirs:+$acl_hardcode_libdir_separator}$dir"
            done
            acl_save_libdir="$libdir"
            libdir="$alldirs"
            eval flag=\"$acl_hardcode_libdir_flag_spec\"
            libdir="$acl_save_libdir"
            $1="$flag"
          else
            dnl The -rpath options are cumulative.
            for dir in $rpathdirs; do
              acl_save_libdir="$libdir"
              libdir="$dir"
              eval flag=\"$acl_hardcode_libdir_flag_spec\"
              libdir="$acl_save_libdir"
              $1="${$1}${$1:+ }$flag"
            done
          fi
        fi
      fi
    fi
  fi
  AC_SUBST([$1])
])
m4trace:m4/lib-prefix.m4:12: -1- AC_DEFUN([AC_LIB_ARG_WITH], [AC_ARG_WITH([$1],[[$2]],[$3],[$4])])
m4trace:m4/lib-prefix.m4:22: -1- AC_DEFUN([AC_LIB_PREFIX], [
  AC_BEFORE([$0], [AC_LIB_LINKFLAGS])
  AC_REQUIRE([AC_PROG_CC])
  AC_REQUIRE([AC_CANONICAL_HOST])
  AC_REQUIRE([AC_LIB_PREPARE_MULTILIB])
  AC_REQUIRE([AC_LIB_PREPARE_PREFIX])
  dnl By default, look in $includedir and $libdir.
  use_additional=yes
  AC_LIB_WITH_FINAL_PREFIX([
    eval additional_includedir=\"$includedir\"
    eval additional_libdir=\"$libdir\"
  ])
  AC_LIB_ARG_WITH([lib-prefix],
[  --with-lib-prefix[=DIR] search for libraries in DIR/include and DIR/lib
  --without-lib-prefix    don't search for libraries in includedir and libdir],
[
    if test "X$withval" = "Xno"; then
      use_additional=no
    else
      if test "X$withval" = "X"; then
        AC_LIB_WITH_FINAL_PREFIX([
          eval additional_includedir=\"$includedir\"
          eval additional_libdir=\"$libdir\"
        ])
      else
        additional_includedir="$withval/include"
        additional_libdir="$withval/$acl_libdirstem"
      fi
    fi
])
  if test $use_additional = yes; then
    dnl Potentially add $additional_includedir to $CPPFLAGS.
    dnl But don't add it
    dnl   1. if it's the standard /usr/include,
    dnl   2. if it's already present in $CPPFLAGS,
    dnl   3. if it's /usr/local/include and we are using GCC on Linux,
    dnl   4. if it doesn't exist as a directory.
    if test "X$additional_includedir" != "X/usr/include"; then
      haveit=
      for x in $CPPFLAGS; do
        AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
        if test "X$x" = "X-I$additional_includedir"; then
          haveit=yes
          break
        fi
      done
      if test -z "$haveit"; then
        if test "X$additional_includedir" = "X/usr/local/include"; then
          if test -n "$GCC"; then
            case $host_os in
              linux* | gnu* | k*bsd*-gnu) haveit=yes;;
            esac
          fi
        fi
        if test -z "$haveit"; then
          if test -d "$additional_includedir"; then
            dnl Really add $additional_includedir to $CPPFLAGS.
            CPPFLAGS="${CPPFLAGS}${CPPFLAGS:+ }-I$additional_includedir"
          fi
        fi
      fi
    fi
    dnl Potentially add $additional_libdir to $LDFLAGS.
    dnl But don't add it
    dnl   1. if it's the standard /usr/lib,
    dnl   2. if it's already present in $LDFLAGS,
    dnl   3. if it's /usr/local/lib and we are using GCC on Linux,
    dnl   4. if it doesn't exist as a directory.
    if test "X$additional_libdir" != "X/usr/$acl_libdirstem"; then
      haveit=
      for x in $LDFLAGS; do
        AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
        if test "X$x" = "X-L$additional_libdir"; then
          haveit=yes
          break
        fi
      done
      if test -z "$haveit"; then
        if test "X$additional_libdir" = "X/usr/local/$acl_libdirstem"; then
          if test -n "$GCC"; then
            case $host_os in
              linux*) haveit=yes;;
            esac
          fi
        fi
        if test -z "$haveit"; then
          if test -d "$additional_libdir"; then
            dnl Really add $additional_libdir to $LDFLAGS.
            LDFLAGS="${LDFLAGS}${LDFLAGS:+ }-L$additional_libdir"
          fi
        fi
      fi
    fi
  fi
])
m4trace:m4/lib-prefix.m4:122: -1- AC_DEFUN([AC_LIB_PREPARE_PREFIX], [
  dnl Unfortunately, prefix and exec_prefix get only finally determined
  dnl at the end of configure.
  if test "X$prefix" = "XNONE"; then
    acl_final_prefix="$ac_default_prefix"
  else
    acl_final_prefix="$prefix"
  fi
  if test "X$exec_prefix" = "XNONE"; then
    acl_final_exec_prefix='${prefix}'
  else
    acl_final_exec_prefix="$exec_prefix"
  fi
  acl_save_prefix="$prefix"
  prefix="$acl_final_prefix"
  eval acl_final_exec_prefix=\"$acl_final_exec_prefix\"
  prefix="$acl_save_prefix"
])
m4trace:m4/lib-prefix.m4:145: -1- AC_DEFUN([AC_LIB_WITH_FINAL_PREFIX], [
  acl_save_prefix="$prefix"
  prefix="$acl_final_prefix"
  acl_save_exec_prefix="$exec_prefix"
  exec_prefix="$acl_final_exec_prefix"
  $1
  exec_prefix="$acl_save_exec_prefix"
  prefix="$acl_save_prefix"
])
m4trace:m4/lib-prefix.m4:162: -1- AC_DEFUN([AC_LIB_PREPARE_MULTILIB], [
  dnl There is no formal standard regarding lib and lib64.
  dnl On glibc systems, the current practice is that on a system supporting
  dnl 32-bit and 64-bit instruction sets or ABIs, 64-bit libraries go under
  dnl $prefix/lib64 and 32-bit libraries go under $prefix/lib. We determine
  dnl the compiler's default mode by looking at the compiler's library search
  dnl path. If at least one of its elements ends in /lib64 or points to a
  dnl directory whose absolute pathname ends in /lib64, we assume a 64-bit ABI.
  dnl Otherwise we use the default, namely "lib".
  dnl On Solaris systems, the current practice is that on a system supporting
  dnl 32-bit and 64-bit instruction sets or ABIs, 64-bit libraries go under
  dnl $prefix/lib/64 (which is a symlink to either $prefix/lib/sparcv9 or
  dnl $prefix/lib/amd64) and 32-bit libraries go under $prefix/lib.
  AC_REQUIRE([AC_CANONICAL_HOST])
  acl_libdirstem=lib
  acl_libdirstem2=
  case "$host_os" in
    solaris*)
      dnl See Solaris 10 Software Developer Collection > Solaris 64-bit Developer's Guide > The Development Environment
      dnl <http://docs.sun.com/app/docs/doc/816-5138/dev-env?l=en&a=view>.
      dnl "Portable Makefiles should refer to any library directories using the 64 symbolic link."
      dnl But we want to recognize the sparcv9 or amd64 subdirectory also if the
      dnl symlink is missing, so we set acl_libdirstem2 too.
      AC_CACHE_CHECK([for 64-bit host], [gl_cv_solaris_64bit],
        [AC_EGREP_CPP([sixtyfour bits], [
#ifdef _LP64
sixtyfour bits
#endif
           ], [gl_cv_solaris_64bit=yes], [gl_cv_solaris_64bit=no])
        ])
      if test $gl_cv_solaris_64bit = yes; then
        acl_libdirstem=lib/64
        case "$host_cpu" in
          sparc*)        acl_libdirstem2=lib/sparcv9 ;;
          i*86 | x86_64) acl_libdirstem2=lib/amd64 ;;
        esac
      fi
      ;;
    *)
      searchpath=`(LC_ALL=C $CC -print-search-dirs) 2>/dev/null | sed -n -e 's,^libraries: ,,p' | sed -e 's,^=,,'`
      if test -n "$searchpath"; then
        acl_save_IFS="${IFS= 	}"; IFS=":"
        for searchdir in $searchpath; do
          if test -d "$searchdir"; then
            case "$searchdir" in
              */lib64/ | */lib64 ) acl_libdirstem=lib64 ;;
              */../ | */.. )
                # Better ignore directories of this form. They are misleading.
                ;;
              *) searchdir=`cd "$searchdir" && pwd`
                 case "$searchdir" in
                   */lib64 ) acl_libdirstem=lib64 ;;
                 esac ;;
            esac
          fi
        done
        IFS="$acl_save_IFS"
      fi
      ;;
  esac
  test -n "$acl_libdirstem2" || acl_libdirstem2="$acl_libdirstem"
])
m4trace:m4/libtool.m4:61: -1- AC_DEFUN([LT_INIT], [AC_PREREQ([2.62])dnl We use AC_PATH_PROGS_FEATURE_CHECK
AC_REQUIRE([AC_CONFIG_AUX_DIR_DEFAULT])dnl
AC_BEFORE([$0], [LT_LANG])dnl
AC_BEFORE([$0], [LT_OUTPUT])dnl
AC_BEFORE([$0], [LTDL_INIT])dnl
m4_require([_LT_CHECK_BUILDDIR])dnl

dnl Autoconf doesn't catch unexpanded LT_ macros by default:
m4_pattern_forbid([^_?LT_[A-Z_]+$])dnl
m4_pattern_allow([^(_LT_EOF|LT_DLGLOBAL|LT_DLLAZY_OR_NOW|LT_MULTI_MODULE)$])dnl
dnl aclocal doesn't pull ltoptions.m4, ltsugar.m4, or ltversion.m4
dnl unless we require an AC_DEFUNed macro:
AC_REQUIRE([LTOPTIONS_VERSION])dnl
AC_REQUIRE([LTSUGAR_VERSION])dnl
AC_REQUIRE([LTVERSION_VERSION])dnl
AC_REQUIRE([LTOBSOLETE_VERSION])dnl
m4_require([_LT_PROG_LTMAIN])dnl

_LT_SHELL_INIT([SHELL=${CONFIG_SHELL-/bin/sh}])

dnl Parse OPTIONS
_LT_SET_OPTIONS([$0], [$1])

# This can be used to rebuild libtool when needed
LIBTOOL_DEPS=$ltmain

# Always use our own libtool.
LIBTOOL='$(SHELL) $(top_builddir)/libtool'
AC_SUBST(LIBTOOL)dnl

_LT_SETUP

# Only expand once:
m4_define([LT_INIT])
])
m4trace:m4/libtool.m4:99: -1- AU_DEFUN([AC_PROG_LIBTOOL], [m4_if($#, 0, [LT_INIT], [LT_INIT($@)])])
m4trace:m4/libtool.m4:99: -1- AC_DEFUN([AC_PROG_LIBTOOL], [AC_DIAGNOSE([obsolete], [The macro `AC_PROG_LIBTOOL' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_INIT], [LT_INIT($@)])])
m4trace:m4/libtool.m4:100: -1- AU_DEFUN([AM_PROG_LIBTOOL], [m4_if($#, 0, [LT_INIT], [LT_INIT($@)])])
m4trace:m4/libtool.m4:100: -1- AC_DEFUN([AM_PROG_LIBTOOL], [AC_DIAGNOSE([obsolete], [The macro `AM_PROG_LIBTOOL' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_INIT], [LT_INIT($@)])])
m4trace:m4/libtool.m4:619: -1- AC_DEFUN([LT_OUTPUT], [: ${CONFIG_LT=./config.lt}
AC_MSG_NOTICE([creating $CONFIG_LT])
_LT_GENERATED_FILE_INIT(["$CONFIG_LT"],
[# Run this file to recreate a libtool stub with the current configuration.])

cat >>"$CONFIG_LT" <<\_LTEOF
lt_cl_silent=false
exec AS_MESSAGE_LOG_FD>>config.log
{
  echo
  AS_BOX([Running $as_me.])
} >&AS_MESSAGE_LOG_FD

lt_cl_help="\
'$as_me' creates a local libtool stub from the current configuration,
for use in further configure time tests before the real libtool is
generated.

Usage: $[0] [[OPTIONS]]

  -h, --help      print this help, then exit
  -V, --version   print version number, then exit
  -q, --quiet     do not print progress messages
  -d, --debug     don't remove temporary files

Report bugs to <<EMAIL>>."

lt_cl_version="\
m4_ifset([AC_PACKAGE_NAME], [AC_PACKAGE_NAME ])config.lt[]dnl
m4_ifset([AC_PACKAGE_VERSION], [ AC_PACKAGE_VERSION])
configured by $[0], generated by m4_PACKAGE_STRING.

Copyright (C) 2011 Free Software Foundation, Inc.
This config.lt script is free software; the Free Software Foundation
gives unlimited permision to copy, distribute and modify it."

while test 0 != $[#]
do
  case $[1] in
    --version | --v* | -V )
      echo "$lt_cl_version"; exit 0 ;;
    --help | --h* | -h )
      echo "$lt_cl_help"; exit 0 ;;
    --debug | --d* | -d )
      debug=: ;;
    --quiet | --q* | --silent | --s* | -q )
      lt_cl_silent=: ;;

    -*) AC_MSG_ERROR([unrecognized option: $[1]
Try '$[0] --help' for more information.]) ;;

    *) AC_MSG_ERROR([unrecognized argument: $[1]
Try '$[0] --help' for more information.]) ;;
  esac
  shift
done

if $lt_cl_silent; then
  exec AS_MESSAGE_FD>/dev/null
fi
_LTEOF

cat >>"$CONFIG_LT" <<_LTEOF
_LT_OUTPUT_LIBTOOL_COMMANDS_INIT
_LTEOF

cat >>"$CONFIG_LT" <<\_LTEOF
AC_MSG_NOTICE([creating $ofile])
_LT_OUTPUT_LIBTOOL_COMMANDS
AS_EXIT(0)
_LTEOF
chmod +x "$CONFIG_LT"

# configure is writing to config.log, but config.lt does its own redirection,
# appending to config.log, which fails on DOS, as config.log is still kept
# open by configure.  Here we exec the FD to /dev/null, effectively closing
# config.log, so it can be properly (re)opened and appended to by config.lt.
lt_cl_success=:
test yes = "$silent" &&
  lt_config_lt_args="$lt_config_lt_args --quiet"
exec AS_MESSAGE_LOG_FD>/dev/null
$SHELL "$CONFIG_LT" $lt_config_lt_args || lt_cl_success=false
exec AS_MESSAGE_LOG_FD>>config.log
$lt_cl_success || AS_EXIT(1)
])
m4trace:m4/libtool.m4:811: -1- AC_DEFUN([LT_SUPPORTED_TAG], [])
m4trace:m4/libtool.m4:822: -1- AC_DEFUN([LT_LANG], [AC_BEFORE([$0], [LT_OUTPUT])dnl
m4_case([$1],
  [C],			[_LT_LANG(C)],
  [C++],		[_LT_LANG(CXX)],
  [Go],			[_LT_LANG(GO)],
  [Java],		[_LT_LANG(GCJ)],
  [Fortran 77],		[_LT_LANG(F77)],
  [Fortran],		[_LT_LANG(FC)],
  [Windows Resource],	[_LT_LANG(RC)],
  [m4_ifdef([_LT_LANG_]$1[_CONFIG],
    [_LT_LANG($1)],
    [m4_fatal([$0: unsupported language: "$1"])])])dnl
])
m4trace:m4/libtool.m4:914: -1- AU_DEFUN([AC_LIBTOOL_CXX], [LT_LANG(C++)])
m4trace:m4/libtool.m4:914: -1- AC_DEFUN([AC_LIBTOOL_CXX], [AC_DIAGNOSE([obsolete], [The macro `AC_LIBTOOL_CXX' is obsolete.
You should run autoupdate.])dnl
LT_LANG(C++)])
m4trace:m4/libtool.m4:915: -1- AU_DEFUN([AC_LIBTOOL_F77], [LT_LANG(Fortran 77)])
m4trace:m4/libtool.m4:915: -1- AC_DEFUN([AC_LIBTOOL_F77], [AC_DIAGNOSE([obsolete], [The macro `AC_LIBTOOL_F77' is obsolete.
You should run autoupdate.])dnl
LT_LANG(Fortran 77)])
m4trace:m4/libtool.m4:916: -1- AU_DEFUN([AC_LIBTOOL_FC], [LT_LANG(Fortran)])
m4trace:m4/libtool.m4:916: -1- AC_DEFUN([AC_LIBTOOL_FC], [AC_DIAGNOSE([obsolete], [The macro `AC_LIBTOOL_FC' is obsolete.
You should run autoupdate.])dnl
LT_LANG(Fortran)])
m4trace:m4/libtool.m4:917: -1- AU_DEFUN([AC_LIBTOOL_GCJ], [LT_LANG(Java)])
m4trace:m4/libtool.m4:917: -1- AC_DEFUN([AC_LIBTOOL_GCJ], [AC_DIAGNOSE([obsolete], [The macro `AC_LIBTOOL_GCJ' is obsolete.
You should run autoupdate.])dnl
LT_LANG(Java)])
m4trace:m4/libtool.m4:918: -1- AU_DEFUN([AC_LIBTOOL_RC], [LT_LANG(Windows Resource)])
m4trace:m4/libtool.m4:918: -1- AC_DEFUN([AC_LIBTOOL_RC], [AC_DIAGNOSE([obsolete], [The macro `AC_LIBTOOL_RC' is obsolete.
You should run autoupdate.])dnl
LT_LANG(Windows Resource)])
m4trace:m4/libtool.m4:1246: -1- AC_DEFUN([_LT_WITH_SYSROOT], [AC_MSG_CHECKING([for sysroot])
AC_ARG_WITH([sysroot],
[AS_HELP_STRING([--with-sysroot@<:@=DIR@:>@],
  [Search for dependent libraries within DIR (or the compiler's sysroot
   if not specified).])],
[], [with_sysroot=no])

dnl lt_sysroot will always be passed unquoted.  We quote it here
dnl in case the user passed a directory name.
lt_sysroot=
case $with_sysroot in #(
 yes)
   if test yes = "$GCC"; then
     lt_sysroot=`$CC --print-sysroot 2>/dev/null`
   fi
   ;; #(
 /*)
   lt_sysroot=`echo "$with_sysroot" | sed -e "$sed_quote_subst"`
   ;; #(
 no|'')
   ;; #(
 *)
   AC_MSG_RESULT([$with_sysroot])
   AC_MSG_ERROR([The sysroot must be an absolute path.])
   ;;
esac

 AC_MSG_RESULT([${lt_sysroot:-no}])
_LT_DECL([], [lt_sysroot], [0], [The root where to search for ]dnl
[dependent libraries, and where our libraries should be installed.])])
m4trace:m4/libtool.m4:1577: -1- AC_DEFUN([_LT_COMPILER_OPTION], [m4_require([_LT_FILEUTILS_DEFAULTS])dnl
m4_require([_LT_DECL_SED])dnl
AC_CACHE_CHECK([$1], [$2],
  [$2=no
   m4_if([$4], , [ac_outfile=conftest.$ac_objext], [ac_outfile=$4])
   echo "$lt_simple_compile_test_code" > conftest.$ac_ext
   lt_compiler_flag="$3"  ## exclude from sc_useless_quotes_in_assignment
   # Insert the option either (1) after the last *FLAGS variable, or
   # (2) before a word containing "conftest.", or (3) at the end.
   # Note that $ac_compile itself does not contain backslashes and begins
   # with a dollar sign (not a hyphen), so the echo should work correctly.
   # The option is referenced via a variable to avoid confusing sed.
   lt_compile=`echo "$ac_compile" | $SED \
   -e 's:.*FLAGS}\{0,1\} :&$lt_compiler_flag :; t' \
   -e 's: [[^ ]]*conftest\.: $lt_compiler_flag&:; t' \
   -e 's:$: $lt_compiler_flag:'`
   (eval echo "\"\$as_me:$LINENO: $lt_compile\"" >&AS_MESSAGE_LOG_FD)
   (eval "$lt_compile" 2>conftest.err)
   ac_status=$?
   cat conftest.err >&AS_MESSAGE_LOG_FD
   echo "$as_me:$LINENO: \$? = $ac_status" >&AS_MESSAGE_LOG_FD
   if (exit $ac_status) && test -s "$ac_outfile"; then
     # The compiler can only warn and ignore the option if not recognized
     # So say no if there are warnings other than the usual output.
     $ECHO "$_lt_compiler_boilerplate" | $SED '/^$/d' >conftest.exp
     $SED '/^$/d; /^ *+/d' conftest.err >conftest.er2
     if test ! -s conftest.er2 || diff conftest.exp conftest.er2 >/dev/null; then
       $2=yes
     fi
   fi
   $RM conftest*
])

if test yes = "[$]$2"; then
    m4_if([$5], , :, [$5])
else
    m4_if([$6], , :, [$6])
fi
])
m4trace:m4/libtool.m4:1619: -1- AU_DEFUN([AC_LIBTOOL_COMPILER_OPTION], [m4_if($#, 0, [_LT_COMPILER_OPTION], [_LT_COMPILER_OPTION($@)])])
m4trace:m4/libtool.m4:1619: -1- AC_DEFUN([AC_LIBTOOL_COMPILER_OPTION], [AC_DIAGNOSE([obsolete], [The macro `AC_LIBTOOL_COMPILER_OPTION' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [_LT_COMPILER_OPTION], [_LT_COMPILER_OPTION($@)])])
m4trace:m4/libtool.m4:1628: -1- AC_DEFUN([_LT_LINKER_OPTION], [m4_require([_LT_FILEUTILS_DEFAULTS])dnl
m4_require([_LT_DECL_SED])dnl
AC_CACHE_CHECK([$1], [$2],
  [$2=no
   save_LDFLAGS=$LDFLAGS
   LDFLAGS="$LDFLAGS $3"
   echo "$lt_simple_link_test_code" > conftest.$ac_ext
   if (eval $ac_link 2>conftest.err) && test -s conftest$ac_exeext; then
     # The linker can only warn and ignore the option if not recognized
     # So say no if there are warnings
     if test -s conftest.err; then
       # Append any errors to the config.log.
       cat conftest.err 1>&AS_MESSAGE_LOG_FD
       $ECHO "$_lt_linker_boilerplate" | $SED '/^$/d' > conftest.exp
       $SED '/^$/d; /^ *+/d' conftest.err >conftest.er2
       if diff conftest.exp conftest.er2 >/dev/null; then
         $2=yes
       fi
     else
       $2=yes
     fi
   fi
   $RM -r conftest*
   LDFLAGS=$save_LDFLAGS
])

if test yes = "[$]$2"; then
    m4_if([$4], , :, [$4])
else
    m4_if([$5], , :, [$5])
fi
])
m4trace:m4/libtool.m4:1663: -1- AU_DEFUN([AC_LIBTOOL_LINKER_OPTION], [m4_if($#, 0, [_LT_LINKER_OPTION], [_LT_LINKER_OPTION($@)])])
m4trace:m4/libtool.m4:1663: -1- AC_DEFUN([AC_LIBTOOL_LINKER_OPTION], [AC_DIAGNOSE([obsolete], [The macro `AC_LIBTOOL_LINKER_OPTION' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [_LT_LINKER_OPTION], [_LT_LINKER_OPTION($@)])])
m4trace:m4/libtool.m4:1670: -1- AC_DEFUN([LT_CMD_MAX_LEN], [AC_REQUIRE([AC_CANONICAL_HOST])dnl
# find the maximum length of command line arguments
AC_MSG_CHECKING([the maximum length of command line arguments])
AC_CACHE_VAL([lt_cv_sys_max_cmd_len], [dnl
  i=0
  teststring=ABCD

  case $build_os in
  msdosdjgpp*)
    # On DJGPP, this test can blow up pretty badly due to problems in libc
    # (any single argument exceeding 2000 bytes causes a buffer overrun
    # during glob expansion).  Even if it were fixed, the result of this
    # check would be larger than it should be.
    lt_cv_sys_max_cmd_len=12288;    # 12K is about right
    ;;

  gnu*)
    # Under GNU Hurd, this test is not required because there is
    # no limit to the length of command line arguments.
    # Libtool will interpret -1 as no limit whatsoever
    lt_cv_sys_max_cmd_len=-1;
    ;;

  cygwin* | mingw* | cegcc*)
    # On Win9x/ME, this test blows up -- it succeeds, but takes
    # about 5 minutes as the teststring grows exponentially.
    # Worse, since 9x/ME are not pre-emptively multitasking,
    # you end up with a "frozen" computer, even though with patience
    # the test eventually succeeds (with a max line length of 256k).
    # Instead, let's just punt: use the minimum linelength reported by
    # all of the supported platforms: 8192 (on NT/2K/XP).
    lt_cv_sys_max_cmd_len=8192;
    ;;

  mint*)
    # On MiNT this can take a long time and run out of memory.
    lt_cv_sys_max_cmd_len=8192;
    ;;

  amigaos*)
    # On AmigaOS with pdksh, this test takes hours, literally.
    # So we just punt and use a minimum line length of 8192.
    lt_cv_sys_max_cmd_len=8192;
    ;;

  bitrig* | darwin* | dragonfly* | freebsd* | netbsd* | openbsd*)
    # This has been around since 386BSD, at least.  Likely further.
    if test -x /sbin/sysctl; then
      lt_cv_sys_max_cmd_len=`/sbin/sysctl -n kern.argmax`
    elif test -x /usr/sbin/sysctl; then
      lt_cv_sys_max_cmd_len=`/usr/sbin/sysctl -n kern.argmax`
    else
      lt_cv_sys_max_cmd_len=65536	# usable default for all BSDs
    fi
    # And add a safety zone
    lt_cv_sys_max_cmd_len=`expr $lt_cv_sys_max_cmd_len \/ 4`
    lt_cv_sys_max_cmd_len=`expr $lt_cv_sys_max_cmd_len \* 3`
    ;;

  interix*)
    # We know the value 262144 and hardcode it with a safety zone (like BSD)
    lt_cv_sys_max_cmd_len=196608
    ;;

  os2*)
    # The test takes a long time on OS/2.
    lt_cv_sys_max_cmd_len=8192
    ;;

  osf*)
    # Dr. Hans Ekkehard Plesser reports seeing a kernel panic running configure
    # due to this test when exec_disable_arg_limit is 1 on Tru64. It is not
    # nice to cause kernel panics so lets avoid the loop below.
    # First set a reasonable default.
    lt_cv_sys_max_cmd_len=16384
    #
    if test -x /sbin/sysconfig; then
      case `/sbin/sysconfig -q proc exec_disable_arg_limit` in
        *1*) lt_cv_sys_max_cmd_len=-1 ;;
      esac
    fi
    ;;
  sco3.2v5*)
    lt_cv_sys_max_cmd_len=102400
    ;;
  sysv5* | sco5v6* | sysv4.2uw2*)
    kargmax=`grep ARG_MAX /etc/conf/cf.d/stune 2>/dev/null`
    if test -n "$kargmax"; then
      lt_cv_sys_max_cmd_len=`echo $kargmax | sed 's/.*[[	 ]]//'`
    else
      lt_cv_sys_max_cmd_len=32768
    fi
    ;;
  *)
    lt_cv_sys_max_cmd_len=`(getconf ARG_MAX) 2> /dev/null`
    if test -n "$lt_cv_sys_max_cmd_len" && \
       test undefined != "$lt_cv_sys_max_cmd_len"; then
      lt_cv_sys_max_cmd_len=`expr $lt_cv_sys_max_cmd_len \/ 4`
      lt_cv_sys_max_cmd_len=`expr $lt_cv_sys_max_cmd_len \* 3`
    else
      # Make teststring a little bigger before we do anything with it.
      # a 1K string should be a reasonable start.
      for i in 1 2 3 4 5 6 7 8; do
        teststring=$teststring$teststring
      done
      SHELL=${SHELL-${CONFIG_SHELL-/bin/sh}}
      # If test is not a shell built-in, we'll probably end up computing a
      # maximum length that is only half of the actual maximum length, but
      # we can't tell.
      while { test X`env echo "$teststring$teststring" 2>/dev/null` \
	         = "X$teststring$teststring"; } >/dev/null 2>&1 &&
	      test 17 != "$i" # 1/2 MB should be enough
      do
        i=`expr $i + 1`
        teststring=$teststring$teststring
      done
      # Only check the string length outside the loop.
      lt_cv_sys_max_cmd_len=`expr "X$teststring" : ".*" 2>&1`
      teststring=
      # Add a significant safety factor because C++ compilers can tack on
      # massive amounts of additional arguments before passing them to the
      # linker.  It appears as though 1/2 is a usable value.
      lt_cv_sys_max_cmd_len=`expr $lt_cv_sys_max_cmd_len \/ 2`
    fi
    ;;
  esac
])
if test -n "$lt_cv_sys_max_cmd_len"; then
  AC_MSG_RESULT($lt_cv_sys_max_cmd_len)
else
  AC_MSG_RESULT(none)
fi
max_cmd_len=$lt_cv_sys_max_cmd_len
_LT_DECL([], [max_cmd_len], [0],
    [What is the maximum length of a command?])
])
m4trace:m4/libtool.m4:1809: -1- AU_DEFUN([AC_LIBTOOL_SYS_MAX_CMD_LEN], [m4_if($#, 0, [LT_CMD_MAX_LEN], [LT_CMD_MAX_LEN($@)])])
m4trace:m4/libtool.m4:1809: -1- AC_DEFUN([AC_LIBTOOL_SYS_MAX_CMD_LEN], [AC_DIAGNOSE([obsolete], [The macro `AC_LIBTOOL_SYS_MAX_CMD_LEN' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_CMD_MAX_LEN], [LT_CMD_MAX_LEN($@)])])
m4trace:m4/libtool.m4:1920: -1- AC_DEFUN([LT_SYS_DLOPEN_SELF], [m4_require([_LT_HEADER_DLFCN])dnl
if test yes != "$enable_dlopen"; then
  enable_dlopen=unknown
  enable_dlopen_self=unknown
  enable_dlopen_self_static=unknown
else
  lt_cv_dlopen=no
  lt_cv_dlopen_libs=

  case $host_os in
  beos*)
    lt_cv_dlopen=load_add_on
    lt_cv_dlopen_libs=
    lt_cv_dlopen_self=yes
    ;;

  mingw* | pw32* | cegcc*)
    lt_cv_dlopen=LoadLibrary
    lt_cv_dlopen_libs=
    ;;

  cygwin*)
    lt_cv_dlopen=dlopen
    lt_cv_dlopen_libs=
    ;;

  darwin*)
    # if libdl is installed we need to link against it
    AC_CHECK_LIB([dl], [dlopen],
		[lt_cv_dlopen=dlopen lt_cv_dlopen_libs=-ldl],[
    lt_cv_dlopen=dyld
    lt_cv_dlopen_libs=
    lt_cv_dlopen_self=yes
    ])
    ;;

  tpf*)
    # Don't try to run any link tests for TPF.  We know it's impossible
    # because TPF is a cross-compiler, and we know how we open DSOs.
    lt_cv_dlopen=dlopen
    lt_cv_dlopen_libs=
    lt_cv_dlopen_self=no
    ;;

  *)
    AC_CHECK_FUNC([shl_load],
	  [lt_cv_dlopen=shl_load],
      [AC_CHECK_LIB([dld], [shl_load],
	    [lt_cv_dlopen=shl_load lt_cv_dlopen_libs=-ldld],
	[AC_CHECK_FUNC([dlopen],
	      [lt_cv_dlopen=dlopen],
	  [AC_CHECK_LIB([dl], [dlopen],
		[lt_cv_dlopen=dlopen lt_cv_dlopen_libs=-ldl],
	    [AC_CHECK_LIB([svld], [dlopen],
		  [lt_cv_dlopen=dlopen lt_cv_dlopen_libs=-lsvld],
	      [AC_CHECK_LIB([dld], [dld_link],
		    [lt_cv_dlopen=dld_link lt_cv_dlopen_libs=-ldld])
	      ])
	    ])
	  ])
	])
      ])
    ;;
  esac

  if test no = "$lt_cv_dlopen"; then
    enable_dlopen=no
  else
    enable_dlopen=yes
  fi

  case $lt_cv_dlopen in
  dlopen)
    save_CPPFLAGS=$CPPFLAGS
    test yes = "$ac_cv_header_dlfcn_h" && CPPFLAGS="$CPPFLAGS -DHAVE_DLFCN_H"

    save_LDFLAGS=$LDFLAGS
    wl=$lt_prog_compiler_wl eval LDFLAGS=\"\$LDFLAGS $export_dynamic_flag_spec\"

    save_LIBS=$LIBS
    LIBS="$lt_cv_dlopen_libs $LIBS"

    AC_CACHE_CHECK([whether a program can dlopen itself],
	  lt_cv_dlopen_self, [dnl
	  _LT_TRY_DLOPEN_SELF(
	    lt_cv_dlopen_self=yes, lt_cv_dlopen_self=yes,
	    lt_cv_dlopen_self=no, lt_cv_dlopen_self=cross)
    ])

    if test yes = "$lt_cv_dlopen_self"; then
      wl=$lt_prog_compiler_wl eval LDFLAGS=\"\$LDFLAGS $lt_prog_compiler_static\"
      AC_CACHE_CHECK([whether a statically linked program can dlopen itself],
	  lt_cv_dlopen_self_static, [dnl
	  _LT_TRY_DLOPEN_SELF(
	    lt_cv_dlopen_self_static=yes, lt_cv_dlopen_self_static=yes,
	    lt_cv_dlopen_self_static=no,  lt_cv_dlopen_self_static=cross)
      ])
    fi

    CPPFLAGS=$save_CPPFLAGS
    LDFLAGS=$save_LDFLAGS
    LIBS=$save_LIBS
    ;;
  esac

  case $lt_cv_dlopen_self in
  yes|no) enable_dlopen_self=$lt_cv_dlopen_self ;;
  *) enable_dlopen_self=unknown ;;
  esac

  case $lt_cv_dlopen_self_static in
  yes|no) enable_dlopen_self_static=$lt_cv_dlopen_self_static ;;
  *) enable_dlopen_self_static=unknown ;;
  esac
fi
_LT_DECL([dlopen_support], [enable_dlopen], [0],
	 [Whether dlopen is supported])
_LT_DECL([dlopen_self], [enable_dlopen_self], [0],
	 [Whether dlopen of programs is supported])
_LT_DECL([dlopen_self_static], [enable_dlopen_self_static], [0],
	 [Whether dlopen of statically linked programs is supported])
])
m4trace:m4/libtool.m4:2045: -1- AU_DEFUN([AC_LIBTOOL_DLOPEN_SELF], [m4_if($#, 0, [LT_SYS_DLOPEN_SELF], [LT_SYS_DLOPEN_SELF($@)])])
m4trace:m4/libtool.m4:2045: -1- AC_DEFUN([AC_LIBTOOL_DLOPEN_SELF], [AC_DIAGNOSE([obsolete], [The macro `AC_LIBTOOL_DLOPEN_SELF' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_SYS_DLOPEN_SELF], [LT_SYS_DLOPEN_SELF($@)])])
m4trace:m4/libtool.m4:3166: -1- AC_DEFUN([_LT_PATH_TOOL_PREFIX], [m4_require([_LT_DECL_EGREP])dnl
AC_MSG_CHECKING([for $1])
AC_CACHE_VAL(lt_cv_path_MAGIC_CMD,
[case $MAGIC_CMD in
[[\\/*] |  ?:[\\/]*])
  lt_cv_path_MAGIC_CMD=$MAGIC_CMD # Let the user override the test with a path.
  ;;
*)
  lt_save_MAGIC_CMD=$MAGIC_CMD
  lt_save_ifs=$IFS; IFS=$PATH_SEPARATOR
dnl $ac_dummy forces splitting on constant user-supplied paths.
dnl POSIX.2 word splitting is done only on the output of word expansions,
dnl not every word.  This closes a longstanding sh security hole.
  ac_dummy="m4_if([$2], , $PATH, [$2])"
  for ac_dir in $ac_dummy; do
    IFS=$lt_save_ifs
    test -z "$ac_dir" && ac_dir=.
    if test -f "$ac_dir/$1"; then
      lt_cv_path_MAGIC_CMD=$ac_dir/"$1"
      if test -n "$file_magic_test_file"; then
	case $deplibs_check_method in
	"file_magic "*)
	  file_magic_regex=`expr "$deplibs_check_method" : "file_magic \(.*\)"`
	  MAGIC_CMD=$lt_cv_path_MAGIC_CMD
	  if eval $file_magic_cmd \$file_magic_test_file 2> /dev/null |
	    $EGREP "$file_magic_regex" > /dev/null; then
	    :
	  else
	    cat <<_LT_EOF 1>&2

*** Warning: the command libtool uses to detect shared libraries,
*** $file_magic_cmd, produces output that libtool cannot recognize.
*** The result is that libtool may fail to recognize shared libraries
*** as such.  This will affect the creation of libtool libraries that
*** depend on shared libraries, but programs linked with such libtool
*** libraries will work regardless of this problem.  Nevertheless, you
*** may want to report the problem to your system manager and/or to
*** <EMAIL>

_LT_EOF
	  fi ;;
	esac
      fi
      break
    fi
  done
  IFS=$lt_save_ifs
  MAGIC_CMD=$lt_save_MAGIC_CMD
  ;;
esac])
MAGIC_CMD=$lt_cv_path_MAGIC_CMD
if test -n "$MAGIC_CMD"; then
  AC_MSG_RESULT($MAGIC_CMD)
else
  AC_MSG_RESULT(no)
fi
_LT_DECL([], [MAGIC_CMD], [0],
	 [Used to examine libraries when file_magic_cmd begins with "file"])dnl
])
m4trace:m4/libtool.m4:3228: -1- AU_DEFUN([AC_PATH_TOOL_PREFIX], [m4_if($#, 0, [_LT_PATH_TOOL_PREFIX], [_LT_PATH_TOOL_PREFIX($@)])])
m4trace:m4/libtool.m4:3228: -1- AC_DEFUN([AC_PATH_TOOL_PREFIX], [AC_DIAGNOSE([obsolete], [The macro `AC_PATH_TOOL_PREFIX' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [_LT_PATH_TOOL_PREFIX], [_LT_PATH_TOOL_PREFIX($@)])])
m4trace:m4/libtool.m4:3251: -1- AC_DEFUN([LT_PATH_LD], [AC_REQUIRE([AC_PROG_CC])dnl
AC_REQUIRE([AC_CANONICAL_HOST])dnl
AC_REQUIRE([AC_CANONICAL_BUILD])dnl
m4_require([_LT_DECL_SED])dnl
m4_require([_LT_DECL_EGREP])dnl
m4_require([_LT_PROG_ECHO_BACKSLASH])dnl

AC_ARG_WITH([gnu-ld],
    [AS_HELP_STRING([--with-gnu-ld],
	[assume the C compiler uses GNU ld @<:@default=no@:>@])],
    [test no = "$withval" || with_gnu_ld=yes],
    [with_gnu_ld=no])dnl

ac_prog=ld
if test yes = "$GCC"; then
  # Check if gcc -print-prog-name=ld gives a path.
  AC_MSG_CHECKING([for ld used by $CC])
  case $host in
  *-*-mingw*)
    # gcc leaves a trailing carriage return, which upsets mingw
    ac_prog=`($CC -print-prog-name=ld) 2>&5 | tr -d '\015'` ;;
  *)
    ac_prog=`($CC -print-prog-name=ld) 2>&5` ;;
  esac
  case $ac_prog in
    # Accept absolute paths.
    [[\\/]]* | ?:[[\\/]]*)
      re_direlt='/[[^/]][[^/]]*/\.\./'
      # Canonicalize the pathname of ld
      ac_prog=`$ECHO "$ac_prog"| $SED 's%\\\\%/%g'`
      while $ECHO "$ac_prog" | $GREP "$re_direlt" > /dev/null 2>&1; do
	ac_prog=`$ECHO $ac_prog| $SED "s%$re_direlt%/%"`
      done
      test -z "$LD" && LD=$ac_prog
      ;;
  "")
    # If it fails, then pretend we aren't using GCC.
    ac_prog=ld
    ;;
  *)
    # If it is relative, then search for the first ld in PATH.
    with_gnu_ld=unknown
    ;;
  esac
elif test yes = "$with_gnu_ld"; then
  AC_MSG_CHECKING([for GNU ld])
else
  AC_MSG_CHECKING([for non-GNU ld])
fi
AC_CACHE_VAL(lt_cv_path_LD,
[if test -z "$LD"; then
  lt_save_ifs=$IFS; IFS=$PATH_SEPARATOR
  for ac_dir in $PATH; do
    IFS=$lt_save_ifs
    test -z "$ac_dir" && ac_dir=.
    if test -f "$ac_dir/$ac_prog" || test -f "$ac_dir/$ac_prog$ac_exeext"; then
      lt_cv_path_LD=$ac_dir/$ac_prog
      # Check to see if the program is GNU ld.  I'd rather use --version,
      # but apparently some variants of GNU ld only accept -v.
      # Break only if it was the GNU/non-GNU ld that we prefer.
      case `"$lt_cv_path_LD" -v 2>&1 </dev/null` in
      *GNU* | *'with BFD'*)
	test no != "$with_gnu_ld" && break
	;;
      *)
	test yes != "$with_gnu_ld" && break
	;;
      esac
    fi
  done
  IFS=$lt_save_ifs
else
  lt_cv_path_LD=$LD # Let the user override the test with a path.
fi])
LD=$lt_cv_path_LD
if test -n "$LD"; then
  AC_MSG_RESULT($LD)
else
  AC_MSG_RESULT(no)
fi
test -z "$LD" && AC_MSG_ERROR([no acceptable ld found in \$PATH])
_LT_PATH_LD_GNU
AC_SUBST([LD])

_LT_TAGDECL([], [LD], [1], [The linker used to build libraries])
])
m4trace:m4/libtool.m4:3340: -1- AU_DEFUN([AM_PROG_LD], [m4_if($#, 0, [LT_PATH_LD], [LT_PATH_LD($@)])])
m4trace:m4/libtool.m4:3340: -1- AC_DEFUN([AM_PROG_LD], [AC_DIAGNOSE([obsolete], [The macro `AM_PROG_LD' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_PATH_LD], [LT_PATH_LD($@)])])
m4trace:m4/libtool.m4:3341: -1- AU_DEFUN([AC_PROG_LD], [m4_if($#, 0, [LT_PATH_LD], [LT_PATH_LD($@)])])
m4trace:m4/libtool.m4:3341: -1- AC_DEFUN([AC_PROG_LD], [AC_DIAGNOSE([obsolete], [The macro `AC_PROG_LD' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_PATH_LD], [LT_PATH_LD($@)])])
m4trace:m4/libtool.m4:3670: -1- AC_DEFUN([LT_PATH_NM], [AC_REQUIRE([AC_PROG_CC])dnl
AC_CACHE_CHECK([for BSD- or MS-compatible name lister (nm)], lt_cv_path_NM,
[if test -n "$NM"; then
  # Let the user override the test.
  lt_cv_path_NM=$NM
else
  lt_nm_to_check=${ac_tool_prefix}nm
  if test -n "$ac_tool_prefix" && test "$build" = "$host"; then
    lt_nm_to_check="$lt_nm_to_check nm"
  fi
  for lt_tmp_nm in $lt_nm_to_check; do
    lt_save_ifs=$IFS; IFS=$PATH_SEPARATOR
    for ac_dir in $PATH /usr/ccs/bin/elf /usr/ccs/bin /usr/ucb /bin; do
      IFS=$lt_save_ifs
      test -z "$ac_dir" && ac_dir=.
      tmp_nm=$ac_dir/$lt_tmp_nm
      if test -f "$tmp_nm" || test -f "$tmp_nm$ac_exeext"; then
	# Check to see if the nm accepts a BSD-compat flag.
	# Adding the 'sed 1q' prevents false positives on HP-UX, which says:
	#   nm: unknown option "B" ignored
	# Tru64's nm complains that /dev/null is an invalid object file
	# MSYS converts /dev/null to NUL, MinGW nm treats NUL as empty
	case $build_os in
	mingw*) lt_bad_file=conftest.nm/nofile ;;
	*) lt_bad_file=/dev/null ;;
	esac
	case `"$tmp_nm" -B $lt_bad_file 2>&1 | sed '1q'` in
	*$lt_bad_file* | *'Invalid file or object type'*)
	  lt_cv_path_NM="$tmp_nm -B"
	  break 2
	  ;;
	*)
	  case `"$tmp_nm" -p /dev/null 2>&1 | sed '1q'` in
	  */dev/null*)
	    lt_cv_path_NM="$tmp_nm -p"
	    break 2
	    ;;
	  *)
	    lt_cv_path_NM=${lt_cv_path_NM="$tmp_nm"} # keep the first match, but
	    continue # so that we can try to find one that supports BSD flags
	    ;;
	  esac
	  ;;
	esac
      fi
    done
    IFS=$lt_save_ifs
  done
  : ${lt_cv_path_NM=no}
fi])
if test no != "$lt_cv_path_NM"; then
  NM=$lt_cv_path_NM
else
  # Didn't find any BSD compatible name lister, look for dumpbin.
  if test -n "$DUMPBIN"; then :
    # Let the user override the test.
  else
    AC_CHECK_TOOLS(DUMPBIN, [dumpbin "link -dump"], :)
    case `$DUMPBIN -symbols -headers /dev/null 2>&1 | sed '1q'` in
    *COFF*)
      DUMPBIN="$DUMPBIN -symbols -headers"
      ;;
    *)
      DUMPBIN=:
      ;;
    esac
  fi
  AC_SUBST([DUMPBIN])
  if test : != "$DUMPBIN"; then
    NM=$DUMPBIN
  fi
fi
test -z "$NM" && NM=nm
AC_SUBST([NM])
_LT_DECL([], [NM], [1], [A BSD- or MS-compatible name lister])dnl

AC_CACHE_CHECK([the name lister ($NM) interface], [lt_cv_nm_interface],
  [lt_cv_nm_interface="BSD nm"
  echo "int some_variable = 0;" > conftest.$ac_ext
  (eval echo "\"\$as_me:$LINENO: $ac_compile\"" >&AS_MESSAGE_LOG_FD)
  (eval "$ac_compile" 2>conftest.err)
  cat conftest.err >&AS_MESSAGE_LOG_FD
  (eval echo "\"\$as_me:$LINENO: $NM \\\"conftest.$ac_objext\\\"\"" >&AS_MESSAGE_LOG_FD)
  (eval "$NM \"conftest.$ac_objext\"" 2>conftest.err > conftest.out)
  cat conftest.err >&AS_MESSAGE_LOG_FD
  (eval echo "\"\$as_me:$LINENO: output\"" >&AS_MESSAGE_LOG_FD)
  cat conftest.out >&AS_MESSAGE_LOG_FD
  if $GREP 'External.*some_variable' conftest.out > /dev/null; then
    lt_cv_nm_interface="MS dumpbin"
  fi
  rm -f conftest*])
])
m4trace:m4/libtool.m4:3765: -1- AU_DEFUN([AM_PROG_NM], [m4_if($#, 0, [LT_PATH_NM], [LT_PATH_NM($@)])])
m4trace:m4/libtool.m4:3765: -1- AC_DEFUN([AM_PROG_NM], [AC_DIAGNOSE([obsolete], [The macro `AM_PROG_NM' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_PATH_NM], [LT_PATH_NM($@)])])
m4trace:m4/libtool.m4:3766: -1- AU_DEFUN([AC_PROG_NM], [m4_if($#, 0, [LT_PATH_NM], [LT_PATH_NM($@)])])
m4trace:m4/libtool.m4:3766: -1- AC_DEFUN([AC_PROG_NM], [AC_DIAGNOSE([obsolete], [The macro `AC_PROG_NM' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_PATH_NM], [LT_PATH_NM($@)])])
m4trace:m4/libtool.m4:3837: -1- AC_DEFUN([_LT_DLL_DEF_P], [dnl
  test DEF = "`$SED -n dnl
    -e '\''s/^[[	 ]]*//'\'' dnl Strip leading whitespace
    -e '\''/^\(;.*\)*$/d'\'' dnl      Delete empty lines and comments
    -e '\''s/^\(EXPORTS\|LIBRARY\)\([[	 ]].*\)*$/DEF/p'\'' dnl
    -e q dnl                          Only consider the first "real" line
    $1`" dnl
])
m4trace:m4/libtool.m4:3851: -1- AC_DEFUN([LT_LIB_M], [AC_REQUIRE([AC_CANONICAL_HOST])dnl
LIBM=
case $host in
*-*-beos* | *-*-cegcc* | *-*-cygwin* | *-*-haiku* | *-*-pw32* | *-*-darwin*)
  # These system don't have libm, or don't need it
  ;;
*-ncr-sysv4.3*)
  AC_CHECK_LIB(mw, _mwvalidcheckl, LIBM=-lmw)
  AC_CHECK_LIB(m, cos, LIBM="$LIBM -lm")
  ;;
*)
  AC_CHECK_LIB(m, cos, LIBM=-lm)
  ;;
esac
AC_SUBST([LIBM])
])
m4trace:m4/libtool.m4:3870: -1- AU_DEFUN([AC_CHECK_LIBM], [m4_if($#, 0, [LT_LIB_M], [LT_LIB_M($@)])])
m4trace:m4/libtool.m4:3870: -1- AC_DEFUN([AC_CHECK_LIBM], [AC_DIAGNOSE([obsolete], [The macro `AC_CHECK_LIBM' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_LIB_M], [LT_LIB_M($@)])])
m4trace:m4/libtool.m4:8147: -1- AC_DEFUN([LT_PROG_GCJ], [m4_ifdef([AC_PROG_GCJ], [AC_PROG_GCJ],
  [m4_ifdef([A][M_PROG_GCJ], [A][M_PROG_GCJ],
    [AC_CHECK_TOOL(GCJ, gcj,)
      test set = "${GCJFLAGS+set}" || GCJFLAGS="-g -O2"
      AC_SUBST(GCJFLAGS)])])[]dnl
])
m4trace:m4/libtool.m4:8156: -1- AU_DEFUN([LT_AC_PROG_GCJ], [m4_if($#, 0, [LT_PROG_GCJ], [LT_PROG_GCJ($@)])])
m4trace:m4/libtool.m4:8156: -1- AC_DEFUN([LT_AC_PROG_GCJ], [AC_DIAGNOSE([obsolete], [The macro `LT_AC_PROG_GCJ' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_PROG_GCJ], [LT_PROG_GCJ($@)])])
m4trace:m4/libtool.m4:8163: -1- AC_DEFUN([LT_PROG_GO], [AC_CHECK_TOOL(GOC, gccgo,)
])
m4trace:m4/libtool.m4:8170: -1- AC_DEFUN([LT_PROG_RC], [AC_CHECK_TOOL(RC, windres,)
])
m4trace:m4/libtool.m4:8175: -1- AU_DEFUN([LT_AC_PROG_RC], [m4_if($#, 0, [LT_PROG_RC], [LT_PROG_RC($@)])])
m4trace:m4/libtool.m4:8175: -1- AC_DEFUN([LT_AC_PROG_RC], [AC_DIAGNOSE([obsolete], [The macro `LT_AC_PROG_RC' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [LT_PROG_RC], [LT_PROG_RC($@)])])
m4trace:m4/libtool.m4:8295: -1- AU_DEFUN([LT_AC_PROG_SED], [m4_if($#, 0, [AC_PROG_SED], [AC_PROG_SED($@)])])
m4trace:m4/libtool.m4:8295: -1- AC_DEFUN([LT_AC_PROG_SED], [AC_DIAGNOSE([obsolete], [The macro `LT_AC_PROG_SED' is obsolete.
You should run autoupdate.])dnl
m4_if($#, 0, [AC_PROG_SED], [AC_PROG_SED($@)])])
m4trace:m4/lock.m4:9: -1- AC_DEFUN([gl_LOCK], [
  AC_REQUIRE([gl_THREADLIB])
  if test "$gl_threads_api" = posix; then
    # OSF/1 4.0 and Mac OS X 10.1 lack the pthread_rwlock_t type and the
    # pthread_rwlock_* functions.
    AC_CHECK_TYPE([pthread_rwlock_t],
      [AC_DEFINE([HAVE_PTHREAD_RWLOCK], [1],
         [Define if the POSIX multithreading library has read/write locks.])],
      [],
      [#include <pthread.h>])
    # glibc defines PTHREAD_MUTEX_RECURSIVE as enum, not as a macro.
    AC_COMPILE_IFELSE([
      AC_LANG_PROGRAM(
        [[#include <pthread.h>]],
        [[
#if __FreeBSD__ == 4
error "No, in FreeBSD 4.0 recursive mutexes actually don't work."
#elif (defined __ENVIRONMENT_MAC_OS_X_VERSION_MIN_REQUIRED__ \
       && __ENVIRONMENT_MAC_OS_X_VERSION_MIN_REQUIRED__ < 1070)
error "No, in Mac OS X < 10.7 recursive mutexes actually don't work."
#else
int x = (int)PTHREAD_MUTEX_RECURSIVE;
return !x;
#endif
        ]])],
      [AC_DEFINE([HAVE_PTHREAD_MUTEX_RECURSIVE], [1],
         [Define if the <pthread.h> defines PTHREAD_MUTEX_RECURSIVE.])])
  fi
  gl_PREREQ_LOCK
])
m4trace:m4/lock.m4:42: -1- AC_DEFUN([gl_PREREQ_LOCK], [:])
m4trace:m4/longlong.m4:17: -1- AC_DEFUN([AC_TYPE_LONG_LONG_INT], [
  AC_REQUIRE([AC_TYPE_UNSIGNED_LONG_LONG_INT])
  AC_CACHE_CHECK([for long long int], [ac_cv_type_long_long_int],
     [ac_cv_type_long_long_int=yes
      if test "x${ac_cv_prog_cc_c99-no}" = xno; then
        ac_cv_type_long_long_int=$ac_cv_type_unsigned_long_long_int
        if test $ac_cv_type_long_long_int = yes; then
          dnl Catch a bug in Tandem NonStop Kernel (OSS) cc -O circa 2004.
          dnl If cross compiling, assume the bug is not important, since
          dnl nobody cross compiles for this platform as far as we know.
          AC_RUN_IFELSE(
            [AC_LANG_PROGRAM(
               [[@%:@include <limits.h>
                 @%:@ifndef LLONG_MAX
                 @%:@ define HALF \
                          (1LL << (sizeof (long long int) * CHAR_BIT - 2))
                 @%:@ define LLONG_MAX (HALF - 1 + HALF)
                 @%:@endif]],
               [[long long int n = 1;
                 int i;
                 for (i = 0; ; i++)
                   {
                     long long int m = n << i;
                     if (m >> i != n)
                       return 1;
                     if (LLONG_MAX / 2 < m)
                       break;
                   }
                 return 0;]])],
            [],
            [ac_cv_type_long_long_int=no],
            [:])
        fi
      fi])
  if test $ac_cv_type_long_long_int = yes; then
    AC_DEFINE([HAVE_LONG_LONG_INT], [1],
      [Define to 1 if the system has the type 'long long int'.])
  fi
])
m4trace:m4/longlong.m4:67: -1- AC_DEFUN([AC_TYPE_UNSIGNED_LONG_LONG_INT], [
  AC_CACHE_CHECK([for unsigned long long int],
    [ac_cv_type_unsigned_long_long_int],
    [ac_cv_type_unsigned_long_long_int=yes
     if test "x${ac_cv_prog_cc_c99-no}" = xno; then
       AC_LINK_IFELSE(
         [_AC_TYPE_LONG_LONG_SNIPPET],
         [],
         [ac_cv_type_unsigned_long_long_int=no])
     fi])
  if test $ac_cv_type_unsigned_long_long_int = yes; then
    AC_DEFINE([HAVE_UNSIGNED_LONG_LONG_INT], [1],
      [Define to 1 if the system has the type 'unsigned long long int'.])
  fi
])
m4trace:m4/longlong.m4:89: -1- AC_DEFUN([_AC_TYPE_LONG_LONG_SNIPPET], [
  AC_LANG_PROGRAM(
    [[/* For now, do not test the preprocessor; as of 2007 there are too many
         implementations with broken preprocessors.  Perhaps this can
         be revisited in 2012.  In the meantime, code should not expect
         #if to work with literals wider than 32 bits.  */
      /* Test literals.  */
      long long int ll = 9223372036854775807ll;
      long long int nll = -9223372036854775807LL;
      unsigned long long int ull = 18446744073709551615ULL;
      /* Test constant expressions.   */
      typedef int a[((-9223372036854775807LL < 0 && 0 < 9223372036854775807ll)
                     ? 1 : -1)];
      typedef int b[(18446744073709551615ULL <= (unsigned long long int) -1
                     ? 1 : -1)];
      int i = 63;]],
    [[/* Test availability of runtime routines for shift and division.  */
      long long int llmax = 9223372036854775807ll;
      unsigned long long int ullmax = 18446744073709551615ull;
      return ((ll << 63) | (ll >> 63) | (ll < i) | (ll > i)
              | (llmax / ll) | (llmax % ll)
              | (ull << 63) | (ull >> 63) | (ull << i) | (ull >> i)
              | (ullmax / ull) | (ullmax % ull));]])
])
m4trace:m4/ltoptions.m4:14: -1- AC_DEFUN([LTOPTIONS_VERSION], [m4_if([1])])
m4trace:m4/ltoptions.m4:113: -1- AU_DEFUN([AC_LIBTOOL_DLOPEN], [_LT_SET_OPTION([LT_INIT], [dlopen])
AC_DIAGNOSE([obsolete],
[$0: Remove this warning and the call to _LT_SET_OPTION when you
put the 'dlopen' option into LT_INIT's first parameter.])
])
m4trace:m4/ltoptions.m4:113: -1- AC_DEFUN([AC_LIBTOOL_DLOPEN], [AC_DIAGNOSE([obsolete], [The macro `AC_LIBTOOL_DLOPEN' is obsolete.
You should run autoupdate.])dnl
_LT_SET_OPTION([LT_INIT], [dlopen])
AC_DIAGNOSE([obsolete],
[$0: Remove this warning and the call to _LT_SET_OPTION when you
put the 'dlopen' option into LT_INIT's first parameter.])
])
m4trace:m4/ltoptions.m4:148: -1- AU_DEFUN([AC_LIBTOOL_WIN32_DLL], [AC_REQUIRE([AC_CANONICAL_HOST])dnl
_LT_SET_OPTION([LT_INIT], [win32-dll])
AC_DIAGNOSE([obsolete],
[$0: Remove this warning and the call to _LT_SET_OPTION when you
put the 'win32-dll' option into LT_INIT's first parameter.])
])
m4trace:m4/ltoptions.m4:148: -1- AC_DEFUN([AC_LIBTOOL_WIN32_DLL], [AC_DIAGNOSE([obsolete], [The macro `AC_LIBTOOL_WIN32_DLL' is obsolete.
You should run autoupdate.])dnl
AC_REQUIRE([AC_CANONICAL_HOST])dnl
_LT_SET_OPTION([LT_INIT], [win32-dll])
AC_DIAGNOSE([obsolete],
[$0: Remove this warning and the call to _LT_SET_OPTION when you
put the 'win32-dll' option into LT_INIT's first parameter.])
])
m4trace:m4/ltoptions.m4:197: -1- AC_DEFUN([AC_ENABLE_SHARED], [_LT_SET_OPTION([LT_INIT], m4_if([$1], [no], [disable-])[shared])
])
m4trace:m4/ltoptions.m4:201: -1- AC_DEFUN([AC_DISABLE_SHARED], [_LT_SET_OPTION([LT_INIT], [disable-shared])
])
m4trace:m4/ltoptions.m4:205: -1- AU_DEFUN([AM_ENABLE_SHARED], [AC_ENABLE_SHARED($@)])
m4trace:m4/ltoptions.m4:205: -1- AC_DEFUN([AM_ENABLE_SHARED], [AC_DIAGNOSE([obsolete], [The macro `AM_ENABLE_SHARED' is obsolete.
You should run autoupdate.])dnl
AC_ENABLE_SHARED($@)])
m4trace:m4/ltoptions.m4:206: -1- AU_DEFUN([AM_DISABLE_SHARED], [AC_DISABLE_SHARED($@)])
m4trace:m4/ltoptions.m4:206: -1- AC_DEFUN([AM_DISABLE_SHARED], [AC_DIAGNOSE([obsolete], [The macro `AM_DISABLE_SHARED' is obsolete.
You should run autoupdate.])dnl
AC_DISABLE_SHARED($@)])
m4trace:m4/ltoptions.m4:251: -1- AC_DEFUN([AC_ENABLE_STATIC], [_LT_SET_OPTION([LT_INIT], m4_if([$1], [no], [disable-])[static])
])
m4trace:m4/ltoptions.m4:255: -1- AC_DEFUN([AC_DISABLE_STATIC], [_LT_SET_OPTION([LT_INIT], [disable-static])
])
m4trace:m4/ltoptions.m4:259: -1- AU_DEFUN([AM_ENABLE_STATIC], [AC_ENABLE_STATIC($@)])
m4trace:m4/ltoptions.m4:259: -1- AC_DEFUN([AM_ENABLE_STATIC], [AC_DIAGNOSE([obsolete], [The macro `AM_ENABLE_STATIC' is obsolete.
You should run autoupdate.])dnl
AC_ENABLE_STATIC($@)])
m4trace:m4/ltoptions.m4:260: -1- AU_DEFUN([AM_DISABLE_STATIC], [AC_DISABLE_STATIC($@)])
m4trace:m4/ltoptions.m4:260: -1- AC_DEFUN([AM_DISABLE_STATIC], [AC_DIAGNOSE([obsolete], [The macro `AM_DISABLE_STATIC' is obsolete.
You should run autoupdate.])dnl
AC_DISABLE_STATIC($@)])
m4trace:m4/ltoptions.m4:305: -1- AU_DEFUN([AC_ENABLE_FAST_INSTALL], [_LT_SET_OPTION([LT_INIT], m4_if([$1], [no], [disable-])[fast-install])
AC_DIAGNOSE([obsolete],
[$0: Remove this warning and the call to _LT_SET_OPTION when you put
the 'fast-install' option into LT_INIT's first parameter.])
])
m4trace:m4/ltoptions.m4:305: -1- AC_DEFUN([AC_ENABLE_FAST_INSTALL], [AC_DIAGNOSE([obsolete], [The macro `AC_ENABLE_FAST_INSTALL' is obsolete.
You should run autoupdate.])dnl
_LT_SET_OPTION([LT_INIT], m4_if([$1], [no], [disable-])[fast-install])
AC_DIAGNOSE([obsolete],
[$0: Remove this warning and the call to _LT_SET_OPTION when you put
the 'fast-install' option into LT_INIT's first parameter.])
])
m4trace:m4/ltoptions.m4:312: -1- AU_DEFUN([AC_DISABLE_FAST_INSTALL], [_LT_SET_OPTION([LT_INIT], [disable-fast-install])
AC_DIAGNOSE([obsolete],
[$0: Remove this warning and the call to _LT_SET_OPTION when you put
the 'disable-fast-install' option into LT_INIT's first parameter.])
])
m4trace:m4/ltoptions.m4:312: -1- AC_DEFUN([AC_DISABLE_FAST_INSTALL], [AC_DIAGNOSE([obsolete], [The macro `AC_DISABLE_FAST_INSTALL' is obsolete.
You should run autoupdate.])dnl
_LT_SET_OPTION([LT_INIT], [disable-fast-install])
AC_DIAGNOSE([obsolete],
[$0: Remove this warning and the call to _LT_SET_OPTION when you put
the 'disable-fast-install' option into LT_INIT's first parameter.])
])
m4trace:m4/ltoptions.m4:411: -1- AU_DEFUN([AC_LIBTOOL_PICMODE], [_LT_SET_OPTION([LT_INIT], [pic-only])
AC_DIAGNOSE([obsolete],
[$0: Remove this warning and the call to _LT_SET_OPTION when you
put the 'pic-only' option into LT_INIT's first parameter.])
])
m4trace:m4/ltoptions.m4:411: -1- AC_DEFUN([AC_LIBTOOL_PICMODE], [AC_DIAGNOSE([obsolete], [The macro `AC_LIBTOOL_PICMODE' is obsolete.
You should run autoupdate.])dnl
_LT_SET_OPTION([LT_INIT], [pic-only])
AC_DIAGNOSE([obsolete],
[$0: Remove this warning and the call to _LT_SET_OPTION when you
put the 'pic-only' option into LT_INIT's first parameter.])
])
m4trace:m4/ltsugar.m4:14: -1- AC_DEFUN([LTSUGAR_VERSION], [m4_if([0.1])])
m4trace:m4/ltversion.m4:18: -1- AC_DEFUN([LTVERSION_VERSION], [macro_version='2.4.6'
macro_revision='2.4.6'
_LT_DECL(, macro_version, 0, [Which release of libtool.m4 was used?])
_LT_DECL(, macro_revision, 0)
])
m4trace:m4/lt~obsolete.m4:37: -1- AC_DEFUN([LTOBSOLETE_VERSION], [m4_if([1])])
m4trace:m4/lt~obsolete.m4:41: -1- AC_DEFUN([_LT_AC_PROG_ECHO_BACKSLASH])
m4trace:m4/lt~obsolete.m4:42: -1- AC_DEFUN([_LT_AC_SHELL_INIT])
m4trace:m4/lt~obsolete.m4:43: -1- AC_DEFUN([_LT_AC_SYS_LIBPATH_AIX])
m4trace:m4/lt~obsolete.m4:45: -1- AC_DEFUN([_LT_AC_TAGVAR])
m4trace:m4/lt~obsolete.m4:46: -1- AC_DEFUN([AC_LTDL_ENABLE_INSTALL])
m4trace:m4/lt~obsolete.m4:47: -1- AC_DEFUN([AC_LTDL_PREOPEN])
m4trace:m4/lt~obsolete.m4:48: -1- AC_DEFUN([_LT_AC_SYS_COMPILER])
m4trace:m4/lt~obsolete.m4:49: -1- AC_DEFUN([_LT_AC_LOCK])
m4trace:m4/lt~obsolete.m4:50: -1- AC_DEFUN([AC_LIBTOOL_SYS_OLD_ARCHIVE])
m4trace:m4/lt~obsolete.m4:51: -1- AC_DEFUN([_LT_AC_TRY_DLOPEN_SELF])
m4trace:m4/lt~obsolete.m4:52: -1- AC_DEFUN([AC_LIBTOOL_PROG_CC_C_O])
m4trace:m4/lt~obsolete.m4:53: -1- AC_DEFUN([AC_LIBTOOL_SYS_HARD_LINK_LOCKS])
m4trace:m4/lt~obsolete.m4:54: -1- AC_DEFUN([AC_LIBTOOL_OBJDIR])
m4trace:m4/lt~obsolete.m4:55: -1- AC_DEFUN([AC_LTDL_OBJDIR])
m4trace:m4/lt~obsolete.m4:56: -1- AC_DEFUN([AC_LIBTOOL_PROG_LD_HARDCODE_LIBPATH])
m4trace:m4/lt~obsolete.m4:57: -1- AC_DEFUN([AC_LIBTOOL_SYS_LIB_STRIP])
m4trace:m4/lt~obsolete.m4:58: -1- AC_DEFUN([AC_PATH_MAGIC])
m4trace:m4/lt~obsolete.m4:59: -1- AC_DEFUN([AC_PROG_LD_GNU])
m4trace:m4/lt~obsolete.m4:60: -1- AC_DEFUN([AC_PROG_LD_RELOAD_FLAG])
m4trace:m4/lt~obsolete.m4:61: -1- AC_DEFUN([AC_DEPLIBS_CHECK_METHOD])
m4trace:m4/lt~obsolete.m4:62: -1- AC_DEFUN([AC_LIBTOOL_PROG_COMPILER_NO_RTTI])
m4trace:m4/lt~obsolete.m4:63: -1- AC_DEFUN([AC_LIBTOOL_SYS_GLOBAL_SYMBOL_PIPE])
m4trace:m4/lt~obsolete.m4:64: -1- AC_DEFUN([AC_LIBTOOL_PROG_COMPILER_PIC])
m4trace:m4/lt~obsolete.m4:65: -1- AC_DEFUN([AC_LIBTOOL_PROG_LD_SHLIBS])
m4trace:m4/lt~obsolete.m4:66: -1- AC_DEFUN([AC_LIBTOOL_POSTDEP_PREDEP])
m4trace:m4/lt~obsolete.m4:67: -1- AC_DEFUN([LT_AC_PROG_EGREP])
m4trace:m4/lt~obsolete.m4:72: -1- AC_DEFUN([_AC_PROG_LIBTOOL])
m4trace:m4/lt~obsolete.m4:73: -1- AC_DEFUN([AC_LIBTOOL_SETUP])
m4trace:m4/lt~obsolete.m4:74: -1- AC_DEFUN([_LT_AC_CHECK_DLFCN])
m4trace:m4/lt~obsolete.m4:75: -1- AC_DEFUN([AC_LIBTOOL_SYS_DYNAMIC_LINKER])
m4trace:m4/lt~obsolete.m4:76: -1- AC_DEFUN([_LT_AC_TAGCONFIG])
m4trace:m4/lt~obsolete.m4:78: -1- AC_DEFUN([_LT_AC_LANG_CXX])
m4trace:m4/lt~obsolete.m4:79: -1- AC_DEFUN([_LT_AC_LANG_F77])
m4trace:m4/lt~obsolete.m4:80: -1- AC_DEFUN([_LT_AC_LANG_GCJ])
m4trace:m4/lt~obsolete.m4:81: -1- AC_DEFUN([AC_LIBTOOL_LANG_C_CONFIG])
m4trace:m4/lt~obsolete.m4:82: -1- AC_DEFUN([_LT_AC_LANG_C_CONFIG])
m4trace:m4/lt~obsolete.m4:83: -1- AC_DEFUN([AC_LIBTOOL_LANG_CXX_CONFIG])
m4trace:m4/lt~obsolete.m4:84: -1- AC_DEFUN([_LT_AC_LANG_CXX_CONFIG])
m4trace:m4/lt~obsolete.m4:85: -1- AC_DEFUN([AC_LIBTOOL_LANG_F77_CONFIG])
m4trace:m4/lt~obsolete.m4:86: -1- AC_DEFUN([_LT_AC_LANG_F77_CONFIG])
m4trace:m4/lt~obsolete.m4:87: -1- AC_DEFUN([AC_LIBTOOL_LANG_GCJ_CONFIG])
m4trace:m4/lt~obsolete.m4:88: -1- AC_DEFUN([_LT_AC_LANG_GCJ_CONFIG])
m4trace:m4/lt~obsolete.m4:89: -1- AC_DEFUN([AC_LIBTOOL_LANG_RC_CONFIG])
m4trace:m4/lt~obsolete.m4:90: -1- AC_DEFUN([_LT_AC_LANG_RC_CONFIG])
m4trace:m4/lt~obsolete.m4:91: -1- AC_DEFUN([AC_LIBTOOL_CONFIG])
m4trace:m4/lt~obsolete.m4:92: -1- AC_DEFUN([_LT_AC_FILE_LTDLL_C])
m4trace:m4/lt~obsolete.m4:94: -1- AC_DEFUN([_LT_AC_PROG_CXXCPP])
m4trace:m4/lt~obsolete.m4:97: -1- AC_DEFUN([_LT_PROG_F77])
m4trace:m4/lt~obsolete.m4:98: -1- AC_DEFUN([_LT_PROG_FC])
m4trace:m4/lt~obsolete.m4:99: -1- AC_DEFUN([_LT_PROG_CXX])
m4trace:m4/mm-enable-plugin.m4:13: -1- AC_DEFUN([MM_ENABLE_ALL_PLUGINS], [dnl
AC_ARG_ENABLE(all-plugins,
              AS_HELP_STRING([--enable-all-plugins],
              [Build all plugins [[default=yes]]]),
              [],
              [enable_all_plugins=yes])
])
m4trace:m4/mm-enable-plugin.m4:24: -1- AC_DEFUN([MM_ENABLE_PLUGIN], [dnl
m4_pushdef([var_enable_plugin], patsubst([enable_plugin_$1], -, _))dnl
m4_pushdef([VAR_ENABLE_PLUGIN], patsubst(translit([enable_plugin_$1], [a-z], [A-Z]), -, _))dnl
AC_ARG_ENABLE(plugin-$1,
              AS_HELP_STRING([--enable-plugin-$1], [Build $1 plugin]),
              [],
              [var_enable_plugin=$enable_all_plugins])
if test "x$var_enable_plugin" = "xyes"; then
  AC_DEFINE([VAR_ENABLE_PLUGIN], 1, [Define if $1 plugin is enabled])
m4_ifval([$2],[m4_foreach(with_shared,[$2],[dnl
  with_shared="yes"
])])dnl
fi
AM_CONDITIONAL(VAR_ENABLE_PLUGIN, [test "x$var_enable_plugin" = "xyes"])
m4_popdef([VAR_ENABLE_PLUGIN])dnl
m4_popdef([var_enable_plugin])dnl
])
m4trace:m4/mm-enable-plugin.m4:45: -1- AC_DEFUN([MM_BUILD_SHARED], [dnl
m4_pushdef([with_shared], patsubst([with_shared_$1], -, _))dnl
m4_pushdef([WITH_SHARED], patsubst(translit([with_shared_$1], [a-z], [A-Z]), -, _))dnl
AM_CONDITIONAL(WITH_SHARED, test "x$with_shared" = "xyes")
if test "x$with_shared" = "xyes"; then
  AC_DEFINE([WITH_SHARED], 1, [Define if $1 utils are built])
else
  with_shared="no"
fi
m4_popdef([WITH_SHARED])dnl
m4_popdef([with_shared])dnl
])
m4trace:m4/nls.m4:23: -1- AC_DEFUN([AM_NLS], [
  AC_MSG_CHECKING([whether NLS is requested])
  dnl Default is enabled NLS
  AC_ARG_ENABLE([nls],
    [  --disable-nls           do not use Native Language Support],
    USE_NLS=$enableval, USE_NLS=yes)
  AC_MSG_RESULT([$USE_NLS])
  AC_SUBST([USE_NLS])
])
m4trace:m4/po.m4:23: -1- AC_DEFUN([AM_PO_SUBDIRS], [
  AC_REQUIRE([AC_PROG_MAKE_SET])dnl
  AC_REQUIRE([AC_PROG_INSTALL])dnl
  AC_REQUIRE([AC_PROG_MKDIR_P])dnl
  AC_REQUIRE([AC_PROG_SED])dnl
  AC_REQUIRE([AM_NLS])dnl

  dnl Release version of the gettext macros. This is used to ensure that
  dnl the gettext macros and po/Makefile.in.in are in sync.
  AC_SUBST([GETTEXT_MACRO_VERSION], [0.19])

  dnl Perform the following tests also if --disable-nls has been given,
  dnl because they are needed for "make dist" to work.

  dnl Search for GNU msgfmt in the PATH.
  dnl The first test excludes Solaris msgfmt and early GNU msgfmt versions.
  dnl The second test excludes FreeBSD msgfmt.
  AM_PATH_PROG_WITH_TEST(MSGFMT, msgfmt,
    [$ac_dir/$ac_word --statistics /dev/null >&]AS_MESSAGE_LOG_FD[ 2>&1 &&
     (if $ac_dir/$ac_word --statistics /dev/null 2>&1 >/dev/null | grep usage >/dev/null; then exit 1; else exit 0; fi)],
    :)
  AC_PATH_PROG([GMSGFMT], [gmsgfmt], [$MSGFMT])

  dnl Test whether it is GNU msgfmt >= 0.15.
changequote(,)dnl
  case `$MSGFMT --version | sed 1q | sed -e 's,^[^0-9]*,,'` in
    '' | 0.[0-9] | 0.[0-9].* | 0.1[0-4] | 0.1[0-4].*) MSGFMT_015=: ;;
    *) MSGFMT_015=$MSGFMT ;;
  esac
changequote([,])dnl
  AC_SUBST([MSGFMT_015])
changequote(,)dnl
  case `$GMSGFMT --version | sed 1q | sed -e 's,^[^0-9]*,,'` in
    '' | 0.[0-9] | 0.[0-9].* | 0.1[0-4] | 0.1[0-4].*) GMSGFMT_015=: ;;
    *) GMSGFMT_015=$GMSGFMT ;;
  esac
changequote([,])dnl
  AC_SUBST([GMSGFMT_015])

  dnl Search for GNU xgettext 0.12 or newer in the PATH.
  dnl The first test excludes Solaris xgettext and early GNU xgettext versions.
  dnl The second test excludes FreeBSD xgettext.
  AM_PATH_PROG_WITH_TEST(XGETTEXT, xgettext,
    [$ac_dir/$ac_word --omit-header --copyright-holder= --msgid-bugs-address= /dev/null >&]AS_MESSAGE_LOG_FD[ 2>&1 &&
     (if $ac_dir/$ac_word --omit-header --copyright-holder= --msgid-bugs-address= /dev/null 2>&1 >/dev/null | grep usage >/dev/null; then exit 1; else exit 0; fi)],
    :)
  dnl Remove leftover from FreeBSD xgettext call.
  rm -f messages.po

  dnl Test whether it is GNU xgettext >= 0.15.
changequote(,)dnl
  case `$XGETTEXT --version | sed 1q | sed -e 's,^[^0-9]*,,'` in
    '' | 0.[0-9] | 0.[0-9].* | 0.1[0-4] | 0.1[0-4].*) XGETTEXT_015=: ;;
    *) XGETTEXT_015=$XGETTEXT ;;
  esac
changequote([,])dnl
  AC_SUBST([XGETTEXT_015])

  dnl Search for GNU msgmerge 0.11 or newer in the PATH.
  AM_PATH_PROG_WITH_TEST(MSGMERGE, msgmerge,
    [$ac_dir/$ac_word --update -q /dev/null /dev/null >&]AS_MESSAGE_LOG_FD[ 2>&1], :)

  dnl Installation directories.
  dnl Autoconf >= 2.60 defines localedir. For older versions of autoconf, we
  dnl have to define it here, so that it can be used in po/Makefile.
  test -n "$localedir" || localedir='${datadir}/locale'
  AC_SUBST([localedir])

  dnl Support for AM_XGETTEXT_OPTION.
  test -n "${XGETTEXT_EXTRA_OPTIONS+set}" || XGETTEXT_EXTRA_OPTIONS=
  AC_SUBST([XGETTEXT_EXTRA_OPTIONS])

  AC_CONFIG_COMMANDS([po-directories], [[
    for ac_file in $CONFIG_FILES; do
      # Support "outfile[:infile[:infile...]]"
      case "$ac_file" in
        *:*) ac_file=`echo "$ac_file"|sed 's%:.*%%'` ;;
      esac
      # PO directories have a Makefile.in generated from Makefile.in.in.
      case "$ac_file" in */Makefile.in)
        # Adjust a relative srcdir.
        ac_dir=`echo "$ac_file"|sed 's%/[^/][^/]*$%%'`
        ac_dir_suffix=/`echo "$ac_dir"|sed 's%^\./%%'`
        ac_dots=`echo "$ac_dir_suffix"|sed 's%/[^/]*%../%g'`
        # In autoconf-2.13 it is called $ac_given_srcdir.
        # In autoconf-2.50 it is called $srcdir.
        test -n "$ac_given_srcdir" || ac_given_srcdir="$srcdir"
        case "$ac_given_srcdir" in
          .)  top_srcdir=`echo $ac_dots|sed 's%/$%%'` ;;
          /*) top_srcdir="$ac_given_srcdir" ;;
          *)  top_srcdir="$ac_dots$ac_given_srcdir" ;;
        esac
        # Treat a directory as a PO directory if and only if it has a
        # POTFILES.in file. This allows packages to have multiple PO
        # directories under different names or in different locations.
        if test -f "$ac_given_srcdir/$ac_dir/POTFILES.in"; then
          rm -f "$ac_dir/POTFILES"
          test -n "$as_me" && echo "$as_me: creating $ac_dir/POTFILES" || echo "creating $ac_dir/POTFILES"
          gt_tab=`printf '\t'`
          cat "$ac_given_srcdir/$ac_dir/POTFILES.in" | sed -e "/^#/d" -e "/^[ ${gt_tab}]*\$/d" -e "s,.*,     $top_srcdir/& \\\\," | sed -e "\$s/\(.*\) \\\\/\1/" > "$ac_dir/POTFILES"
          POMAKEFILEDEPS="POTFILES.in"
          # ALL_LINGUAS, POFILES, UPDATEPOFILES, DUMMYPOFILES, GMOFILES depend
          # on $ac_dir but don't depend on user-specified configuration
          # parameters.
          if test -f "$ac_given_srcdir/$ac_dir/LINGUAS"; then
            # The LINGUAS file contains the set of available languages.
            if test -n "$OBSOLETE_ALL_LINGUAS"; then
              test -n "$as_me" && echo "$as_me: setting ALL_LINGUAS in configure.in is obsolete" || echo "setting ALL_LINGUAS in configure.in is obsolete"
            fi
            ALL_LINGUAS_=`sed -e "/^#/d" -e "s/#.*//" "$ac_given_srcdir/$ac_dir/LINGUAS"`
            # Hide the ALL_LINGUAS assignment from automake < 1.5.
            eval 'ALL_LINGUAS''=$ALL_LINGUAS_'
            POMAKEFILEDEPS="$POMAKEFILEDEPS LINGUAS"
          else
            # The set of available languages was given in configure.in.
            # Hide the ALL_LINGUAS assignment from automake < 1.5.
            eval 'ALL_LINGUAS''=$OBSOLETE_ALL_LINGUAS'
          fi
          # Compute POFILES
          # as      $(foreach lang, $(ALL_LINGUAS), $(srcdir)/$(lang).po)
          # Compute UPDATEPOFILES
          # as      $(foreach lang, $(ALL_LINGUAS), $(lang).po-update)
          # Compute DUMMYPOFILES
          # as      $(foreach lang, $(ALL_LINGUAS), $(lang).nop)
          # Compute GMOFILES
          # as      $(foreach lang, $(ALL_LINGUAS), $(srcdir)/$(lang).gmo)
          case "$ac_given_srcdir" in
            .) srcdirpre= ;;
            *) srcdirpre='$(srcdir)/' ;;
          esac
          POFILES=
          UPDATEPOFILES=
          DUMMYPOFILES=
          GMOFILES=
          for lang in $ALL_LINGUAS; do
            POFILES="$POFILES $srcdirpre$lang.po"
            UPDATEPOFILES="$UPDATEPOFILES $lang.po-update"
            DUMMYPOFILES="$DUMMYPOFILES $lang.nop"
            GMOFILES="$GMOFILES $srcdirpre$lang.gmo"
          done
          # CATALOGS depends on both $ac_dir and the user's LINGUAS
          # environment variable.
          INST_LINGUAS=
          if test -n "$ALL_LINGUAS"; then
            for presentlang in $ALL_LINGUAS; do
              useit=no
              if test "%UNSET%" != "$LINGUAS"; then
                desiredlanguages="$LINGUAS"
              else
                desiredlanguages="$ALL_LINGUAS"
              fi
              for desiredlang in $desiredlanguages; do
                # Use the presentlang catalog if desiredlang is
                #   a. equal to presentlang, or
                #   b. a variant of presentlang (because in this case,
                #      presentlang can be used as a fallback for messages
                #      which are not translated in the desiredlang catalog).
                case "$desiredlang" in
                  "$presentlang"*) useit=yes;;
                esac
              done
              if test $useit = yes; then
                INST_LINGUAS="$INST_LINGUAS $presentlang"
              fi
            done
          fi
          CATALOGS=
          if test -n "$INST_LINGUAS"; then
            for lang in $INST_LINGUAS; do
              CATALOGS="$CATALOGS $lang.gmo"
            done
          fi
          test -n "$as_me" && echo "$as_me: creating $ac_dir/Makefile" || echo "creating $ac_dir/Makefile"
          sed -e "/^POTFILES =/r $ac_dir/POTFILES" -e "/^# Makevars/r $ac_given_srcdir/$ac_dir/Makevars" -e "s|@POFILES@|$POFILES|g" -e "s|@UPDATEPOFILES@|$UPDATEPOFILES|g" -e "s|@DUMMYPOFILES@|$DUMMYPOFILES|g" -e "s|@GMOFILES@|$GMOFILES|g" -e "s|@CATALOGS@|$CATALOGS|g" -e "s|@POMAKEFILEDEPS@|$POMAKEFILEDEPS|g" "$ac_dir/Makefile.in" > "$ac_dir/Makefile"
          for f in "$ac_given_srcdir/$ac_dir"/Rules-*; do
            if test -f "$f"; then
              case "$f" in
                *.orig | *.bak | *~) ;;
                *) cat "$f" >> "$ac_dir/Makefile" ;;
              esac
            fi
          done
        fi
        ;;
      esac
    done]],
   [# Capture the value of obsolete ALL_LINGUAS because we need it to compute
    # POFILES, UPDATEPOFILES, DUMMYPOFILES, GMOFILES, CATALOGS. But hide it
    # from automake < 1.5.
    eval 'OBSOLETE_ALL_LINGUAS''="$ALL_LINGUAS"'
    # Capture the value of LINGUAS because we need it to compute CATALOGS.
    LINGUAS="${LINGUAS-%UNSET%}"
   ])
])
m4trace:m4/po.m4:220: -1- AC_DEFUN([AM_POSTPROCESS_PO_MAKEFILE], [
  # When this code is run, in config.status, two variables have already been
  # set:
  # - OBSOLETE_ALL_LINGUAS is the value of LINGUAS set in configure.in,
  # - LINGUAS is the value of the environment variable LINGUAS at configure
  #   time.

changequote(,)dnl
  # Adjust a relative srcdir.
  ac_dir=`echo "$ac_file"|sed 's%/[^/][^/]*$%%'`
  ac_dir_suffix=/`echo "$ac_dir"|sed 's%^\./%%'`
  ac_dots=`echo "$ac_dir_suffix"|sed 's%/[^/]*%../%g'`
  # In autoconf-2.13 it is called $ac_given_srcdir.
  # In autoconf-2.50 it is called $srcdir.
  test -n "$ac_given_srcdir" || ac_given_srcdir="$srcdir"
  case "$ac_given_srcdir" in
    .)  top_srcdir=`echo $ac_dots|sed 's%/$%%'` ;;
    /*) top_srcdir="$ac_given_srcdir" ;;
    *)  top_srcdir="$ac_dots$ac_given_srcdir" ;;
  esac

  # Find a way to echo strings without interpreting backslash.
  if test "X`(echo '\t') 2>/dev/null`" = 'X\t'; then
    gt_echo='echo'
  else
    if test "X`(printf '%s\n' '\t') 2>/dev/null`" = 'X\t'; then
      gt_echo='printf %s\n'
    else
      echo_func () {
        cat <<EOT
$*
EOT
      }
      gt_echo='echo_func'
    fi
  fi

  # A sed script that extracts the value of VARIABLE from a Makefile.
  tab=`printf '\t'`
  sed_x_variable='
# Test if the hold space is empty.
x
s/P/P/
x
ta
# Yes it was empty. Look if we have the expected variable definition.
/^['"${tab}"' ]*VARIABLE['"${tab}"' ]*=/{
  # Seen the first line of the variable definition.
  s/^['"${tab}"' ]*VARIABLE['"${tab}"' ]*=//
  ba
}
bd
:a
# Here we are processing a line from the variable definition.
# Remove comment, more precisely replace it with a space.
s/#.*$/ /
# See if the line ends in a backslash.
tb
:b
s/\\$//
# Print the line, without the trailing backslash.
p
tc
# There was no trailing backslash. The end of the variable definition is
# reached. Clear the hold space.
s/^.*$//
x
bd
:c
# A trailing backslash means that the variable definition continues in the
# next line. Put a nonempty string into the hold space to indicate this.
s/^.*$/P/
x
:d
'
changequote([,])dnl

  # Set POTFILES to the value of the Makefile variable POTFILES.
  sed_x_POTFILES=`$gt_echo "$sed_x_variable" | sed -e '/^ *#/d' -e 's/VARIABLE/POTFILES/g'`
  POTFILES=`sed -n -e "$sed_x_POTFILES" < "$ac_file"`
  # Compute POTFILES_DEPS as
  #   $(foreach file, $(POTFILES), $(top_srcdir)/$(file))
  POTFILES_DEPS=
  for file in $POTFILES; do
    POTFILES_DEPS="$POTFILES_DEPS "'$(top_srcdir)/'"$file"
  done
  POMAKEFILEDEPS=""

  if test -n "$OBSOLETE_ALL_LINGUAS"; then
    test -n "$as_me" && echo "$as_me: setting ALL_LINGUAS in configure.in is obsolete" || echo "setting ALL_LINGUAS in configure.in is obsolete"
  fi
  if test -f "$ac_given_srcdir/$ac_dir/LINGUAS"; then
    # The LINGUAS file contains the set of available languages.
    ALL_LINGUAS_=`sed -e "/^#/d" -e "s/#.*//" "$ac_given_srcdir/$ac_dir/LINGUAS"`
    POMAKEFILEDEPS="$POMAKEFILEDEPS LINGUAS"
  else
    # Set ALL_LINGUAS to the value of the Makefile variable LINGUAS.
    sed_x_LINGUAS=`$gt_echo "$sed_x_variable" | sed -e '/^ *#/d' -e 's/VARIABLE/LINGUAS/g'`
    ALL_LINGUAS_=`sed -n -e "$sed_x_LINGUAS" < "$ac_file"`
  fi
  # Hide the ALL_LINGUAS assignment from automake < 1.5.
  eval 'ALL_LINGUAS''=$ALL_LINGUAS_'
  # Compute POFILES
  # as      $(foreach lang, $(ALL_LINGUAS), $(srcdir)/$(lang).po)
  # Compute UPDATEPOFILES
  # as      $(foreach lang, $(ALL_LINGUAS), $(lang).po-update)
  # Compute DUMMYPOFILES
  # as      $(foreach lang, $(ALL_LINGUAS), $(lang).nop)
  # Compute GMOFILES
  # as      $(foreach lang, $(ALL_LINGUAS), $(srcdir)/$(lang).gmo)
  # Compute PROPERTIESFILES
  # as      $(foreach lang, $(ALL_LINGUAS), $(top_srcdir)/$(DOMAIN)_$(lang).properties)
  # Compute CLASSFILES
  # as      $(foreach lang, $(ALL_LINGUAS), $(top_srcdir)/$(DOMAIN)_$(lang).class)
  # Compute QMFILES
  # as      $(foreach lang, $(ALL_LINGUAS), $(srcdir)/$(lang).qm)
  # Compute MSGFILES
  # as      $(foreach lang, $(ALL_LINGUAS), $(srcdir)/$(frob $(lang)).msg)
  # Compute RESOURCESDLLFILES
  # as      $(foreach lang, $(ALL_LINGUAS), $(srcdir)/$(frob $(lang))/$(DOMAIN).resources.dll)
  case "$ac_given_srcdir" in
    .) srcdirpre= ;;
    *) srcdirpre='$(srcdir)/' ;;
  esac
  POFILES=
  UPDATEPOFILES=
  DUMMYPOFILES=
  GMOFILES=
  PROPERTIESFILES=
  CLASSFILES=
  QMFILES=
  MSGFILES=
  RESOURCESDLLFILES=
  for lang in $ALL_LINGUAS; do
    POFILES="$POFILES $srcdirpre$lang.po"
    UPDATEPOFILES="$UPDATEPOFILES $lang.po-update"
    DUMMYPOFILES="$DUMMYPOFILES $lang.nop"
    GMOFILES="$GMOFILES $srcdirpre$lang.gmo"
    PROPERTIESFILES="$PROPERTIESFILES \$(top_srcdir)/\$(DOMAIN)_$lang.properties"
    CLASSFILES="$CLASSFILES \$(top_srcdir)/\$(DOMAIN)_$lang.class"
    QMFILES="$QMFILES $srcdirpre$lang.qm"
    frobbedlang=`echo $lang | sed -e 's/\..*$//' -e 'y/ABCDEFGHIJKLMNOPQRSTUVWXYZ/abcdefghijklmnopqrstuvwxyz/'`
    MSGFILES="$MSGFILES $srcdirpre$frobbedlang.msg"
    frobbedlang=`echo $lang | sed -e 's/_/-/g' -e 's/^sr-CS/sr-SP/' -e 's/@latin$/-Latn/' -e 's/@cyrillic$/-Cyrl/' -e 's/^sr-SP$/sr-SP-Latn/' -e 's/^uz-UZ$/uz-UZ-Latn/'`
    RESOURCESDLLFILES="$RESOURCESDLLFILES $srcdirpre$frobbedlang/\$(DOMAIN).resources.dll"
  done
  # CATALOGS depends on both $ac_dir and the user's LINGUAS
  # environment variable.
  INST_LINGUAS=
  if test -n "$ALL_LINGUAS"; then
    for presentlang in $ALL_LINGUAS; do
      useit=no
      if test "%UNSET%" != "$LINGUAS"; then
        desiredlanguages="$LINGUAS"
      else
        desiredlanguages="$ALL_LINGUAS"
      fi
      for desiredlang in $desiredlanguages; do
        # Use the presentlang catalog if desiredlang is
        #   a. equal to presentlang, or
        #   b. a variant of presentlang (because in this case,
        #      presentlang can be used as a fallback for messages
        #      which are not translated in the desiredlang catalog).
        case "$desiredlang" in
          "$presentlang"*) useit=yes;;
        esac
      done
      if test $useit = yes; then
        INST_LINGUAS="$INST_LINGUAS $presentlang"
      fi
    done
  fi
  CATALOGS=
  JAVACATALOGS=
  QTCATALOGS=
  TCLCATALOGS=
  CSHARPCATALOGS=
  if test -n "$INST_LINGUAS"; then
    for lang in $INST_LINGUAS; do
      CATALOGS="$CATALOGS $lang.gmo"
      JAVACATALOGS="$JAVACATALOGS \$(DOMAIN)_$lang.properties"
      QTCATALOGS="$QTCATALOGS $lang.qm"
      frobbedlang=`echo $lang | sed -e 's/\..*$//' -e 'y/ABCDEFGHIJKLMNOPQRSTUVWXYZ/abcdefghijklmnopqrstuvwxyz/'`
      TCLCATALOGS="$TCLCATALOGS $frobbedlang.msg"
      frobbedlang=`echo $lang | sed -e 's/_/-/g' -e 's/^sr-CS/sr-SP/' -e 's/@latin$/-Latn/' -e 's/@cyrillic$/-Cyrl/' -e 's/^sr-SP$/sr-SP-Latn/' -e 's/^uz-UZ$/uz-UZ-Latn/'`
      CSHARPCATALOGS="$CSHARPCATALOGS $frobbedlang/\$(DOMAIN).resources.dll"
    done
  fi

  sed -e "s|@POTFILES_DEPS@|$POTFILES_DEPS|g" -e "s|@POFILES@|$POFILES|g" -e "s|@UPDATEPOFILES@|$UPDATEPOFILES|g" -e "s|@DUMMYPOFILES@|$DUMMYPOFILES|g" -e "s|@GMOFILES@|$GMOFILES|g" -e "s|@PROPERTIESFILES@|$PROPERTIESFILES|g" -e "s|@CLASSFILES@|$CLASSFILES|g" -e "s|@QMFILES@|$QMFILES|g" -e "s|@MSGFILES@|$MSGFILES|g" -e "s|@RESOURCESDLLFILES@|$RESOURCESDLLFILES|g" -e "s|@CATALOGS@|$CATALOGS|g" -e "s|@JAVACATALOGS@|$JAVACATALOGS|g" -e "s|@QTCATALOGS@|$QTCATALOGS|g" -e "s|@TCLCATALOGS@|$TCLCATALOGS|g" -e "s|@CSHARPCATALOGS@|$CSHARPCATALOGS|g" -e 's,^#distdir:,distdir:,' < "$ac_file" > "$ac_file.tmp"
  tab=`printf '\t'`
  if grep -l '@TCLCATALOGS@' "$ac_file" > /dev/null; then
    # Add dependencies that cannot be formulated as a simple suffix rule.
    for lang in $ALL_LINGUAS; do
      frobbedlang=`echo $lang | sed -e 's/\..*$//' -e 'y/ABCDEFGHIJKLMNOPQRSTUVWXYZ/abcdefghijklmnopqrstuvwxyz/'`
      cat >> "$ac_file.tmp" <<EOF
$frobbedlang.msg: $lang.po
${tab}@echo "\$(MSGFMT) -c --tcl -d \$(srcdir) -l $lang $srcdirpre$lang.po"; \
${tab}\$(MSGFMT) -c --tcl -d "\$(srcdir)" -l $lang $srcdirpre$lang.po || { rm -f "\$(srcdir)/$frobbedlang.msg"; exit 1; }
EOF
    done
  fi
  if grep -l '@CSHARPCATALOGS@' "$ac_file" > /dev/null; then
    # Add dependencies that cannot be formulated as a simple suffix rule.
    for lang in $ALL_LINGUAS; do
      frobbedlang=`echo $lang | sed -e 's/_/-/g' -e 's/^sr-CS/sr-SP/' -e 's/@latin$/-Latn/' -e 's/@cyrillic$/-Cyrl/' -e 's/^sr-SP$/sr-SP-Latn/' -e 's/^uz-UZ$/uz-UZ-Latn/'`
      cat >> "$ac_file.tmp" <<EOF
$frobbedlang/\$(DOMAIN).resources.dll: $lang.po
${tab}@echo "\$(MSGFMT) -c --csharp -d \$(srcdir) -l $lang $srcdirpre$lang.po -r \$(DOMAIN)"; \
${tab}\$(MSGFMT) -c --csharp -d "\$(srcdir)" -l $lang $srcdirpre$lang.po -r "\$(DOMAIN)" || { rm -f "\$(srcdir)/$frobbedlang.msg"; exit 1; }
EOF
    done
  fi
  if test -n "$POMAKEFILEDEPS"; then
    cat >> "$ac_file.tmp" <<EOF
Makefile: $POMAKEFILEDEPS
EOF
  fi
  mv "$ac_file.tmp" "$ac_file"
])
m4trace:m4/po.m4:443: -1- AC_DEFUN([AM_XGETTEXT_OPTION_INIT], [
  XGETTEXT_EXTRA_OPTIONS=
])
m4trace:m4/po.m4:449: -1- AC_DEFUN([AM_XGETTEXT_OPTION], [
  AC_REQUIRE([AM_XGETTEXT_OPTION_INIT])
  XGETTEXT_EXTRA_OPTIONS="$XGETTEXT_EXTRA_OPTIONS $1"
])
m4trace:m4/printf-posix.m4:11: -1- AC_DEFUN([gt_PRINTF_POSIX], [
  AC_REQUIRE([AC_PROG_CC])
  AC_CACHE_CHECK([whether printf() supports POSIX/XSI format strings],
    gt_cv_func_printf_posix,
    [
      AC_RUN_IFELSE(
        [AC_LANG_SOURCE([[
#include <stdio.h>
#include <string.h>
/* The string "%2$d %1$d", with dollar characters protected from the shell's
   dollar expansion (possibly an autoconf bug).  */
static char format[] = { '%', '2', '$', 'd', ' ', '%', '1', '$', 'd', '\0' };
static char buf[100];
int main ()
{
  sprintf (buf, format, 33, 55);
  return (strcmp (buf, "55 33") != 0);
}]])],
        [gt_cv_func_printf_posix=yes],
        [gt_cv_func_printf_posix=no],
        [
          AC_EGREP_CPP([notposix], [
#if defined __NetBSD__ || defined __BEOS__ || defined _MSC_VER || defined __MINGW32__ || defined __CYGWIN__
  notposix
#endif
            ],
            [gt_cv_func_printf_posix="guessing no"],
            [gt_cv_func_printf_posix="guessing yes"])
        ])
    ])
  case $gt_cv_func_printf_posix in
    *yes)
      AC_DEFINE([HAVE_POSIX_PRINTF], [1],
        [Define if your printf() function supports format strings with positions.])
      ;;
  esac
])
m4trace:m4/progtest.m4:25: -1- AC_DEFUN([AM_PATH_PROG_WITH_TEST], [
# Prepare PATH_SEPARATOR.
# The user is always right.
if test "${PATH_SEPARATOR+set}" != set; then
  # Determine PATH_SEPARATOR by trying to find /bin/sh in a PATH which
  # contains only /bin. Note that ksh looks also at the FPATH variable,
  # so we have to set that as well for the test.
  PATH_SEPARATOR=:
  (PATH='/bin;/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 \
    && { (PATH='/bin:/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 \
           || PATH_SEPARATOR=';'
       }
fi

# Find out how to test for executable files. Don't use a zero-byte file,
# as systems may use methods other than mode bits to determine executability.
cat >conf$$.file <<_ASEOF
#! /bin/sh
exit 0
_ASEOF
chmod +x conf$$.file
if test -x conf$$.file >/dev/null 2>&1; then
  ac_executable_p="test -x"
else
  ac_executable_p="test -f"
fi
rm -f conf$$.file

# Extract the first word of "$2", so it can be a program name with args.
set dummy $2; ac_word=[$]2
AC_MSG_CHECKING([for $ac_word])
AC_CACHE_VAL([ac_cv_path_$1],
[case "[$]$1" in
  [[\\/]]* | ?:[[\\/]]*)
    ac_cv_path_$1="[$]$1" # Let the user override the test with a path.
    ;;
  *)
    ac_save_IFS="$IFS"; IFS=$PATH_SEPARATOR
    for ac_dir in ifelse([$5], , $PATH, [$5]); do
      IFS="$ac_save_IFS"
      test -z "$ac_dir" && ac_dir=.
      for ac_exec_ext in '' $ac_executable_extensions; do
        if $ac_executable_p "$ac_dir/$ac_word$ac_exec_ext"; then
          echo "$as_me: trying $ac_dir/$ac_word..." >&AS_MESSAGE_LOG_FD
          if [$3]; then
            ac_cv_path_$1="$ac_dir/$ac_word$ac_exec_ext"
            break 2
          fi
        fi
      done
    done
    IFS="$ac_save_IFS"
dnl If no 4th arg is given, leave the cache variable unset,
dnl so AC_PATH_PROGS will keep looking.
ifelse([$4], , , [  test -z "[$]ac_cv_path_$1" && ac_cv_path_$1="$4"
])dnl
    ;;
esac])dnl
$1="$ac_cv_path_$1"
if test ifelse([$4], , [-n "[$]$1"], ["[$]$1" != "$4"]); then
  AC_MSG_RESULT([$][$1])
else
  AC_MSG_RESULT([no])
fi
AC_SUBST([$1])dnl
])
m4trace:m4/size_max.m4:9: -1- AC_DEFUN([gl_SIZE_MAX], [
  AC_CHECK_HEADERS([stdint.h])
  dnl First test whether the system already has SIZE_MAX.
  AC_CACHE_CHECK([for SIZE_MAX], [gl_cv_size_max], [
    gl_cv_size_max=
    AC_EGREP_CPP([Found it], [
#include <limits.h>
#if HAVE_STDINT_H
#include <stdint.h>
#endif
#ifdef SIZE_MAX
Found it
#endif
], [gl_cv_size_max=yes])
    if test -z "$gl_cv_size_max"; then
      dnl Define it ourselves. Here we assume that the type 'size_t' is not wider
      dnl than the type 'unsigned long'. Try hard to find a definition that can
      dnl be used in a preprocessor #if, i.e. doesn't contain a cast.
      AC_COMPUTE_INT([size_t_bits_minus_1], [sizeof (size_t) * CHAR_BIT - 1],
        [#include <stddef.h>
#include <limits.h>], [size_t_bits_minus_1=])
      AC_COMPUTE_INT([fits_in_uint], [sizeof (size_t) <= sizeof (unsigned int)],
        [#include <stddef.h>], [fits_in_uint=])
      if test -n "$size_t_bits_minus_1" && test -n "$fits_in_uint"; then
        if test $fits_in_uint = 1; then
          dnl Even though SIZE_MAX fits in an unsigned int, it must be of type
          dnl 'unsigned long' if the type 'size_t' is the same as 'unsigned long'.
          AC_COMPILE_IFELSE(
            [AC_LANG_PROGRAM(
               [[#include <stddef.h>
                 extern size_t foo;
                 extern unsigned long foo;
               ]],
               [[]])],
            [fits_in_uint=0])
        fi
        dnl We cannot use 'expr' to simplify this expression, because 'expr'
        dnl works only with 'long' integers in the host environment, while we
        dnl might be cross-compiling from a 32-bit platform to a 64-bit platform.
        if test $fits_in_uint = 1; then
          gl_cv_size_max="(((1U << $size_t_bits_minus_1) - 1) * 2 + 1)"
        else
          gl_cv_size_max="(((1UL << $size_t_bits_minus_1) - 1) * 2 + 1)"
        fi
      else
        dnl Shouldn't happen, but who knows...
        gl_cv_size_max='((size_t)~(size_t)0)'
      fi
    fi
  ])
  if test "$gl_cv_size_max" != yes; then
    AC_DEFINE_UNQUOTED([SIZE_MAX], [$gl_cv_size_max],
      [Define as the maximum value of type 'size_t', if the system doesn't define it.])
  fi
  dnl Don't redefine SIZE_MAX in config.h if config.h is re-included after
  dnl <stdint.h>. Remember that the #undef in AH_VERBATIM gets replaced with
  dnl #define by AC_DEFINE_UNQUOTED.
  AH_VERBATIM([SIZE_MAX],
[/* Define as the maximum value of type 'size_t', if the system doesn't define
   it. */
#ifndef SIZE_MAX
# undef SIZE_MAX
#endif])
])
m4trace:m4/stdint_h.m4:12: -1- AC_DEFUN([gl_AC_HEADER_STDINT_H], [
  AC_CACHE_CHECK([for stdint.h], [gl_cv_header_stdint_h],
    [AC_COMPILE_IFELSE(
       [AC_LANG_PROGRAM(
          [[#include <sys/types.h>
            #include <stdint.h>]],
          [[uintmax_t i = (uintmax_t) -1; return !i;]])],
       [gl_cv_header_stdint_h=yes],
       [gl_cv_header_stdint_h=no])])
  if test $gl_cv_header_stdint_h = yes; then
    AC_DEFINE_UNQUOTED([HAVE_STDINT_H_WITH_UINTMAX], [1],
      [Define if <stdint.h> exists, doesn't clash with <sys/types.h>,
       and declares uintmax_t. ])
  fi
])
m4trace:m4/threadlib.m4:29: -1- AC_DEFUN([gl_THREADLIB_EARLY], [
  AC_REQUIRE([gl_THREADLIB_EARLY_BODY])
])
m4trace:m4/threadlib.m4:36: -1- AC_DEFUN([gl_THREADLIB_EARLY_BODY], [
  dnl Ordering constraints: This macro modifies CPPFLAGS in a way that
  dnl influences the result of the autoconf tests that test for *_unlocked
  dnl declarations, on AIX 5 at least. Therefore it must come early.
  AC_BEFORE([$0], [gl_FUNC_GLIBC_UNLOCKED_IO])dnl
  AC_BEFORE([$0], [gl_ARGP])dnl

  AC_REQUIRE([AC_CANONICAL_HOST])
  dnl _GNU_SOURCE is needed for pthread_rwlock_t on glibc systems.
  dnl AC_USE_SYSTEM_EXTENSIONS was introduced in autoconf 2.60 and obsoletes
  dnl AC_GNU_SOURCE.
  m4_ifdef([AC_USE_SYSTEM_EXTENSIONS],
    [AC_REQUIRE([AC_USE_SYSTEM_EXTENSIONS])],
    [AC_REQUIRE([AC_GNU_SOURCE])])
  dnl Check for multithreading.
  m4_ifdef([gl_THREADLIB_DEFAULT_NO],
    [m4_divert_text([DEFAULTS], [gl_use_threads_default=no])],
    [m4_divert_text([DEFAULTS], [gl_use_threads_default=])])
  AC_ARG_ENABLE([threads],
AC_HELP_STRING([--enable-threads={posix|solaris|pth|windows}], [specify multithreading API])m4_ifdef([gl_THREADLIB_DEFAULT_NO], [], [
AC_HELP_STRING([--disable-threads], [build without multithread safety])]),
    [gl_use_threads=$enableval],
    [if test -n "$gl_use_threads_default"; then
       gl_use_threads="$gl_use_threads_default"
     else
changequote(,)dnl
       case "$host_os" in
         dnl Disable multithreading by default on OSF/1, because it interferes
         dnl with fork()/exec(): When msgexec is linked with -lpthread, its
         dnl child process gets an endless segmentation fault inside execvp().
         dnl Disable multithreading by default on Cygwin 1.5.x, because it has
         dnl bugs that lead to endless loops or crashes. See
         dnl <http://cygwin.com/ml/cygwin/2009-08/msg00283.html>.
         osf*) gl_use_threads=no ;;
         cygwin*)
               case `uname -r` in
                 1.[0-5].*) gl_use_threads=no ;;
                 *)         gl_use_threads=yes ;;
               esac
               ;;
         *)    gl_use_threads=yes ;;
       esac
changequote([,])dnl
     fi
    ])
  if test "$gl_use_threads" = yes || test "$gl_use_threads" = posix; then
    # For using <pthread.h>:
    case "$host_os" in
      osf*)
        # On OSF/1, the compiler needs the flag -D_REENTRANT so that it
        # groks <pthread.h>. cc also understands the flag -pthread, but
        # we don't use it because 1. gcc-2.95 doesn't understand -pthread,
        # 2. putting a flag into CPPFLAGS that has an effect on the linker
        # causes the AC_LINK_IFELSE test below to succeed unexpectedly,
        # leading to wrong values of LIBTHREAD and LTLIBTHREAD.
        CPPFLAGS="$CPPFLAGS -D_REENTRANT"
        ;;
    esac
    # Some systems optimize for single-threaded programs by default, and
    # need special flags to disable these optimizations. For example, the
    # definition of 'errno' in <errno.h>.
    case "$host_os" in
      aix* | freebsd*) CPPFLAGS="$CPPFLAGS -D_THREAD_SAFE" ;;
      solaris*) CPPFLAGS="$CPPFLAGS -D_REENTRANT" ;;
    esac
  fi
])
m4trace:m4/threadlib.m4:107: -1- AC_DEFUN([gl_THREADLIB_BODY], [
  AC_REQUIRE([gl_THREADLIB_EARLY_BODY])
  gl_threads_api=none
  LIBTHREAD=
  LTLIBTHREAD=
  LIBMULTITHREAD=
  LTLIBMULTITHREAD=
  if test "$gl_use_threads" != no; then
    dnl Check whether the compiler and linker support weak declarations.
    AC_CACHE_CHECK([whether imported symbols can be declared weak],
      [gl_cv_have_weak],
      [gl_cv_have_weak=no
       dnl First, test whether the compiler accepts it syntactically.
       AC_LINK_IFELSE(
         [AC_LANG_PROGRAM(
            [[extern void xyzzy ();
#pragma weak xyzzy]],
            [[xyzzy();]])],
         [gl_cv_have_weak=maybe])
       if test $gl_cv_have_weak = maybe; then
         dnl Second, test whether it actually works. On Cygwin 1.7.2, with
         dnl gcc 4.3, symbols declared weak always evaluate to the address 0.
         AC_RUN_IFELSE(
           [AC_LANG_SOURCE([[
#include <stdio.h>
#pragma weak fputs
int main ()
{
  return (fputs == NULL);
}]])],
           [gl_cv_have_weak=yes],
           [gl_cv_have_weak=no],
           [dnl When cross-compiling, assume that only ELF platforms support
            dnl weak symbols.
            AC_EGREP_CPP([Extensible Linking Format],
              [#ifdef __ELF__
               Extensible Linking Format
               #endif
              ],
              [gl_cv_have_weak="guessing yes"],
              [gl_cv_have_weak="guessing no"])
           ])
       fi
      ])
    if test "$gl_use_threads" = yes || test "$gl_use_threads" = posix; then
      # On OSF/1, the compiler needs the flag -pthread or -D_REENTRANT so that
      # it groks <pthread.h>. It's added above, in gl_THREADLIB_EARLY_BODY.
      AC_CHECK_HEADER([pthread.h],
        [gl_have_pthread_h=yes], [gl_have_pthread_h=no])
      if test "$gl_have_pthread_h" = yes; then
        # Other possible tests:
        #   -lpthreads (FSU threads, PCthreads)
        #   -lgthreads
        gl_have_pthread=
        # Test whether both pthread_mutex_lock and pthread_mutexattr_init exist
        # in libc. IRIX 6.5 has the first one in both libc and libpthread, but
        # the second one only in libpthread, and lock.c needs it.
        #
        # If -pthread works, prefer it to -lpthread, since Ubuntu 14.04
        # needs -pthread for some reason.  See:
        # http://lists.gnu.org/archive/html/bug-gnulib/2014-09/msg00023.html
        save_LIBS=$LIBS
        for gl_pthread in '' '-pthread'; do
          LIBS="$LIBS $gl_pthread"
          AC_LINK_IFELSE(
            [AC_LANG_PROGRAM(
               [[#include <pthread.h>
                 pthread_mutex_t m;
                 pthread_mutexattr_t ma;
               ]],
               [[pthread_mutex_lock (&m);
                 pthread_mutexattr_init (&ma);]])],
            [gl_have_pthread=yes
             LIBTHREAD=$gl_pthread LTLIBTHREAD=$gl_pthread
             LIBMULTITHREAD=$gl_pthread LTLIBMULTITHREAD=$gl_pthread])
          LIBS=$save_LIBS
          test -n "$gl_have_pthread" && break
        done

        # Test for libpthread by looking for pthread_kill. (Not pthread_self,
        # since it is defined as a macro on OSF/1.)
        if test -n "$gl_have_pthread" && test -z "$LIBTHREAD"; then
          # The program links fine without libpthread. But it may actually
          # need to link with libpthread in order to create multiple threads.
          AC_CHECK_LIB([pthread], [pthread_kill],
            [LIBMULTITHREAD=-lpthread LTLIBMULTITHREAD=-lpthread
             # On Solaris and HP-UX, most pthread functions exist also in libc.
             # Therefore pthread_in_use() needs to actually try to create a
             # thread: pthread_create from libc will fail, whereas
             # pthread_create will actually create a thread.
             case "$host_os" in
               solaris* | hpux*)
                 AC_DEFINE([PTHREAD_IN_USE_DETECTION_HARD], [1],
                   [Define if the pthread_in_use() detection is hard.])
             esac
            ])
        elif test -z "$gl_have_pthread"; then
          # Some library is needed. Try libpthread and libc_r.
          AC_CHECK_LIB([pthread], [pthread_kill],
            [gl_have_pthread=yes
             LIBTHREAD=-lpthread LTLIBTHREAD=-lpthread
             LIBMULTITHREAD=-lpthread LTLIBMULTITHREAD=-lpthread])
          if test -z "$gl_have_pthread"; then
            # For FreeBSD 4.
            AC_CHECK_LIB([c_r], [pthread_kill],
              [gl_have_pthread=yes
               LIBTHREAD=-lc_r LTLIBTHREAD=-lc_r
               LIBMULTITHREAD=-lc_r LTLIBMULTITHREAD=-lc_r])
          fi
        fi
        if test -n "$gl_have_pthread"; then
          gl_threads_api=posix
          AC_DEFINE([USE_POSIX_THREADS], [1],
            [Define if the POSIX multithreading library can be used.])
          if test -n "$LIBMULTITHREAD" || test -n "$LTLIBMULTITHREAD"; then
            if case "$gl_cv_have_weak" in *yes) true;; *) false;; esac; then
              AC_DEFINE([USE_POSIX_THREADS_WEAK], [1],
                [Define if references to the POSIX multithreading library should be made weak.])
              LIBTHREAD=
              LTLIBTHREAD=
            fi
          fi
        fi
      fi
    fi
    if test -z "$gl_have_pthread"; then
      if test "$gl_use_threads" = yes || test "$gl_use_threads" = solaris; then
        gl_have_solaristhread=
        gl_save_LIBS="$LIBS"
        LIBS="$LIBS -lthread"
        AC_LINK_IFELSE(
          [AC_LANG_PROGRAM(
             [[
#include <thread.h>
#include <synch.h>
             ]],
             [[thr_self();]])],
          [gl_have_solaristhread=yes])
        LIBS="$gl_save_LIBS"
        if test -n "$gl_have_solaristhread"; then
          gl_threads_api=solaris
          LIBTHREAD=-lthread
          LTLIBTHREAD=-lthread
          LIBMULTITHREAD="$LIBTHREAD"
          LTLIBMULTITHREAD="$LTLIBTHREAD"
          AC_DEFINE([USE_SOLARIS_THREADS], [1],
            [Define if the old Solaris multithreading library can be used.])
          if case "$gl_cv_have_weak" in *yes) true;; *) false;; esac; then
            AC_DEFINE([USE_SOLARIS_THREADS_WEAK], [1],
              [Define if references to the old Solaris multithreading library should be made weak.])
            LIBTHREAD=
            LTLIBTHREAD=
          fi
        fi
      fi
    fi
    if test "$gl_use_threads" = pth; then
      gl_save_CPPFLAGS="$CPPFLAGS"
      AC_LIB_LINKFLAGS([pth])
      gl_have_pth=
      gl_save_LIBS="$LIBS"
      LIBS="$LIBS $LIBPTH"
      AC_LINK_IFELSE(
        [AC_LANG_PROGRAM([[#include <pth.h>]], [[pth_self();]])],
        [gl_have_pth=yes])
      LIBS="$gl_save_LIBS"
      if test -n "$gl_have_pth"; then
        gl_threads_api=pth
        LIBTHREAD="$LIBPTH"
        LTLIBTHREAD="$LTLIBPTH"
        LIBMULTITHREAD="$LIBTHREAD"
        LTLIBMULTITHREAD="$LTLIBTHREAD"
        AC_DEFINE([USE_PTH_THREADS], [1],
          [Define if the GNU Pth multithreading library can be used.])
        if test -n "$LIBMULTITHREAD" || test -n "$LTLIBMULTITHREAD"; then
          if case "$gl_cv_have_weak" in *yes) true;; *) false;; esac; then
            AC_DEFINE([USE_PTH_THREADS_WEAK], [1],
              [Define if references to the GNU Pth multithreading library should be made weak.])
            LIBTHREAD=
            LTLIBTHREAD=
          fi
        fi
      else
        CPPFLAGS="$gl_save_CPPFLAGS"
      fi
    fi
    if test -z "$gl_have_pthread"; then
      case "$gl_use_threads" in
        yes | windows | win32) # The 'win32' is for backward compatibility.
          if { case "$host_os" in
                 mingw*) true;;
                 *) false;;
               esac
             }; then
            gl_threads_api=windows
            AC_DEFINE([USE_WINDOWS_THREADS], [1],
              [Define if the native Windows multithreading API can be used.])
          fi
          ;;
      esac
    fi
  fi
  AC_MSG_CHECKING([for multithread API to use])
  AC_MSG_RESULT([$gl_threads_api])
  AC_SUBST([LIBTHREAD])
  AC_SUBST([LTLIBTHREAD])
  AC_SUBST([LIBMULTITHREAD])
  AC_SUBST([LTLIBMULTITHREAD])
])
m4trace:m4/threadlib.m4:318: -1- AC_DEFUN([gl_THREADLIB], [
  AC_REQUIRE([gl_THREADLIB_EARLY])
  AC_REQUIRE([gl_THREADLIB_BODY])
])
m4trace:m4/threadlib.m4:331: -1- AC_DEFUN([gl_DISABLE_THREADS], [
  m4_divert_text([INIT_PREPARE], [gl_use_threads_default=no])
])
m4trace:m4/uintmax_t.m4:14: -1- AC_DEFUN([gl_AC_TYPE_UINTMAX_T], [
  AC_REQUIRE([gl_AC_HEADER_INTTYPES_H])
  AC_REQUIRE([gl_AC_HEADER_STDINT_H])
  if test $gl_cv_header_inttypes_h = no && test $gl_cv_header_stdint_h = no; then
    AC_REQUIRE([AC_TYPE_UNSIGNED_LONG_LONG_INT])
    test $ac_cv_type_unsigned_long_long_int = yes \
      && ac_type='unsigned long long' \
      || ac_type='unsigned long'
    AC_DEFINE_UNQUOTED([uintmax_t], [$ac_type],
      [Define to unsigned long or unsigned long long
       if <stdint.h> and <inttypes.h> don't define.])
  else
    AC_DEFINE([HAVE_UINTMAX_T], [1],
      [Define if you have the 'uintmax_t' type in <stdint.h> or <inttypes.h>.])
  fi
])
m4trace:m4/vapigen.m4:24: -1- AC_DEFUN([VAPIGEN_CHECK], [
  AS_IF([test "x$3" != "xyes"], [
      m4_provide_if([GOBJECT_INTROSPECTION_CHECK], [], [
          m4_provide_if([GOBJECT_INTROSPECTION_REQUIRE], [], [
              AC_MSG_ERROR([[You must call GOBJECT_INTROSPECTION_CHECK or GOBJECT_INTROSPECTION_REQUIRE before using VAPIGEN_CHECK unless using the FOUND_INTROSPECTION argument is "yes"]])
            ])
        ])
    ])

  AC_ARG_ENABLE([vala],
    [AS_HELP_STRING([--enable-vala[=@<:@no/auto/yes@:>@]],[build Vala bindings @<:@default=]ifelse($4,,auto,$4)[@:>@])],,[
      AS_IF([test "x$4" = "x"], [
          enable_vala=auto
        ], [
          enable_vala=$4
        ])
    ])

  AS_CASE([$enable_vala], [no], [enable_vala=no],
      [yes], [
        AS_IF([test "x$3" != "xyes" -a "x$found_introspection" != "xyes"], [
            AC_MSG_ERROR([Vala bindings require GObject Introspection])
          ])
      ], [auto], [
        AS_IF([test "x$3" != "xyes" -a "x$found_introspection" != "xyes"], [
            enable_vala=no
          ])
      ], [
        AC_MSG_ERROR([Invalid argument passed to --enable-vala, should be one of @<:@no/auto/yes@:>@])
      ])

  AS_IF([test "x$2" = "x"], [
      vapigen_pkg_name=vapigen
    ], [
      vapigen_pkg_name=vapigen-$2
    ])
  AS_IF([test "x$1" = "x"], [
      vapigen_pkg="$vapigen_pkg_name"
    ], [
      vapigen_pkg="$vapigen_pkg_name >= $1"
    ])

  PKG_PROG_PKG_CONFIG

  PKG_CHECK_EXISTS([$vapigen_pkg], [
      AS_IF([test "$enable_vala" = "auto"], [
          enable_vala=yes
        ])
    ], [
      AS_CASE([$enable_vala], [yes], [
          AC_MSG_ERROR([$vapigen_pkg not found])
        ], [auto], [
          enable_vala=no
        ])
    ])

  AC_MSG_CHECKING([for vapigen])

  AS_CASE([$enable_vala],
    [yes], [
      VAPIGEN=`$PKG_CONFIG --variable=vapigen $vapigen_pkg_name`
      VAPIGEN_MAKEFILE=`$PKG_CONFIG --variable=datadir $vapigen_pkg_name`/vala/Makefile.vapigen
      AS_IF([test "x$2" = "x"], [
          VAPIGEN_VAPIDIR=`$PKG_CONFIG --variable=vapidir $vapigen_pkg_name`
        ], [
          VAPIGEN_VAPIDIR=`$PKG_CONFIG --variable=vapidir_versioned $vapigen_pkg_name`
        ])
    ])

  AC_MSG_RESULT([$enable_vala])

  AC_SUBST([VAPIGEN])
  AC_SUBST([VAPIGEN_VAPIDIR])
  AC_SUBST([VAPIGEN_MAKEFILE])

  AM_CONDITIONAL(ENABLE_VAPIGEN, test "x$enable_vala" = "xyes")
])
m4trace:m4/visibility.m4:23: -1- AC_DEFUN([gl_VISIBILITY], [
  AC_REQUIRE([AC_PROG_CC])
  CFLAG_VISIBILITY=
  HAVE_VISIBILITY=0
  if test -n "$GCC"; then
    dnl First, check whether -Werror can be added to the command line, or
    dnl whether it leads to an error because of some other option that the
    dnl user has put into $CC $CFLAGS $CPPFLAGS.
    AC_MSG_CHECKING([whether the -Werror option is usable])
    AC_CACHE_VAL([gl_cv_cc_vis_werror], [
      gl_save_CFLAGS="$CFLAGS"
      CFLAGS="$CFLAGS -Werror"
      AC_COMPILE_IFELSE(
        [AC_LANG_PROGRAM([[]], [[]])],
        [gl_cv_cc_vis_werror=yes],
        [gl_cv_cc_vis_werror=no])
      CFLAGS="$gl_save_CFLAGS"])
    AC_MSG_RESULT([$gl_cv_cc_vis_werror])
    dnl Now check whether visibility declarations are supported.
    AC_MSG_CHECKING([for simple visibility declarations])
    AC_CACHE_VAL([gl_cv_cc_visibility], [
      gl_save_CFLAGS="$CFLAGS"
      CFLAGS="$CFLAGS -fvisibility=hidden"
      dnl We use the option -Werror and a function dummyfunc, because on some
      dnl platforms (Cygwin 1.7) the use of -fvisibility triggers a warning
      dnl "visibility attribute not supported in this configuration; ignored"
      dnl at the first function definition in every compilation unit, and we
      dnl don't want to use the option in this case.
      if test $gl_cv_cc_vis_werror = yes; then
        CFLAGS="$CFLAGS -Werror"
      fi
      AC_COMPILE_IFELSE(
        [AC_LANG_PROGRAM(
           [[extern __attribute__((__visibility__("hidden"))) int hiddenvar;
             extern __attribute__((__visibility__("default"))) int exportedvar;
             extern __attribute__((__visibility__("hidden"))) int hiddenfunc (void);
             extern __attribute__((__visibility__("default"))) int exportedfunc (void);
             void dummyfunc (void) {}
           ]],
           [[]])],
        [gl_cv_cc_visibility=yes],
        [gl_cv_cc_visibility=no])
      CFLAGS="$gl_save_CFLAGS"])
    AC_MSG_RESULT([$gl_cv_cc_visibility])
    if test $gl_cv_cc_visibility = yes; then
      CFLAG_VISIBILITY="-fvisibility=hidden"
      HAVE_VISIBILITY=1
    fi
  fi
  AC_SUBST([CFLAG_VISIBILITY])
  AC_SUBST([HAVE_VISIBILITY])
  AC_DEFINE_UNQUOTED([HAVE_VISIBILITY], [$HAVE_VISIBILITY],
    [Define to 1 or 0, depending whether the compiler supports simple visibility declarations.])
])
m4trace:m4/wchar_t.m4:11: -1- AC_DEFUN([gt_TYPE_WCHAR_T], [
  AC_CACHE_CHECK([for wchar_t], [gt_cv_c_wchar_t],
    [AC_COMPILE_IFELSE(
       [AC_LANG_PROGRAM(
          [[#include <stddef.h>
            wchar_t foo = (wchar_t)'\0';]],
          [[]])],
       [gt_cv_c_wchar_t=yes],
       [gt_cv_c_wchar_t=no])])
  if test $gt_cv_c_wchar_t = yes; then
    AC_DEFINE([HAVE_WCHAR_T], [1], [Define if you have the 'wchar_t' type.])
  fi
])
m4trace:m4/wint_t.m4:11: -1- AC_DEFUN([gt_TYPE_WINT_T], [
  AC_CACHE_CHECK([for wint_t], [gt_cv_c_wint_t],
    [AC_COMPILE_IFELSE(
       [AC_LANG_PROGRAM(
          [[
/* Tru64 with Desktop Toolkit C has a bug: <stdio.h> must be included before
   <wchar.h>.
   BSD/OS 4.0.1 has a bug: <stddef.h>, <stdio.h> and <time.h> must be included
   before <wchar.h>.  */
#include <stddef.h>
#include <stdio.h>
#include <time.h>
#include <wchar.h>
            wint_t foo = (wchar_t)'\0';]],
          [[]])],
       [gt_cv_c_wint_t=yes],
       [gt_cv_c_wint_t=no])])
  if test $gt_cv_c_wint_t = yes; then
    AC_DEFINE([HAVE_WINT_T], [1], [Define if you have the 'wint_t' type.])
  fi
])
m4trace:m4/xsize.m4:7: -1- AC_DEFUN([gl_XSIZE], [
  dnl Prerequisites of lib/xsize.h.
  AC_REQUIRE([gl_SIZE_MAX])
  AC_CHECK_HEADERS([stdint.h])
])
m4trace:configure.ac:28: -1- m4_pattern_forbid([^_?A[CHUM]_])
m4trace:configure.ac:28: -1- m4_pattern_forbid([_AC_])
m4trace:configure.ac:28: -1- m4_pattern_forbid([^LIBOBJS$], [do not use LIBOBJS directly, use AC_LIBOBJ (see section `AC_LIBOBJ vs LIBOBJS'])
m4trace:configure.ac:28: -1- m4_pattern_allow([^AS_FLAGS$])
m4trace:configure.ac:28: -1- m4_pattern_forbid([^_?m4_])
m4trace:configure.ac:28: -1- m4_pattern_forbid([^dnl$])
m4trace:configure.ac:28: -1- m4_pattern_forbid([^_?AS_])
m4trace:configure.ac:28: -1- m4_pattern_allow([^SHELL$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PATH_SEPARATOR$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_URL$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^exec_prefix$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^prefix$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^program_transform_name$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^bindir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^sbindir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^libexecdir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^datarootdir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^datadir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^sysconfdir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^sharedstatedir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^localstatedir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^runstatedir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^includedir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^oldincludedir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^docdir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^infodir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^htmldir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^dvidir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^pdfdir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^psdir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^libdir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^localedir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^mandir$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE_URL$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^DEFS$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^ECHO_C$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^ECHO_N$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^ECHO_T$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^build_alias$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^host_alias$])
m4trace:configure.ac:28: -1- m4_pattern_allow([^target_alias$])
m4trace:configure.ac:32: -1- AM_INIT_AUTOMAKE([1.11.2 subdir-objects tar-ustar no-dist-gzip dist-xz -Wno-portability])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AM_[A-Z]+FLAGS$])
m4trace:configure.ac:32: -1- AM_SET_CURRENT_AUTOMAKE_VERSION
m4trace:configure.ac:32: -1- AM_AUTOMAKE_VERSION([1.16.1])
m4trace:configure.ac:32: -1- _AM_AUTOCONF_VERSION([2.69])
m4trace:configure.ac:32: -1- m4_pattern_allow([^INSTALL_PROGRAM$])
m4trace:configure.ac:32: -1- m4_pattern_allow([^INSTALL_SCRIPT$])
m4trace:configure.ac:32: -1- m4_pattern_allow([^INSTALL_DATA$])
m4trace:configure.ac:32: -1- m4_pattern_allow([^am__isrc$])
m4trace:configure.ac:32: -1- _AM_SUBST_NOTMAKE([am__isrc])
m4trace:configure.ac:32: -1- m4_pattern_allow([^CYGPATH_W$])
m4trace:configure.ac:32: -1- _AM_SET_OPTIONS([1.11.2 subdir-objects tar-ustar no-dist-gzip dist-xz -Wno-portability])
m4trace:configure.ac:32: -1- _AM_SET_OPTION([1.11.2])
m4trace:configure.ac:32: -2- _AM_MANGLE_OPTION([1.11.2])
m4trace:configure.ac:32: -1- _AM_SET_OPTION([subdir-objects])
m4trace:configure.ac:32: -2- _AM_MANGLE_OPTION([subdir-objects])
m4trace:configure.ac:32: -1- _AM_SET_OPTION([tar-ustar])
m4trace:configure.ac:32: -2- _AM_MANGLE_OPTION([tar-ustar])
m4trace:configure.ac:32: -1- _AM_SET_OPTION([no-dist-gzip])
m4trace:configure.ac:32: -2- _AM_MANGLE_OPTION([no-dist-gzip])
m4trace:configure.ac:32: -1- _AM_SET_OPTION([dist-xz])
m4trace:configure.ac:32: -2- _AM_MANGLE_OPTION([dist-xz])
m4trace:configure.ac:32: -1- _AM_SET_OPTION([-Wno-portability])
m4trace:configure.ac:32: -2- _AM_MANGLE_OPTION([-Wno-portability])
m4trace:configure.ac:32: -1- m4_pattern_allow([^PACKAGE$])
m4trace:configure.ac:32: -1- m4_pattern_allow([^VERSION$])
m4trace:configure.ac:32: -1- _AM_IF_OPTION([no-define], [], [AC_DEFINE_UNQUOTED([PACKAGE], ["$PACKAGE"], [Name of package])
 AC_DEFINE_UNQUOTED([VERSION], ["$VERSION"], [Version number of package])])
m4trace:configure.ac:32: -2- _AM_MANGLE_OPTION([no-define])
m4trace:configure.ac:32: -1- m4_pattern_allow([^PACKAGE$])
m4trace:configure.ac:32: -1- m4_pattern_allow([^VERSION$])
m4trace:configure.ac:32: -1- AM_SANITY_CHECK
m4trace:configure.ac:32: -1- AM_MISSING_PROG([ACLOCAL], [aclocal-${am__api_version}])
m4trace:configure.ac:32: -1- AM_MISSING_HAS_RUN
m4trace:configure.ac:32: -1- AM_AUX_DIR_EXPAND
m4trace:configure.ac:32: -1- m4_pattern_allow([^ACLOCAL$])
m4trace:configure.ac:32: -1- AM_MISSING_PROG([AUTOCONF], [autoconf])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AUTOCONF$])
m4trace:configure.ac:32: -1- AM_MISSING_PROG([AUTOMAKE], [automake-${am__api_version}])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AUTOMAKE$])
m4trace:configure.ac:32: -1- AM_MISSING_PROG([AUTOHEADER], [autoheader])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AUTOHEADER$])
m4trace:configure.ac:32: -1- AM_MISSING_PROG([MAKEINFO], [makeinfo])
m4trace:configure.ac:32: -1- m4_pattern_allow([^MAKEINFO$])
m4trace:configure.ac:32: -1- AM_PROG_INSTALL_SH
m4trace:configure.ac:32: -1- m4_pattern_allow([^install_sh$])
m4trace:configure.ac:32: -1- AM_PROG_INSTALL_STRIP
m4trace:configure.ac:32: -1- m4_pattern_allow([^STRIP$])
m4trace:configure.ac:32: -1- m4_pattern_allow([^INSTALL_STRIP_PROGRAM$])
m4trace:configure.ac:32: -1- m4_pattern_allow([^MKDIR_P$])
m4trace:configure.ac:32: -1- m4_pattern_allow([^mkdir_p$])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AWK$])
m4trace:configure.ac:32: -1- m4_pattern_allow([^SET_MAKE$])
m4trace:configure.ac:32: -1- AM_SET_LEADING_DOT
m4trace:configure.ac:32: -1- m4_pattern_allow([^am__leading_dot$])
m4trace:configure.ac:32: -1- _AM_IF_OPTION([tar-ustar], [_AM_PROG_TAR([ustar])], [_AM_IF_OPTION([tar-pax], [_AM_PROG_TAR([pax])],
			     [_AM_PROG_TAR([v7])])])
m4trace:configure.ac:32: -2- _AM_MANGLE_OPTION([tar-ustar])
m4trace:configure.ac:32: -1- _AM_PROG_TAR([ustar])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AMTAR$])
m4trace:configure.ac:32: -1- AM_RUN_LOG([$_am_tar --version])
m4trace:configure.ac:32: -1- AM_RUN_LOG([tardir=conftest.dir && eval $am__tar_ >conftest.tar])
m4trace:configure.ac:32: -1- AM_RUN_LOG([$am__untar <conftest.tar])
m4trace:configure.ac:32: -1- AM_RUN_LOG([cat conftest.dir/file])
m4trace:configure.ac:32: -1- m4_pattern_allow([^am__tar$])
m4trace:configure.ac:32: -1- m4_pattern_allow([^am__untar$])
m4trace:configure.ac:32: -1- _AM_IF_OPTION([no-dependencies], [], [AC_PROVIDE_IFELSE([AC_PROG_CC],
		  [_AM_DEPENDENCIES([CC])],
		  [m4_define([AC_PROG_CC],
			     m4_defn([AC_PROG_CC])[_AM_DEPENDENCIES([CC])])])dnl
AC_PROVIDE_IFELSE([AC_PROG_CXX],
		  [_AM_DEPENDENCIES([CXX])],
		  [m4_define([AC_PROG_CXX],
			     m4_defn([AC_PROG_CXX])[_AM_DEPENDENCIES([CXX])])])dnl
AC_PROVIDE_IFELSE([AC_PROG_OBJC],
		  [_AM_DEPENDENCIES([OBJC])],
		  [m4_define([AC_PROG_OBJC],
			     m4_defn([AC_PROG_OBJC])[_AM_DEPENDENCIES([OBJC])])])dnl
AC_PROVIDE_IFELSE([AC_PROG_OBJCXX],
		  [_AM_DEPENDENCIES([OBJCXX])],
		  [m4_define([AC_PROG_OBJCXX],
			     m4_defn([AC_PROG_OBJCXX])[_AM_DEPENDENCIES([OBJCXX])])])dnl
])
m4trace:configure.ac:32: -2- _AM_MANGLE_OPTION([no-dependencies])
m4trace:configure.ac:32: -1- AM_SILENT_RULES
m4trace:configure.ac:32: -1- m4_pattern_allow([^AM_V$])
m4trace:configure.ac:32: -1- AM_SUBST_NOTMAKE([AM_V])
m4trace:configure.ac:32: -1- _AM_SUBST_NOTMAKE([AM_V])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AM_DEFAULT_V$])
m4trace:configure.ac:32: -1- AM_SUBST_NOTMAKE([AM_DEFAULT_V])
m4trace:configure.ac:32: -1- _AM_SUBST_NOTMAKE([AM_DEFAULT_V])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AM_DEFAULT_VERBOSITY$])
m4trace:configure.ac:32: -1- m4_pattern_allow([^AM_BACKSLASH$])
m4trace:configure.ac:32: -1- _AM_SUBST_NOTMAKE([AM_BACKSLASH])
m4trace:configure.ac:33: -1- AM_SILENT_RULES([yes])
m4trace:configure.ac:33: -1- m4_pattern_allow([^AM_V$])
m4trace:configure.ac:33: -1- AM_SUBST_NOTMAKE([AM_V])
m4trace:configure.ac:33: -1- _AM_SUBST_NOTMAKE([AM_V])
m4trace:configure.ac:33: -1- m4_pattern_allow([^AM_DEFAULT_V$])
m4trace:configure.ac:33: -1- AM_SUBST_NOTMAKE([AM_DEFAULT_V])
m4trace:configure.ac:33: -1- _AM_SUBST_NOTMAKE([AM_DEFAULT_V])
m4trace:configure.ac:33: -1- m4_pattern_allow([^AM_DEFAULT_VERBOSITY$])
m4trace:configure.ac:33: -1- m4_pattern_allow([^AM_BACKSLASH$])
m4trace:configure.ac:33: -1- _AM_SUBST_NOTMAKE([AM_BACKSLASH])
m4trace:configure.ac:35: -1- AM_MAINTAINER_MODE([enable])
m4trace:configure.ac:35: -1- AM_CONDITIONAL([MAINTAINER_MODE], [test $USE_MAINTAINER_MODE = yes])
m4trace:configure.ac:35: -1- m4_pattern_allow([^MAINTAINER_MODE_TRUE$])
m4trace:configure.ac:35: -1- m4_pattern_allow([^MAINTAINER_MODE_FALSE$])
m4trace:configure.ac:35: -1- _AM_SUBST_NOTMAKE([MAINTAINER_MODE_TRUE])
m4trace:configure.ac:35: -1- _AM_SUBST_NOTMAKE([MAINTAINER_MODE_FALSE])
m4trace:configure.ac:35: -1- m4_pattern_allow([^MAINT$])
m4trace:configure.ac:37: -1- AM_SILENT_RULES([yes])
m4trace:configure.ac:37: -1- m4_pattern_allow([^AM_V$])
m4trace:configure.ac:37: -1- AM_SUBST_NOTMAKE([AM_V])
m4trace:configure.ac:37: -1- _AM_SUBST_NOTMAKE([AM_V])
m4trace:configure.ac:37: -1- m4_pattern_allow([^AM_DEFAULT_V$])
m4trace:configure.ac:37: -1- AM_SUBST_NOTMAKE([AM_DEFAULT_V])
m4trace:configure.ac:37: -1- _AM_SUBST_NOTMAKE([AM_DEFAULT_V])
m4trace:configure.ac:37: -1- m4_pattern_allow([^AM_DEFAULT_VERBOSITY$])
m4trace:configure.ac:37: -1- m4_pattern_allow([^AM_BACKSLASH$])
m4trace:configure.ac:37: -1- _AM_SUBST_NOTMAKE([AM_BACKSLASH])
m4trace:configure.ac:39: -1- AC_CONFIG_MACRO_DIR([m4])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CFLAGS$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^ac_ct_CC$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^EXEEXT$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^OBJEXT$])
m4trace:configure.ac:44: -1- _AM_PROG_CC_C_O
m4trace:configure.ac:44: -1- AM_RUN_LOG([$CC -c conftest.$ac_ext -o conftest2.$ac_objext])
m4trace:configure.ac:44: -1- _AM_DEPENDENCIES([CC])
m4trace:configure.ac:44: -1- AM_SET_DEPDIR
m4trace:configure.ac:44: -1- m4_pattern_allow([^DEPDIR$])
m4trace:configure.ac:44: -1- AM_OUTPUT_DEPENDENCY_COMMANDS
m4trace:configure.ac:44: -1- AM_MAKE_INCLUDE
m4trace:configure.ac:44: -1- AM_RUN_LOG([${MAKE-make} -f confmf.$s && cat confinc.out])
m4trace:configure.ac:44: -1- m4_pattern_allow([^am__include$])
m4trace:configure.ac:44: -1- AM_DEP_TRACK
m4trace:configure.ac:44: -1- AM_CONDITIONAL([AMDEP], [test "x$enable_dependency_tracking" != xno])
m4trace:configure.ac:44: -1- m4_pattern_allow([^AMDEP_TRUE$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^AMDEP_FALSE$])
m4trace:configure.ac:44: -1- _AM_SUBST_NOTMAKE([AMDEP_TRUE])
m4trace:configure.ac:44: -1- _AM_SUBST_NOTMAKE([AMDEP_FALSE])
m4trace:configure.ac:44: -1- m4_pattern_allow([^AMDEPBACKSLASH$])
m4trace:configure.ac:44: -1- _AM_SUBST_NOTMAKE([AMDEPBACKSLASH])
m4trace:configure.ac:44: -1- m4_pattern_allow([^am__nodep$])
m4trace:configure.ac:44: -1- _AM_SUBST_NOTMAKE([am__nodep])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CCDEPMODE$])
m4trace:configure.ac:44: -1- AM_CONDITIONAL([am__fastdepCC], [
  test "x$enable_dependency_tracking" != xno \
  && test "$am_cv_CC_dependencies_compiler_type" = gcc3])
m4trace:configure.ac:44: -1- m4_pattern_allow([^am__fastdepCC_TRUE$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^am__fastdepCC_FALSE$])
m4trace:configure.ac:44: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_TRUE])
m4trace:configure.ac:44: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_FALSE])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CPP$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^CPP$])
m4trace:configure.ac:44: -1- AC_PROG_EGREP
m4trace:configure.ac:44: -1- m4_pattern_allow([^GREP$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^EGREP$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^STDC_HEADERS$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^_POSIX_SOURCE$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^_POSIX_1_SOURCE$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^_MINIX$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^__EXTENSIONS__$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^_ALL_SOURCE$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^_GNU_SOURCE$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^_POSIX_PTHREAD_SEMANTICS$])
m4trace:configure.ac:44: -1- m4_pattern_allow([^_TANDEM_SOURCE$])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CFLAGS$])
m4trace:configure.ac:47: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.ac:47: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:47: -1- m4_pattern_allow([^ac_ct_CC$])
m4trace:configure.ac:47: -1- _AM_PROG_CC_C_O
m4trace:configure.ac:47: -1- AM_RUN_LOG([$CC -c conftest.$ac_ext -o conftest2.$ac_objext])
m4trace:configure.ac:47: -1- _AM_DEPENDENCIES([CC])
m4trace:configure.ac:47: -1- m4_pattern_allow([^CCDEPMODE$])
m4trace:configure.ac:47: -1- AM_CONDITIONAL([am__fastdepCC], [
  test "x$enable_dependency_tracking" != xno \
  && test "$am_cv_CC_dependencies_compiler_type" = gcc3])
m4trace:configure.ac:47: -1- m4_pattern_allow([^am__fastdepCC_TRUE$])
m4trace:configure.ac:47: -1- m4_pattern_allow([^am__fastdepCC_FALSE$])
m4trace:configure.ac:47: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_TRUE])
m4trace:configure.ac:47: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_FALSE])
m4trace:configure.ac:48: -1- AM_PROG_CC_C_O
m4trace:configure.ac:54: -1- LT_INIT([disable-static])
m4trace:configure.ac:54: -1- m4_pattern_forbid([^_?LT_[A-Z_]+$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^(_LT_EOF|LT_DLGLOBAL|LT_DLLAZY_OR_NOW|LT_MULTI_MODULE)$])
m4trace:configure.ac:54: -1- LTOPTIONS_VERSION
m4trace:configure.ac:54: -1- LTSUGAR_VERSION
m4trace:configure.ac:54: -1- LTVERSION_VERSION
m4trace:configure.ac:54: -1- LTOBSOLETE_VERSION
m4trace:configure.ac:54: -1- _LT_PROG_LTMAIN
m4trace:configure.ac:54: -1- m4_pattern_allow([^LIBTOOL$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^build$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^build_cpu$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^build_vendor$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^build_os$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^host$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^host_cpu$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^host_vendor$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^host_os$])
m4trace:configure.ac:54: -1- _LT_PREPARE_SED_QUOTE_VARS
m4trace:configure.ac:54: -1- _LT_PROG_ECHO_BACKSLASH
m4trace:configure.ac:54: -1- LT_PATH_LD
m4trace:configure.ac:54: -1- m4_pattern_allow([^SED$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^FGREP$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^GREP$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^LD$])
m4trace:configure.ac:54: -1- LT_PATH_NM
m4trace:configure.ac:54: -1- m4_pattern_allow([^DUMPBIN$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^ac_ct_DUMPBIN$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^DUMPBIN$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^NM$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^LN_S$])
m4trace:configure.ac:54: -1- LT_CMD_MAX_LEN
m4trace:configure.ac:54: -1- m4_pattern_allow([^OBJDUMP$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^OBJDUMP$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^DLLTOOL$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^DLLTOOL$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^AR$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^ac_ct_AR$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^STRIP$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^RANLIB$])
m4trace:configure.ac:54: -1- _LT_WITH_SYSROOT
m4trace:configure.ac:54: -1- m4_pattern_allow([LT_OBJDIR])
m4trace:configure.ac:54: -1- m4_pattern_allow([^LT_OBJDIR$])
m4trace:configure.ac:54: -1- _LT_CC_BASENAME([$compiler])
m4trace:configure.ac:54: -1- _LT_PATH_TOOL_PREFIX([${ac_tool_prefix}file], [/usr/bin$PATH_SEPARATOR$PATH])
m4trace:configure.ac:54: -1- _LT_PATH_TOOL_PREFIX([file], [/usr/bin$PATH_SEPARATOR$PATH])
m4trace:configure.ac:54: -1- LT_SUPPORTED_TAG([CC])
m4trace:configure.ac:54: -1- _LT_COMPILER_BOILERPLATE
m4trace:configure.ac:54: -1- _LT_LINKER_BOILERPLATE
m4trace:configure.ac:54: -1- _LT_COMPILER_OPTION([if $compiler supports -fno-rtti -fno-exceptions], [lt_cv_prog_compiler_rtti_exceptions], [-fno-rtti -fno-exceptions], [], [_LT_TAGVAR(lt_prog_compiler_no_builtin_flag, )="$_LT_TAGVAR(lt_prog_compiler_no_builtin_flag, ) -fno-rtti -fno-exceptions"])
m4trace:configure.ac:54: -1- _LT_COMPILER_OPTION([if $compiler PIC flag $_LT_TAGVAR(lt_prog_compiler_pic, ) works], [_LT_TAGVAR(lt_cv_prog_compiler_pic_works, )], [$_LT_TAGVAR(lt_prog_compiler_pic, )@&t@m4_if([],[],[ -DPIC],[m4_if([],[CXX],[ -DPIC],[])])], [], [case $_LT_TAGVAR(lt_prog_compiler_pic, ) in
     "" | " "*) ;;
     *) _LT_TAGVAR(lt_prog_compiler_pic, )=" $_LT_TAGVAR(lt_prog_compiler_pic, )" ;;
     esac], [_LT_TAGVAR(lt_prog_compiler_pic, )=
     _LT_TAGVAR(lt_prog_compiler_can_build_shared, )=no])
m4trace:configure.ac:54: -1- _LT_LINKER_OPTION([if $compiler static flag $lt_tmp_static_flag works], [lt_cv_prog_compiler_static_works], [$lt_tmp_static_flag], [], [_LT_TAGVAR(lt_prog_compiler_static, )=])
m4trace:configure.ac:54: -1- m4_pattern_allow([^MANIFEST_TOOL$])
m4trace:configure.ac:54: -1- _LT_DLL_DEF_P([$export_symbols])
m4trace:configure.ac:54: -1- _LT_DLL_DEF_P([$export_symbols])
m4trace:configure.ac:54: -1- _LT_REQUIRED_DARWIN_CHECKS
m4trace:configure.ac:54: -1- m4_pattern_allow([^DSYMUTIL$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^NMEDIT$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^LIPO$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^OTOOL$])
m4trace:configure.ac:54: -1- m4_pattern_allow([^OTOOL64$])
m4trace:configure.ac:54: -1- _LT_LINKER_OPTION([if $CC understands -b], [lt_cv_prog_compiler__b], [-b], [_LT_TAGVAR(archive_cmds, )='$CC -b $wl+h $wl$soname $wl+b $wl$install_libdir -o $lib $libobjs $deplibs $compiler_flags'], [_LT_TAGVAR(archive_cmds, )='$LD -b +h $soname +b $install_libdir -o $lib $libobjs $deplibs $linker_flags'])
m4trace:configure.ac:54: -1- m4_pattern_allow([^LT_SYS_LIBRARY_PATH$])
m4trace:configure.ac:54: -1- LT_SYS_DLOPEN_SELF
m4trace:configure.ac:54: -1- m4_pattern_allow([^HAVE_DLFCN_H$])
m4trace:configure.ac:61: -1- m4_pattern_forbid([^AX_(COMPILER_FLAGS|COMPILER_FLAGS_(CFLAGS|GIR|LDFLAGS))\b], [Unexpanded AX_ macro found. Please install GNU autoconf-archive])
m4trace:configure.ac:65: -1- AX_IS_RELEASE([git-directory])
m4trace:configure.ac:75: -1- AX_COMPILER_FLAGS([])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_COMPILER_FLAGS_CFLAGS])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_COMPILER_FLAGS_LDFLAGS])
m4trace:configure.ac:75: -1- AX_COMPILER_FLAGS_CFLAGS([], [$ax_compiler_flags_is_release], [], [   ])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_COMPILE_FLAGS])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])
m4trace:configure.ac:75: -1- AX_CHECK_COMPILE_FLAG([-Werror=unknown-warning-option], [
        ax_compiler_flags_test="-Werror=unknown-warning-option"
    ], [
        ax_compiler_flags_test=""
    ])
m4trace:configure.ac:75: -1- AX_CHECK_COMPILE_FLAG([-Wno-suggest-attribute=format], [
        ax_compiler_no_suggest_attribute_flags="-Wno-suggest-attribute=format"
    ], [
        ax_compiler_no_suggest_attribute_flags=""
    ])
m4trace:configure.ac:75: -1- AX_APPEND_COMPILE_FLAGS([ dnl
        -fno-strict-aliasing dnl
         dnl
    ], [WARN_CFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:75: -1- AX_CHECK_COMPILE_FLAG([$flag], [AX_APPEND_FLAG([$flag], [WARN_CFLAGS])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([$flag], [WARN_CFLAGS])
m4trace:configure.ac:75: -1- AX_APPEND_COMPILE_FLAGS([ dnl
                -Wnested-externs dnl
                -Wmissing-prototypes dnl
                -Wstrict-prototypes dnl
                -Wdeclaration-after-statement dnl
                -Wimplicit-function-declaration dnl
                -Wold-style-definition dnl
                -Wjump-misses-init dnl
            ], [WARN_CFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:75: -1- AX_CHECK_COMPILE_FLAG([$flag], [AX_APPEND_FLAG([$flag], [WARN_CFLAGS])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([$flag], [WARN_CFLAGS])
m4trace:configure.ac:75: -1- AX_APPEND_COMPILE_FLAGS([ dnl
            -Wall dnl
            -Wextra dnl
            -Wundef dnl
            -Wwrite-strings dnl
            -Wpointer-arith dnl
            -Wmissing-declarations dnl
            -Wredundant-decls dnl
            -Wno-unused-parameter dnl
            -Wno-missing-field-initializers dnl
            -Wformat=2 dnl
            -Wcast-align dnl
            -Wformat-nonliteral dnl
            -Wformat-security dnl
            -Wsign-compare dnl
            -Wstrict-aliasing dnl
            -Wshadow dnl
            -Winline dnl
            -Wpacked dnl
            -Wmissing-format-attribute dnl
            -Wmissing-noreturn dnl
            -Winit-self dnl
            -Wredundant-decls dnl
            -Wmissing-include-dirs dnl
            -Wunused-but-set-variable dnl
            -Warray-bounds dnl
            -Wreturn-type dnl
            -Wswitch-enum dnl
            -Wswitch-default dnl
            -Wduplicated-cond dnl
            -Wduplicated-branches dnl
            -Wlogical-op dnl
            -Wrestrict dnl
            -Wnull-dereference dnl
            -Wdouble-promotion dnl
                dnl
             dnl
             dnl
             dnl
        ], [WARN_CFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:75: -1- AX_CHECK_COMPILE_FLAG([$flag], [AX_APPEND_FLAG([$flag], [WARN_CFLAGS])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([$flag], [WARN_CFLAGS])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([-Werror], [WARN_CFLAGS])
m4trace:configure.ac:75: -1- AX_APPEND_COMPILE_FLAGS([ dnl
            [$ax_compiler_no_suggest_attribute_flags] dnl
        ], [WARN_CFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:75: -1- AX_CHECK_COMPILE_FLAG([$flag], [AX_APPEND_FLAG([$flag], [WARN_CFLAGS])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([$flag], [WARN_CFLAGS])
m4trace:configure.ac:75: -1- AX_APPEND_COMPILE_FLAGS([-Wno-error=$(AS_ECHO([$flag]) | $SED 's/^-Wno-//')], [WARN_CFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:75: -1- AX_CHECK_COMPILE_FLAG([$flag], [AX_APPEND_FLAG([$flag], [WARN_CFLAGS])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([$flag], [WARN_CFLAGS])
m4trace:configure.ac:75: -1- m4_pattern_allow([^WARN_CFLAGS$])
m4trace:configure.ac:75: -1- AX_COMPILER_FLAGS_LDFLAGS([], [$ax_compiler_flags_is_release], [], [   ])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_LINK_FLAGS])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_CHECK_LINK_FLAG])
m4trace:configure.ac:75: -1- AX_CHECK_COMPILE_FLAG([-Werror=unknown-warning-option], [
        ax_compiler_flags_test="-Werror=unknown-warning-option"
    ], [
        ax_compiler_flags_test=""
    ])
m4trace:configure.ac:75: -1- AX_CHECK_LINK_FLAG([-Wl,--as-needed], [
        AX_APPEND_LINK_FLAGS([-Wl,--as-needed],
          [AM_LDFLAGS],[$ax_compiler_flags_test])
    ])
m4trace:configure.ac:75: -1- AX_APPEND_LINK_FLAGS([-Wl,--as-needed], [AM_LDFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_CHECK_LINK_FLAG])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:75: -1- AX_CHECK_LINK_FLAG([$flag], [AX_APPEND_FLAG([$flag], [m4_default([AM_LDFLAGS], [LDFLAGS])])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([$flag], [m4_default([AM_LDFLAGS], [LDFLAGS])])
m4trace:configure.ac:75: -1- AX_CHECK_LINK_FLAG([-Wl,-z,relro], [
        AX_APPEND_LINK_FLAGS([-Wl,-z,relro],
          [AM_LDFLAGS],[$ax_compiler_flags_test])
    ])
m4trace:configure.ac:75: -1- AX_APPEND_LINK_FLAGS([-Wl,-z,relro], [AM_LDFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_CHECK_LINK_FLAG])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:75: -1- AX_CHECK_LINK_FLAG([$flag], [AX_APPEND_FLAG([$flag], [m4_default([AM_LDFLAGS], [LDFLAGS])])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([$flag], [m4_default([AM_LDFLAGS], [LDFLAGS])])
m4trace:configure.ac:75: -1- AX_CHECK_LINK_FLAG([-Wl,-z,now], [
        AX_APPEND_LINK_FLAGS([-Wl,-z,now],
          [AM_LDFLAGS],[$ax_compiler_flags_test])
    ])
m4trace:configure.ac:75: -1- AX_APPEND_LINK_FLAGS([-Wl,-z,now], [AM_LDFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_CHECK_LINK_FLAG])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:75: -1- AX_CHECK_LINK_FLAG([$flag], [AX_APPEND_FLAG([$flag], [m4_default([AM_LDFLAGS], [LDFLAGS])])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([$flag], [m4_default([AM_LDFLAGS], [LDFLAGS])])
m4trace:configure.ac:75: -1- AX_CHECK_LINK_FLAG([-Wl,-z,noexecstack], [
        AX_APPEND_LINK_FLAGS([-Wl,-z,noexecstack],
          [AM_LDFLAGS],[$ax_compiler_flags_test])
    ])
m4trace:configure.ac:75: -1- AX_APPEND_LINK_FLAGS([-Wl,-z,noexecstack], [AM_LDFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_CHECK_LINK_FLAG])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:75: -1- AX_CHECK_LINK_FLAG([$flag], [AX_APPEND_FLAG([$flag], [m4_default([AM_LDFLAGS], [LDFLAGS])])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([$flag], [m4_default([AM_LDFLAGS], [LDFLAGS])])
m4trace:configure.ac:75: -1- AX_CHECK_LINK_FLAG([-Wl,--no-as-needed], [
        ax_compiler_flags_as_needed_option="-Wl,--no-as-needed"
    ], [
        ax_compiler_flags_as_needed_option=""
    ])
m4trace:configure.ac:75: -1- AX_CHECK_LINK_FLAG([-Wl,--fatal-warnings], [
        ax_compiler_flags_fatal_warnings_option="-Wl,--fatal-warnings"
    ])
m4trace:configure.ac:75: -1- AX_CHECK_LINK_FLAG([-Wl,-fatal_warnings], [
        ax_compiler_flags_fatal_warnings_option="-Wl,-fatal_warnings"
    ])
m4trace:configure.ac:75: -1- AX_APPEND_LINK_FLAGS([ dnl
        $ax_compiler_flags_as_needed_option dnl
         dnl
    ], [WARN_LDFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_CHECK_LINK_FLAG])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:75: -1- AX_CHECK_LINK_FLAG([$flag], [AX_APPEND_FLAG([$flag], [m4_default([WARN_LDFLAGS], [LDFLAGS])])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([$flag], [m4_default([WARN_LDFLAGS], [LDFLAGS])])
m4trace:configure.ac:75: -1- AX_APPEND_LINK_FLAGS([      ], [WARN_LDFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_CHECK_LINK_FLAG])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:75: -1- AX_CHECK_LINK_FLAG([$flag], [AX_APPEND_FLAG([$flag], [m4_default([WARN_LDFLAGS], [LDFLAGS])])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([$flag], [m4_default([WARN_LDFLAGS], [LDFLAGS])])
m4trace:configure.ac:75: -1- AX_APPEND_LINK_FLAGS([ dnl
            $ax_compiler_flags_fatal_warnings_option dnl
        ], [WARN_LDFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_CHECK_LINK_FLAG])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:75: -1- AX_CHECK_LINK_FLAG([$flag], [AX_APPEND_FLAG([$flag], [m4_default([WARN_LDFLAGS], [LDFLAGS])])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([$flag], [m4_default([WARN_LDFLAGS], [LDFLAGS])])
m4trace:configure.ac:75: -1- m4_pattern_allow([^WARN_LDFLAGS$])
m4trace:configure.ac:75: -1- AX_COMPILER_FLAGS_GIR([WARN_SCANNERFLAGS], [$ax_compiler_flags_is_release])
m4trace:configure.ac:75: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([], [WARN_SCANNERFLAGS])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([ dnl
            --warn-all dnl
             dnl
             dnl
             dnl
             dnl
        ], [WARN_SCANNERFLAGS])
m4trace:configure.ac:75: -1- AX_APPEND_FLAG([ dnl
            --warn-error dnl
        ], [WARN_SCANNERFLAGS])
m4trace:configure.ac:75: -1- m4_pattern_allow([^WARN_SCANNERFLAGS$])
m4trace:configure.ac:76: -1- AX_COMPILER_FLAGS_CFLAGS([], [], [], [${DISABLED_WARNINGS}])
m4trace:configure.ac:76: -1- AX_REQUIRE_DEFINED([AX_APPEND_COMPILE_FLAGS])
m4trace:configure.ac:76: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:76: -1- AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])
m4trace:configure.ac:76: -1- AX_CHECK_COMPILE_FLAG([-Werror=unknown-warning-option], [
        ax_compiler_flags_test="-Werror=unknown-warning-option"
    ], [
        ax_compiler_flags_test=""
    ])
m4trace:configure.ac:76: -1- AX_CHECK_COMPILE_FLAG([-Wno-suggest-attribute=format], [
        ax_compiler_no_suggest_attribute_flags="-Wno-suggest-attribute=format"
    ], [
        ax_compiler_no_suggest_attribute_flags=""
    ])
m4trace:configure.ac:76: -1- AX_APPEND_COMPILE_FLAGS([ dnl
        -fno-strict-aliasing dnl
         dnl
    ], [WARN_CFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:76: -1- AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])
m4trace:configure.ac:76: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:76: -1- AX_CHECK_COMPILE_FLAG([$flag], [AX_APPEND_FLAG([$flag], [WARN_CFLAGS])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:76: -1- AX_APPEND_FLAG([$flag], [WARN_CFLAGS])
m4trace:configure.ac:76: -1- AX_APPEND_COMPILE_FLAGS([ dnl
                -Wnested-externs dnl
                -Wmissing-prototypes dnl
                -Wstrict-prototypes dnl
                -Wdeclaration-after-statement dnl
                -Wimplicit-function-declaration dnl
                -Wold-style-definition dnl
                -Wjump-misses-init dnl
            ], [WARN_CFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:76: -1- AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])
m4trace:configure.ac:76: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:76: -1- AX_CHECK_COMPILE_FLAG([$flag], [AX_APPEND_FLAG([$flag], [WARN_CFLAGS])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:76: -1- AX_APPEND_FLAG([$flag], [WARN_CFLAGS])
m4trace:configure.ac:76: -1- AX_APPEND_COMPILE_FLAGS([ dnl
            -Wall dnl
            -Wextra dnl
            -Wundef dnl
            -Wwrite-strings dnl
            -Wpointer-arith dnl
            -Wmissing-declarations dnl
            -Wredundant-decls dnl
            -Wno-unused-parameter dnl
            -Wno-missing-field-initializers dnl
            -Wformat=2 dnl
            -Wcast-align dnl
            -Wformat-nonliteral dnl
            -Wformat-security dnl
            -Wsign-compare dnl
            -Wstrict-aliasing dnl
            -Wshadow dnl
            -Winline dnl
            -Wpacked dnl
            -Wmissing-format-attribute dnl
            -Wmissing-noreturn dnl
            -Winit-self dnl
            -Wredundant-decls dnl
            -Wmissing-include-dirs dnl
            -Wunused-but-set-variable dnl
            -Warray-bounds dnl
            -Wreturn-type dnl
            -Wswitch-enum dnl
            -Wswitch-default dnl
            -Wduplicated-cond dnl
            -Wduplicated-branches dnl
            -Wlogical-op dnl
            -Wrestrict dnl
            -Wnull-dereference dnl
            -Wdouble-promotion dnl
            ${DISABLED_WARNINGS} dnl
             dnl
             dnl
             dnl
        ], [WARN_CFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:76: -1- AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])
m4trace:configure.ac:76: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:76: -1- AX_CHECK_COMPILE_FLAG([$flag], [AX_APPEND_FLAG([$flag], [WARN_CFLAGS])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:76: -1- AX_APPEND_FLAG([$flag], [WARN_CFLAGS])
m4trace:configure.ac:76: -1- AX_APPEND_FLAG([-Werror], [WARN_CFLAGS])
m4trace:configure.ac:76: -1- AX_APPEND_COMPILE_FLAGS([ dnl
            [$ax_compiler_no_suggest_attribute_flags] dnl
        ], [WARN_CFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:76: -1- AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])
m4trace:configure.ac:76: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:76: -1- AX_CHECK_COMPILE_FLAG([$flag], [AX_APPEND_FLAG([$flag], [WARN_CFLAGS])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:76: -1- AX_APPEND_FLAG([$flag], [WARN_CFLAGS])
m4trace:configure.ac:76: -1- AX_APPEND_COMPILE_FLAGS([-Wno-error=$(AS_ECHO([$flag]) | $SED 's/^-Wno-//')], [WARN_CFLAGS], [$ax_compiler_flags_test])
m4trace:configure.ac:76: -1- AX_REQUIRE_DEFINED([AX_CHECK_COMPILE_FLAG])
m4trace:configure.ac:76: -1- AX_REQUIRE_DEFINED([AX_APPEND_FLAG])
m4trace:configure.ac:76: -1- AX_CHECK_COMPILE_FLAG([$flag], [AX_APPEND_FLAG([$flag], [WARN_CFLAGS])], [], [$ax_compiler_flags_test], [])
m4trace:configure.ac:76: -1- AX_APPEND_FLAG([$flag], [WARN_CFLAGS])
m4trace:configure.ac:76: -1- m4_pattern_allow([^WARN_CFLAGS$])
m4trace:configure.ac:92: -1- m4_pattern_allow([^MM_MAJOR_VERSION$])
m4trace:configure.ac:93: -1- m4_pattern_allow([^MM_MINOR_VERSION$])
m4trace:configure.ac:94: -1- m4_pattern_allow([^MM_MICRO_VERSION$])
m4trace:configure.ac:95: -1- m4_pattern_allow([^MM_VERSION$])
m4trace:configure.ac:101: -1- m4_pattern_allow([^MM_GLIB_LT_CURRENT$])
m4trace:configure.ac:102: -1- m4_pattern_allow([^MM_GLIB_LT_REVISION$])
m4trace:configure.ac:103: -1- m4_pattern_allow([^MM_GLIB_LT_AGE$])
m4trace:configure.ac:109: -1- GTK_DOC_CHECK([1.0])
m4trace:configure.ac:109: -1- PKG_PROG_PKG_CONFIG
m4trace:configure.ac:109: -1- m4_pattern_forbid([^_?PKG_[A-Z_]+$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^PKG_CONFIG(_(PATH|LIBDIR|SYSROOT_DIR|ALLOW_SYSTEM_(CFLAGS|LIBS)))?$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^PKG_CONFIG_(DISABLE_UNINSTALLED|TOP_BUILD_DIR|DEBUG_SPEW)$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^PKG_CONFIG$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^PKG_CONFIG_PATH$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^PKG_CONFIG_LIBDIR$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^PKG_CONFIG$])
m4trace:configure.ac:109: -1- PKG_CHECK_EXISTS([$gtk_doc_requires], [have_gtk_doc=yes], [have_gtk_doc=no])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTKDOC_CHECK$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTKDOC_CHECK_PATH$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTKDOC_REBASE$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTKDOC_MKPDF$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^HTML_DIR$])
m4trace:configure.ac:109: -1- PKG_CHECK_MODULES([GTKDOC_DEPS], [glib-2.0 >= 2.10.0 gobject-2.0  >= 2.10.0], [], [:])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTKDOC_DEPS_CFLAGS$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTKDOC_DEPS_LIBS$])
m4trace:configure.ac:109: -1- PKG_CHECK_EXISTS([glib-2.0 >= 2.10.0 gobject-2.0  >= 2.10.0], [pkg_cv_[]GTKDOC_DEPS_CFLAGS=`$PKG_CONFIG --[]cflags "glib-2.0 >= 2.10.0 gobject-2.0  >= 2.10.0" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:109: -1- PKG_CHECK_EXISTS([glib-2.0 >= 2.10.0 gobject-2.0  >= 2.10.0], [pkg_cv_[]GTKDOC_DEPS_LIBS=`$PKG_CONFIG --[]libs "glib-2.0 >= 2.10.0 gobject-2.0  >= 2.10.0" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:109: -1- _PKG_SHORT_ERRORS_SUPPORTED
m4trace:configure.ac:109: -1- m4_pattern_allow([^AM_DEFAULT_VERBOSITY$])
m4trace:configure.ac:109: -1- AM_CONDITIONAL([HAVE_GTK_DOC], [test x$have_gtk_doc = xyes])
m4trace:configure.ac:109: -1- m4_pattern_allow([^HAVE_GTK_DOC_TRUE$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^HAVE_GTK_DOC_FALSE$])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([HAVE_GTK_DOC_TRUE])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([HAVE_GTK_DOC_FALSE])
m4trace:configure.ac:109: -1- AM_CONDITIONAL([ENABLE_GTK_DOC], [test x$enable_gtk_doc = xyes])
m4trace:configure.ac:109: -1- m4_pattern_allow([^ENABLE_GTK_DOC_TRUE$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^ENABLE_GTK_DOC_FALSE$])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([ENABLE_GTK_DOC_TRUE])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([ENABLE_GTK_DOC_FALSE])
m4trace:configure.ac:109: -1- AM_CONDITIONAL([GTK_DOC_BUILD_HTML], [test x$enable_gtk_doc_html = xyes])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_BUILD_HTML_TRUE$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_BUILD_HTML_FALSE$])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_BUILD_HTML_TRUE])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_BUILD_HTML_FALSE])
m4trace:configure.ac:109: -1- AM_CONDITIONAL([GTK_DOC_BUILD_PDF], [test x$enable_gtk_doc_pdf = xyes])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_BUILD_PDF_TRUE$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_BUILD_PDF_FALSE$])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_BUILD_PDF_TRUE])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_BUILD_PDF_FALSE])
m4trace:configure.ac:109: -1- AM_CONDITIONAL([GTK_DOC_USE_LIBTOOL], [test -n "$LIBTOOL"])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_USE_LIBTOOL_TRUE$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_USE_LIBTOOL_FALSE$])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_USE_LIBTOOL_TRUE])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_USE_LIBTOOL_FALSE])
m4trace:configure.ac:109: -1- AM_CONDITIONAL([GTK_DOC_USE_REBASE], [test -n "$GTKDOC_REBASE"])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_USE_REBASE_TRUE$])
m4trace:configure.ac:109: -1- m4_pattern_allow([^GTK_DOC_USE_REBASE_FALSE$])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_USE_REBASE_TRUE])
m4trace:configure.ac:109: -1- _AM_SUBST_NOTMAKE([GTK_DOC_USE_REBASE_FALSE])
m4trace:configure.ac:115: -1- AM_GNU_GETTEXT([external])
m4trace:configure.ac:115: -1- AM_GNU_GETTEXT_NEED([])
m4trace:configure.ac:115: -1- AM_PO_SUBDIRS
m4trace:configure.ac:115: -1- AM_NLS
m4trace:configure.ac:115: -1- m4_pattern_allow([^USE_NLS$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^GETTEXT_MACRO_VERSION$])
m4trace:configure.ac:115: -1- AM_PATH_PROG_WITH_TEST([MSGFMT], [msgfmt], [$ac_dir/$ac_word --statistics /dev/null >&5 2>&1 &&
     (if $ac_dir/$ac_word --statistics /dev/null 2>&1 >/dev/null | grep usage >/dev/null; then exit 1; else exit 0; fi)], [:])
m4trace:configure.ac:115: -1- m4_pattern_allow([^MSGFMT$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^GMSGFMT$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^MSGFMT_015$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^GMSGFMT_015$])
m4trace:configure.ac:115: -1- AM_PATH_PROG_WITH_TEST([XGETTEXT], [xgettext], [$ac_dir/$ac_word --omit-header --copyright-holder= --msgid-bugs-address= /dev/null >&5 2>&1 &&
     (if $ac_dir/$ac_word --omit-header --copyright-holder= --msgid-bugs-address= /dev/null 2>&1 >/dev/null | grep usage >/dev/null; then exit 1; else exit 0; fi)], [:])
m4trace:configure.ac:115: -1- m4_pattern_allow([^XGETTEXT$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^XGETTEXT_015$])
m4trace:configure.ac:115: -1- AM_PATH_PROG_WITH_TEST([MSGMERGE], [msgmerge], [$ac_dir/$ac_word --update -q /dev/null /dev/null >&5 2>&1], [:])
m4trace:configure.ac:115: -1- m4_pattern_allow([^MSGMERGE$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^localedir$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^XGETTEXT_EXTRA_OPTIONS$])
m4trace:configure.ac:115: -1- AC_LIB_PREPARE_PREFIX
m4trace:configure.ac:115: -1- AC_LIB_RPATH
m4trace:configure.ac:115: -1- AC_LIB_PROG_LD
m4trace:configure.ac:115: -1- AC_LIB_PROG_LD_GNU
m4trace:configure.ac:115: -1- AM_ICONV_LINKFLAGS_BODY
m4trace:configure.ac:115: -1- AC_LIB_LINKFLAGS_BODY([iconv])
m4trace:configure.ac:115: -1- AC_LIB_PREPARE_MULTILIB
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([
    eval additional_includedir=\"$includedir\"
    eval additional_libdir=\"$libdir\"
  ])
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([
          eval additional_includedir=\"$includedir\"
          eval additional_libdir=\"$libdir\"
        ])
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
m4trace:configure.ac:115: -1- gt_INTL_MACOSX
m4trace:configure.ac:115: -1- m4_pattern_allow([^HAVE_CFPREFERENCESCOPYAPPVALUE$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^HAVE_CFLOCALECOPYCURRENT$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^INTL_MACOSX_LIBS$])
m4trace:configure.ac:115: -1- AM_ICONV_LINK
m4trace:configure.ac:115: -1- AC_LIB_APPENDTOVAR([CPPFLAGS], [$INCICONV])
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
m4trace:configure.ac:115: -1- m4_pattern_allow([^HAVE_ICONV$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^LIBICONV$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^LTLIBICONV$])
m4trace:configure.ac:115: -1- AC_LIB_LINKFLAGS_BODY([intl])
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([
    eval additional_includedir=\"$includedir\"
    eval additional_libdir=\"$libdir\"
  ])
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([
          eval additional_includedir=\"$includedir\"
          eval additional_libdir=\"$libdir\"
        ])
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
m4trace:configure.ac:115: -1- m4_pattern_allow([^ENABLE_NLS$])
m4trace:configure.ac:115: -1- AC_LIB_APPENDTOVAR([CPPFLAGS], [$INCINTL])
m4trace:configure.ac:115: -1- AC_LIB_WITH_FINAL_PREFIX([eval x=\"$x\"])
m4trace:configure.ac:115: -1- m4_pattern_allow([^HAVE_GETTEXT$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^HAVE_DCGETTEXT$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^INTLLIBS$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^LIBINTL$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^LTLIBINTL$])
m4trace:configure.ac:115: -1- m4_pattern_allow([^POSUB$])
m4trace:configure.ac:116: -1- AM_GNU_GETTEXT_VERSION([0.19.8])
m4trace:configure.ac:119: -1- m4_pattern_allow([^GETTEXT_PACKAGE$])
m4trace:configure.ac:120: -1- m4_pattern_allow([^GETTEXT_PACKAGE$])
m4trace:configure.ac:131: -1- PKG_CHECK_MODULES([MM], [glib-2.0 >= $GLIB_MIN_VERSION
                  gmodule-2.0
                  gobject-2.0
                  gio-2.0
                  gio-unix-2.0])
m4trace:configure.ac:131: -1- m4_pattern_allow([^MM_CFLAGS$])
m4trace:configure.ac:131: -1- m4_pattern_allow([^MM_LIBS$])
m4trace:configure.ac:131: -1- PKG_CHECK_EXISTS([glib-2.0 >= $GLIB_MIN_VERSION
                  gmodule-2.0
                  gobject-2.0
                  gio-2.0
                  gio-unix-2.0], [pkg_cv_[]MM_CFLAGS=`$PKG_CONFIG --[]cflags "glib-2.0 >= $GLIB_MIN_VERSION
                  gmodule-2.0
                  gobject-2.0
                  gio-2.0
                  gio-unix-2.0" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:131: -1- PKG_CHECK_EXISTS([glib-2.0 >= $GLIB_MIN_VERSION
                  gmodule-2.0
                  gobject-2.0
                  gio-2.0
                  gio-unix-2.0], [pkg_cv_[]MM_LIBS=`$PKG_CONFIG --[]libs "glib-2.0 >= $GLIB_MIN_VERSION
                  gmodule-2.0
                  gobject-2.0
                  gio-2.0
                  gio-unix-2.0" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:131: -1- _PKG_SHORT_ERRORS_SUPPORTED
m4trace:configure.ac:138: -1- m4_pattern_allow([^MM_CFLAGS$])
m4trace:configure.ac:139: -1- m4_pattern_allow([^MM_LIBS$])
m4trace:configure.ac:141: -1- PKG_CHECK_MODULES([LIBMM_GLIB], [glib-2.0 >= $GLIB_MIN_VERSION
                  gobject-2.0
                  gio-2.0
                  gio-unix-2.0])
m4trace:configure.ac:141: -1- m4_pattern_allow([^LIBMM_GLIB_CFLAGS$])
m4trace:configure.ac:141: -1- m4_pattern_allow([^LIBMM_GLIB_LIBS$])
m4trace:configure.ac:141: -1- PKG_CHECK_EXISTS([glib-2.0 >= $GLIB_MIN_VERSION
                  gobject-2.0
                  gio-2.0
                  gio-unix-2.0], [pkg_cv_[]LIBMM_GLIB_CFLAGS=`$PKG_CONFIG --[]cflags "glib-2.0 >= $GLIB_MIN_VERSION
                  gobject-2.0
                  gio-2.0
                  gio-unix-2.0" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:141: -1- PKG_CHECK_EXISTS([glib-2.0 >= $GLIB_MIN_VERSION
                  gobject-2.0
                  gio-2.0
                  gio-unix-2.0], [pkg_cv_[]LIBMM_GLIB_LIBS=`$PKG_CONFIG --[]libs "glib-2.0 >= $GLIB_MIN_VERSION
                  gobject-2.0
                  gio-2.0
                  gio-unix-2.0" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:141: -1- _PKG_SHORT_ERRORS_SUPPORTED
m4trace:configure.ac:147: -1- m4_pattern_allow([^LIBMM_GLIB_CFLAGS$])
m4trace:configure.ac:148: -1- m4_pattern_allow([^LIBMM_GLIB_LIBS$])
m4trace:configure.ac:150: -1- PKG_CHECK_MODULES([MMCLI], [glib-2.0 >= $GLIB_MIN_VERSION
                  gobject-2.0
                  gio-2.0])
m4trace:configure.ac:150: -1- m4_pattern_allow([^MMCLI_CFLAGS$])
m4trace:configure.ac:150: -1- m4_pattern_allow([^MMCLI_LIBS$])
m4trace:configure.ac:150: -1- PKG_CHECK_EXISTS([glib-2.0 >= $GLIB_MIN_VERSION
                  gobject-2.0
                  gio-2.0], [pkg_cv_[]MMCLI_CFLAGS=`$PKG_CONFIG --[]cflags "glib-2.0 >= $GLIB_MIN_VERSION
                  gobject-2.0
                  gio-2.0" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:150: -1- PKG_CHECK_EXISTS([glib-2.0 >= $GLIB_MIN_VERSION
                  gobject-2.0
                  gio-2.0], [pkg_cv_[]MMCLI_LIBS=`$PKG_CONFIG --[]libs "glib-2.0 >= $GLIB_MIN_VERSION
                  gobject-2.0
                  gio-2.0" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:150: -1- _PKG_SHORT_ERRORS_SUPPORTED
m4trace:configure.ac:155: -1- m4_pattern_allow([^MMCLI_CFLAGS$])
m4trace:configure.ac:156: -1- m4_pattern_allow([^MMCLI_LIBS$])
m4trace:configure.ac:160: -1- m4_pattern_allow([^GLIB_MKENUMS$])
m4trace:configure.ac:163: -1- m4_pattern_allow([^GDBUS_CODEGEN$])
m4trace:configure.ac:166: -1- m4_pattern_allow([^XSLTPROC_CHECK$])
m4trace:configure.ac:176: -1- AX_CODE_COVERAGE
m4trace:configure.ac:176: -1- AM_CONDITIONAL([CODE_COVERAGE_ENABLED], [test x$enable_code_coverage = xyes])
m4trace:configure.ac:176: -1- m4_pattern_allow([^CODE_COVERAGE_ENABLED_TRUE$])
m4trace:configure.ac:176: -1- m4_pattern_allow([^CODE_COVERAGE_ENABLED_FALSE$])
m4trace:configure.ac:176: -1- _AM_SUBST_NOTMAKE([CODE_COVERAGE_ENABLED_TRUE])
m4trace:configure.ac:176: -1- _AM_SUBST_NOTMAKE([CODE_COVERAGE_ENABLED_FALSE])
m4trace:configure.ac:176: -1- m4_pattern_allow([^CODE_COVERAGE_ENABLED$])
m4trace:configure.ac:176: -1- m4_pattern_allow([^GCOV$])
m4trace:configure.ac:176: -1- m4_pattern_allow([^GCOV$])
m4trace:configure.ac:176: -1- m4_pattern_allow([^LCOV$])
m4trace:configure.ac:176: -1- m4_pattern_allow([^GENHTML$])
m4trace:configure.ac:176: -1- m4_pattern_allow([^CODE_COVERAGE_CFLAGS$])
m4trace:configure.ac:176: -1- m4_pattern_allow([^CODE_COVERAGE_LDFLAGS$])
m4trace:configure.ac:176: -1- m4_pattern_allow([^CODE_COVERAGE_RULES$])
m4trace:configure.ac:176: -1- _AM_SUBST_NOTMAKE([CODE_COVERAGE_RULES])
m4trace:configure.ac:183: -1- GOBJECT_INTROSPECTION_CHECK([0.9.6])
m4trace:configure.ac:183: -1- PKG_CHECK_EXISTS([gobject-introspection-1.0], [], [as_fn_error $? "gobject-introspection-1.0 is not installed" "$LINENO" 5])
m4trace:configure.ac:183: -1- PKG_CHECK_EXISTS([gobject-introspection-1.0 >= 0.9.6], [found_introspection=yes], [as_fn_error $? "You need to have gobject-introspection >= 0.9.6 installed to build ModemManager" "$LINENO" 5])
m4trace:configure.ac:183: -1- PKG_CHECK_EXISTS([gobject-introspection-1.0 >= 0.9.6], [found_introspection=yes], [found_introspection=no])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_SCANNER$])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_COMPILER$])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_GENERATE$])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_GIRDIR$])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_TYPELIBDIR$])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_CFLAGS$])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_LIBS$])
m4trace:configure.ac:183: -1- m4_pattern_allow([^INTROSPECTION_MAKEFILE$])
m4trace:configure.ac:183: -1- AM_CONDITIONAL([HAVE_INTROSPECTION], [test "x$found_introspection" = "xyes"])
m4trace:configure.ac:183: -1- m4_pattern_allow([^HAVE_INTROSPECTION_TRUE$])
m4trace:configure.ac:183: -1- m4_pattern_allow([^HAVE_INTROSPECTION_FALSE$])
m4trace:configure.ac:183: -1- _AM_SUBST_NOTMAKE([HAVE_INTROSPECTION_TRUE])
m4trace:configure.ac:183: -1- _AM_SUBST_NOTMAKE([HAVE_INTROSPECTION_FALSE])
m4trace:configure.ac:186: -1- VAPIGEN_CHECK([0.18])
m4trace:configure.ac:186: -1- PKG_PROG_PKG_CONFIG
m4trace:configure.ac:186: -1- m4_pattern_forbid([^_?PKG_[A-Z_]+$])
m4trace:configure.ac:186: -1- m4_pattern_allow([^PKG_CONFIG(_(PATH|LIBDIR|SYSROOT_DIR|ALLOW_SYSTEM_(CFLAGS|LIBS)))?$])
m4trace:configure.ac:186: -1- m4_pattern_allow([^PKG_CONFIG_(DISABLE_UNINSTALLED|TOP_BUILD_DIR|DEBUG_SPEW)$])
m4trace:configure.ac:186: -1- m4_pattern_allow([^PKG_CONFIG$])
m4trace:configure.ac:186: -1- m4_pattern_allow([^PKG_CONFIG_PATH$])
m4trace:configure.ac:186: -1- m4_pattern_allow([^PKG_CONFIG_LIBDIR$])
m4trace:configure.ac:186: -1- m4_pattern_allow([^PKG_CONFIG$])
m4trace:configure.ac:186: -1- PKG_CHECK_EXISTS([$vapigen_pkg], [
      AS_IF([test "$enable_vala" = "auto"], [
          enable_vala=yes
        ])
    ], [
      AS_CASE([$enable_vala], [yes], [
          AC_MSG_ERROR([$vapigen_pkg not found])
        ], [auto], [
          enable_vala=no
        ])
    ])
m4trace:configure.ac:186: -1- m4_pattern_allow([^VAPIGEN$])
m4trace:configure.ac:186: -1- m4_pattern_allow([^VAPIGEN_VAPIDIR$])
m4trace:configure.ac:186: -1- m4_pattern_allow([^VAPIGEN_MAKEFILE$])
m4trace:configure.ac:186: -1- AM_CONDITIONAL([ENABLE_VAPIGEN], [test "x$enable_vala" = "xyes"])
m4trace:configure.ac:186: -1- m4_pattern_allow([^ENABLE_VAPIGEN_TRUE$])
m4trace:configure.ac:186: -1- m4_pattern_allow([^ENABLE_VAPIGEN_FALSE$])
m4trace:configure.ac:186: -1- _AM_SUBST_NOTMAKE([ENABLE_VAPIGEN_TRUE])
m4trace:configure.ac:186: -1- _AM_SUBST_NOTMAKE([ENABLE_VAPIGEN_FALSE])
m4trace:configure.ac:202: -1- m4_pattern_allow([^DBUS_SYS_DIR$])
m4trace:configure.ac:211: -1- m4_pattern_allow([^UDEV_BASE_DIR$])
m4trace:configure.ac:217: -1- m4_pattern_allow([^SYSTEMD_UNIT_DIR$])
m4trace:configure.ac:219: -1- AM_CONDITIONAL([HAVE_SYSTEMD], [test -n "$SYSTEMD_UNIT_DIR" -a "$SYSTEMD_UNIT_DIR" != xno ])
m4trace:configure.ac:219: -1- m4_pattern_allow([^HAVE_SYSTEMD_TRUE$])
m4trace:configure.ac:219: -1- m4_pattern_allow([^HAVE_SYSTEMD_FALSE$])
m4trace:configure.ac:219: -1- _AM_SUBST_NOTMAKE([HAVE_SYSTEMD_TRUE])
m4trace:configure.ac:219: -1- _AM_SUBST_NOTMAKE([HAVE_SYSTEMD_FALSE])
m4trace:configure.ac:228: -1- AM_CONDITIONAL([WITH_UDEV], [test "x$with_udev" = "xyes"])
m4trace:configure.ac:228: -1- m4_pattern_allow([^WITH_UDEV_TRUE$])
m4trace:configure.ac:228: -1- m4_pattern_allow([^WITH_UDEV_FALSE$])
m4trace:configure.ac:228: -1- _AM_SUBST_NOTMAKE([WITH_UDEV_TRUE])
m4trace:configure.ac:228: -1- _AM_SUBST_NOTMAKE([WITH_UDEV_FALSE])
m4trace:configure.ac:231: -1- PKG_CHECK_MODULES([GUDEV], [gudev-1.0 >= $GUDEV_VERSION], [have_gudev=yes], [have_gudev=no])
m4trace:configure.ac:231: -1- m4_pattern_allow([^GUDEV_CFLAGS$])
m4trace:configure.ac:231: -1- m4_pattern_allow([^GUDEV_LIBS$])
m4trace:configure.ac:231: -1- PKG_CHECK_EXISTS([gudev-1.0 >= $GUDEV_VERSION], [pkg_cv_[]GUDEV_CFLAGS=`$PKG_CONFIG --[]cflags "gudev-1.0 >= $GUDEV_VERSION" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:231: -1- PKG_CHECK_EXISTS([gudev-1.0 >= $GUDEV_VERSION], [pkg_cv_[]GUDEV_LIBS=`$PKG_CONFIG --[]libs "gudev-1.0 >= $GUDEV_VERSION" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:231: -1- _PKG_SHORT_ERRORS_SUPPORTED
m4trace:configure.ac:235: -1- m4_pattern_allow([^WITH_UDEV$])
m4trace:configure.ac:236: -1- m4_pattern_allow([^GUDEV_CFLAGS$])
m4trace:configure.ac:237: -1- m4_pattern_allow([^GUDEV_LIBS$])
m4trace:configure.ac:249: -1- PKG_CHECK_MODULES([LIBSYSTEMD], [libsystemd >= 209], [have_libsystemd=yes], [have_libsystemd=no])
m4trace:configure.ac:249: -1- m4_pattern_allow([^LIBSYSTEMD_CFLAGS$])
m4trace:configure.ac:249: -1- m4_pattern_allow([^LIBSYSTEMD_LIBS$])
m4trace:configure.ac:249: -1- PKG_CHECK_EXISTS([libsystemd >= 209], [pkg_cv_[]LIBSYSTEMD_CFLAGS=`$PKG_CONFIG --[]cflags "libsystemd >= 209" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:249: -1- PKG_CHECK_EXISTS([libsystemd >= 209], [pkg_cv_[]LIBSYSTEMD_LIBS=`$PKG_CONFIG --[]libs "libsystemd >= 209" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:249: -1- _PKG_SHORT_ERRORS_SUPPORTED
m4trace:configure.ac:250: -1- PKG_CHECK_MODULES([LIBSYSTEMD_LOGIN], [libsystemd-login >= 183], [have_libsystemd_login=yes], [have_libsystemd_login=no])
m4trace:configure.ac:250: -1- m4_pattern_allow([^LIBSYSTEMD_LOGIN_CFLAGS$])
m4trace:configure.ac:250: -1- m4_pattern_allow([^LIBSYSTEMD_LOGIN_LIBS$])
m4trace:configure.ac:250: -1- PKG_CHECK_EXISTS([libsystemd-login >= 183], [pkg_cv_[]LIBSYSTEMD_LOGIN_CFLAGS=`$PKG_CONFIG --[]cflags "libsystemd-login >= 183" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:250: -1- PKG_CHECK_EXISTS([libsystemd-login >= 183], [pkg_cv_[]LIBSYSTEMD_LOGIN_LIBS=`$PKG_CONFIG --[]libs "libsystemd-login >= 183" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:250: -1- _PKG_SHORT_ERRORS_SUPPORTED
m4trace:configure.ac:269: -1- m4_pattern_allow([^WITH_SYSTEMD_SUSPEND_RESUME$])
m4trace:configure.ac:276: -1- AM_CONDITIONAL([WITH_SYSTEMD_SUSPEND_RESUME], [test "x$with_systemd_suspend_resume" = "xyes"])
m4trace:configure.ac:276: -1- m4_pattern_allow([^WITH_SYSTEMD_SUSPEND_RESUME_TRUE$])
m4trace:configure.ac:276: -1- m4_pattern_allow([^WITH_SYSTEMD_SUSPEND_RESUME_FALSE$])
m4trace:configure.ac:276: -1- _AM_SUBST_NOTMAKE([WITH_SYSTEMD_SUSPEND_RESUME_TRUE])
m4trace:configure.ac:276: -1- _AM_SUBST_NOTMAKE([WITH_SYSTEMD_SUSPEND_RESUME_FALSE])
m4trace:configure.ac:300: -1- m4_pattern_allow([^WITH_SYSTEMD_JOURNAL$])
m4trace:configure.ac:307: -1- AM_CONDITIONAL([WITH_SYSTEMD_JOURNAL], [test "x$with_systemd_journal" = "xyes"])
m4trace:configure.ac:307: -1- m4_pattern_allow([^WITH_SYSTEMD_JOURNAL_TRUE$])
m4trace:configure.ac:307: -1- m4_pattern_allow([^WITH_SYSTEMD_JOURNAL_FALSE$])
m4trace:configure.ac:307: -1- _AM_SUBST_NOTMAKE([WITH_SYSTEMD_JOURNAL_TRUE])
m4trace:configure.ac:307: -1- _AM_SUBST_NOTMAKE([WITH_SYSTEMD_JOURNAL_FALSE])
m4trace:configure.ac:313: -1- PKG_CHECK_MODULES([POLKIT], [polkit-gobject-1 >= 0.97], [have_polkit=yes], [have_polkit=no])
m4trace:configure.ac:313: -1- m4_pattern_allow([^POLKIT_CFLAGS$])
m4trace:configure.ac:313: -1- m4_pattern_allow([^POLKIT_LIBS$])
m4trace:configure.ac:313: -1- PKG_CHECK_EXISTS([polkit-gobject-1 >= 0.97], [pkg_cv_[]POLKIT_CFLAGS=`$PKG_CONFIG --[]cflags "polkit-gobject-1 >= 0.97" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:313: -1- PKG_CHECK_EXISTS([polkit-gobject-1 >= 0.97], [pkg_cv_[]POLKIT_LIBS=`$PKG_CONFIG --[]libs "polkit-gobject-1 >= 0.97" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:313: -1- _PKG_SHORT_ERRORS_SUPPORTED
m4trace:configure.ac:349: -1- m4_pattern_allow([^WITH_POLKIT$])
m4trace:configure.ac:350: -1- m4_pattern_allow([^POLKIT_CFLAGS$])
m4trace:configure.ac:351: -1- m4_pattern_allow([^POLKIT_LIBS$])
m4trace:configure.ac:352: -1- m4_pattern_allow([^MM_DEFAULT_USER_POLICY$])
m4trace:configure.ac:355: -1- m4_pattern_allow([^MM_POLKIT_SERVICE$])
m4trace:configure.ac:356: -1- AM_CONDITIONAL([WITH_POLKIT], [test "x$with_polkit" != "xno"])
m4trace:configure.ac:356: -1- m4_pattern_allow([^WITH_POLKIT_TRUE$])
m4trace:configure.ac:356: -1- m4_pattern_allow([^WITH_POLKIT_FALSE$])
m4trace:configure.ac:356: -1- _AM_SUBST_NOTMAKE([WITH_POLKIT_TRUE])
m4trace:configure.ac:356: -1- _AM_SUBST_NOTMAKE([WITH_POLKIT_FALSE])
m4trace:configure.ac:372: -1- m4_pattern_allow([^WITH_AT_COMMAND_VIA_DBUS$])
m4trace:configure.ac:382: -1- AM_CONDITIONAL([WITH_MBIM], [test "x$with_mbim" = "xyes"])
m4trace:configure.ac:382: -1- m4_pattern_allow([^WITH_MBIM_TRUE$])
m4trace:configure.ac:382: -1- m4_pattern_allow([^WITH_MBIM_FALSE$])
m4trace:configure.ac:382: -1- _AM_SUBST_NOTMAKE([WITH_MBIM_TRUE])
m4trace:configure.ac:382: -1- _AM_SUBST_NOTMAKE([WITH_MBIM_FALSE])
m4trace:configure.ac:385: -1- PKG_CHECK_MODULES([MBIM], [mbim-glib >= $LIBMBIM_VERSION], [have_mbim=yes], [have_mbim=no])
m4trace:configure.ac:385: -1- m4_pattern_allow([^MBIM_CFLAGS$])
m4trace:configure.ac:385: -1- m4_pattern_allow([^MBIM_LIBS$])
m4trace:configure.ac:385: -1- PKG_CHECK_EXISTS([mbim-glib >= $LIBMBIM_VERSION], [pkg_cv_[]MBIM_CFLAGS=`$PKG_CONFIG --[]cflags "mbim-glib >= $LIBMBIM_VERSION" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:385: -1- PKG_CHECK_EXISTS([mbim-glib >= $LIBMBIM_VERSION], [pkg_cv_[]MBIM_LIBS=`$PKG_CONFIG --[]libs "mbim-glib >= $LIBMBIM_VERSION" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:385: -1- _PKG_SHORT_ERRORS_SUPPORTED
m4trace:configure.ac:389: -1- m4_pattern_allow([^WITH_MBIM$])
m4trace:configure.ac:390: -1- m4_pattern_allow([^MBIM_CFLAGS$])
m4trace:configure.ac:391: -1- m4_pattern_allow([^MBIM_LIBS$])
m4trace:configure.ac:406: -1- AM_CONDITIONAL([WITH_QMI], [test "x$with_qmi" = "xyes"])
m4trace:configure.ac:406: -1- m4_pattern_allow([^WITH_QMI_TRUE$])
m4trace:configure.ac:406: -1- m4_pattern_allow([^WITH_QMI_FALSE$])
m4trace:configure.ac:406: -1- _AM_SUBST_NOTMAKE([WITH_QMI_TRUE])
m4trace:configure.ac:406: -1- _AM_SUBST_NOTMAKE([WITH_QMI_FALSE])
m4trace:configure.ac:409: -1- PKG_CHECK_MODULES([QMI], [qmi-glib >= $LIBQMI_VERSION], [have_qmi=yes], [have_qmi=no])
m4trace:configure.ac:409: -1- m4_pattern_allow([^QMI_CFLAGS$])
m4trace:configure.ac:409: -1- m4_pattern_allow([^QMI_LIBS$])
m4trace:configure.ac:409: -1- PKG_CHECK_EXISTS([qmi-glib >= $LIBQMI_VERSION], [pkg_cv_[]QMI_CFLAGS=`$PKG_CONFIG --[]cflags "qmi-glib >= $LIBQMI_VERSION" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:409: -1- PKG_CHECK_EXISTS([qmi-glib >= $LIBQMI_VERSION], [pkg_cv_[]QMI_LIBS=`$PKG_CONFIG --[]libs "qmi-glib >= $LIBQMI_VERSION" 2>/dev/null`
		      test "x$?" != "x0" && pkg_failed=yes ], [pkg_failed=yes])
m4trace:configure.ac:409: -1- _PKG_SHORT_ERRORS_SUPPORTED
m4trace:configure.ac:413: -1- m4_pattern_allow([^WITH_QMI$])
m4trace:configure.ac:414: -1- m4_pattern_allow([^QMI_CFLAGS$])
m4trace:configure.ac:415: -1- m4_pattern_allow([^QMI_LIBS$])
m4trace:configure.ac:428: -1- m4_pattern_allow([^MM_DIST_VERSION$])
m4trace:configure.ac:435: -1- AM_CONDITIONAL([QCDM_STANDALONE], [test "yes" = "no"])
m4trace:configure.ac:435: -1- m4_pattern_allow([^QCDM_STANDALONE_TRUE$])
m4trace:configure.ac:435: -1- m4_pattern_allow([^QCDM_STANDALONE_FALSE$])
m4trace:configure.ac:435: -1- _AM_SUBST_NOTMAKE([QCDM_STANDALONE_TRUE])
m4trace:configure.ac:435: -1- _AM_SUBST_NOTMAKE([QCDM_STANDALONE_FALSE])
m4trace:configure.ac:455: -1- MM_ENABLE_ALL_PLUGINS
m4trace:configure.ac:457: -1- MM_ENABLE_PLUGIN([generic])
m4trace:configure.ac:457: -1- m4_pattern_allow([^ENABLE_PLUGIN_GENERIC$])
m4trace:configure.ac:457: -1- AM_CONDITIONAL([ENABLE_PLUGIN_GENERIC], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:457: -1- m4_pattern_allow([^ENABLE_PLUGIN_GENERIC_TRUE$])
m4trace:configure.ac:457: -1- m4_pattern_allow([^ENABLE_PLUGIN_GENERIC_FALSE$])
m4trace:configure.ac:457: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_GENERIC_TRUE])
m4trace:configure.ac:457: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_GENERIC_FALSE])
m4trace:configure.ac:458: -1- MM_ENABLE_PLUGIN([altair-lte])
m4trace:configure.ac:458: -1- m4_pattern_allow([^ENABLE_PLUGIN_ALTAIR_LTE$])
m4trace:configure.ac:458: -1- AM_CONDITIONAL([ENABLE_PLUGIN_ALTAIR_LTE], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:458: -1- m4_pattern_allow([^ENABLE_PLUGIN_ALTAIR_LTE_TRUE$])
m4trace:configure.ac:458: -1- m4_pattern_allow([^ENABLE_PLUGIN_ALTAIR_LTE_FALSE$])
m4trace:configure.ac:458: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ALTAIR_LTE_TRUE])
m4trace:configure.ac:458: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ALTAIR_LTE_FALSE])
m4trace:configure.ac:459: -1- MM_ENABLE_PLUGIN([anydata])
m4trace:configure.ac:459: -1- m4_pattern_allow([^ENABLE_PLUGIN_ANYDATA$])
m4trace:configure.ac:459: -1- AM_CONDITIONAL([ENABLE_PLUGIN_ANYDATA], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:459: -1- m4_pattern_allow([^ENABLE_PLUGIN_ANYDATA_TRUE$])
m4trace:configure.ac:459: -1- m4_pattern_allow([^ENABLE_PLUGIN_ANYDATA_FALSE$])
m4trace:configure.ac:459: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ANYDATA_TRUE])
m4trace:configure.ac:459: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ANYDATA_FALSE])
m4trace:configure.ac:460: -1- MM_ENABLE_PLUGIN([broadmobi])
m4trace:configure.ac:460: -1- m4_pattern_allow([^ENABLE_PLUGIN_BROADMOBI$])
m4trace:configure.ac:460: -1- AM_CONDITIONAL([ENABLE_PLUGIN_BROADMOBI], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:460: -1- m4_pattern_allow([^ENABLE_PLUGIN_BROADMOBI_TRUE$])
m4trace:configure.ac:460: -1- m4_pattern_allow([^ENABLE_PLUGIN_BROADMOBI_FALSE$])
m4trace:configure.ac:460: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_BROADMOBI_TRUE])
m4trace:configure.ac:460: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_BROADMOBI_FALSE])
m4trace:configure.ac:461: -1- MM_ENABLE_PLUGIN([cinterion])
m4trace:configure.ac:461: -1- m4_pattern_allow([^ENABLE_PLUGIN_CINTERION$])
m4trace:configure.ac:461: -1- AM_CONDITIONAL([ENABLE_PLUGIN_CINTERION], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:461: -1- m4_pattern_allow([^ENABLE_PLUGIN_CINTERION_TRUE$])
m4trace:configure.ac:461: -1- m4_pattern_allow([^ENABLE_PLUGIN_CINTERION_FALSE$])
m4trace:configure.ac:461: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_CINTERION_TRUE])
m4trace:configure.ac:461: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_CINTERION_FALSE])
m4trace:configure.ac:462: -1- MM_ENABLE_PLUGIN([dell], [with_shared_sierra,
                  with_shared_novatel,
                  with_shared_xmm,
                  with_shared_telit,
                  with_shared_foxconn])
m4trace:configure.ac:462: -1- m4_pattern_allow([^ENABLE_PLUGIN_DELL$])
m4trace:configure.ac:462: -1- AM_CONDITIONAL([ENABLE_PLUGIN_DELL], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:462: -1- m4_pattern_allow([^ENABLE_PLUGIN_DELL_TRUE$])
m4trace:configure.ac:462: -1- m4_pattern_allow([^ENABLE_PLUGIN_DELL_FALSE$])
m4trace:configure.ac:462: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_DELL_TRUE])
m4trace:configure.ac:462: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_DELL_FALSE])
m4trace:configure.ac:468: -1- MM_ENABLE_PLUGIN([dlink])
m4trace:configure.ac:468: -1- m4_pattern_allow([^ENABLE_PLUGIN_DLINK$])
m4trace:configure.ac:468: -1- AM_CONDITIONAL([ENABLE_PLUGIN_DLINK], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:468: -1- m4_pattern_allow([^ENABLE_PLUGIN_DLINK_TRUE$])
m4trace:configure.ac:468: -1- m4_pattern_allow([^ENABLE_PLUGIN_DLINK_FALSE$])
m4trace:configure.ac:468: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_DLINK_TRUE])
m4trace:configure.ac:468: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_DLINK_FALSE])
m4trace:configure.ac:471: -1- MM_ENABLE_PLUGIN([foxconn], [with_shared_foxconn])
m4trace:configure.ac:471: -1- m4_pattern_allow([^ENABLE_PLUGIN_FOXCONN$])
m4trace:configure.ac:471: -1- AM_CONDITIONAL([ENABLE_PLUGIN_FOXCONN], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:471: -1- m4_pattern_allow([^ENABLE_PLUGIN_FOXCONN_TRUE$])
m4trace:configure.ac:471: -1- m4_pattern_allow([^ENABLE_PLUGIN_FOXCONN_FALSE$])
m4trace:configure.ac:471: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_FOXCONN_TRUE])
m4trace:configure.ac:471: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_FOXCONN_FALSE])
m4trace:configure.ac:473: -1- MM_ENABLE_PLUGIN([haier])
m4trace:configure.ac:473: -1- m4_pattern_allow([^ENABLE_PLUGIN_HAIER$])
m4trace:configure.ac:473: -1- AM_CONDITIONAL([ENABLE_PLUGIN_HAIER], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:473: -1- m4_pattern_allow([^ENABLE_PLUGIN_HAIER_TRUE$])
m4trace:configure.ac:473: -1- m4_pattern_allow([^ENABLE_PLUGIN_HAIER_FALSE$])
m4trace:configure.ac:473: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_HAIER_TRUE])
m4trace:configure.ac:473: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_HAIER_FALSE])
m4trace:configure.ac:474: -1- MM_ENABLE_PLUGIN([huawei])
m4trace:configure.ac:474: -1- m4_pattern_allow([^ENABLE_PLUGIN_HUAWEI$])
m4trace:configure.ac:474: -1- AM_CONDITIONAL([ENABLE_PLUGIN_HUAWEI], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:474: -1- m4_pattern_allow([^ENABLE_PLUGIN_HUAWEI_TRUE$])
m4trace:configure.ac:474: -1- m4_pattern_allow([^ENABLE_PLUGIN_HUAWEI_FALSE$])
m4trace:configure.ac:474: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_HUAWEI_TRUE])
m4trace:configure.ac:474: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_HUAWEI_FALSE])
m4trace:configure.ac:475: -1- MM_ENABLE_PLUGIN([iridium])
m4trace:configure.ac:475: -1- m4_pattern_allow([^ENABLE_PLUGIN_IRIDIUM$])
m4trace:configure.ac:475: -1- AM_CONDITIONAL([ENABLE_PLUGIN_IRIDIUM], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:475: -1- m4_pattern_allow([^ENABLE_PLUGIN_IRIDIUM_TRUE$])
m4trace:configure.ac:475: -1- m4_pattern_allow([^ENABLE_PLUGIN_IRIDIUM_FALSE$])
m4trace:configure.ac:475: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_IRIDIUM_TRUE])
m4trace:configure.ac:475: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_IRIDIUM_FALSE])
m4trace:configure.ac:476: -1- MM_ENABLE_PLUGIN([linktop])
m4trace:configure.ac:476: -1- m4_pattern_allow([^ENABLE_PLUGIN_LINKTOP$])
m4trace:configure.ac:476: -1- AM_CONDITIONAL([ENABLE_PLUGIN_LINKTOP], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:476: -1- m4_pattern_allow([^ENABLE_PLUGIN_LINKTOP_TRUE$])
m4trace:configure.ac:476: -1- m4_pattern_allow([^ENABLE_PLUGIN_LINKTOP_FALSE$])
m4trace:configure.ac:476: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_LINKTOP_TRUE])
m4trace:configure.ac:476: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_LINKTOP_FALSE])
m4trace:configure.ac:477: -1- MM_ENABLE_PLUGIN([longcheer])
m4trace:configure.ac:477: -1- m4_pattern_allow([^ENABLE_PLUGIN_LONGCHEER$])
m4trace:configure.ac:477: -1- AM_CONDITIONAL([ENABLE_PLUGIN_LONGCHEER], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:477: -1- m4_pattern_allow([^ENABLE_PLUGIN_LONGCHEER_TRUE$])
m4trace:configure.ac:477: -1- m4_pattern_allow([^ENABLE_PLUGIN_LONGCHEER_FALSE$])
m4trace:configure.ac:477: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_LONGCHEER_TRUE])
m4trace:configure.ac:477: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_LONGCHEER_FALSE])
m4trace:configure.ac:478: -1- MM_ENABLE_PLUGIN([mbm])
m4trace:configure.ac:478: -1- m4_pattern_allow([^ENABLE_PLUGIN_MBM$])
m4trace:configure.ac:478: -1- AM_CONDITIONAL([ENABLE_PLUGIN_MBM], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:478: -1- m4_pattern_allow([^ENABLE_PLUGIN_MBM_TRUE$])
m4trace:configure.ac:478: -1- m4_pattern_allow([^ENABLE_PLUGIN_MBM_FALSE$])
m4trace:configure.ac:478: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_MBM_TRUE])
m4trace:configure.ac:478: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_MBM_FALSE])
m4trace:configure.ac:479: -1- MM_ENABLE_PLUGIN([motorola])
m4trace:configure.ac:479: -1- m4_pattern_allow([^ENABLE_PLUGIN_MOTOROLA$])
m4trace:configure.ac:479: -1- AM_CONDITIONAL([ENABLE_PLUGIN_MOTOROLA], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:479: -1- m4_pattern_allow([^ENABLE_PLUGIN_MOTOROLA_TRUE$])
m4trace:configure.ac:479: -1- m4_pattern_allow([^ENABLE_PLUGIN_MOTOROLA_FALSE$])
m4trace:configure.ac:479: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_MOTOROLA_TRUE])
m4trace:configure.ac:479: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_MOTOROLA_FALSE])
m4trace:configure.ac:480: -1- MM_ENABLE_PLUGIN([mtk])
m4trace:configure.ac:480: -1- m4_pattern_allow([^ENABLE_PLUGIN_MTK$])
m4trace:configure.ac:480: -1- AM_CONDITIONAL([ENABLE_PLUGIN_MTK], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:480: -1- m4_pattern_allow([^ENABLE_PLUGIN_MTK_TRUE$])
m4trace:configure.ac:480: -1- m4_pattern_allow([^ENABLE_PLUGIN_MTK_FALSE$])
m4trace:configure.ac:480: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_MTK_TRUE])
m4trace:configure.ac:480: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_MTK_FALSE])
m4trace:configure.ac:481: -1- MM_ENABLE_PLUGIN([nokia])
m4trace:configure.ac:481: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOKIA$])
m4trace:configure.ac:481: -1- AM_CONDITIONAL([ENABLE_PLUGIN_NOKIA], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:481: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOKIA_TRUE$])
m4trace:configure.ac:481: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOKIA_FALSE$])
m4trace:configure.ac:481: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOKIA_TRUE])
m4trace:configure.ac:481: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOKIA_FALSE])
m4trace:configure.ac:482: -1- MM_ENABLE_PLUGIN([nokia-icera], [with_shared_icera])
m4trace:configure.ac:482: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOKIA_ICERA$])
m4trace:configure.ac:482: -1- AM_CONDITIONAL([ENABLE_PLUGIN_NOKIA_ICERA], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:482: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOKIA_ICERA_TRUE$])
m4trace:configure.ac:482: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOKIA_ICERA_FALSE$])
m4trace:configure.ac:482: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOKIA_ICERA_TRUE])
m4trace:configure.ac:482: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOKIA_ICERA_FALSE])
m4trace:configure.ac:484: -1- MM_ENABLE_PLUGIN([novatel], [with_shared_novatel])
m4trace:configure.ac:484: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOVATEL$])
m4trace:configure.ac:484: -1- AM_CONDITIONAL([ENABLE_PLUGIN_NOVATEL], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:484: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOVATEL_TRUE$])
m4trace:configure.ac:484: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOVATEL_FALSE$])
m4trace:configure.ac:484: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOVATEL_TRUE])
m4trace:configure.ac:484: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOVATEL_FALSE])
m4trace:configure.ac:486: -1- MM_ENABLE_PLUGIN([novatel-lte])
m4trace:configure.ac:486: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOVATEL_LTE$])
m4trace:configure.ac:486: -1- AM_CONDITIONAL([ENABLE_PLUGIN_NOVATEL_LTE], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:486: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOVATEL_LTE_TRUE$])
m4trace:configure.ac:486: -1- m4_pattern_allow([^ENABLE_PLUGIN_NOVATEL_LTE_FALSE$])
m4trace:configure.ac:486: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOVATEL_LTE_TRUE])
m4trace:configure.ac:486: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_NOVATEL_LTE_FALSE])
m4trace:configure.ac:487: -1- MM_ENABLE_PLUGIN([option], [with_shared_option])
m4trace:configure.ac:487: -1- m4_pattern_allow([^ENABLE_PLUGIN_OPTION$])
m4trace:configure.ac:487: -1- AM_CONDITIONAL([ENABLE_PLUGIN_OPTION], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:487: -1- m4_pattern_allow([^ENABLE_PLUGIN_OPTION_TRUE$])
m4trace:configure.ac:487: -1- m4_pattern_allow([^ENABLE_PLUGIN_OPTION_FALSE$])
m4trace:configure.ac:487: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_OPTION_TRUE])
m4trace:configure.ac:487: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_OPTION_FALSE])
m4trace:configure.ac:489: -1- MM_ENABLE_PLUGIN([option-hso], [with_shared_option])
m4trace:configure.ac:489: -1- m4_pattern_allow([^ENABLE_PLUGIN_OPTION_HSO$])
m4trace:configure.ac:489: -1- AM_CONDITIONAL([ENABLE_PLUGIN_OPTION_HSO], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:489: -1- m4_pattern_allow([^ENABLE_PLUGIN_OPTION_HSO_TRUE$])
m4trace:configure.ac:489: -1- m4_pattern_allow([^ENABLE_PLUGIN_OPTION_HSO_FALSE$])
m4trace:configure.ac:489: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_OPTION_HSO_TRUE])
m4trace:configure.ac:489: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_OPTION_HSO_FALSE])
m4trace:configure.ac:491: -1- MM_ENABLE_PLUGIN([pantech])
m4trace:configure.ac:491: -1- m4_pattern_allow([^ENABLE_PLUGIN_PANTECH$])
m4trace:configure.ac:491: -1- AM_CONDITIONAL([ENABLE_PLUGIN_PANTECH], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:491: -1- m4_pattern_allow([^ENABLE_PLUGIN_PANTECH_TRUE$])
m4trace:configure.ac:491: -1- m4_pattern_allow([^ENABLE_PLUGIN_PANTECH_FALSE$])
m4trace:configure.ac:491: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_PANTECH_TRUE])
m4trace:configure.ac:491: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_PANTECH_FALSE])
m4trace:configure.ac:492: -1- MM_ENABLE_PLUGIN([quectel])
m4trace:configure.ac:492: -1- m4_pattern_allow([^ENABLE_PLUGIN_QUECTEL$])
m4trace:configure.ac:492: -1- AM_CONDITIONAL([ENABLE_PLUGIN_QUECTEL], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:492: -1- m4_pattern_allow([^ENABLE_PLUGIN_QUECTEL_TRUE$])
m4trace:configure.ac:492: -1- m4_pattern_allow([^ENABLE_PLUGIN_QUECTEL_FALSE$])
m4trace:configure.ac:492: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_QUECTEL_TRUE])
m4trace:configure.ac:492: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_QUECTEL_FALSE])
m4trace:configure.ac:493: -1- MM_ENABLE_PLUGIN([samsung], [with_shared_icera])
m4trace:configure.ac:493: -1- m4_pattern_allow([^ENABLE_PLUGIN_SAMSUNG$])
m4trace:configure.ac:493: -1- AM_CONDITIONAL([ENABLE_PLUGIN_SAMSUNG], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:493: -1- m4_pattern_allow([^ENABLE_PLUGIN_SAMSUNG_TRUE$])
m4trace:configure.ac:493: -1- m4_pattern_allow([^ENABLE_PLUGIN_SAMSUNG_FALSE$])
m4trace:configure.ac:493: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SAMSUNG_TRUE])
m4trace:configure.ac:493: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SAMSUNG_FALSE])
m4trace:configure.ac:495: -1- MM_ENABLE_PLUGIN([sierra-legacy], [with_shared_icera,
                  with_shared_sierra])
m4trace:configure.ac:495: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIERRA_LEGACY$])
m4trace:configure.ac:495: -1- AM_CONDITIONAL([ENABLE_PLUGIN_SIERRA_LEGACY], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:495: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIERRA_LEGACY_TRUE$])
m4trace:configure.ac:495: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIERRA_LEGACY_FALSE$])
m4trace:configure.ac:495: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SIERRA_LEGACY_TRUE])
m4trace:configure.ac:495: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SIERRA_LEGACY_FALSE])
m4trace:configure.ac:498: -1- MM_ENABLE_PLUGIN([sierra])
m4trace:configure.ac:498: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIERRA$])
m4trace:configure.ac:498: -1- AM_CONDITIONAL([ENABLE_PLUGIN_SIERRA], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:498: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIERRA_TRUE$])
m4trace:configure.ac:498: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIERRA_FALSE$])
m4trace:configure.ac:498: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SIERRA_TRUE])
m4trace:configure.ac:498: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SIERRA_FALSE])
m4trace:configure.ac:499: -1- MM_ENABLE_PLUGIN([simtech])
m4trace:configure.ac:499: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIMTECH$])
m4trace:configure.ac:499: -1- AM_CONDITIONAL([ENABLE_PLUGIN_SIMTECH], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:499: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIMTECH_TRUE$])
m4trace:configure.ac:499: -1- m4_pattern_allow([^ENABLE_PLUGIN_SIMTECH_FALSE$])
m4trace:configure.ac:499: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SIMTECH_TRUE])
m4trace:configure.ac:499: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_SIMTECH_FALSE])
m4trace:configure.ac:500: -1- MM_ENABLE_PLUGIN([telit], [with_shared_telit])
m4trace:configure.ac:500: -1- m4_pattern_allow([^ENABLE_PLUGIN_TELIT$])
m4trace:configure.ac:500: -1- AM_CONDITIONAL([ENABLE_PLUGIN_TELIT], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:500: -1- m4_pattern_allow([^ENABLE_PLUGIN_TELIT_TRUE$])
m4trace:configure.ac:500: -1- m4_pattern_allow([^ENABLE_PLUGIN_TELIT_FALSE$])
m4trace:configure.ac:500: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_TELIT_TRUE])
m4trace:configure.ac:500: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_TELIT_FALSE])
m4trace:configure.ac:502: -1- MM_ENABLE_PLUGIN([thuraya])
m4trace:configure.ac:502: -1- m4_pattern_allow([^ENABLE_PLUGIN_THURAYA$])
m4trace:configure.ac:502: -1- AM_CONDITIONAL([ENABLE_PLUGIN_THURAYA], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:502: -1- m4_pattern_allow([^ENABLE_PLUGIN_THURAYA_TRUE$])
m4trace:configure.ac:502: -1- m4_pattern_allow([^ENABLE_PLUGIN_THURAYA_FALSE$])
m4trace:configure.ac:502: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_THURAYA_TRUE])
m4trace:configure.ac:502: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_THURAYA_FALSE])
m4trace:configure.ac:503: -1- MM_ENABLE_PLUGIN([tplink])
m4trace:configure.ac:503: -1- m4_pattern_allow([^ENABLE_PLUGIN_TPLINK$])
m4trace:configure.ac:503: -1- AM_CONDITIONAL([ENABLE_PLUGIN_TPLINK], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:503: -1- m4_pattern_allow([^ENABLE_PLUGIN_TPLINK_TRUE$])
m4trace:configure.ac:503: -1- m4_pattern_allow([^ENABLE_PLUGIN_TPLINK_FALSE$])
m4trace:configure.ac:503: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_TPLINK_TRUE])
m4trace:configure.ac:503: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_TPLINK_FALSE])
m4trace:configure.ac:504: -1- MM_ENABLE_PLUGIN([ublox])
m4trace:configure.ac:504: -1- m4_pattern_allow([^ENABLE_PLUGIN_UBLOX$])
m4trace:configure.ac:504: -1- AM_CONDITIONAL([ENABLE_PLUGIN_UBLOX], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:504: -1- m4_pattern_allow([^ENABLE_PLUGIN_UBLOX_TRUE$])
m4trace:configure.ac:504: -1- m4_pattern_allow([^ENABLE_PLUGIN_UBLOX_FALSE$])
m4trace:configure.ac:504: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_UBLOX_TRUE])
m4trace:configure.ac:504: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_UBLOX_FALSE])
m4trace:configure.ac:505: -1- MM_ENABLE_PLUGIN([via])
m4trace:configure.ac:505: -1- m4_pattern_allow([^ENABLE_PLUGIN_VIA$])
m4trace:configure.ac:505: -1- AM_CONDITIONAL([ENABLE_PLUGIN_VIA], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:505: -1- m4_pattern_allow([^ENABLE_PLUGIN_VIA_TRUE$])
m4trace:configure.ac:505: -1- m4_pattern_allow([^ENABLE_PLUGIN_VIA_FALSE$])
m4trace:configure.ac:505: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_VIA_TRUE])
m4trace:configure.ac:505: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_VIA_FALSE])
m4trace:configure.ac:506: -1- MM_ENABLE_PLUGIN([wavecom])
m4trace:configure.ac:506: -1- m4_pattern_allow([^ENABLE_PLUGIN_WAVECOM$])
m4trace:configure.ac:506: -1- AM_CONDITIONAL([ENABLE_PLUGIN_WAVECOM], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:506: -1- m4_pattern_allow([^ENABLE_PLUGIN_WAVECOM_TRUE$])
m4trace:configure.ac:506: -1- m4_pattern_allow([^ENABLE_PLUGIN_WAVECOM_FALSE$])
m4trace:configure.ac:506: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_WAVECOM_TRUE])
m4trace:configure.ac:506: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_WAVECOM_FALSE])
m4trace:configure.ac:507: -1- MM_ENABLE_PLUGIN([x22x])
m4trace:configure.ac:507: -1- m4_pattern_allow([^ENABLE_PLUGIN_X22X$])
m4trace:configure.ac:507: -1- AM_CONDITIONAL([ENABLE_PLUGIN_X22X], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:507: -1- m4_pattern_allow([^ENABLE_PLUGIN_X22X_TRUE$])
m4trace:configure.ac:507: -1- m4_pattern_allow([^ENABLE_PLUGIN_X22X_FALSE$])
m4trace:configure.ac:507: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_X22X_TRUE])
m4trace:configure.ac:507: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_X22X_FALSE])
m4trace:configure.ac:508: -1- MM_ENABLE_PLUGIN([zte], [with_shared_icera])
m4trace:configure.ac:508: -1- m4_pattern_allow([^ENABLE_PLUGIN_ZTE$])
m4trace:configure.ac:508: -1- AM_CONDITIONAL([ENABLE_PLUGIN_ZTE], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:508: -1- m4_pattern_allow([^ENABLE_PLUGIN_ZTE_TRUE$])
m4trace:configure.ac:508: -1- m4_pattern_allow([^ENABLE_PLUGIN_ZTE_FALSE$])
m4trace:configure.ac:508: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ZTE_TRUE])
m4trace:configure.ac:508: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ZTE_FALSE])
m4trace:configure.ac:510: -1- MM_ENABLE_PLUGIN([me3630])
m4trace:configure.ac:510: -1- m4_pattern_allow([^ENABLE_PLUGIN_ME3630$])
m4trace:configure.ac:510: -1- AM_CONDITIONAL([ENABLE_PLUGIN_ME3630], [test "x$var_enable_plugin" = "xyes"])
m4trace:configure.ac:510: -1- m4_pattern_allow([^ENABLE_PLUGIN_ME3630_TRUE$])
m4trace:configure.ac:510: -1- m4_pattern_allow([^ENABLE_PLUGIN_ME3630_FALSE$])
m4trace:configure.ac:510: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ME3630_TRUE])
m4trace:configure.ac:510: -1- _AM_SUBST_NOTMAKE([ENABLE_PLUGIN_ME3630_FALSE])
m4trace:configure.ac:512: -1- MM_BUILD_SHARED([icera])
m4trace:configure.ac:512: -1- AM_CONDITIONAL([WITH_SHARED_ICERA], [test "x$with_shared_icera" = "xyes"])
m4trace:configure.ac:512: -1- m4_pattern_allow([^WITH_SHARED_ICERA_TRUE$])
m4trace:configure.ac:512: -1- m4_pattern_allow([^WITH_SHARED_ICERA_FALSE$])
m4trace:configure.ac:512: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_ICERA_TRUE])
m4trace:configure.ac:512: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_ICERA_FALSE])
m4trace:configure.ac:512: -1- m4_pattern_allow([^WITH_SHARED_ICERA$])
m4trace:configure.ac:513: -1- MM_BUILD_SHARED([sierra])
m4trace:configure.ac:513: -1- AM_CONDITIONAL([WITH_SHARED_SIERRA], [test "x$with_shared_sierra" = "xyes"])
m4trace:configure.ac:513: -1- m4_pattern_allow([^WITH_SHARED_SIERRA_TRUE$])
m4trace:configure.ac:513: -1- m4_pattern_allow([^WITH_SHARED_SIERRA_FALSE$])
m4trace:configure.ac:513: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_SIERRA_TRUE])
m4trace:configure.ac:513: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_SIERRA_FALSE])
m4trace:configure.ac:513: -1- m4_pattern_allow([^WITH_SHARED_SIERRA$])
m4trace:configure.ac:514: -1- MM_BUILD_SHARED([option])
m4trace:configure.ac:514: -1- AM_CONDITIONAL([WITH_SHARED_OPTION], [test "x$with_shared_option" = "xyes"])
m4trace:configure.ac:514: -1- m4_pattern_allow([^WITH_SHARED_OPTION_TRUE$])
m4trace:configure.ac:514: -1- m4_pattern_allow([^WITH_SHARED_OPTION_FALSE$])
m4trace:configure.ac:514: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_OPTION_TRUE])
m4trace:configure.ac:514: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_OPTION_FALSE])
m4trace:configure.ac:514: -1- m4_pattern_allow([^WITH_SHARED_OPTION$])
m4trace:configure.ac:515: -1- MM_BUILD_SHARED([novatel])
m4trace:configure.ac:515: -1- AM_CONDITIONAL([WITH_SHARED_NOVATEL], [test "x$with_shared_novatel" = "xyes"])
m4trace:configure.ac:515: -1- m4_pattern_allow([^WITH_SHARED_NOVATEL_TRUE$])
m4trace:configure.ac:515: -1- m4_pattern_allow([^WITH_SHARED_NOVATEL_FALSE$])
m4trace:configure.ac:515: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_NOVATEL_TRUE])
m4trace:configure.ac:515: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_NOVATEL_FALSE])
m4trace:configure.ac:515: -1- m4_pattern_allow([^WITH_SHARED_NOVATEL$])
m4trace:configure.ac:516: -1- MM_BUILD_SHARED([xmm])
m4trace:configure.ac:516: -1- AM_CONDITIONAL([WITH_SHARED_XMM], [test "x$with_shared_xmm" = "xyes"])
m4trace:configure.ac:516: -1- m4_pattern_allow([^WITH_SHARED_XMM_TRUE$])
m4trace:configure.ac:516: -1- m4_pattern_allow([^WITH_SHARED_XMM_FALSE$])
m4trace:configure.ac:516: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_XMM_TRUE])
m4trace:configure.ac:516: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_XMM_FALSE])
m4trace:configure.ac:516: -1- m4_pattern_allow([^WITH_SHARED_XMM$])
m4trace:configure.ac:517: -1- MM_BUILD_SHARED([telit])
m4trace:configure.ac:517: -1- AM_CONDITIONAL([WITH_SHARED_TELIT], [test "x$with_shared_telit" = "xyes"])
m4trace:configure.ac:517: -1- m4_pattern_allow([^WITH_SHARED_TELIT_TRUE$])
m4trace:configure.ac:517: -1- m4_pattern_allow([^WITH_SHARED_TELIT_FALSE$])
m4trace:configure.ac:517: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_TELIT_TRUE])
m4trace:configure.ac:517: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_TELIT_FALSE])
m4trace:configure.ac:517: -1- m4_pattern_allow([^WITH_SHARED_TELIT$])
m4trace:configure.ac:518: -1- MM_BUILD_SHARED([foxconn])
m4trace:configure.ac:518: -1- AM_CONDITIONAL([WITH_SHARED_FOXCONN], [test "x$with_shared_foxconn" = "xyes"])
m4trace:configure.ac:518: -1- m4_pattern_allow([^WITH_SHARED_FOXCONN_TRUE$])
m4trace:configure.ac:518: -1- m4_pattern_allow([^WITH_SHARED_FOXCONN_FALSE$])
m4trace:configure.ac:518: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_FOXCONN_TRUE])
m4trace:configure.ac:518: -1- _AM_SUBST_NOTMAKE([WITH_SHARED_FOXCONN_FALSE])
m4trace:configure.ac:518: -1- m4_pattern_allow([^WITH_SHARED_FOXCONN$])
m4trace:configure.ac:563: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:563: -1- m4_pattern_allow([^LTLIBOBJS$])
m4trace:configure.ac:563: -1- AM_CONDITIONAL([am__EXEEXT], [test -n "$EXEEXT"])
m4trace:configure.ac:563: -1- m4_pattern_allow([^am__EXEEXT_TRUE$])
m4trace:configure.ac:563: -1- m4_pattern_allow([^am__EXEEXT_FALSE$])
m4trace:configure.ac:563: -1- _AM_SUBST_NOTMAKE([am__EXEEXT_TRUE])
m4trace:configure.ac:563: -1- _AM_SUBST_NOTMAKE([am__EXEEXT_FALSE])
m4trace:configure.ac:563: -1- _AC_AM_CONFIG_HEADER_HOOK(["$ac_file"])
m4trace:configure.ac:563: -1- _AM_OUTPUT_DEPENDENCY_COMMANDS
m4trace:configure.ac:563: -1- AM_RUN_LOG([cd "$am_dirpart" \
      && sed -e '/# am--include-marker/d' "$am_filepart" \
        | $MAKE -f - am--depfiles])
m4trace:configure.ac:563: -1- _LT_PROG_LTMAIN

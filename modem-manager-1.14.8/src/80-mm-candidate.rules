# do not edit this file, it will be overwritten on update

# Tag any devices that MM might be interested in; if ModemManager is started
# up right after udev, when MM explicitly requests devices on startup it may
# get devices that haven't had all rules run yet.  Thus, we tag devices we're
# interested in and when handling devices during MM startup we ignore any
# that don't have this tag.  MM will still get the udev 'add' event for the
# device a short while later and then process it as normal.

ACTION!="add|change|move|bind", GOTO="mm_candidate_end"

# Opening bound but disconnected Bluetooth RFCOMM ttys would initiate the
# connection. Don't do that.
KERNEL=="rfcomm*", DEVPATH=="*/virtual/*", GOTO="mm_candidate_end"

SUBSYSTEM=="tty", ENV{ID_MM_CANDIDATE}="1"
SUBSYSTEM=="net", ENV{ID_MM_CANDIDATE}="1"
KERNEL=="cdc-wdm*", SUBSYSTEM=="usb", ENV{ID_MM_CANDIDATE}="1"
KERNEL=="cdc-wdm*", SUBSYSTEM=="usbmisc", ENV{ID_MM_CANDIDATE}="1"

LABEL="mm_candidate_end"

# Makefile.in generated by automake 1.16.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@



VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
sbin_PROGRAMS = ModemManager$(EXEEXT)
@WITH_QMI_TRUE@am__append_1 = $(QMI_CFLAGS)
@WITH_QMI_TRUE@am__append_2 = $(QMI_LIBS)
@WITH_MBIM_TRUE@am__append_3 = $(MBIM_CFLAGS)
@WITH_MBIM_TRUE@am__append_4 = $(MBIM_LIBS)
@WITH_POLKIT_TRUE@am__append_5 = $(POLKIT_CFLAGS)
@WITH_POLKIT_TRUE@am__append_6 = $(POLKIT_LIBS)
@WITH_SYSTEMD_JOURNAL_TRUE@am__append_7 = $(LIBSYSTEMD_CFLAGS)
@WITH_SYSTEMD_JOURNAL_TRUE@am__append_8 = $(LIBSYSTEMD_LIBS)
@WITH_QMI_TRUE@am__append_9 = \
@WITH_QMI_TRUE@	mm-modem-helpers-qmi.c \
@WITH_QMI_TRUE@	mm-modem-helpers-qmi.h \
@WITH_QMI_TRUE@	$(NULL)

@WITH_MBIM_TRUE@am__append_10 = \
@WITH_MBIM_TRUE@	mm-modem-helpers-mbim.c \
@WITH_MBIM_TRUE@	mm-modem-helpers-mbim.h \
@WITH_MBIM_TRUE@	$(NULL)

@WITH_UDEV_TRUE@am__append_11 = \
@WITH_UDEV_TRUE@	kerneldevice/mm-kernel-device-udev.h \
@WITH_UDEV_TRUE@	kerneldevice/mm-kernel-device-udev.c \
@WITH_UDEV_TRUE@	$(NULL)

@WITH_QMI_TRUE@am__append_12 = \
@WITH_QMI_TRUE@	mm-port-qmi.c \
@WITH_QMI_TRUE@	mm-port-qmi.h \
@WITH_QMI_TRUE@	$(NULL)

@WITH_MBIM_TRUE@am__append_13 = \
@WITH_MBIM_TRUE@	mm-port-mbim.c \
@WITH_MBIM_TRUE@	mm-port-mbim.h \
@WITH_MBIM_TRUE@	$(NULL)


# Additional suspend/resume support via systemd
@WITH_SYSTEMD_SUSPEND_RESUME_TRUE@am__append_14 = mm-sleep-monitor.h mm-sleep-monitor.c

# Additional QMI support in ModemManager
@WITH_QMI_TRUE@am__append_15 = \
@WITH_QMI_TRUE@	mm-shared-qmi.h \
@WITH_QMI_TRUE@	mm-shared-qmi.c \
@WITH_QMI_TRUE@	mm-sms-qmi.h \
@WITH_QMI_TRUE@	mm-sms-qmi.c \
@WITH_QMI_TRUE@	mm-sim-qmi.h \
@WITH_QMI_TRUE@	mm-sim-qmi.c \
@WITH_QMI_TRUE@	mm-bearer-qmi.h \
@WITH_QMI_TRUE@	mm-bearer-qmi.c \
@WITH_QMI_TRUE@	mm-broadband-modem-qmi.h \
@WITH_QMI_TRUE@	mm-broadband-modem-qmi.c \
@WITH_QMI_TRUE@	$(NULL)


# Additional MBIM support in ModemManager
@WITH_MBIM_TRUE@am__append_16 = \
@WITH_MBIM_TRUE@	mm-sms-mbim.h \
@WITH_MBIM_TRUE@	mm-sms-mbim.c \
@WITH_MBIM_TRUE@	mm-sim-mbim.h \
@WITH_MBIM_TRUE@	mm-sim-mbim.c \
@WITH_MBIM_TRUE@	mm-bearer-mbim.h \
@WITH_MBIM_TRUE@	mm-bearer-mbim.c \
@WITH_MBIM_TRUE@	mm-broadband-modem-mbim.h \
@WITH_MBIM_TRUE@	mm-broadband-modem-mbim.c \
@WITH_MBIM_TRUE@	$(NULL)

subdir = src
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_code_coverage.m4 \
	$(top_srcdir)/m4/gettext.m4 $(top_srcdir)/m4/gtk-doc.m4 \
	$(top_srcdir)/m4/iconv.m4 $(top_srcdir)/m4/intlmacosx.m4 \
	$(top_srcdir)/m4/introspection.m4 $(top_srcdir)/m4/lib-ld.m4 \
	$(top_srcdir)/m4/lib-link.m4 $(top_srcdir)/m4/lib-prefix.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/mm-enable-plugin.m4 $(top_srcdir)/m4/nls.m4 \
	$(top_srcdir)/m4/po.m4 $(top_srcdir)/m4/progtest.m4 \
	$(top_srcdir)/m4/vapigen.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__installdirs = "$(DESTDIR)$(sbindir)" "$(DESTDIR)$(udevrulesdir)"
PROGRAMS = $(sbin_PROGRAMS)
LTLIBRARIES = $(noinst_LTLIBRARIES)
libhelpers_la_LIBADD =
am__libhelpers_la_SOURCES_DIST = mm-log-object.h mm-log-object.c \
	mm-log.c mm-log.h mm-log-test.h mm-error-helpers.c \
	mm-error-helpers.h mm-modem-helpers.c mm-modem-helpers.h \
	mm-charsets.c mm-charsets.h mm-sms-part.h mm-sms-part.c \
	mm-sms-part-3gpp.h mm-sms-part-3gpp.c mm-sms-part-cdma.h \
	mm-sms-part-cdma.c mm-modem-helpers-qmi.c \
	mm-modem-helpers-qmi.h mm-modem-helpers-mbim.c \
	mm-modem-helpers-mbim.h
@WITH_QMI_TRUE@am__objects_1 = mm-modem-helpers-qmi.lo
@WITH_MBIM_TRUE@am__objects_2 = mm-modem-helpers-mbim.lo
am_libhelpers_la_OBJECTS = mm-log-object.lo mm-log.lo \
	mm-error-helpers.lo mm-modem-helpers.lo mm-charsets.lo \
	mm-sms-part.lo mm-sms-part-3gpp.lo mm-sms-part-cdma.lo \
	$(am__objects_1) $(am__objects_2)
am__objects_3 = mm-helper-enums-types.lo
nodist_libhelpers_la_OBJECTS = $(am__objects_3)
libhelpers_la_OBJECTS = $(am_libhelpers_la_OBJECTS) \
	$(nodist_libhelpers_la_OBJECTS)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
libkerneldevice_la_DEPENDENCIES =  \
	$(top_builddir)/libmm-glib/libmm-glib.la \
	$(builddir)/libhelpers.la
am__libkerneldevice_la_SOURCES_DIST = kerneldevice/mm-kernel-device.h \
	kerneldevice/mm-kernel-device.c \
	kerneldevice/mm-kernel-device-generic.h \
	kerneldevice/mm-kernel-device-generic.c \
	kerneldevice/mm-kernel-device-generic-rules.h \
	kerneldevice/mm-kernel-device-generic-rules.c \
	kerneldevice/mm-kernel-device-udev.h \
	kerneldevice/mm-kernel-device-udev.c
am__dirstamp = $(am__leading_dot)dirstamp
@WITH_UDEV_TRUE@am__objects_4 = kerneldevice/libkerneldevice_la-mm-kernel-device-udev.lo
am_libkerneldevice_la_OBJECTS =  \
	kerneldevice/libkerneldevice_la-mm-kernel-device.lo \
	kerneldevice/libkerneldevice_la-mm-kernel-device-generic.lo \
	kerneldevice/libkerneldevice_la-mm-kernel-device-generic-rules.lo \
	$(am__objects_4)
libkerneldevice_la_OBJECTS = $(am_libkerneldevice_la_OBJECTS)
libport_la_DEPENDENCIES = $(top_builddir)/libqcdm/src/libqcdm.la \
	$(top_builddir)/libmm-glib/libmm-glib.la \
	$(builddir)/libkerneldevice.la
am__libport_la_SOURCES_DIST = mm-port.c mm-port.h mm-port-serial.c \
	mm-port-serial.h mm-port-serial-at.c mm-port-serial-at.h \
	mm-port-serial-qcdm.c mm-port-serial-qcdm.h \
	mm-port-serial-gps.c mm-port-serial-gps.h mm-serial-parsers.c \
	mm-serial-parsers.h mm-port-qmi.c mm-port-qmi.h mm-port-mbim.c \
	mm-port-mbim.h
@WITH_QMI_TRUE@am__objects_5 = mm-port-qmi.lo
@WITH_MBIM_TRUE@am__objects_6 = mm-port-mbim.lo
am_libport_la_OBJECTS = mm-port.lo mm-port-serial.lo \
	mm-port-serial-at.lo mm-port-serial-qcdm.lo \
	mm-port-serial-gps.lo mm-serial-parsers.lo $(am__objects_5) \
	$(am__objects_6)
am__objects_7 = mm-port-enums-types.lo
nodist_libport_la_OBJECTS = $(am__objects_7)
libport_la_OBJECTS = $(am_libport_la_OBJECTS) \
	$(nodist_libport_la_OBJECTS)
am__ModemManager_SOURCES_DIST = main.c mm-context.h mm-context.c \
	mm-utils.h mm-private-boxed-types.h mm-private-boxed-types.c \
	mm-auth-provider.h mm-auth-provider.c mm-filter.h mm-filter.c \
	mm-base-manager.c mm-base-manager.h mm-device.c mm-device.h \
	mm-plugin-manager.c mm-plugin-manager.h mm-base-sim.h \
	mm-base-sim.c mm-base-bearer.h mm-base-bearer.c \
	mm-broadband-bearer.h mm-broadband-bearer.c mm-bearer-list.h \
	mm-bearer-list.c mm-base-modem-at.h mm-base-modem-at.c \
	mm-base-modem.h mm-base-modem.c mm-base-sms.h mm-base-sms.c \
	mm-base-call.h mm-base-call.c mm-sms-list.h mm-sms-list.c \
	mm-call-list.h mm-call-list.c mm-iface-modem.h \
	mm-iface-modem.c mm-iface-modem-3gpp.h mm-iface-modem-3gpp.c \
	mm-iface-modem-3gpp-ussd.h mm-iface-modem-3gpp-ussd.c \
	mm-iface-modem-cdma.h mm-iface-modem-cdma.c \
	mm-iface-modem-simple.h mm-iface-modem-simple.c \
	mm-iface-modem-location.h mm-iface-modem-location.c \
	mm-iface-modem-messaging.h mm-iface-modem-messaging.c \
	mm-iface-modem-voice.h mm-iface-modem-voice.c \
	mm-iface-modem-time.h mm-iface-modem-time.c \
	mm-iface-modem-firmware.h mm-iface-modem-firmware.c \
	mm-iface-modem-signal.h mm-iface-modem-signal.c \
	mm-iface-modem-oma.h mm-iface-modem-oma.c mm-broadband-modem.h \
	mm-broadband-modem.c mm-port-probe.h mm-port-probe.c \
	mm-port-probe-at.h mm-port-probe-at.c mm-plugin.c mm-plugin.h \
	mm-shared.h mm-sleep-monitor.h mm-sleep-monitor.c \
	mm-shared-qmi.h mm-shared-qmi.c mm-sms-qmi.h mm-sms-qmi.c \
	mm-sim-qmi.h mm-sim-qmi.c mm-bearer-qmi.h mm-bearer-qmi.c \
	mm-broadband-modem-qmi.h mm-broadband-modem-qmi.c \
	mm-sms-mbim.h mm-sms-mbim.c mm-sim-mbim.h mm-sim-mbim.c \
	mm-bearer-mbim.h mm-bearer-mbim.c mm-broadband-modem-mbim.h \
	mm-broadband-modem-mbim.c
@WITH_SYSTEMD_SUSPEND_RESUME_TRUE@am__objects_8 = ModemManager-mm-sleep-monitor.$(OBJEXT)
@WITH_QMI_TRUE@am__objects_9 = ModemManager-mm-shared-qmi.$(OBJEXT) \
@WITH_QMI_TRUE@	ModemManager-mm-sms-qmi.$(OBJEXT) \
@WITH_QMI_TRUE@	ModemManager-mm-sim-qmi.$(OBJEXT) \
@WITH_QMI_TRUE@	ModemManager-mm-bearer-qmi.$(OBJEXT) \
@WITH_QMI_TRUE@	ModemManager-mm-broadband-modem-qmi.$(OBJEXT)
@WITH_MBIM_TRUE@am__objects_10 = ModemManager-mm-sms-mbim.$(OBJEXT) \
@WITH_MBIM_TRUE@	ModemManager-mm-sim-mbim.$(OBJEXT) \
@WITH_MBIM_TRUE@	ModemManager-mm-bearer-mbim.$(OBJEXT) \
@WITH_MBIM_TRUE@	ModemManager-mm-broadband-modem-mbim.$(OBJEXT)
am_ModemManager_OBJECTS = ModemManager-main.$(OBJEXT) \
	ModemManager-mm-context.$(OBJEXT) \
	ModemManager-mm-private-boxed-types.$(OBJEXT) \
	ModemManager-mm-auth-provider.$(OBJEXT) \
	ModemManager-mm-filter.$(OBJEXT) \
	ModemManager-mm-base-manager.$(OBJEXT) \
	ModemManager-mm-device.$(OBJEXT) \
	ModemManager-mm-plugin-manager.$(OBJEXT) \
	ModemManager-mm-base-sim.$(OBJEXT) \
	ModemManager-mm-base-bearer.$(OBJEXT) \
	ModemManager-mm-broadband-bearer.$(OBJEXT) \
	ModemManager-mm-bearer-list.$(OBJEXT) \
	ModemManager-mm-base-modem-at.$(OBJEXT) \
	ModemManager-mm-base-modem.$(OBJEXT) \
	ModemManager-mm-base-sms.$(OBJEXT) \
	ModemManager-mm-base-call.$(OBJEXT) \
	ModemManager-mm-sms-list.$(OBJEXT) \
	ModemManager-mm-call-list.$(OBJEXT) \
	ModemManager-mm-iface-modem.$(OBJEXT) \
	ModemManager-mm-iface-modem-3gpp.$(OBJEXT) \
	ModemManager-mm-iface-modem-3gpp-ussd.$(OBJEXT) \
	ModemManager-mm-iface-modem-cdma.$(OBJEXT) \
	ModemManager-mm-iface-modem-simple.$(OBJEXT) \
	ModemManager-mm-iface-modem-location.$(OBJEXT) \
	ModemManager-mm-iface-modem-messaging.$(OBJEXT) \
	ModemManager-mm-iface-modem-voice.$(OBJEXT) \
	ModemManager-mm-iface-modem-time.$(OBJEXT) \
	ModemManager-mm-iface-modem-firmware.$(OBJEXT) \
	ModemManager-mm-iface-modem-signal.$(OBJEXT) \
	ModemManager-mm-iface-modem-oma.$(OBJEXT) \
	ModemManager-mm-broadband-modem.$(OBJEXT) \
	ModemManager-mm-port-probe.$(OBJEXT) \
	ModemManager-mm-port-probe-at.$(OBJEXT) \
	ModemManager-mm-plugin.$(OBJEXT) $(am__objects_8) \
	$(am__objects_9) $(am__objects_10)
am__objects_11 = ModemManager-mm-daemon-enums-types.$(OBJEXT)
nodist_ModemManager_OBJECTS = $(am__objects_11)
ModemManager_OBJECTS = $(am_ModemManager_OBJECTS) \
	$(nodist_ModemManager_OBJECTS)
ModemManager_DEPENDENCIES = $(top_builddir)/libqcdm/src/libqcdm.la \
	$(top_builddir)/libmm-glib/libmm-glib.la \
	$(top_builddir)/libmm-glib/generated/tests/libmm-test-generated.la \
	$(builddir)/libport.la
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/ModemManager-main.Po \
	./$(DEPDIR)/ModemManager-mm-auth-provider.Po \
	./$(DEPDIR)/ModemManager-mm-base-bearer.Po \
	./$(DEPDIR)/ModemManager-mm-base-call.Po \
	./$(DEPDIR)/ModemManager-mm-base-manager.Po \
	./$(DEPDIR)/ModemManager-mm-base-modem-at.Po \
	./$(DEPDIR)/ModemManager-mm-base-modem.Po \
	./$(DEPDIR)/ModemManager-mm-base-sim.Po \
	./$(DEPDIR)/ModemManager-mm-base-sms.Po \
	./$(DEPDIR)/ModemManager-mm-bearer-list.Po \
	./$(DEPDIR)/ModemManager-mm-bearer-mbim.Po \
	./$(DEPDIR)/ModemManager-mm-bearer-qmi.Po \
	./$(DEPDIR)/ModemManager-mm-broadband-bearer.Po \
	./$(DEPDIR)/ModemManager-mm-broadband-modem-mbim.Po \
	./$(DEPDIR)/ModemManager-mm-broadband-modem-qmi.Po \
	./$(DEPDIR)/ModemManager-mm-broadband-modem.Po \
	./$(DEPDIR)/ModemManager-mm-call-list.Po \
	./$(DEPDIR)/ModemManager-mm-context.Po \
	./$(DEPDIR)/ModemManager-mm-daemon-enums-types.Po \
	./$(DEPDIR)/ModemManager-mm-device.Po \
	./$(DEPDIR)/ModemManager-mm-filter.Po \
	./$(DEPDIR)/ModemManager-mm-iface-modem-3gpp-ussd.Po \
	./$(DEPDIR)/ModemManager-mm-iface-modem-3gpp.Po \
	./$(DEPDIR)/ModemManager-mm-iface-modem-cdma.Po \
	./$(DEPDIR)/ModemManager-mm-iface-modem-firmware.Po \
	./$(DEPDIR)/ModemManager-mm-iface-modem-location.Po \
	./$(DEPDIR)/ModemManager-mm-iface-modem-messaging.Po \
	./$(DEPDIR)/ModemManager-mm-iface-modem-oma.Po \
	./$(DEPDIR)/ModemManager-mm-iface-modem-signal.Po \
	./$(DEPDIR)/ModemManager-mm-iface-modem-simple.Po \
	./$(DEPDIR)/ModemManager-mm-iface-modem-time.Po \
	./$(DEPDIR)/ModemManager-mm-iface-modem-voice.Po \
	./$(DEPDIR)/ModemManager-mm-iface-modem.Po \
	./$(DEPDIR)/ModemManager-mm-plugin-manager.Po \
	./$(DEPDIR)/ModemManager-mm-plugin.Po \
	./$(DEPDIR)/ModemManager-mm-port-probe-at.Po \
	./$(DEPDIR)/ModemManager-mm-port-probe.Po \
	./$(DEPDIR)/ModemManager-mm-private-boxed-types.Po \
	./$(DEPDIR)/ModemManager-mm-shared-qmi.Po \
	./$(DEPDIR)/ModemManager-mm-sim-mbim.Po \
	./$(DEPDIR)/ModemManager-mm-sim-qmi.Po \
	./$(DEPDIR)/ModemManager-mm-sleep-monitor.Po \
	./$(DEPDIR)/ModemManager-mm-sms-list.Po \
	./$(DEPDIR)/ModemManager-mm-sms-mbim.Po \
	./$(DEPDIR)/ModemManager-mm-sms-qmi.Po \
	./$(DEPDIR)/mm-charsets.Plo ./$(DEPDIR)/mm-error-helpers.Plo \
	./$(DEPDIR)/mm-helper-enums-types.Plo \
	./$(DEPDIR)/mm-log-object.Plo ./$(DEPDIR)/mm-log.Plo \
	./$(DEPDIR)/mm-modem-helpers-mbim.Plo \
	./$(DEPDIR)/mm-modem-helpers-qmi.Plo \
	./$(DEPDIR)/mm-modem-helpers.Plo \
	./$(DEPDIR)/mm-port-enums-types.Plo \
	./$(DEPDIR)/mm-port-mbim.Plo ./$(DEPDIR)/mm-port-qmi.Plo \
	./$(DEPDIR)/mm-port-serial-at.Plo \
	./$(DEPDIR)/mm-port-serial-gps.Plo \
	./$(DEPDIR)/mm-port-serial-qcdm.Plo \
	./$(DEPDIR)/mm-port-serial.Plo ./$(DEPDIR)/mm-port.Plo \
	./$(DEPDIR)/mm-serial-parsers.Plo \
	./$(DEPDIR)/mm-sms-part-3gpp.Plo \
	./$(DEPDIR)/mm-sms-part-cdma.Plo ./$(DEPDIR)/mm-sms-part.Plo \
	kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-generic-rules.Plo \
	kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-generic.Plo \
	kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-udev.Plo \
	kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device.Plo
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(libhelpers_la_SOURCES) $(nodist_libhelpers_la_SOURCES) \
	$(libkerneldevice_la_SOURCES) $(libport_la_SOURCES) \
	$(nodist_libport_la_SOURCES) $(ModemManager_SOURCES) \
	$(nodist_ModemManager_SOURCES)
DIST_SOURCES = $(am__libhelpers_la_SOURCES_DIST) \
	$(am__libkerneldevice_la_SOURCES_DIST) \
	$(am__libport_la_SOURCES_DIST) \
	$(am__ModemManager_SOURCES_DIST)
RECURSIVE_TARGETS = all-recursive check-recursive cscopelist-recursive \
	ctags-recursive dvi-recursive html-recursive info-recursive \
	install-data-recursive install-dvi-recursive \
	install-exec-recursive install-html-recursive \
	install-info-recursive install-pdf-recursive \
	install-ps-recursive install-recursive installcheck-recursive \
	installdirs-recursive pdf-recursive ps-recursive \
	tags-recursive uninstall-recursive
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
DATA = $(udevrules_DATA)
RECURSIVE_CLEAN_TARGETS = mostlyclean-recursive clean-recursive	\
  distclean-recursive maintainer-clean-recursive
am__recursive_targets = \
  $(RECURSIVE_TARGETS) \
  $(RECURSIVE_CLEAN_TARGETS) \
  $(am__extra_recursive_targets)
AM_RECURSIVE_TARGETS = $(am__recursive_targets:-recursive=) TAGS CTAGS \
	distdir distdir-am
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
DIST_SUBDIRS = $(SUBDIRS)
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
am__relativize = \
  dir0=`pwd`; \
  sed_first='s,^\([^/]*\)/.*$$,\1,'; \
  sed_rest='s,^[^/]*/*,,'; \
  sed_last='s,^.*/\([^/]*\)$$,\1,'; \
  sed_butlast='s,/*[^/]*$$,,'; \
  while test -n "$$dir1"; do \
    first=`echo "$$dir1" | sed -e "$$sed_first"`; \
    if test "$$first" != "."; then \
      if test "$$first" = ".."; then \
        dir2=`echo "$$dir0" | sed -e "$$sed_last"`/"$$dir2"; \
        dir0=`echo "$$dir0" | sed -e "$$sed_butlast"`; \
      else \
        first2=`echo "$$dir2" | sed -e "$$sed_first"`; \
        if test "$$first2" = "$$first"; then \
          dir2=`echo "$$dir2" | sed -e "$$sed_rest"`; \
        else \
          dir2="../$$dir2"; \
        fi; \
        dir0="$$dir0"/"$$first"; \
      fi; \
    fi; \
    dir1=`echo "$$dir1" | sed -e "$$sed_rest"`; \
  done; \
  reldir="$$dir2"
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CODE_COVERAGE_CFLAGS = @CODE_COVERAGE_CFLAGS@
CODE_COVERAGE_ENABLED = @CODE_COVERAGE_ENABLED@
CODE_COVERAGE_LDFLAGS = @CODE_COVERAGE_LDFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CYGPATH_W = @CYGPATH_W@
DBUS_SYS_DIR = @DBUS_SYS_DIR@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
GCOV = @GCOV@
GDBUS_CODEGEN = @GDBUS_CODEGEN@
GENHTML = @GENHTML@
GETTEXT_MACRO_VERSION = @GETTEXT_MACRO_VERSION@
GETTEXT_PACKAGE = @GETTEXT_PACKAGE@
GLIB_MKENUMS = @GLIB_MKENUMS@
GMSGFMT = @GMSGFMT@
GMSGFMT_015 = @GMSGFMT_015@
GREP = @GREP@
GTKDOC_CHECK = @GTKDOC_CHECK@
GTKDOC_CHECK_PATH = @GTKDOC_CHECK_PATH@
GTKDOC_DEPS_CFLAGS = @GTKDOC_DEPS_CFLAGS@
GTKDOC_DEPS_LIBS = @GTKDOC_DEPS_LIBS@
GTKDOC_MKPDF = @GTKDOC_MKPDF@
GTKDOC_REBASE = @GTKDOC_REBASE@
GUDEV_CFLAGS = @GUDEV_CFLAGS@
GUDEV_LIBS = @GUDEV_LIBS@
HTML_DIR = @HTML_DIR@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
INTLLIBS = @INTLLIBS@
INTL_MACOSX_LIBS = @INTL_MACOSX_LIBS@
INTROSPECTION_CFLAGS = @INTROSPECTION_CFLAGS@
INTROSPECTION_COMPILER = @INTROSPECTION_COMPILER@
INTROSPECTION_GENERATE = @INTROSPECTION_GENERATE@
INTROSPECTION_GIRDIR = @INTROSPECTION_GIRDIR@
INTROSPECTION_LIBS = @INTROSPECTION_LIBS@
INTROSPECTION_MAKEFILE = @INTROSPECTION_MAKEFILE@
INTROSPECTION_SCANNER = @INTROSPECTION_SCANNER@
INTROSPECTION_TYPELIBDIR = @INTROSPECTION_TYPELIBDIR@
LCOV = @LCOV@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBICONV = @LIBICONV@
LIBINTL = @LIBINTL@
LIBMM_GLIB_CFLAGS = @LIBMM_GLIB_CFLAGS@
LIBMM_GLIB_LIBS = @LIBMM_GLIB_LIBS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBSYSTEMD_CFLAGS = @LIBSYSTEMD_CFLAGS@
LIBSYSTEMD_LIBS = @LIBSYSTEMD_LIBS@
LIBSYSTEMD_LOGIN_CFLAGS = @LIBSYSTEMD_LOGIN_CFLAGS@
LIBSYSTEMD_LOGIN_LIBS = @LIBSYSTEMD_LOGIN_LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBICONV = @LTLIBICONV@
LTLIBINTL = @LTLIBINTL@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAINT = @MAINT@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MBIM_CFLAGS = @MBIM_CFLAGS@
MBIM_LIBS = @MBIM_LIBS@
MKDIR_P = @MKDIR_P@
MMCLI_CFLAGS = @MMCLI_CFLAGS@
MMCLI_LIBS = @MMCLI_LIBS@
MM_CFLAGS = @MM_CFLAGS@
MM_DEFAULT_USER_POLICY = @MM_DEFAULT_USER_POLICY@
MM_GLIB_LT_AGE = @MM_GLIB_LT_AGE@
MM_GLIB_LT_CURRENT = @MM_GLIB_LT_CURRENT@
MM_GLIB_LT_REVISION = @MM_GLIB_LT_REVISION@
MM_LIBS = @MM_LIBS@
MM_MAJOR_VERSION = @MM_MAJOR_VERSION@
MM_MICRO_VERSION = @MM_MICRO_VERSION@
MM_MINOR_VERSION = @MM_MINOR_VERSION@
MM_POLKIT_SERVICE = @MM_POLKIT_SERVICE@
MM_VERSION = @MM_VERSION@
MSGFMT = @MSGFMT@
MSGFMT_015 = @MSGFMT_015@
MSGMERGE = @MSGMERGE@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PKG_CONFIG = @PKG_CONFIG@
PKG_CONFIG_LIBDIR = @PKG_CONFIG_LIBDIR@
PKG_CONFIG_PATH = @PKG_CONFIG_PATH@
POLKIT_CFLAGS = @POLKIT_CFLAGS@
POLKIT_LIBS = @POLKIT_LIBS@
POSUB = @POSUB@
QMI_CFLAGS = @QMI_CFLAGS@
QMI_LIBS = @QMI_LIBS@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
SYSTEMD_UNIT_DIR = @SYSTEMD_UNIT_DIR@
UDEV_BASE_DIR = @UDEV_BASE_DIR@
USE_NLS = @USE_NLS@
VAPIGEN = @VAPIGEN@
VAPIGEN_MAKEFILE = @VAPIGEN_MAKEFILE@
VAPIGEN_VAPIDIR = @VAPIGEN_VAPIDIR@
VERSION = @VERSION@
WARN_CFLAGS = @WARN_CFLAGS@
WARN_LDFLAGS = @WARN_LDFLAGS@
WARN_SCANNERFLAGS = @WARN_SCANNERFLAGS@
XGETTEXT = @XGETTEXT@
XGETTEXT_015 = @XGETTEXT_015@
XGETTEXT_EXTRA_OPTIONS = @XGETTEXT_EXTRA_OPTIONS@
XSLTPROC_CHECK = @XSLTPROC_CHECK@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
SUBDIRS = . tests

################################################################################
# helpers library
################################################################################

################################################################################
# kerneldevice library
################################################################################

################################################################################
# ports library
################################################################################
noinst_LTLIBRARIES = libhelpers.la libkerneldevice.la libport.la
EXTRA_DIST = $(udevrules_DATA)

# Request to build enum types before anything else

# Request to build enum types before anything else

# Request to build enum types before anything else
BUILT_SOURCES = $(HELPER_ENUMS_GENERATED) $(PORT_ENUMS_GENERATED) \
	$(DAEMON_ENUMS_GENERATED)
CLEANFILES = $(HELPER_ENUMS_GENERATED) $(PORT_ENUMS_GENERATED) \
	$(DAEMON_ENUMS_GENERATED)
AM_CFLAGS = $(WARN_CFLAGS) $(MM_CFLAGS) $(CODE_COVERAGE_CFLAGS) \
	$(GUDEV_CFLAGS) -I$(top_srcdir) -I$(top_srcdir)/include \
	-I$(top_builddir)/include -I$(top_srcdir)/libqcdm/src \
	-I$(top_srcdir)/libmm-glib \
	-I${top_builddir}/libmm-glib/generated \
	-I${top_builddir}/libmm-glib/generated/tests \
	-I$(srcdir)/kerneldevice $(NULL) $(am__append_1) \
	$(am__append_3) $(am__append_5) $(am__append_7)
AM_LDFLAGS = $(WARN_LDFLAGS) $(MM_LIBS) $(CODE_COVERAGE_LDFLAGS) \
	$(GUDEV_LIBS) $(NULL) $(am__append_2) $(am__append_4) \
	$(am__append_6) $(am__append_8)

################################################################################
# generic udev rules
################################################################################
udevrulesdir = $(UDEV_BASE_DIR)/rules.d
udevrules_DATA = \
	77-mm-usb-device-blacklist.rules \
	77-mm-pcmcia-device-blacklist.rules \
	77-mm-usb-serial-adapters-greylist.rules \
	80-mm-candidate.rules \
	$(NULL)

HELPER_ENUMS_INPUTS = \
	$(srcdir)/mm-sms-part.h \
	$(srcdir)/mm-modem-helpers.h \
	$(NULL)

HELPER_ENUMS_GENERATED = \
	mm-helper-enums-types.h \
	mm-helper-enums-types.c \
	$(NULL)

libhelpers_la_SOURCES = mm-log-object.h mm-log-object.c mm-log.c \
	mm-log.h mm-log-test.h mm-error-helpers.c mm-error-helpers.h \
	mm-modem-helpers.c mm-modem-helpers.h mm-charsets.c \
	mm-charsets.h mm-sms-part.h mm-sms-part.c mm-sms-part-3gpp.h \
	mm-sms-part-3gpp.c mm-sms-part-cdma.h mm-sms-part-cdma.c \
	$(NULL) $(am__append_9) $(am__append_10)
nodist_libhelpers_la_SOURCES = $(HELPER_ENUMS_GENERATED)
libkerneldevice_la_CPPFLAGS = \
	-DUDEVRULESDIR=\"$(udevrulesdir)\" \
	$(NULL)

libkerneldevice_la_SOURCES = kerneldevice/mm-kernel-device.h \
	kerneldevice/mm-kernel-device.c \
	kerneldevice/mm-kernel-device-generic.h \
	kerneldevice/mm-kernel-device-generic.c \
	kerneldevice/mm-kernel-device-generic-rules.h \
	kerneldevice/mm-kernel-device-generic-rules.c $(NULL) \
	$(am__append_11)
libkerneldevice_la_LIBADD = \
	$(top_builddir)/libmm-glib/libmm-glib.la \
	$(builddir)/libhelpers.la \
	$(NULL)

PORT_ENUMS_INPUTS = \
	$(srcdir)/mm-port.h \
	$(srcdir)/mm-port-serial-at.h \
	$(NULL)

PORT_ENUMS_GENERATED = \
	mm-port-enums-types.h \
	mm-port-enums-types.c \
	$(NULL)

libport_la_SOURCES = mm-port.c mm-port.h mm-port-serial.c \
	mm-port-serial.h mm-port-serial-at.c mm-port-serial-at.h \
	mm-port-serial-qcdm.c mm-port-serial-qcdm.h \
	mm-port-serial-gps.c mm-port-serial-gps.h mm-serial-parsers.c \
	mm-serial-parsers.h $(NULL) $(am__append_12) $(am__append_13)
nodist_libport_la_SOURCES = $(PORT_ENUMS_GENERATED)
libport_la_LIBADD = \
	$(top_builddir)/libqcdm/src/libqcdm.la \
	$(top_builddir)/libmm-glib/libmm-glib.la \
	$(builddir)/libkerneldevice.la \
	$(NULL)

DAEMON_ENUMS_INPUTS = \
	$(srcdir)/mm-filter.h \
	$(srcdir)/mm-base-bearer.h \
	$(srcdir)/mm-port-probe.h  \
	$(NULL)

DAEMON_ENUMS_GENERATED = \
	mm-daemon-enums-types.h \
	mm-daemon-enums-types.c \
	$(NULL)

ModemManager_CPPFLAGS = \
	-DPLUGINDIR=\"$(pkglibdir)\" \
	-DMM_COMPILATION \
	$(NULL)

ModemManager_LDADD = \
	$(top_builddir)/libqcdm/src/libqcdm.la \
	$(top_builddir)/libmm-glib/libmm-glib.la \
	$(top_builddir)/libmm-glib/generated/tests/libmm-test-generated.la \
	$(builddir)/libport.la \
	$(NULL)

ModemManager_SOURCES = main.c mm-context.h mm-context.c mm-utils.h \
	mm-private-boxed-types.h mm-private-boxed-types.c \
	mm-auth-provider.h mm-auth-provider.c mm-filter.h mm-filter.c \
	mm-base-manager.c mm-base-manager.h mm-device.c mm-device.h \
	mm-plugin-manager.c mm-plugin-manager.h mm-base-sim.h \
	mm-base-sim.c mm-base-bearer.h mm-base-bearer.c \
	mm-broadband-bearer.h mm-broadband-bearer.c mm-bearer-list.h \
	mm-bearer-list.c mm-base-modem-at.h mm-base-modem-at.c \
	mm-base-modem.h mm-base-modem.c mm-base-sms.h mm-base-sms.c \
	mm-base-call.h mm-base-call.c mm-sms-list.h mm-sms-list.c \
	mm-call-list.h mm-call-list.c mm-iface-modem.h \
	mm-iface-modem.c mm-iface-modem-3gpp.h mm-iface-modem-3gpp.c \
	mm-iface-modem-3gpp-ussd.h mm-iface-modem-3gpp-ussd.c \
	mm-iface-modem-cdma.h mm-iface-modem-cdma.c \
	mm-iface-modem-simple.h mm-iface-modem-simple.c \
	mm-iface-modem-location.h mm-iface-modem-location.c \
	mm-iface-modem-messaging.h mm-iface-modem-messaging.c \
	mm-iface-modem-voice.h mm-iface-modem-voice.c \
	mm-iface-modem-time.h mm-iface-modem-time.c \
	mm-iface-modem-firmware.h mm-iface-modem-firmware.c \
	mm-iface-modem-signal.h mm-iface-modem-signal.c \
	mm-iface-modem-oma.h mm-iface-modem-oma.c mm-broadband-modem.h \
	mm-broadband-modem.c mm-port-probe.h mm-port-probe.c \
	mm-port-probe-at.h mm-port-probe-at.c mm-plugin.c mm-plugin.h \
	mm-shared.h $(NULL) $(am__append_14) $(am__append_15) \
	$(am__append_16)
nodist_ModemManager_SOURCES = $(DAEMON_ENUMS_GENERATED)
all: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) all-recursive

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in: @MAINTAINER_MODE_TRUE@ $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu src/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu src/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure: @MAINTAINER_MODE_TRUE@ $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4): @MAINTAINER_MODE_TRUE@ $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
install-sbinPROGRAMS: $(sbin_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(sbin_PROGRAMS)'; test -n "$(sbindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(sbindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(sbindir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	 || test -f $$p1 \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	    echo " $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(sbindir)$$dir'"; \
	    $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(sbindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-sbinPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(sbin_PROGRAMS)'; test -n "$(sbindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(sbindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(sbindir)" && rm -f $$files

clean-sbinPROGRAMS:
	@list='$(sbin_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

clean-noinstLTLIBRARIES:
	-test -z "$(noinst_LTLIBRARIES)" || rm -f $(noinst_LTLIBRARIES)
	@list='$(noinst_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}

libhelpers.la: $(libhelpers_la_OBJECTS) $(libhelpers_la_DEPENDENCIES) $(EXTRA_libhelpers_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK)  $(libhelpers_la_OBJECTS) $(libhelpers_la_LIBADD) $(LIBS)
kerneldevice/$(am__dirstamp):
	@$(MKDIR_P) kerneldevice
	@: > kerneldevice/$(am__dirstamp)
kerneldevice/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) kerneldevice/$(DEPDIR)
	@: > kerneldevice/$(DEPDIR)/$(am__dirstamp)
kerneldevice/libkerneldevice_la-mm-kernel-device.lo:  \
	kerneldevice/$(am__dirstamp) \
	kerneldevice/$(DEPDIR)/$(am__dirstamp)
kerneldevice/libkerneldevice_la-mm-kernel-device-generic.lo:  \
	kerneldevice/$(am__dirstamp) \
	kerneldevice/$(DEPDIR)/$(am__dirstamp)
kerneldevice/libkerneldevice_la-mm-kernel-device-generic-rules.lo:  \
	kerneldevice/$(am__dirstamp) \
	kerneldevice/$(DEPDIR)/$(am__dirstamp)
kerneldevice/libkerneldevice_la-mm-kernel-device-udev.lo:  \
	kerneldevice/$(am__dirstamp) \
	kerneldevice/$(DEPDIR)/$(am__dirstamp)

libkerneldevice.la: $(libkerneldevice_la_OBJECTS) $(libkerneldevice_la_DEPENDENCIES) $(EXTRA_libkerneldevice_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK)  $(libkerneldevice_la_OBJECTS) $(libkerneldevice_la_LIBADD) $(LIBS)

libport.la: $(libport_la_OBJECTS) $(libport_la_DEPENDENCIES) $(EXTRA_libport_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK)  $(libport_la_OBJECTS) $(libport_la_LIBADD) $(LIBS)

ModemManager$(EXEEXT): $(ModemManager_OBJECTS) $(ModemManager_DEPENDENCIES) $(EXTRA_ModemManager_DEPENDENCIES) 
	@rm -f ModemManager$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(ModemManager_OBJECTS) $(ModemManager_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f kerneldevice/*.$(OBJEXT)
	-rm -f kerneldevice/*.lo

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-auth-provider.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-base-bearer.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-base-call.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-base-manager.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-base-modem-at.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-base-modem.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-base-sim.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-base-sms.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-bearer-list.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-bearer-mbim.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-bearer-qmi.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-broadband-bearer.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-broadband-modem-mbim.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-broadband-modem-qmi.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-broadband-modem.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-call-list.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-context.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-daemon-enums-types.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-device.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-filter.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-iface-modem-3gpp-ussd.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-iface-modem-3gpp.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-iface-modem-cdma.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-iface-modem-firmware.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-iface-modem-location.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-iface-modem-messaging.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-iface-modem-oma.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-iface-modem-signal.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-iface-modem-simple.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-iface-modem-time.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-iface-modem-voice.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-iface-modem.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-plugin-manager.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-plugin.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-port-probe-at.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-port-probe.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-private-boxed-types.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-shared-qmi.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-sim-mbim.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-sim-qmi.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-sleep-monitor.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-sms-list.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-sms-mbim.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ModemManager-mm-sms-qmi.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-charsets.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-error-helpers.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-helper-enums-types.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-log-object.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-log.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-modem-helpers-mbim.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-modem-helpers-qmi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-modem-helpers.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-port-enums-types.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-port-mbim.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-port-qmi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-port-serial-at.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-port-serial-gps.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-port-serial-qcdm.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-port-serial.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-port.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-serial-parsers.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-sms-part-3gpp.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-sms-part-cdma.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mm-sms-part.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-generic-rules.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-generic.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-udev.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device.Plo@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

kerneldevice/libkerneldevice_la-mm-kernel-device.lo: kerneldevice/mm-kernel-device.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libkerneldevice_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT kerneldevice/libkerneldevice_la-mm-kernel-device.lo -MD -MP -MF kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device.Tpo -c -o kerneldevice/libkerneldevice_la-mm-kernel-device.lo `test -f 'kerneldevice/mm-kernel-device.c' || echo '$(srcdir)/'`kerneldevice/mm-kernel-device.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device.Tpo kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='kerneldevice/mm-kernel-device.c' object='kerneldevice/libkerneldevice_la-mm-kernel-device.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libkerneldevice_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o kerneldevice/libkerneldevice_la-mm-kernel-device.lo `test -f 'kerneldevice/mm-kernel-device.c' || echo '$(srcdir)/'`kerneldevice/mm-kernel-device.c

kerneldevice/libkerneldevice_la-mm-kernel-device-generic.lo: kerneldevice/mm-kernel-device-generic.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libkerneldevice_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT kerneldevice/libkerneldevice_la-mm-kernel-device-generic.lo -MD -MP -MF kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-generic.Tpo -c -o kerneldevice/libkerneldevice_la-mm-kernel-device-generic.lo `test -f 'kerneldevice/mm-kernel-device-generic.c' || echo '$(srcdir)/'`kerneldevice/mm-kernel-device-generic.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-generic.Tpo kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-generic.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='kerneldevice/mm-kernel-device-generic.c' object='kerneldevice/libkerneldevice_la-mm-kernel-device-generic.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libkerneldevice_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o kerneldevice/libkerneldevice_la-mm-kernel-device-generic.lo `test -f 'kerneldevice/mm-kernel-device-generic.c' || echo '$(srcdir)/'`kerneldevice/mm-kernel-device-generic.c

kerneldevice/libkerneldevice_la-mm-kernel-device-generic-rules.lo: kerneldevice/mm-kernel-device-generic-rules.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libkerneldevice_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT kerneldevice/libkerneldevice_la-mm-kernel-device-generic-rules.lo -MD -MP -MF kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-generic-rules.Tpo -c -o kerneldevice/libkerneldevice_la-mm-kernel-device-generic-rules.lo `test -f 'kerneldevice/mm-kernel-device-generic-rules.c' || echo '$(srcdir)/'`kerneldevice/mm-kernel-device-generic-rules.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-generic-rules.Tpo kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-generic-rules.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='kerneldevice/mm-kernel-device-generic-rules.c' object='kerneldevice/libkerneldevice_la-mm-kernel-device-generic-rules.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libkerneldevice_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o kerneldevice/libkerneldevice_la-mm-kernel-device-generic-rules.lo `test -f 'kerneldevice/mm-kernel-device-generic-rules.c' || echo '$(srcdir)/'`kerneldevice/mm-kernel-device-generic-rules.c

kerneldevice/libkerneldevice_la-mm-kernel-device-udev.lo: kerneldevice/mm-kernel-device-udev.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libkerneldevice_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT kerneldevice/libkerneldevice_la-mm-kernel-device-udev.lo -MD -MP -MF kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-udev.Tpo -c -o kerneldevice/libkerneldevice_la-mm-kernel-device-udev.lo `test -f 'kerneldevice/mm-kernel-device-udev.c' || echo '$(srcdir)/'`kerneldevice/mm-kernel-device-udev.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-udev.Tpo kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-udev.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='kerneldevice/mm-kernel-device-udev.c' object='kerneldevice/libkerneldevice_la-mm-kernel-device-udev.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libkerneldevice_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o kerneldevice/libkerneldevice_la-mm-kernel-device-udev.lo `test -f 'kerneldevice/mm-kernel-device-udev.c' || echo '$(srcdir)/'`kerneldevice/mm-kernel-device-udev.c

ModemManager-main.o: main.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-main.o -MD -MP -MF $(DEPDIR)/ModemManager-main.Tpo -c -o ModemManager-main.o `test -f 'main.c' || echo '$(srcdir)/'`main.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-main.Tpo $(DEPDIR)/ModemManager-main.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='main.c' object='ModemManager-main.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-main.o `test -f 'main.c' || echo '$(srcdir)/'`main.c

ModemManager-main.obj: main.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-main.obj -MD -MP -MF $(DEPDIR)/ModemManager-main.Tpo -c -o ModemManager-main.obj `if test -f 'main.c'; then $(CYGPATH_W) 'main.c'; else $(CYGPATH_W) '$(srcdir)/main.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-main.Tpo $(DEPDIR)/ModemManager-main.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='main.c' object='ModemManager-main.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-main.obj `if test -f 'main.c'; then $(CYGPATH_W) 'main.c'; else $(CYGPATH_W) '$(srcdir)/main.c'; fi`

ModemManager-mm-context.o: mm-context.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-context.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-context.Tpo -c -o ModemManager-mm-context.o `test -f 'mm-context.c' || echo '$(srcdir)/'`mm-context.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-context.Tpo $(DEPDIR)/ModemManager-mm-context.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-context.c' object='ModemManager-mm-context.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-context.o `test -f 'mm-context.c' || echo '$(srcdir)/'`mm-context.c

ModemManager-mm-context.obj: mm-context.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-context.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-context.Tpo -c -o ModemManager-mm-context.obj `if test -f 'mm-context.c'; then $(CYGPATH_W) 'mm-context.c'; else $(CYGPATH_W) '$(srcdir)/mm-context.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-context.Tpo $(DEPDIR)/ModemManager-mm-context.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-context.c' object='ModemManager-mm-context.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-context.obj `if test -f 'mm-context.c'; then $(CYGPATH_W) 'mm-context.c'; else $(CYGPATH_W) '$(srcdir)/mm-context.c'; fi`

ModemManager-mm-private-boxed-types.o: mm-private-boxed-types.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-private-boxed-types.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-private-boxed-types.Tpo -c -o ModemManager-mm-private-boxed-types.o `test -f 'mm-private-boxed-types.c' || echo '$(srcdir)/'`mm-private-boxed-types.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-private-boxed-types.Tpo $(DEPDIR)/ModemManager-mm-private-boxed-types.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-private-boxed-types.c' object='ModemManager-mm-private-boxed-types.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-private-boxed-types.o `test -f 'mm-private-boxed-types.c' || echo '$(srcdir)/'`mm-private-boxed-types.c

ModemManager-mm-private-boxed-types.obj: mm-private-boxed-types.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-private-boxed-types.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-private-boxed-types.Tpo -c -o ModemManager-mm-private-boxed-types.obj `if test -f 'mm-private-boxed-types.c'; then $(CYGPATH_W) 'mm-private-boxed-types.c'; else $(CYGPATH_W) '$(srcdir)/mm-private-boxed-types.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-private-boxed-types.Tpo $(DEPDIR)/ModemManager-mm-private-boxed-types.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-private-boxed-types.c' object='ModemManager-mm-private-boxed-types.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-private-boxed-types.obj `if test -f 'mm-private-boxed-types.c'; then $(CYGPATH_W) 'mm-private-boxed-types.c'; else $(CYGPATH_W) '$(srcdir)/mm-private-boxed-types.c'; fi`

ModemManager-mm-auth-provider.o: mm-auth-provider.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-auth-provider.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-auth-provider.Tpo -c -o ModemManager-mm-auth-provider.o `test -f 'mm-auth-provider.c' || echo '$(srcdir)/'`mm-auth-provider.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-auth-provider.Tpo $(DEPDIR)/ModemManager-mm-auth-provider.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-auth-provider.c' object='ModemManager-mm-auth-provider.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-auth-provider.o `test -f 'mm-auth-provider.c' || echo '$(srcdir)/'`mm-auth-provider.c

ModemManager-mm-auth-provider.obj: mm-auth-provider.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-auth-provider.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-auth-provider.Tpo -c -o ModemManager-mm-auth-provider.obj `if test -f 'mm-auth-provider.c'; then $(CYGPATH_W) 'mm-auth-provider.c'; else $(CYGPATH_W) '$(srcdir)/mm-auth-provider.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-auth-provider.Tpo $(DEPDIR)/ModemManager-mm-auth-provider.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-auth-provider.c' object='ModemManager-mm-auth-provider.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-auth-provider.obj `if test -f 'mm-auth-provider.c'; then $(CYGPATH_W) 'mm-auth-provider.c'; else $(CYGPATH_W) '$(srcdir)/mm-auth-provider.c'; fi`

ModemManager-mm-filter.o: mm-filter.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-filter.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-filter.Tpo -c -o ModemManager-mm-filter.o `test -f 'mm-filter.c' || echo '$(srcdir)/'`mm-filter.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-filter.Tpo $(DEPDIR)/ModemManager-mm-filter.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-filter.c' object='ModemManager-mm-filter.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-filter.o `test -f 'mm-filter.c' || echo '$(srcdir)/'`mm-filter.c

ModemManager-mm-filter.obj: mm-filter.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-filter.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-filter.Tpo -c -o ModemManager-mm-filter.obj `if test -f 'mm-filter.c'; then $(CYGPATH_W) 'mm-filter.c'; else $(CYGPATH_W) '$(srcdir)/mm-filter.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-filter.Tpo $(DEPDIR)/ModemManager-mm-filter.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-filter.c' object='ModemManager-mm-filter.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-filter.obj `if test -f 'mm-filter.c'; then $(CYGPATH_W) 'mm-filter.c'; else $(CYGPATH_W) '$(srcdir)/mm-filter.c'; fi`

ModemManager-mm-base-manager.o: mm-base-manager.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-base-manager.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-base-manager.Tpo -c -o ModemManager-mm-base-manager.o `test -f 'mm-base-manager.c' || echo '$(srcdir)/'`mm-base-manager.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-base-manager.Tpo $(DEPDIR)/ModemManager-mm-base-manager.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-base-manager.c' object='ModemManager-mm-base-manager.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-base-manager.o `test -f 'mm-base-manager.c' || echo '$(srcdir)/'`mm-base-manager.c

ModemManager-mm-base-manager.obj: mm-base-manager.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-base-manager.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-base-manager.Tpo -c -o ModemManager-mm-base-manager.obj `if test -f 'mm-base-manager.c'; then $(CYGPATH_W) 'mm-base-manager.c'; else $(CYGPATH_W) '$(srcdir)/mm-base-manager.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-base-manager.Tpo $(DEPDIR)/ModemManager-mm-base-manager.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-base-manager.c' object='ModemManager-mm-base-manager.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-base-manager.obj `if test -f 'mm-base-manager.c'; then $(CYGPATH_W) 'mm-base-manager.c'; else $(CYGPATH_W) '$(srcdir)/mm-base-manager.c'; fi`

ModemManager-mm-device.o: mm-device.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-device.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-device.Tpo -c -o ModemManager-mm-device.o `test -f 'mm-device.c' || echo '$(srcdir)/'`mm-device.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-device.Tpo $(DEPDIR)/ModemManager-mm-device.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-device.c' object='ModemManager-mm-device.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-device.o `test -f 'mm-device.c' || echo '$(srcdir)/'`mm-device.c

ModemManager-mm-device.obj: mm-device.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-device.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-device.Tpo -c -o ModemManager-mm-device.obj `if test -f 'mm-device.c'; then $(CYGPATH_W) 'mm-device.c'; else $(CYGPATH_W) '$(srcdir)/mm-device.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-device.Tpo $(DEPDIR)/ModemManager-mm-device.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-device.c' object='ModemManager-mm-device.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-device.obj `if test -f 'mm-device.c'; then $(CYGPATH_W) 'mm-device.c'; else $(CYGPATH_W) '$(srcdir)/mm-device.c'; fi`

ModemManager-mm-plugin-manager.o: mm-plugin-manager.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-plugin-manager.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-plugin-manager.Tpo -c -o ModemManager-mm-plugin-manager.o `test -f 'mm-plugin-manager.c' || echo '$(srcdir)/'`mm-plugin-manager.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-plugin-manager.Tpo $(DEPDIR)/ModemManager-mm-plugin-manager.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-plugin-manager.c' object='ModemManager-mm-plugin-manager.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-plugin-manager.o `test -f 'mm-plugin-manager.c' || echo '$(srcdir)/'`mm-plugin-manager.c

ModemManager-mm-plugin-manager.obj: mm-plugin-manager.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-plugin-manager.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-plugin-manager.Tpo -c -o ModemManager-mm-plugin-manager.obj `if test -f 'mm-plugin-manager.c'; then $(CYGPATH_W) 'mm-plugin-manager.c'; else $(CYGPATH_W) '$(srcdir)/mm-plugin-manager.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-plugin-manager.Tpo $(DEPDIR)/ModemManager-mm-plugin-manager.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-plugin-manager.c' object='ModemManager-mm-plugin-manager.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-plugin-manager.obj `if test -f 'mm-plugin-manager.c'; then $(CYGPATH_W) 'mm-plugin-manager.c'; else $(CYGPATH_W) '$(srcdir)/mm-plugin-manager.c'; fi`

ModemManager-mm-base-sim.o: mm-base-sim.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-base-sim.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-base-sim.Tpo -c -o ModemManager-mm-base-sim.o `test -f 'mm-base-sim.c' || echo '$(srcdir)/'`mm-base-sim.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-base-sim.Tpo $(DEPDIR)/ModemManager-mm-base-sim.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-base-sim.c' object='ModemManager-mm-base-sim.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-base-sim.o `test -f 'mm-base-sim.c' || echo '$(srcdir)/'`mm-base-sim.c

ModemManager-mm-base-sim.obj: mm-base-sim.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-base-sim.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-base-sim.Tpo -c -o ModemManager-mm-base-sim.obj `if test -f 'mm-base-sim.c'; then $(CYGPATH_W) 'mm-base-sim.c'; else $(CYGPATH_W) '$(srcdir)/mm-base-sim.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-base-sim.Tpo $(DEPDIR)/ModemManager-mm-base-sim.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-base-sim.c' object='ModemManager-mm-base-sim.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-base-sim.obj `if test -f 'mm-base-sim.c'; then $(CYGPATH_W) 'mm-base-sim.c'; else $(CYGPATH_W) '$(srcdir)/mm-base-sim.c'; fi`

ModemManager-mm-base-bearer.o: mm-base-bearer.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-base-bearer.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-base-bearer.Tpo -c -o ModemManager-mm-base-bearer.o `test -f 'mm-base-bearer.c' || echo '$(srcdir)/'`mm-base-bearer.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-base-bearer.Tpo $(DEPDIR)/ModemManager-mm-base-bearer.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-base-bearer.c' object='ModemManager-mm-base-bearer.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-base-bearer.o `test -f 'mm-base-bearer.c' || echo '$(srcdir)/'`mm-base-bearer.c

ModemManager-mm-base-bearer.obj: mm-base-bearer.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-base-bearer.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-base-bearer.Tpo -c -o ModemManager-mm-base-bearer.obj `if test -f 'mm-base-bearer.c'; then $(CYGPATH_W) 'mm-base-bearer.c'; else $(CYGPATH_W) '$(srcdir)/mm-base-bearer.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-base-bearer.Tpo $(DEPDIR)/ModemManager-mm-base-bearer.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-base-bearer.c' object='ModemManager-mm-base-bearer.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-base-bearer.obj `if test -f 'mm-base-bearer.c'; then $(CYGPATH_W) 'mm-base-bearer.c'; else $(CYGPATH_W) '$(srcdir)/mm-base-bearer.c'; fi`

ModemManager-mm-broadband-bearer.o: mm-broadband-bearer.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-broadband-bearer.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-broadband-bearer.Tpo -c -o ModemManager-mm-broadband-bearer.o `test -f 'mm-broadband-bearer.c' || echo '$(srcdir)/'`mm-broadband-bearer.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-broadband-bearer.Tpo $(DEPDIR)/ModemManager-mm-broadband-bearer.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-broadband-bearer.c' object='ModemManager-mm-broadband-bearer.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-broadband-bearer.o `test -f 'mm-broadband-bearer.c' || echo '$(srcdir)/'`mm-broadband-bearer.c

ModemManager-mm-broadband-bearer.obj: mm-broadband-bearer.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-broadband-bearer.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-broadband-bearer.Tpo -c -o ModemManager-mm-broadband-bearer.obj `if test -f 'mm-broadband-bearer.c'; then $(CYGPATH_W) 'mm-broadband-bearer.c'; else $(CYGPATH_W) '$(srcdir)/mm-broadband-bearer.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-broadband-bearer.Tpo $(DEPDIR)/ModemManager-mm-broadband-bearer.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-broadband-bearer.c' object='ModemManager-mm-broadband-bearer.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-broadband-bearer.obj `if test -f 'mm-broadband-bearer.c'; then $(CYGPATH_W) 'mm-broadband-bearer.c'; else $(CYGPATH_W) '$(srcdir)/mm-broadband-bearer.c'; fi`

ModemManager-mm-bearer-list.o: mm-bearer-list.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-bearer-list.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-bearer-list.Tpo -c -o ModemManager-mm-bearer-list.o `test -f 'mm-bearer-list.c' || echo '$(srcdir)/'`mm-bearer-list.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-bearer-list.Tpo $(DEPDIR)/ModemManager-mm-bearer-list.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-bearer-list.c' object='ModemManager-mm-bearer-list.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-bearer-list.o `test -f 'mm-bearer-list.c' || echo '$(srcdir)/'`mm-bearer-list.c

ModemManager-mm-bearer-list.obj: mm-bearer-list.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-bearer-list.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-bearer-list.Tpo -c -o ModemManager-mm-bearer-list.obj `if test -f 'mm-bearer-list.c'; then $(CYGPATH_W) 'mm-bearer-list.c'; else $(CYGPATH_W) '$(srcdir)/mm-bearer-list.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-bearer-list.Tpo $(DEPDIR)/ModemManager-mm-bearer-list.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-bearer-list.c' object='ModemManager-mm-bearer-list.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-bearer-list.obj `if test -f 'mm-bearer-list.c'; then $(CYGPATH_W) 'mm-bearer-list.c'; else $(CYGPATH_W) '$(srcdir)/mm-bearer-list.c'; fi`

ModemManager-mm-base-modem-at.o: mm-base-modem-at.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-base-modem-at.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-base-modem-at.Tpo -c -o ModemManager-mm-base-modem-at.o `test -f 'mm-base-modem-at.c' || echo '$(srcdir)/'`mm-base-modem-at.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-base-modem-at.Tpo $(DEPDIR)/ModemManager-mm-base-modem-at.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-base-modem-at.c' object='ModemManager-mm-base-modem-at.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-base-modem-at.o `test -f 'mm-base-modem-at.c' || echo '$(srcdir)/'`mm-base-modem-at.c

ModemManager-mm-base-modem-at.obj: mm-base-modem-at.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-base-modem-at.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-base-modem-at.Tpo -c -o ModemManager-mm-base-modem-at.obj `if test -f 'mm-base-modem-at.c'; then $(CYGPATH_W) 'mm-base-modem-at.c'; else $(CYGPATH_W) '$(srcdir)/mm-base-modem-at.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-base-modem-at.Tpo $(DEPDIR)/ModemManager-mm-base-modem-at.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-base-modem-at.c' object='ModemManager-mm-base-modem-at.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-base-modem-at.obj `if test -f 'mm-base-modem-at.c'; then $(CYGPATH_W) 'mm-base-modem-at.c'; else $(CYGPATH_W) '$(srcdir)/mm-base-modem-at.c'; fi`

ModemManager-mm-base-modem.o: mm-base-modem.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-base-modem.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-base-modem.Tpo -c -o ModemManager-mm-base-modem.o `test -f 'mm-base-modem.c' || echo '$(srcdir)/'`mm-base-modem.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-base-modem.Tpo $(DEPDIR)/ModemManager-mm-base-modem.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-base-modem.c' object='ModemManager-mm-base-modem.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-base-modem.o `test -f 'mm-base-modem.c' || echo '$(srcdir)/'`mm-base-modem.c

ModemManager-mm-base-modem.obj: mm-base-modem.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-base-modem.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-base-modem.Tpo -c -o ModemManager-mm-base-modem.obj `if test -f 'mm-base-modem.c'; then $(CYGPATH_W) 'mm-base-modem.c'; else $(CYGPATH_W) '$(srcdir)/mm-base-modem.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-base-modem.Tpo $(DEPDIR)/ModemManager-mm-base-modem.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-base-modem.c' object='ModemManager-mm-base-modem.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-base-modem.obj `if test -f 'mm-base-modem.c'; then $(CYGPATH_W) 'mm-base-modem.c'; else $(CYGPATH_W) '$(srcdir)/mm-base-modem.c'; fi`

ModemManager-mm-base-sms.o: mm-base-sms.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-base-sms.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-base-sms.Tpo -c -o ModemManager-mm-base-sms.o `test -f 'mm-base-sms.c' || echo '$(srcdir)/'`mm-base-sms.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-base-sms.Tpo $(DEPDIR)/ModemManager-mm-base-sms.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-base-sms.c' object='ModemManager-mm-base-sms.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-base-sms.o `test -f 'mm-base-sms.c' || echo '$(srcdir)/'`mm-base-sms.c

ModemManager-mm-base-sms.obj: mm-base-sms.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-base-sms.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-base-sms.Tpo -c -o ModemManager-mm-base-sms.obj `if test -f 'mm-base-sms.c'; then $(CYGPATH_W) 'mm-base-sms.c'; else $(CYGPATH_W) '$(srcdir)/mm-base-sms.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-base-sms.Tpo $(DEPDIR)/ModemManager-mm-base-sms.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-base-sms.c' object='ModemManager-mm-base-sms.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-base-sms.obj `if test -f 'mm-base-sms.c'; then $(CYGPATH_W) 'mm-base-sms.c'; else $(CYGPATH_W) '$(srcdir)/mm-base-sms.c'; fi`

ModemManager-mm-base-call.o: mm-base-call.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-base-call.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-base-call.Tpo -c -o ModemManager-mm-base-call.o `test -f 'mm-base-call.c' || echo '$(srcdir)/'`mm-base-call.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-base-call.Tpo $(DEPDIR)/ModemManager-mm-base-call.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-base-call.c' object='ModemManager-mm-base-call.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-base-call.o `test -f 'mm-base-call.c' || echo '$(srcdir)/'`mm-base-call.c

ModemManager-mm-base-call.obj: mm-base-call.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-base-call.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-base-call.Tpo -c -o ModemManager-mm-base-call.obj `if test -f 'mm-base-call.c'; then $(CYGPATH_W) 'mm-base-call.c'; else $(CYGPATH_W) '$(srcdir)/mm-base-call.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-base-call.Tpo $(DEPDIR)/ModemManager-mm-base-call.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-base-call.c' object='ModemManager-mm-base-call.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-base-call.obj `if test -f 'mm-base-call.c'; then $(CYGPATH_W) 'mm-base-call.c'; else $(CYGPATH_W) '$(srcdir)/mm-base-call.c'; fi`

ModemManager-mm-sms-list.o: mm-sms-list.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-sms-list.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-sms-list.Tpo -c -o ModemManager-mm-sms-list.o `test -f 'mm-sms-list.c' || echo '$(srcdir)/'`mm-sms-list.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-sms-list.Tpo $(DEPDIR)/ModemManager-mm-sms-list.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-sms-list.c' object='ModemManager-mm-sms-list.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-sms-list.o `test -f 'mm-sms-list.c' || echo '$(srcdir)/'`mm-sms-list.c

ModemManager-mm-sms-list.obj: mm-sms-list.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-sms-list.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-sms-list.Tpo -c -o ModemManager-mm-sms-list.obj `if test -f 'mm-sms-list.c'; then $(CYGPATH_W) 'mm-sms-list.c'; else $(CYGPATH_W) '$(srcdir)/mm-sms-list.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-sms-list.Tpo $(DEPDIR)/ModemManager-mm-sms-list.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-sms-list.c' object='ModemManager-mm-sms-list.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-sms-list.obj `if test -f 'mm-sms-list.c'; then $(CYGPATH_W) 'mm-sms-list.c'; else $(CYGPATH_W) '$(srcdir)/mm-sms-list.c'; fi`

ModemManager-mm-call-list.o: mm-call-list.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-call-list.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-call-list.Tpo -c -o ModemManager-mm-call-list.o `test -f 'mm-call-list.c' || echo '$(srcdir)/'`mm-call-list.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-call-list.Tpo $(DEPDIR)/ModemManager-mm-call-list.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-call-list.c' object='ModemManager-mm-call-list.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-call-list.o `test -f 'mm-call-list.c' || echo '$(srcdir)/'`mm-call-list.c

ModemManager-mm-call-list.obj: mm-call-list.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-call-list.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-call-list.Tpo -c -o ModemManager-mm-call-list.obj `if test -f 'mm-call-list.c'; then $(CYGPATH_W) 'mm-call-list.c'; else $(CYGPATH_W) '$(srcdir)/mm-call-list.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-call-list.Tpo $(DEPDIR)/ModemManager-mm-call-list.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-call-list.c' object='ModemManager-mm-call-list.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-call-list.obj `if test -f 'mm-call-list.c'; then $(CYGPATH_W) 'mm-call-list.c'; else $(CYGPATH_W) '$(srcdir)/mm-call-list.c'; fi`

ModemManager-mm-iface-modem.o: mm-iface-modem.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem.Tpo -c -o ModemManager-mm-iface-modem.o `test -f 'mm-iface-modem.c' || echo '$(srcdir)/'`mm-iface-modem.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem.Tpo $(DEPDIR)/ModemManager-mm-iface-modem.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem.c' object='ModemManager-mm-iface-modem.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem.o `test -f 'mm-iface-modem.c' || echo '$(srcdir)/'`mm-iface-modem.c

ModemManager-mm-iface-modem.obj: mm-iface-modem.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem.Tpo -c -o ModemManager-mm-iface-modem.obj `if test -f 'mm-iface-modem.c'; then $(CYGPATH_W) 'mm-iface-modem.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem.Tpo $(DEPDIR)/ModemManager-mm-iface-modem.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem.c' object='ModemManager-mm-iface-modem.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem.obj `if test -f 'mm-iface-modem.c'; then $(CYGPATH_W) 'mm-iface-modem.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem.c'; fi`

ModemManager-mm-iface-modem-3gpp.o: mm-iface-modem-3gpp.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-3gpp.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-3gpp.Tpo -c -o ModemManager-mm-iface-modem-3gpp.o `test -f 'mm-iface-modem-3gpp.c' || echo '$(srcdir)/'`mm-iface-modem-3gpp.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-3gpp.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-3gpp.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-3gpp.c' object='ModemManager-mm-iface-modem-3gpp.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-3gpp.o `test -f 'mm-iface-modem-3gpp.c' || echo '$(srcdir)/'`mm-iface-modem-3gpp.c

ModemManager-mm-iface-modem-3gpp.obj: mm-iface-modem-3gpp.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-3gpp.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-3gpp.Tpo -c -o ModemManager-mm-iface-modem-3gpp.obj `if test -f 'mm-iface-modem-3gpp.c'; then $(CYGPATH_W) 'mm-iface-modem-3gpp.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-3gpp.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-3gpp.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-3gpp.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-3gpp.c' object='ModemManager-mm-iface-modem-3gpp.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-3gpp.obj `if test -f 'mm-iface-modem-3gpp.c'; then $(CYGPATH_W) 'mm-iface-modem-3gpp.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-3gpp.c'; fi`

ModemManager-mm-iface-modem-3gpp-ussd.o: mm-iface-modem-3gpp-ussd.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-3gpp-ussd.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-3gpp-ussd.Tpo -c -o ModemManager-mm-iface-modem-3gpp-ussd.o `test -f 'mm-iface-modem-3gpp-ussd.c' || echo '$(srcdir)/'`mm-iface-modem-3gpp-ussd.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-3gpp-ussd.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-3gpp-ussd.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-3gpp-ussd.c' object='ModemManager-mm-iface-modem-3gpp-ussd.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-3gpp-ussd.o `test -f 'mm-iface-modem-3gpp-ussd.c' || echo '$(srcdir)/'`mm-iface-modem-3gpp-ussd.c

ModemManager-mm-iface-modem-3gpp-ussd.obj: mm-iface-modem-3gpp-ussd.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-3gpp-ussd.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-3gpp-ussd.Tpo -c -o ModemManager-mm-iface-modem-3gpp-ussd.obj `if test -f 'mm-iface-modem-3gpp-ussd.c'; then $(CYGPATH_W) 'mm-iface-modem-3gpp-ussd.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-3gpp-ussd.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-3gpp-ussd.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-3gpp-ussd.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-3gpp-ussd.c' object='ModemManager-mm-iface-modem-3gpp-ussd.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-3gpp-ussd.obj `if test -f 'mm-iface-modem-3gpp-ussd.c'; then $(CYGPATH_W) 'mm-iface-modem-3gpp-ussd.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-3gpp-ussd.c'; fi`

ModemManager-mm-iface-modem-cdma.o: mm-iface-modem-cdma.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-cdma.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-cdma.Tpo -c -o ModemManager-mm-iface-modem-cdma.o `test -f 'mm-iface-modem-cdma.c' || echo '$(srcdir)/'`mm-iface-modem-cdma.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-cdma.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-cdma.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-cdma.c' object='ModemManager-mm-iface-modem-cdma.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-cdma.o `test -f 'mm-iface-modem-cdma.c' || echo '$(srcdir)/'`mm-iface-modem-cdma.c

ModemManager-mm-iface-modem-cdma.obj: mm-iface-modem-cdma.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-cdma.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-cdma.Tpo -c -o ModemManager-mm-iface-modem-cdma.obj `if test -f 'mm-iface-modem-cdma.c'; then $(CYGPATH_W) 'mm-iface-modem-cdma.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-cdma.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-cdma.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-cdma.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-cdma.c' object='ModemManager-mm-iface-modem-cdma.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-cdma.obj `if test -f 'mm-iface-modem-cdma.c'; then $(CYGPATH_W) 'mm-iface-modem-cdma.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-cdma.c'; fi`

ModemManager-mm-iface-modem-simple.o: mm-iface-modem-simple.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-simple.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-simple.Tpo -c -o ModemManager-mm-iface-modem-simple.o `test -f 'mm-iface-modem-simple.c' || echo '$(srcdir)/'`mm-iface-modem-simple.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-simple.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-simple.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-simple.c' object='ModemManager-mm-iface-modem-simple.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-simple.o `test -f 'mm-iface-modem-simple.c' || echo '$(srcdir)/'`mm-iface-modem-simple.c

ModemManager-mm-iface-modem-simple.obj: mm-iface-modem-simple.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-simple.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-simple.Tpo -c -o ModemManager-mm-iface-modem-simple.obj `if test -f 'mm-iface-modem-simple.c'; then $(CYGPATH_W) 'mm-iface-modem-simple.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-simple.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-simple.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-simple.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-simple.c' object='ModemManager-mm-iface-modem-simple.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-simple.obj `if test -f 'mm-iface-modem-simple.c'; then $(CYGPATH_W) 'mm-iface-modem-simple.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-simple.c'; fi`

ModemManager-mm-iface-modem-location.o: mm-iface-modem-location.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-location.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-location.Tpo -c -o ModemManager-mm-iface-modem-location.o `test -f 'mm-iface-modem-location.c' || echo '$(srcdir)/'`mm-iface-modem-location.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-location.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-location.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-location.c' object='ModemManager-mm-iface-modem-location.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-location.o `test -f 'mm-iface-modem-location.c' || echo '$(srcdir)/'`mm-iface-modem-location.c

ModemManager-mm-iface-modem-location.obj: mm-iface-modem-location.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-location.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-location.Tpo -c -o ModemManager-mm-iface-modem-location.obj `if test -f 'mm-iface-modem-location.c'; then $(CYGPATH_W) 'mm-iface-modem-location.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-location.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-location.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-location.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-location.c' object='ModemManager-mm-iface-modem-location.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-location.obj `if test -f 'mm-iface-modem-location.c'; then $(CYGPATH_W) 'mm-iface-modem-location.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-location.c'; fi`

ModemManager-mm-iface-modem-messaging.o: mm-iface-modem-messaging.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-messaging.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-messaging.Tpo -c -o ModemManager-mm-iface-modem-messaging.o `test -f 'mm-iface-modem-messaging.c' || echo '$(srcdir)/'`mm-iface-modem-messaging.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-messaging.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-messaging.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-messaging.c' object='ModemManager-mm-iface-modem-messaging.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-messaging.o `test -f 'mm-iface-modem-messaging.c' || echo '$(srcdir)/'`mm-iface-modem-messaging.c

ModemManager-mm-iface-modem-messaging.obj: mm-iface-modem-messaging.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-messaging.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-messaging.Tpo -c -o ModemManager-mm-iface-modem-messaging.obj `if test -f 'mm-iface-modem-messaging.c'; then $(CYGPATH_W) 'mm-iface-modem-messaging.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-messaging.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-messaging.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-messaging.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-messaging.c' object='ModemManager-mm-iface-modem-messaging.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-messaging.obj `if test -f 'mm-iface-modem-messaging.c'; then $(CYGPATH_W) 'mm-iface-modem-messaging.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-messaging.c'; fi`

ModemManager-mm-iface-modem-voice.o: mm-iface-modem-voice.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-voice.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-voice.Tpo -c -o ModemManager-mm-iface-modem-voice.o `test -f 'mm-iface-modem-voice.c' || echo '$(srcdir)/'`mm-iface-modem-voice.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-voice.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-voice.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-voice.c' object='ModemManager-mm-iface-modem-voice.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-voice.o `test -f 'mm-iface-modem-voice.c' || echo '$(srcdir)/'`mm-iface-modem-voice.c

ModemManager-mm-iface-modem-voice.obj: mm-iface-modem-voice.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-voice.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-voice.Tpo -c -o ModemManager-mm-iface-modem-voice.obj `if test -f 'mm-iface-modem-voice.c'; then $(CYGPATH_W) 'mm-iface-modem-voice.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-voice.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-voice.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-voice.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-voice.c' object='ModemManager-mm-iface-modem-voice.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-voice.obj `if test -f 'mm-iface-modem-voice.c'; then $(CYGPATH_W) 'mm-iface-modem-voice.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-voice.c'; fi`

ModemManager-mm-iface-modem-time.o: mm-iface-modem-time.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-time.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-time.Tpo -c -o ModemManager-mm-iface-modem-time.o `test -f 'mm-iface-modem-time.c' || echo '$(srcdir)/'`mm-iface-modem-time.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-time.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-time.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-time.c' object='ModemManager-mm-iface-modem-time.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-time.o `test -f 'mm-iface-modem-time.c' || echo '$(srcdir)/'`mm-iface-modem-time.c

ModemManager-mm-iface-modem-time.obj: mm-iface-modem-time.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-time.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-time.Tpo -c -o ModemManager-mm-iface-modem-time.obj `if test -f 'mm-iface-modem-time.c'; then $(CYGPATH_W) 'mm-iface-modem-time.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-time.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-time.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-time.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-time.c' object='ModemManager-mm-iface-modem-time.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-time.obj `if test -f 'mm-iface-modem-time.c'; then $(CYGPATH_W) 'mm-iface-modem-time.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-time.c'; fi`

ModemManager-mm-iface-modem-firmware.o: mm-iface-modem-firmware.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-firmware.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-firmware.Tpo -c -o ModemManager-mm-iface-modem-firmware.o `test -f 'mm-iface-modem-firmware.c' || echo '$(srcdir)/'`mm-iface-modem-firmware.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-firmware.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-firmware.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-firmware.c' object='ModemManager-mm-iface-modem-firmware.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-firmware.o `test -f 'mm-iface-modem-firmware.c' || echo '$(srcdir)/'`mm-iface-modem-firmware.c

ModemManager-mm-iface-modem-firmware.obj: mm-iface-modem-firmware.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-firmware.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-firmware.Tpo -c -o ModemManager-mm-iface-modem-firmware.obj `if test -f 'mm-iface-modem-firmware.c'; then $(CYGPATH_W) 'mm-iface-modem-firmware.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-firmware.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-firmware.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-firmware.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-firmware.c' object='ModemManager-mm-iface-modem-firmware.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-firmware.obj `if test -f 'mm-iface-modem-firmware.c'; then $(CYGPATH_W) 'mm-iface-modem-firmware.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-firmware.c'; fi`

ModemManager-mm-iface-modem-signal.o: mm-iface-modem-signal.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-signal.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-signal.Tpo -c -o ModemManager-mm-iface-modem-signal.o `test -f 'mm-iface-modem-signal.c' || echo '$(srcdir)/'`mm-iface-modem-signal.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-signal.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-signal.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-signal.c' object='ModemManager-mm-iface-modem-signal.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-signal.o `test -f 'mm-iface-modem-signal.c' || echo '$(srcdir)/'`mm-iface-modem-signal.c

ModemManager-mm-iface-modem-signal.obj: mm-iface-modem-signal.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-signal.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-signal.Tpo -c -o ModemManager-mm-iface-modem-signal.obj `if test -f 'mm-iface-modem-signal.c'; then $(CYGPATH_W) 'mm-iface-modem-signal.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-signal.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-signal.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-signal.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-signal.c' object='ModemManager-mm-iface-modem-signal.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-signal.obj `if test -f 'mm-iface-modem-signal.c'; then $(CYGPATH_W) 'mm-iface-modem-signal.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-signal.c'; fi`

ModemManager-mm-iface-modem-oma.o: mm-iface-modem-oma.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-oma.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-oma.Tpo -c -o ModemManager-mm-iface-modem-oma.o `test -f 'mm-iface-modem-oma.c' || echo '$(srcdir)/'`mm-iface-modem-oma.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-oma.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-oma.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-oma.c' object='ModemManager-mm-iface-modem-oma.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-oma.o `test -f 'mm-iface-modem-oma.c' || echo '$(srcdir)/'`mm-iface-modem-oma.c

ModemManager-mm-iface-modem-oma.obj: mm-iface-modem-oma.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-iface-modem-oma.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-iface-modem-oma.Tpo -c -o ModemManager-mm-iface-modem-oma.obj `if test -f 'mm-iface-modem-oma.c'; then $(CYGPATH_W) 'mm-iface-modem-oma.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-oma.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-iface-modem-oma.Tpo $(DEPDIR)/ModemManager-mm-iface-modem-oma.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-iface-modem-oma.c' object='ModemManager-mm-iface-modem-oma.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-iface-modem-oma.obj `if test -f 'mm-iface-modem-oma.c'; then $(CYGPATH_W) 'mm-iface-modem-oma.c'; else $(CYGPATH_W) '$(srcdir)/mm-iface-modem-oma.c'; fi`

ModemManager-mm-broadband-modem.o: mm-broadband-modem.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-broadband-modem.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-broadband-modem.Tpo -c -o ModemManager-mm-broadband-modem.o `test -f 'mm-broadband-modem.c' || echo '$(srcdir)/'`mm-broadband-modem.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-broadband-modem.Tpo $(DEPDIR)/ModemManager-mm-broadband-modem.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-broadband-modem.c' object='ModemManager-mm-broadband-modem.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-broadband-modem.o `test -f 'mm-broadband-modem.c' || echo '$(srcdir)/'`mm-broadband-modem.c

ModemManager-mm-broadband-modem.obj: mm-broadband-modem.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-broadband-modem.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-broadband-modem.Tpo -c -o ModemManager-mm-broadband-modem.obj `if test -f 'mm-broadband-modem.c'; then $(CYGPATH_W) 'mm-broadband-modem.c'; else $(CYGPATH_W) '$(srcdir)/mm-broadband-modem.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-broadband-modem.Tpo $(DEPDIR)/ModemManager-mm-broadband-modem.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-broadband-modem.c' object='ModemManager-mm-broadband-modem.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-broadband-modem.obj `if test -f 'mm-broadband-modem.c'; then $(CYGPATH_W) 'mm-broadband-modem.c'; else $(CYGPATH_W) '$(srcdir)/mm-broadband-modem.c'; fi`

ModemManager-mm-port-probe.o: mm-port-probe.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-port-probe.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-port-probe.Tpo -c -o ModemManager-mm-port-probe.o `test -f 'mm-port-probe.c' || echo '$(srcdir)/'`mm-port-probe.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-port-probe.Tpo $(DEPDIR)/ModemManager-mm-port-probe.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-port-probe.c' object='ModemManager-mm-port-probe.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-port-probe.o `test -f 'mm-port-probe.c' || echo '$(srcdir)/'`mm-port-probe.c

ModemManager-mm-port-probe.obj: mm-port-probe.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-port-probe.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-port-probe.Tpo -c -o ModemManager-mm-port-probe.obj `if test -f 'mm-port-probe.c'; then $(CYGPATH_W) 'mm-port-probe.c'; else $(CYGPATH_W) '$(srcdir)/mm-port-probe.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-port-probe.Tpo $(DEPDIR)/ModemManager-mm-port-probe.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-port-probe.c' object='ModemManager-mm-port-probe.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-port-probe.obj `if test -f 'mm-port-probe.c'; then $(CYGPATH_W) 'mm-port-probe.c'; else $(CYGPATH_W) '$(srcdir)/mm-port-probe.c'; fi`

ModemManager-mm-port-probe-at.o: mm-port-probe-at.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-port-probe-at.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-port-probe-at.Tpo -c -o ModemManager-mm-port-probe-at.o `test -f 'mm-port-probe-at.c' || echo '$(srcdir)/'`mm-port-probe-at.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-port-probe-at.Tpo $(DEPDIR)/ModemManager-mm-port-probe-at.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-port-probe-at.c' object='ModemManager-mm-port-probe-at.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-port-probe-at.o `test -f 'mm-port-probe-at.c' || echo '$(srcdir)/'`mm-port-probe-at.c

ModemManager-mm-port-probe-at.obj: mm-port-probe-at.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-port-probe-at.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-port-probe-at.Tpo -c -o ModemManager-mm-port-probe-at.obj `if test -f 'mm-port-probe-at.c'; then $(CYGPATH_W) 'mm-port-probe-at.c'; else $(CYGPATH_W) '$(srcdir)/mm-port-probe-at.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-port-probe-at.Tpo $(DEPDIR)/ModemManager-mm-port-probe-at.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-port-probe-at.c' object='ModemManager-mm-port-probe-at.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-port-probe-at.obj `if test -f 'mm-port-probe-at.c'; then $(CYGPATH_W) 'mm-port-probe-at.c'; else $(CYGPATH_W) '$(srcdir)/mm-port-probe-at.c'; fi`

ModemManager-mm-plugin.o: mm-plugin.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-plugin.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-plugin.Tpo -c -o ModemManager-mm-plugin.o `test -f 'mm-plugin.c' || echo '$(srcdir)/'`mm-plugin.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-plugin.Tpo $(DEPDIR)/ModemManager-mm-plugin.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-plugin.c' object='ModemManager-mm-plugin.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-plugin.o `test -f 'mm-plugin.c' || echo '$(srcdir)/'`mm-plugin.c

ModemManager-mm-plugin.obj: mm-plugin.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-plugin.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-plugin.Tpo -c -o ModemManager-mm-plugin.obj `if test -f 'mm-plugin.c'; then $(CYGPATH_W) 'mm-plugin.c'; else $(CYGPATH_W) '$(srcdir)/mm-plugin.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-plugin.Tpo $(DEPDIR)/ModemManager-mm-plugin.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-plugin.c' object='ModemManager-mm-plugin.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-plugin.obj `if test -f 'mm-plugin.c'; then $(CYGPATH_W) 'mm-plugin.c'; else $(CYGPATH_W) '$(srcdir)/mm-plugin.c'; fi`

ModemManager-mm-sleep-monitor.o: mm-sleep-monitor.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-sleep-monitor.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-sleep-monitor.Tpo -c -o ModemManager-mm-sleep-monitor.o `test -f 'mm-sleep-monitor.c' || echo '$(srcdir)/'`mm-sleep-monitor.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-sleep-monitor.Tpo $(DEPDIR)/ModemManager-mm-sleep-monitor.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-sleep-monitor.c' object='ModemManager-mm-sleep-monitor.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-sleep-monitor.o `test -f 'mm-sleep-monitor.c' || echo '$(srcdir)/'`mm-sleep-monitor.c

ModemManager-mm-sleep-monitor.obj: mm-sleep-monitor.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-sleep-monitor.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-sleep-monitor.Tpo -c -o ModemManager-mm-sleep-monitor.obj `if test -f 'mm-sleep-monitor.c'; then $(CYGPATH_W) 'mm-sleep-monitor.c'; else $(CYGPATH_W) '$(srcdir)/mm-sleep-monitor.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-sleep-monitor.Tpo $(DEPDIR)/ModemManager-mm-sleep-monitor.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-sleep-monitor.c' object='ModemManager-mm-sleep-monitor.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-sleep-monitor.obj `if test -f 'mm-sleep-monitor.c'; then $(CYGPATH_W) 'mm-sleep-monitor.c'; else $(CYGPATH_W) '$(srcdir)/mm-sleep-monitor.c'; fi`

ModemManager-mm-shared-qmi.o: mm-shared-qmi.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-shared-qmi.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-shared-qmi.Tpo -c -o ModemManager-mm-shared-qmi.o `test -f 'mm-shared-qmi.c' || echo '$(srcdir)/'`mm-shared-qmi.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-shared-qmi.Tpo $(DEPDIR)/ModemManager-mm-shared-qmi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-shared-qmi.c' object='ModemManager-mm-shared-qmi.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-shared-qmi.o `test -f 'mm-shared-qmi.c' || echo '$(srcdir)/'`mm-shared-qmi.c

ModemManager-mm-shared-qmi.obj: mm-shared-qmi.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-shared-qmi.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-shared-qmi.Tpo -c -o ModemManager-mm-shared-qmi.obj `if test -f 'mm-shared-qmi.c'; then $(CYGPATH_W) 'mm-shared-qmi.c'; else $(CYGPATH_W) '$(srcdir)/mm-shared-qmi.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-shared-qmi.Tpo $(DEPDIR)/ModemManager-mm-shared-qmi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-shared-qmi.c' object='ModemManager-mm-shared-qmi.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-shared-qmi.obj `if test -f 'mm-shared-qmi.c'; then $(CYGPATH_W) 'mm-shared-qmi.c'; else $(CYGPATH_W) '$(srcdir)/mm-shared-qmi.c'; fi`

ModemManager-mm-sms-qmi.o: mm-sms-qmi.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-sms-qmi.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-sms-qmi.Tpo -c -o ModemManager-mm-sms-qmi.o `test -f 'mm-sms-qmi.c' || echo '$(srcdir)/'`mm-sms-qmi.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-sms-qmi.Tpo $(DEPDIR)/ModemManager-mm-sms-qmi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-sms-qmi.c' object='ModemManager-mm-sms-qmi.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-sms-qmi.o `test -f 'mm-sms-qmi.c' || echo '$(srcdir)/'`mm-sms-qmi.c

ModemManager-mm-sms-qmi.obj: mm-sms-qmi.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-sms-qmi.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-sms-qmi.Tpo -c -o ModemManager-mm-sms-qmi.obj `if test -f 'mm-sms-qmi.c'; then $(CYGPATH_W) 'mm-sms-qmi.c'; else $(CYGPATH_W) '$(srcdir)/mm-sms-qmi.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-sms-qmi.Tpo $(DEPDIR)/ModemManager-mm-sms-qmi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-sms-qmi.c' object='ModemManager-mm-sms-qmi.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-sms-qmi.obj `if test -f 'mm-sms-qmi.c'; then $(CYGPATH_W) 'mm-sms-qmi.c'; else $(CYGPATH_W) '$(srcdir)/mm-sms-qmi.c'; fi`

ModemManager-mm-sim-qmi.o: mm-sim-qmi.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-sim-qmi.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-sim-qmi.Tpo -c -o ModemManager-mm-sim-qmi.o `test -f 'mm-sim-qmi.c' || echo '$(srcdir)/'`mm-sim-qmi.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-sim-qmi.Tpo $(DEPDIR)/ModemManager-mm-sim-qmi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-sim-qmi.c' object='ModemManager-mm-sim-qmi.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-sim-qmi.o `test -f 'mm-sim-qmi.c' || echo '$(srcdir)/'`mm-sim-qmi.c

ModemManager-mm-sim-qmi.obj: mm-sim-qmi.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-sim-qmi.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-sim-qmi.Tpo -c -o ModemManager-mm-sim-qmi.obj `if test -f 'mm-sim-qmi.c'; then $(CYGPATH_W) 'mm-sim-qmi.c'; else $(CYGPATH_W) '$(srcdir)/mm-sim-qmi.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-sim-qmi.Tpo $(DEPDIR)/ModemManager-mm-sim-qmi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-sim-qmi.c' object='ModemManager-mm-sim-qmi.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-sim-qmi.obj `if test -f 'mm-sim-qmi.c'; then $(CYGPATH_W) 'mm-sim-qmi.c'; else $(CYGPATH_W) '$(srcdir)/mm-sim-qmi.c'; fi`

ModemManager-mm-bearer-qmi.o: mm-bearer-qmi.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-bearer-qmi.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-bearer-qmi.Tpo -c -o ModemManager-mm-bearer-qmi.o `test -f 'mm-bearer-qmi.c' || echo '$(srcdir)/'`mm-bearer-qmi.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-bearer-qmi.Tpo $(DEPDIR)/ModemManager-mm-bearer-qmi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-bearer-qmi.c' object='ModemManager-mm-bearer-qmi.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-bearer-qmi.o `test -f 'mm-bearer-qmi.c' || echo '$(srcdir)/'`mm-bearer-qmi.c

ModemManager-mm-bearer-qmi.obj: mm-bearer-qmi.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-bearer-qmi.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-bearer-qmi.Tpo -c -o ModemManager-mm-bearer-qmi.obj `if test -f 'mm-bearer-qmi.c'; then $(CYGPATH_W) 'mm-bearer-qmi.c'; else $(CYGPATH_W) '$(srcdir)/mm-bearer-qmi.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-bearer-qmi.Tpo $(DEPDIR)/ModemManager-mm-bearer-qmi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-bearer-qmi.c' object='ModemManager-mm-bearer-qmi.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-bearer-qmi.obj `if test -f 'mm-bearer-qmi.c'; then $(CYGPATH_W) 'mm-bearer-qmi.c'; else $(CYGPATH_W) '$(srcdir)/mm-bearer-qmi.c'; fi`

ModemManager-mm-broadband-modem-qmi.o: mm-broadband-modem-qmi.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-broadband-modem-qmi.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-broadband-modem-qmi.Tpo -c -o ModemManager-mm-broadband-modem-qmi.o `test -f 'mm-broadband-modem-qmi.c' || echo '$(srcdir)/'`mm-broadband-modem-qmi.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-broadband-modem-qmi.Tpo $(DEPDIR)/ModemManager-mm-broadband-modem-qmi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-broadband-modem-qmi.c' object='ModemManager-mm-broadband-modem-qmi.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-broadband-modem-qmi.o `test -f 'mm-broadband-modem-qmi.c' || echo '$(srcdir)/'`mm-broadband-modem-qmi.c

ModemManager-mm-broadband-modem-qmi.obj: mm-broadband-modem-qmi.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-broadband-modem-qmi.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-broadband-modem-qmi.Tpo -c -o ModemManager-mm-broadband-modem-qmi.obj `if test -f 'mm-broadband-modem-qmi.c'; then $(CYGPATH_W) 'mm-broadband-modem-qmi.c'; else $(CYGPATH_W) '$(srcdir)/mm-broadband-modem-qmi.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-broadband-modem-qmi.Tpo $(DEPDIR)/ModemManager-mm-broadband-modem-qmi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-broadband-modem-qmi.c' object='ModemManager-mm-broadband-modem-qmi.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-broadband-modem-qmi.obj `if test -f 'mm-broadband-modem-qmi.c'; then $(CYGPATH_W) 'mm-broadband-modem-qmi.c'; else $(CYGPATH_W) '$(srcdir)/mm-broadband-modem-qmi.c'; fi`

ModemManager-mm-sms-mbim.o: mm-sms-mbim.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-sms-mbim.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-sms-mbim.Tpo -c -o ModemManager-mm-sms-mbim.o `test -f 'mm-sms-mbim.c' || echo '$(srcdir)/'`mm-sms-mbim.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-sms-mbim.Tpo $(DEPDIR)/ModemManager-mm-sms-mbim.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-sms-mbim.c' object='ModemManager-mm-sms-mbim.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-sms-mbim.o `test -f 'mm-sms-mbim.c' || echo '$(srcdir)/'`mm-sms-mbim.c

ModemManager-mm-sms-mbim.obj: mm-sms-mbim.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-sms-mbim.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-sms-mbim.Tpo -c -o ModemManager-mm-sms-mbim.obj `if test -f 'mm-sms-mbim.c'; then $(CYGPATH_W) 'mm-sms-mbim.c'; else $(CYGPATH_W) '$(srcdir)/mm-sms-mbim.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-sms-mbim.Tpo $(DEPDIR)/ModemManager-mm-sms-mbim.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-sms-mbim.c' object='ModemManager-mm-sms-mbim.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-sms-mbim.obj `if test -f 'mm-sms-mbim.c'; then $(CYGPATH_W) 'mm-sms-mbim.c'; else $(CYGPATH_W) '$(srcdir)/mm-sms-mbim.c'; fi`

ModemManager-mm-sim-mbim.o: mm-sim-mbim.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-sim-mbim.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-sim-mbim.Tpo -c -o ModemManager-mm-sim-mbim.o `test -f 'mm-sim-mbim.c' || echo '$(srcdir)/'`mm-sim-mbim.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-sim-mbim.Tpo $(DEPDIR)/ModemManager-mm-sim-mbim.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-sim-mbim.c' object='ModemManager-mm-sim-mbim.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-sim-mbim.o `test -f 'mm-sim-mbim.c' || echo '$(srcdir)/'`mm-sim-mbim.c

ModemManager-mm-sim-mbim.obj: mm-sim-mbim.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-sim-mbim.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-sim-mbim.Tpo -c -o ModemManager-mm-sim-mbim.obj `if test -f 'mm-sim-mbim.c'; then $(CYGPATH_W) 'mm-sim-mbim.c'; else $(CYGPATH_W) '$(srcdir)/mm-sim-mbim.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-sim-mbim.Tpo $(DEPDIR)/ModemManager-mm-sim-mbim.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-sim-mbim.c' object='ModemManager-mm-sim-mbim.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-sim-mbim.obj `if test -f 'mm-sim-mbim.c'; then $(CYGPATH_W) 'mm-sim-mbim.c'; else $(CYGPATH_W) '$(srcdir)/mm-sim-mbim.c'; fi`

ModemManager-mm-bearer-mbim.o: mm-bearer-mbim.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-bearer-mbim.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-bearer-mbim.Tpo -c -o ModemManager-mm-bearer-mbim.o `test -f 'mm-bearer-mbim.c' || echo '$(srcdir)/'`mm-bearer-mbim.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-bearer-mbim.Tpo $(DEPDIR)/ModemManager-mm-bearer-mbim.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-bearer-mbim.c' object='ModemManager-mm-bearer-mbim.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-bearer-mbim.o `test -f 'mm-bearer-mbim.c' || echo '$(srcdir)/'`mm-bearer-mbim.c

ModemManager-mm-bearer-mbim.obj: mm-bearer-mbim.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-bearer-mbim.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-bearer-mbim.Tpo -c -o ModemManager-mm-bearer-mbim.obj `if test -f 'mm-bearer-mbim.c'; then $(CYGPATH_W) 'mm-bearer-mbim.c'; else $(CYGPATH_W) '$(srcdir)/mm-bearer-mbim.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-bearer-mbim.Tpo $(DEPDIR)/ModemManager-mm-bearer-mbim.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-bearer-mbim.c' object='ModemManager-mm-bearer-mbim.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-bearer-mbim.obj `if test -f 'mm-bearer-mbim.c'; then $(CYGPATH_W) 'mm-bearer-mbim.c'; else $(CYGPATH_W) '$(srcdir)/mm-bearer-mbim.c'; fi`

ModemManager-mm-broadband-modem-mbim.o: mm-broadband-modem-mbim.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-broadband-modem-mbim.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-broadband-modem-mbim.Tpo -c -o ModemManager-mm-broadband-modem-mbim.o `test -f 'mm-broadband-modem-mbim.c' || echo '$(srcdir)/'`mm-broadband-modem-mbim.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-broadband-modem-mbim.Tpo $(DEPDIR)/ModemManager-mm-broadband-modem-mbim.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-broadband-modem-mbim.c' object='ModemManager-mm-broadband-modem-mbim.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-broadband-modem-mbim.o `test -f 'mm-broadband-modem-mbim.c' || echo '$(srcdir)/'`mm-broadband-modem-mbim.c

ModemManager-mm-broadband-modem-mbim.obj: mm-broadband-modem-mbim.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-broadband-modem-mbim.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-broadband-modem-mbim.Tpo -c -o ModemManager-mm-broadband-modem-mbim.obj `if test -f 'mm-broadband-modem-mbim.c'; then $(CYGPATH_W) 'mm-broadband-modem-mbim.c'; else $(CYGPATH_W) '$(srcdir)/mm-broadband-modem-mbim.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-broadband-modem-mbim.Tpo $(DEPDIR)/ModemManager-mm-broadband-modem-mbim.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-broadband-modem-mbim.c' object='ModemManager-mm-broadband-modem-mbim.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-broadband-modem-mbim.obj `if test -f 'mm-broadband-modem-mbim.c'; then $(CYGPATH_W) 'mm-broadband-modem-mbim.c'; else $(CYGPATH_W) '$(srcdir)/mm-broadband-modem-mbim.c'; fi`

ModemManager-mm-daemon-enums-types.o: mm-daemon-enums-types.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-daemon-enums-types.o -MD -MP -MF $(DEPDIR)/ModemManager-mm-daemon-enums-types.Tpo -c -o ModemManager-mm-daemon-enums-types.o `test -f 'mm-daemon-enums-types.c' || echo '$(srcdir)/'`mm-daemon-enums-types.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-daemon-enums-types.Tpo $(DEPDIR)/ModemManager-mm-daemon-enums-types.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-daemon-enums-types.c' object='ModemManager-mm-daemon-enums-types.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-daemon-enums-types.o `test -f 'mm-daemon-enums-types.c' || echo '$(srcdir)/'`mm-daemon-enums-types.c

ModemManager-mm-daemon-enums-types.obj: mm-daemon-enums-types.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ModemManager-mm-daemon-enums-types.obj -MD -MP -MF $(DEPDIR)/ModemManager-mm-daemon-enums-types.Tpo -c -o ModemManager-mm-daemon-enums-types.obj `if test -f 'mm-daemon-enums-types.c'; then $(CYGPATH_W) 'mm-daemon-enums-types.c'; else $(CYGPATH_W) '$(srcdir)/mm-daemon-enums-types.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ModemManager-mm-daemon-enums-types.Tpo $(DEPDIR)/ModemManager-mm-daemon-enums-types.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mm-daemon-enums-types.c' object='ModemManager-mm-daemon-enums-types.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ModemManager_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ModemManager-mm-daemon-enums-types.obj `if test -f 'mm-daemon-enums-types.c'; then $(CYGPATH_W) 'mm-daemon-enums-types.c'; else $(CYGPATH_W) '$(srcdir)/mm-daemon-enums-types.c'; fi`

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
	-rm -rf kerneldevice/.libs kerneldevice/_libs
install-udevrulesDATA: $(udevrules_DATA)
	@$(NORMAL_INSTALL)
	@list='$(udevrules_DATA)'; test -n "$(udevrulesdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(udevrulesdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(udevrulesdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(udevrulesdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(udevrulesdir)" || exit $$?; \
	done

uninstall-udevrulesDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(udevrules_DATA)'; test -n "$(udevrulesdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(udevrulesdir)'; $(am__uninstall_files_from_dir)

# This directory's subdirectories are mostly independent; you can cd
# into them and run 'make' without going through this Makefile.
# To change the values of 'make' variables: instead of editing Makefiles,
# (1) if the variable is set in 'config.status', edit 'config.status'
#     (which will cause the Makefiles to be regenerated when you run 'make');
# (2) otherwise, pass the desired values on the 'make' command line.
$(am__recursive_targets):
	@fail=; \
	if $(am__make_keepgoing); then \
	  failcom='fail=yes'; \
	else \
	  failcom='exit 1'; \
	fi; \
	dot_seen=no; \
	target=`echo $@ | sed s/-recursive//`; \
	case "$@" in \
	  distclean-* | maintainer-clean-*) list='$(DIST_SUBDIRS)' ;; \
	  *) list='$(SUBDIRS)' ;; \
	esac; \
	for subdir in $$list; do \
	  echo "Making $$target in $$subdir"; \
	  if test "$$subdir" = "."; then \
	    dot_seen=yes; \
	    local_target="$$target-am"; \
	  else \
	    local_target="$$target"; \
	  fi; \
	  ($(am__cd) $$subdir && $(MAKE) $(AM_MAKEFLAGS) $$local_target) \
	  || eval $$failcom; \
	done; \
	if test "$$dot_seen" = "no"; then \
	  $(MAKE) $(AM_MAKEFLAGS) "$$target-am" || exit 1; \
	fi; test -z "$$fail"

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-recursive
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	if ($(ETAGS) --etags-include --version) >/dev/null 2>&1; then \
	  include_option=--etags-include; \
	  empty_fix=.; \
	else \
	  include_option=--include; \
	  empty_fix=; \
	fi; \
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    test ! -f $$subdir/TAGS || \
	      set "$$@" "$$include_option=$$here/$$subdir/TAGS"; \
	  fi; \
	done; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-recursive

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-recursive

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	@list='$(DIST_SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    $(am__make_dryrun) \
	      || test -d "$(distdir)/$$subdir" \
	      || $(MKDIR_P) "$(distdir)/$$subdir" \
	      || exit 1; \
	    dir1=$$subdir; dir2="$(distdir)/$$subdir"; \
	    $(am__relativize); \
	    new_distdir=$$reldir; \
	    dir1=$$subdir; dir2="$(top_distdir)"; \
	    $(am__relativize); \
	    new_top_distdir=$$reldir; \
	    echo " (cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) top_distdir="$$new_top_distdir" distdir="$$new_distdir" \\"; \
	    echo "     am__remove_distdir=: am__skip_length_check=: am__skip_mode_fix=: distdir)"; \
	    ($(am__cd) $$subdir && \
	      $(MAKE) $(AM_MAKEFLAGS) \
	        top_distdir="$$new_top_distdir" \
	        distdir="$$new_distdir" \
		am__remove_distdir=: \
		am__skip_length_check=: \
		am__skip_mode_fix=: \
	        distdir) \
	      || exit 1; \
	  fi; \
	done
check-am: all-am
check: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) check-recursive
all-am: Makefile $(PROGRAMS) $(LTLIBRARIES) $(DATA)
installdirs: installdirs-recursive
installdirs-am:
	for dir in "$(DESTDIR)$(sbindir)" "$(DESTDIR)$(udevrulesdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) install-recursive
install-exec: install-exec-recursive
install-data: install-data-recursive
uninstall: uninstall-recursive

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-recursive
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f kerneldevice/$(DEPDIR)/$(am__dirstamp)
	-rm -f kerneldevice/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
	-test -z "$(BUILT_SOURCES)" || rm -f $(BUILT_SOURCES)
clean: clean-recursive

clean-am: clean-generic clean-libtool clean-noinstLTLIBRARIES \
	clean-sbinPROGRAMS mostlyclean-am

distclean: distclean-recursive
		-rm -f ./$(DEPDIR)/ModemManager-main.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-auth-provider.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-base-bearer.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-base-call.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-base-manager.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-base-modem-at.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-base-modem.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-base-sim.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-base-sms.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-bearer-list.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-bearer-mbim.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-bearer-qmi.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-broadband-bearer.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-broadband-modem-mbim.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-broadband-modem-qmi.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-broadband-modem.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-call-list.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-context.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-daemon-enums-types.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-device.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-filter.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-3gpp-ussd.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-3gpp.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-cdma.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-firmware.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-location.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-messaging.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-oma.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-signal.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-simple.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-time.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-voice.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-plugin-manager.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-plugin.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-port-probe-at.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-port-probe.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-private-boxed-types.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-shared-qmi.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-sim-mbim.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-sim-qmi.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-sleep-monitor.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-sms-list.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-sms-mbim.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-sms-qmi.Po
	-rm -f ./$(DEPDIR)/mm-charsets.Plo
	-rm -f ./$(DEPDIR)/mm-error-helpers.Plo
	-rm -f ./$(DEPDIR)/mm-helper-enums-types.Plo
	-rm -f ./$(DEPDIR)/mm-log-object.Plo
	-rm -f ./$(DEPDIR)/mm-log.Plo
	-rm -f ./$(DEPDIR)/mm-modem-helpers-mbim.Plo
	-rm -f ./$(DEPDIR)/mm-modem-helpers-qmi.Plo
	-rm -f ./$(DEPDIR)/mm-modem-helpers.Plo
	-rm -f ./$(DEPDIR)/mm-port-enums-types.Plo
	-rm -f ./$(DEPDIR)/mm-port-mbim.Plo
	-rm -f ./$(DEPDIR)/mm-port-qmi.Plo
	-rm -f ./$(DEPDIR)/mm-port-serial-at.Plo
	-rm -f ./$(DEPDIR)/mm-port-serial-gps.Plo
	-rm -f ./$(DEPDIR)/mm-port-serial-qcdm.Plo
	-rm -f ./$(DEPDIR)/mm-port-serial.Plo
	-rm -f ./$(DEPDIR)/mm-port.Plo
	-rm -f ./$(DEPDIR)/mm-serial-parsers.Plo
	-rm -f ./$(DEPDIR)/mm-sms-part-3gpp.Plo
	-rm -f ./$(DEPDIR)/mm-sms-part-cdma.Plo
	-rm -f ./$(DEPDIR)/mm-sms-part.Plo
	-rm -f kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-generic-rules.Plo
	-rm -f kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-generic.Plo
	-rm -f kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-udev.Plo
	-rm -f kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-recursive

dvi-am:

html: html-recursive

html-am:

info: info-recursive

info-am:

install-data-am: install-udevrulesDATA

install-dvi: install-dvi-recursive

install-dvi-am:

install-exec-am: install-sbinPROGRAMS

install-html: install-html-recursive

install-html-am:

install-info: install-info-recursive

install-info-am:

install-man:

install-pdf: install-pdf-recursive

install-pdf-am:

install-ps: install-ps-recursive

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-recursive
		-rm -f ./$(DEPDIR)/ModemManager-main.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-auth-provider.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-base-bearer.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-base-call.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-base-manager.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-base-modem-at.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-base-modem.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-base-sim.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-base-sms.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-bearer-list.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-bearer-mbim.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-bearer-qmi.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-broadband-bearer.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-broadband-modem-mbim.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-broadband-modem-qmi.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-broadband-modem.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-call-list.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-context.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-daemon-enums-types.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-device.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-filter.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-3gpp-ussd.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-3gpp.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-cdma.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-firmware.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-location.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-messaging.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-oma.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-signal.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-simple.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-time.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem-voice.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-iface-modem.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-plugin-manager.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-plugin.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-port-probe-at.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-port-probe.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-private-boxed-types.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-shared-qmi.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-sim-mbim.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-sim-qmi.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-sleep-monitor.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-sms-list.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-sms-mbim.Po
	-rm -f ./$(DEPDIR)/ModemManager-mm-sms-qmi.Po
	-rm -f ./$(DEPDIR)/mm-charsets.Plo
	-rm -f ./$(DEPDIR)/mm-error-helpers.Plo
	-rm -f ./$(DEPDIR)/mm-helper-enums-types.Plo
	-rm -f ./$(DEPDIR)/mm-log-object.Plo
	-rm -f ./$(DEPDIR)/mm-log.Plo
	-rm -f ./$(DEPDIR)/mm-modem-helpers-mbim.Plo
	-rm -f ./$(DEPDIR)/mm-modem-helpers-qmi.Plo
	-rm -f ./$(DEPDIR)/mm-modem-helpers.Plo
	-rm -f ./$(DEPDIR)/mm-port-enums-types.Plo
	-rm -f ./$(DEPDIR)/mm-port-mbim.Plo
	-rm -f ./$(DEPDIR)/mm-port-qmi.Plo
	-rm -f ./$(DEPDIR)/mm-port-serial-at.Plo
	-rm -f ./$(DEPDIR)/mm-port-serial-gps.Plo
	-rm -f ./$(DEPDIR)/mm-port-serial-qcdm.Plo
	-rm -f ./$(DEPDIR)/mm-port-serial.Plo
	-rm -f ./$(DEPDIR)/mm-port.Plo
	-rm -f ./$(DEPDIR)/mm-serial-parsers.Plo
	-rm -f ./$(DEPDIR)/mm-sms-part-3gpp.Plo
	-rm -f ./$(DEPDIR)/mm-sms-part-cdma.Plo
	-rm -f ./$(DEPDIR)/mm-sms-part.Plo
	-rm -f kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-generic-rules.Plo
	-rm -f kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-generic.Plo
	-rm -f kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device-udev.Plo
	-rm -f kerneldevice/$(DEPDIR)/libkerneldevice_la-mm-kernel-device.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-recursive

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-recursive

pdf-am:

ps: ps-recursive

ps-am:

uninstall-am: uninstall-sbinPROGRAMS uninstall-udevrulesDATA

.MAKE: $(am__recursive_targets) all check install install-am \
	install-strip

.PHONY: $(am__recursive_targets) CTAGS GTAGS TAGS all all-am \
	am--depfiles check check-am clean clean-generic clean-libtool \
	clean-noinstLTLIBRARIES clean-sbinPROGRAMS cscopelist-am ctags \
	ctags-am distclean distclean-compile distclean-generic \
	distclean-libtool distclean-tags distdir dvi dvi-am html \
	html-am info info-am install install-am install-data \
	install-data-am install-dvi install-dvi-am install-exec \
	install-exec-am install-html install-html-am install-info \
	install-info-am install-man install-pdf install-pdf-am \
	install-ps install-ps-am install-sbinPROGRAMS install-strip \
	install-udevrulesDATA installcheck installcheck-am installdirs \
	installdirs-am maintainer-clean maintainer-clean-generic \
	mostlyclean mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool pdf pdf-am ps ps-am tags tags-am uninstall \
	uninstall-am uninstall-sbinPROGRAMS uninstall-udevrulesDATA

.PRECIOUS: Makefile


mm-helper-enums-types.h: Makefile.am $(HELPER_ENUMS_INPUTS) $(top_srcdir)/build-aux/mm-enums-template.h
	$(AM_V_GEN) $(GLIB_MKENUMS) \
		--fhead "#include \"mm-sms-part.h\"\n#include \"mm-modem-helpers.h\"\n#ifndef __MM_HELPER_ENUMS_TYPES_H__\n#define __MM_HELPER_ENUMS_TYPES_H__\n" \
		--template $(top_srcdir)/build-aux/mm-enums-template.h \
		--ftail "#endif /* __MM_HELPER_ENUMS_TYPES_H__ */\n" \
		$(HELPER_ENUMS_INPUTS) > $@

mm-helper-enums-types.c: Makefile.am $(top_srcdir)/build-aux/mm-enums-template.c mm-helper-enums-types.h
	$(AM_V_GEN) $(GLIB_MKENUMS) \
		--fhead "#include \"mm-helper-enums-types.h\"" \
		--template $(top_srcdir)/build-aux/mm-enums-template.c \
		$(HELPER_ENUMS_INPUTS) > $@

mm-port-enums-types.h: Makefile.am $(PORT_ENUMS_INPUTS) $(top_srcdir)/build-aux/mm-enums-template.h
	$(AM_V_GEN) $(GLIB_MKENUMS) \
		--fhead "#include \"mm-port.h\"\n#include \"mm-port-serial-at.h\"\n#ifndef __MM_PORT_ENUMS_TYPES_H__\n#define __MM_PORT_ENUMS_TYPES_H__\n" \
		--template $(top_srcdir)/build-aux/mm-enums-template.h \
		--ftail "#endif /* __MM_PORT_ENUMS_TYPES_H__ */\n" \
		$(PORT_ENUMS_INPUTS) > $@

mm-port-enums-types.c: Makefile.am $(top_srcdir)/build-aux/mm-enums-template.c mm-port-enums-types.h
	$(AM_V_GEN) $(GLIB_MKENUMS) \
		--fhead "#include \"mm-port-enums-types.h\"" \
		--template $(top_srcdir)/build-aux/mm-enums-template.c \
		$(PORT_ENUMS_INPUTS) > $@

mm-daemon-enums-types.h: Makefile.am $(DAEMON_ENUMS_INPUTS) $(top_srcdir)/build-aux/mm-enums-template.h
	$(AM_V_GEN) $(GLIB_MKENUMS) \
		--fhead "#include \"mm-filter.h\"\n#include \"mm-base-bearer.h\"\n#include \"mm-port-probe.h\"\n#ifndef __MM_DAEMON_ENUMS_TYPES_H__\n#define __MM_DAEMON_ENUMS_TYPES_H__\n" \
		--template $(top_srcdir)/build-aux/mm-enums-template.h \
		--ftail "#endif /* __MM_DAEMON_ENUMS_TYPES_H__ */\n" \
		$(DAEMON_ENUMS_INPUTS) > $@

mm-daemon-enums-types.c: Makefile.am $(top_srcdir)/build-aux/mm-enums-template.c mm-daemon-enums-types.h
	$(AM_V_GEN) $(GLIB_MKENUMS) \
		--fhead "#include \"mm-daemon-enums-types.h\"" \
		--template $(top_srcdir)/build-aux/mm-enums-template.c \
		$(DAEMON_ENUMS_INPUTS) > $@

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

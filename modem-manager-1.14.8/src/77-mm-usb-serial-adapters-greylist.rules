# do not edit this file, it will be overwritten on update

ACTION!="add|change|move|bind", GOTO="mm_usb_serial_adapters_greylist_end"
SUBSYSTEM!="usb", GOTO="mm_usb_serial_adapters_greylist_end"

# Belkin F5U183 Serial Adapter
ATTRS{idVendor}=="050d", ATTRS{idProduct}=="0103", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"

# FTDI-based serial adapters
#   FTDI does USB to serial converter ICs; and it's very likely that they'll
#   never do modems themselves, so it should be safe to add a rule only based
#   on the vendor Id.
ATTRS{idVendor}=="0403", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"

# Devices using Microchip's VID
ATTRS{idVendor}=="04d8", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"

# ATEN Intl UC-232A (Prolific)
ATTRS{idVendor}=="0557", ATTRS{idProduct}=="2008", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"

# Prolific USB to Serial adapter
ATTRS{idVendor}=="067b", ATTRS{idProduct}=="2303", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"

# Magic Control Technology Corp adapters
ATTRS{idVendor}=="0711", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"

# Cygnal Integrated Products, Inc. CP210x
ATTRS{idVendor}=="10c4", ATTRS{idProduct}=="ea60", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"
ATTRS{idVendor}=="10c4", ATTRS{idProduct}=="ea71", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"

# QinHeng Electronics HL-340
ATTRS{idVendor}=="1a86", ATTRS{idProduct}=="7523", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"

# Atmel Corp. LUFA USB to Serial Adapter Project (Arduino)
ATTRS{idVendor}=="03eb", ATTRS{idProduct}=="204b", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"

# Netchip Technology, Inc. Linux-USB Serial Gadget (CDC ACM mode)
ATTRS{idVendor}=="0525", ATTRS{idProduct}=="a4a7", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"

# Cypress Serial-USB devices
ATTRS{idVendor}=="04B4", ATTRS{idProduct}=="0002", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"
ATTRS{idVendor}=="04B4", ATTRS{idProduct}=="0003", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"
ATTRS{idVendor}=="04B4", ATTRS{idProduct}=="0004", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"
ATTRS{idVendor}=="04B4", ATTRS{idProduct}=="0005", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"
ATTRS{idVendor}=="04B4", ATTRS{idProduct}=="0006", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"
ATTRS{idVendor}=="04B4", ATTRS{idProduct}=="0007", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"
ATTRS{idVendor}=="04B4", ATTRS{idProduct}=="0009", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"
ATTRS{idVendor}=="04B4", ATTRS{idProduct}=="000A", ENV{ID_MM_TTY_MANUAL_SCAN_ONLY}="1"

LABEL="mm_usb_serial_adapters_greylist_end"

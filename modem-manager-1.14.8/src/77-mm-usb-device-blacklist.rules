# do not edit this file, it will be overwritten on update

ACTION!="add|change|move|bind", GOTO="mm_usb_device_blacklist_end"
SUBSYSTEM!="usb", GOTO="mm_usb_device_blacklist_end"

# APC UPS devices
ATTRS{idVendor}=="051d", ENV{ID_MM_TTY_BLACKLIST}="1"

# Sweex 1000VA
ATTRS{idVendor}=="0925", ATTRS{idProduct}=="1234", ENV{ID_MM_TTY_BLACKLIST}="1"

# Agiler UPS
ATTRS{idVendor}=="05b8", ATTRS{idProduct}=="0000", ENV{ID_MM_TTY_BLACKLIST}="1"

# Krauler UP-M500VA
ATTRS{idVendor}=="0001", ATTRS{idProduct}=="0000", ENV{ID_MM_TTY_BLACKLIST}="1"

# Ablerex 625L USB
ATTRS{idVendor}=="ffff", ATTRS{idProduct}=="0000", ENV{ID_MM_TTY_BLACKLIST}="1"

# Belkin F6C1200-UNV
ATTRS{idVendor}=="0665", ATTRS{idProduct}=="5161", ENV{ID_MM_TTY_BLACKLIST}="1"

# Various Liebert and Phoenixtec Power devices
ATTRS{idVendor}=="06da", ENV{ID_MM_TTY_BLACKLIST}="1"

# Unitek Alpha 1200Sx
ATTRS{idVendor}=="0f03", ATTRS{idProduct}=="0001", ENV{ID_MM_TTY_BLACKLIST}="1"

# Various Tripplite devices
ATTRS{idVendor}=="09ae", ENV{ID_MM_TTY_BLACKLIST}="1"

# Various MGE Office Protection Systems devices
ATTRS{idVendor}=="0463", ATTRS{idProduct}=="0001", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="0463", ATTRS{idProduct}=="ffff", ENV{ID_MM_TTY_BLACKLIST}="1"

# CyberPower 900AVR/BC900D
ATTRS{idVendor}=="0764", ATTRS{idProduct}=="0005", ENV{ID_MM_TTY_BLACKLIST}="1"
# CyberPower CP1200AVR/BC1200D
ATTRS{idVendor}=="0764", ATTRS{idProduct}=="0501", ENV{ID_MM_TTY_BLACKLIST}="1"

# Various Belkin devices
ATTRS{idVendor}=="050d", ATTRS{idProduct}=="0980", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="050d", ATTRS{idProduct}=="0900", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="050d", ATTRS{idProduct}=="0910", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="050d", ATTRS{idProduct}=="0912", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="050d", ATTRS{idProduct}=="0551", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="050d", ATTRS{idProduct}=="0751", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="050d", ATTRS{idProduct}=="0375", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="050d", ATTRS{idProduct}=="1100", ENV{ID_MM_TTY_BLACKLIST}="1"

# HP R/T 2200 INTL (like SMART2200RMXL2U)
ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1f0a", ENV{ID_MM_TTY_BLACKLIST}="1"

# Powerware devices
ATTRS{idVendor}=="0592", ATTRS{idProduct}=="0002", ENV{ID_MM_TTY_BLACKLIST}="1"

# Palm Treo 700/900/etc
# Shouldn't be probed themselves, but you can install programs like
# "MobileStream USB Modem" which changes the USB PID of the device to something
# that isn't blacklisted.
ATTRS{idVendor}=="0830", ATTRS{idProduct}=="0061", ENV{ID_MM_TTY_BLACKLIST}="1"

# GlobalScaleTechnologies SheevaPlug
ATTRS{idVendor}=="9e88", ATTRS{idProduct}=="9e8f", ENV{ID_MM_TTY_BLACKLIST}="1"

# Atmel Corp at91sam SAMBA bootloader
ATTRS{idVendor}=="03eb", ATTRS{idProduct}=="6124", ENV{ID_MM_TTY_BLACKLIST}="1"

# All devices from the Swiss Federal Institute of Technology
ATTRS{idVendor}=="0617", ENV{ID_MM_TTY_BLACKLIST}="1"

# West Mountain Radio devices
ATTRS{idVendor}=="10c4", ATTRS{idProduct}=="814a", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="10c4", ATTRS{idProduct}=="814b", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="2405", ATTRS{idProduct}=="0003", ENV{ID_MM_TTY_BLACKLIST}="1"

# Arduinos
ATTRS{idVendor}=="2341", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="2a03", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="1b4f", ATTRS{idProduct}=="9207", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="1b4f", ATTRS{idProduct}=="9208", ENV{ID_MM_TTY_BLACKLIST}="1"

# Chinese clone of Arduino nano with a LGT8F328P MCU
ATTRS{idVendor}=="04d9", ATTRS{idProduct}=="b534", ENV{ID_MM_TTY_BLACKLIST}="1"

# Adafruit Flora
ATTRS{idVendor}=="239a", ATTRS{idProduct}=="0004", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="239a", ATTRS{idProduct}=="8004", ENV{ID_MM_TTY_BLACKLIST}="1"

# All devices from Pololu Corporation
# except some possible future products.
ATTRS{idVendor}=="1ffb", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="1ffb", ATTRS{idProduct}=="00ad", ENV{ID_MM_TTY_BLACKLIST}="0"
ATTRS{idVendor}=="1ffb", ATTRS{idProduct}=="00ae", ENV{ID_MM_TTY_BLACKLIST}="0"

# Altair U-Boot device
ATTRS{idVendor}=="0216", ATTRS{idProduct}=="0051", ENV{ID_MM_TTY_BLACKLIST}="1"

# Bluegiga BLE112B
ATTRS{idVendor}=="2458", ATTRS{idProduct}=="0001", ENV{ID_MM_TTY_BLACKLIST}="1"

# MediaTek GPS chip (HOLUX M-1200E, GlobalTop Gms-d1, etc)
ATTRS{idVendor}=="0e8d", ATTRS{idProduct}=="3329", ENV{ID_MM_TTY_BLACKLIST}="1"

# MediaTek MT65xx preloader
ATTRS{idVendor}=="0e8d", ATTRS{idProduct}=="2000", ENV{ID_MM_TTY_BLACKLIST}="1"

# PS-360 OEM (GPS sold with MS Street and Trips 2005)
ATTRS{idVendor}=="067b", ATTRS{idProduct}=="aaa0", ENV{ID_MM_TTY_BLACKLIST}="1"

# Garmin GPS devices
DRIVERS=="garmin_gps", ENV{ID_MM_TTY_BLACKLIST}="1"

# Garmin ANT+ stick
ATTRS{idVendor}=="0fcf", ATTRS{idProduct}=="1009", ENV{ID_MM_TTY_BLACKLIST}="1"

# Cypress M8-based GPS devices, UPSes, and serial converters
DRIVERS=="cypress_m8", ENV{ID_MM_TTY_BLACKLIST}="1"

# All devices in the Openmoko vendor ID, except usb hubs
ATTRS{idVendor}=="1d50", ATTRS{bDeviceClass}!="09", ENV{ID_MM_TTY_BLACKLIST}="1"

# All devices from 3D Robotics
ATTRS{idVendor}=="26ac", ENV{ID_MM_TTY_BLACKLIST}="1"

# empiriKit science lab controller device
ATTRS{idVendor}=="0425", ATTRS{idProduct}=="0408", ENV{ID_MM_TTY_BLACKLIST}="1"

# Infineon Flashloader used by Intel XMM modem bootloader
ATTRS{idVendor}=="8087", ATTRS{idProduct}=="0716", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="8087", ATTRS{idProduct}=="0801", ENV{ID_MM_TTY_BLACKLIST}="1"

# Intel coredump downloader device
ATTRS{idVendor}=="1519", ATTRS{idProduct}=="f000", ENV{ID_MM_TTY_BLACKLIST}="1"

# GW Instek AFG-2225 arbitrary function generator
ATTRS{idVendor}=="2184", ATTRS{idProduct}=="001c", ENV{ID_MM_TTY_BLACKLIST}="1"

# PalmOS devices - even though some are phones, they are so old they most
# likely are not being used anymore
DRIVERS=="visor", ENV{ID_MM_TTY_BLACKLIST}="1"

# Palmconnect
ATTRS{idVendor}=="0830", ATTRS{idProduct}=="0080", ENV{ID_MM_TTY_BLACKLIST}="1"

# IMC flashing device
ATTRS{idVendor}=="058b", ATTRS{idProduct}=="0041", ENV{ID_MM_TTY_BLACKLIST}="1"

# All devices from the Access Interfacing Solutions (Access Ltd)
# Access IS do not produce modems and are unlikely to do so in future
ATTRS{idVendor}=="0db5", ENV{ID_MM_TTY_BLACKLIST}="1"

# Palm M500
ATTRS{idVendor}=="0830", ATTRS{idProduct}=="0001", ENV{ID_MM_TTY_BLACKLIST}="1"

# Palm M505
ATTRS{idVendor}=="0830", ATTRS{idProduct}=="0002", ENV{ID_MM_TTY_BLACKLIST}="1"

# Palm M515
ATTRS{idVendor}=="0830", ATTRS{idProduct}=="0003", ENV{ID_MM_TTY_BLACKLIST}="1"

# All devices from POSNET POLSKA S.A.
# POSNET POLSKA S.A. do not produce modems and are unlikely to do so in future
ATTRS{idVendor}=="1424", ENV{ID_MM_TTY_BLACKLIST}="1"

# proxmark3
ATTRS{manufacturer}=="proxmark.org", ENV{ID_MM_TTY_BLACKLIST}="1"

# Sigma Sport Docking Station TOPLINE 2009
ATTRS{idVendor}=="1d9d", ATTRS{idProduct}=="1010", ENV{ID_MM_TTY_BLACKLIST}="1"
# Sigma Sport Docking Station TOPLINE 2012
ATTRS{idVendor}=="1d9d", ATTRS{idProduct}=="1011", ENV{ID_MM_TTY_BLACKLIST}="1"

# Telit LE866 flashing device
ATTRS{idVendor}=="216f", ATTRS{idProduct}=="0051", ENV{ID_MM_TTY_BLACKLIST}="1"

# Analog Devices ADALM-PLUTO (PlutoSDR)
ATTRS{idVendor}=="0456", ATTRS{idProduct}=="b673", ENV{ID_MM_TTY_BLACKLIST}="1"

# Renesas development and promotion boards
ATTRS{idVendor}=="045B", ATTRS{idProduct}=="0212", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="0409", ATTRS{idProduct}=="0063", ENV{ID_MM_TTY_BLACKLIST}="1"

# Analog Devices EVAL-ADXL362Z-DB
ATTRS{idVendor}=="064B", ATTRS{idProduct}=="7825", ENV{ID_MM_TTY_BLACKLIST}="1"

# keyboard.io devices
ATTRS{idVendor}=="1209", ATTRS{idProduct}=="2300", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="1209", ATTRS{idProduct}=="2301", ENV{ID_MM_TTY_BLACKLIST}="1"

# Netchip Technology, Inc. Linux-USB Serial Gadget (CDC ACM mode)
ATTRS{idVendor}=="0525", ATTRS{idProduct}=="a4a7", ENV{ID_MM_TTY_BLACKLIST}="1"

# Silicon Labs Telegesis ETRX USB Zigbee dongle
ATTRS{idVendor}=="10c4", ATTRS{idProduct}=="000f", ENV{ID_MM_TTY_BLACKLIST}="1"

# Devices using Microchip's VID
# Dangerous Prototypes Bus Pirate v4
ATTRS{idVendor}=="04d8", ATTRS{idProduct}=="fb00", ENV{ID_MM_TTY_BLACKLIST}="1"
# Pycom Pysense
ATTRS{idVendor}=="04d8", ATTRS{idProduct}=="f012", ENV{ID_MM_TTY_BLACKLIST}="1"
# Pycom Pytrack
ATTRS{idVendor}=="04d8", ATTRS{idProduct}=="f013", ENV{ID_MM_TTY_BLACKLIST}="1"

# All devices from Prusa Research
ATTRS{idVendor}=="2c99", ENV{ID_MM_TTY_BLACKLIST}="1"

# USB-CEC adapters
ATTRS{idVendor}=="2548", ATTRS{idProduct}=="1001", ENV{ID_MM_TTY_BLACKLIST}="1"
ATTRS{idVendor}=="2548", ATTRS{idProduct}=="1002", ENV{ID_MM_TTY_BLACKLIST}="1"

LABEL="mm_usb_device_blacklist_end"

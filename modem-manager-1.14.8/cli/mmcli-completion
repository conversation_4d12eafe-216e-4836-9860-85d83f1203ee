# mmcli(1) completion                                      -*- shell-script -*-

_mmcli()
{
    local cur prev words cword split
    _init_completion -s || return

    case $prev in
        '-G'|'--set-logging')
            COMPREPLY=( $(compgen -W "[ERR,WARN,INFO,DEBUG]" -- $cur) )
            return 0
            ;;
        '-m'|'--modem')
            COMPREPLY=( $(compgen -W "[PATH|INDEX]" -- $cur) )
            return 0
            ;;
        '-b'|'--bearer')
            COMPREPLY=( $(compgen -W "[PATH|INDEX]" -- $cur) )
            return 0
            ;;
        '-i'|'--sim')
            COMPREPLY=( $(compgen -W "[PATH|INDEX]" -- $cur) )
            return 0
            ;;
        '-s'|'--sms')
            COMPREPLY=( $(compgen -W "[PATH|INDEX]" -- $cur) )
            return 0
            ;;
        '--factory-reset')
            COMPREPLY=( $(compgen -W "[CODE]" -- $cur) )
            return 0
            ;;
        '--command')
            COMPREPLY=( $(compgen -W "[COMMAND]" -- $cur) )
            return 0
            ;;
        '--create-bearer')
            COMPREPLY=( $(compgen -W "[key=value,...]" -- $cur) )
            return 0
            ;;
        '--delete-bearer')
            COMPREPLY=( $(compgen -W "[PATH|INDEX]" -- $cur) )
            return 0
            ;;
        '--set-current-capabilities')
            COMPREPLY=( $(compgen -W "[CAPABILITY1|CAPABILITY2...]" -- $cur) )
            return 0
            ;;
        '--set-allowed-modes')
            COMPREPLY=( $(compgen -W "[MODE1|MODE2...]" -- $cur) )
            return 0
            ;;
        '--set-preferred-mode')
            COMPREPLY=( $(compgen -W "[MODE]" -- $cur) )
            return 0
            ;;
        '--set-current-bands')
            COMPREPLY=( $(compgen -W "[BAND1|BAND2...]" -- $cur) )
            return 0
            ;;
        '--3gpp-register-in-operator')
            COMPREPLY=( $(compgen -W "[MCCMNC]" -- $cur) )
            return 0
            ;;
        '--3gpp-ussd-initiate')
            COMPREPLY=( $(compgen -W "[command]" -- $cur) )
            return 0
            ;;
        '--3gpp-ussd-respond')
            COMPREPLY=( $(compgen -W "[response]" -- $cur) )
            return 0
            ;;
        '--cdma-activate')
            COMPREPLY=( $(compgen -W "[CARRIER]" -- $cur) )
            return 0
            ;;
        '--cdma-activate-manual')
            COMPREPLY=( $(compgen -W "[key=value,...]" -- $cur) )
            return 0
            ;;
        '--cdma-activate-manual-with-prl-file')
            _filedir
            return 0
            ;;
        '--simple-connect')
            COMPREPLY=( $(compgen -W "[key=value,...]" -- $cur) )
            return 0
            ;;
        '--location-set-supl-server')
            COMPREPLY=( $(compgen -W "[IP:PORT|URL]" -- $cur) )
            return 0
            ;;
        '--messaging-create-sms')
            COMPREPLY=( $(compgen -W "[key=value,...]" -- $cur) )
            return 0
            ;;
        '--messaging-create-sms-with-data')
            _filedir
            return 0
            ;;
        '--messaging-delete-sms')
            COMPREPLY=( $(compgen -W "[PATH|INDEX]" -- $cur) )
            return 0
            ;;
        '--firmware-select')
            COMPREPLY=( $(compgen -W "[Unique-ID]" -- $cur) )
            return 0
            ;;
        '--signal-setup')
            COMPREPLY=( $(compgen -W "[Rate]" -- $cur) )
            return 0
            ;;
        '--oma-setup')
            COMPREPLY=( $(compgen -W "[FEATURE1|FEATURE2...]" -- $cur) )
            return 0
            ;;
        '--oma-start-client-initiated-session')
            COMPREPLY=( $(compgen -W "[Session-Type]" -- $cur) )
            return 0
            ;;
        '--oma-accept-network-initiated-session')
            COMPREPLY=( $(compgen -W "[Session-ID]" -- $cur) )
            return 0
            ;;
        '--oma-reject-network-initiated-session')
            COMPREPLY=( $(compgen -W "[Session-ID]" -- $cur) )
            return 0
            ;;
        '--pin')
            COMPREPLY=( $(compgen -W "[PIN]" -- $cur) )
            return 0
            ;;
        '--puk')
            COMPREPLY=( $(compgen -W "[PUK]" -- $cur) )
            return 0
            ;;
        '--change-pin')
            COMPREPLY=( $(compgen -W "[New-PIN]" -- $cur) )
            return 0
            ;;
        '--store-in-storage')
            COMPREPLY=( $(compgen -W "[Storage]" -- $cur) )
            return 0
            ;;
        '--create-file-with-data')
            _filedir
            return 0
            ;;
        '--timeout')
            COMPREPLY=( $(compgen -W "[SECONDS]" -- $cur) )
            return 0
            ;;
        '-V'|'--version')
            return 0
            ;;
        '-h'|'--help'|'--help-all'|'--help-manager'|'--help-common'|'--help-modem'|'--help-3gpp'|'--help-cdma'|'--help-simple'|'--help-location'|'--help-messaging'|'--help-time'|'--help-firmware'|'--help-signal'|'--help-oma'|'--help-sim'|'--help-bearer'|'--help-sms')
            return 0
            ;;
    esac

    $split && return 0

    if [[ $cur == -* ]]; then
        COMPREPLY=( $( compgen -W '$( _parse_help "$1" --help-all )' -- "$cur" ) )
        [[ $COMPREPLY == *= ]] && compopt -o nospace
        return 0
    fi
} &&
complete -F _mmcli mmcli

# ex: ts=4 sw=4 et filetype=sh

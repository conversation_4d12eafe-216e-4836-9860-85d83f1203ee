# Makefile.in generated by automake 1.16.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
bin_PROGRAMS = mmcli$(EXEEXT)
@WITH_UDEV_TRUE@am__append_1 = $(GUDEV_CFLAGS)
@WITH_UDEV_TRUE@am__append_2 = $(GUDEV_LIBS)
subdir = cli
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_code_coverage.m4 \
	$(top_srcdir)/m4/gettext.m4 $(top_srcdir)/m4/gtk-doc.m4 \
	$(top_srcdir)/m4/iconv.m4 $(top_srcdir)/m4/intlmacosx.m4 \
	$(top_srcdir)/m4/introspection.m4 $(top_srcdir)/m4/lib-ld.m4 \
	$(top_srcdir)/m4/lib-link.m4 $(top_srcdir)/m4/lib-prefix.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/mm-enable-plugin.m4 $(top_srcdir)/m4/nls.m4 \
	$(top_srcdir)/m4/po.m4 $(top_srcdir)/m4/progtest.m4 \
	$(top_srcdir)/m4/vapigen.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__installdirs = "$(DESTDIR)$(bindir)"
PROGRAMS = $(bin_PROGRAMS)
am_mmcli_OBJECTS = mmcli-mmcli.$(OBJEXT) mmcli-mmcli-common.$(OBJEXT) \
	mmcli-mmcli-output.$(OBJEXT) mmcli-mmcli-manager.$(OBJEXT) \
	mmcli-mmcli-modem.$(OBJEXT) mmcli-mmcli-modem-3gpp.$(OBJEXT) \
	mmcli-mmcli-modem-cdma.$(OBJEXT) \
	mmcli-mmcli-modem-simple.$(OBJEXT) \
	mmcli-mmcli-modem-location.$(OBJEXT) \
	mmcli-mmcli-modem-messaging.$(OBJEXT) \
	mmcli-mmcli-modem-voice.$(OBJEXT) \
	mmcli-mmcli-modem-time.$(OBJEXT) \
	mmcli-mmcli-modem-firmware.$(OBJEXT) \
	mmcli-mmcli-modem-signal.$(OBJEXT) \
	mmcli-mmcli-modem-oma.$(OBJEXT) mmcli-mmcli-bearer.$(OBJEXT) \
	mmcli-mmcli-sim.$(OBJEXT) mmcli-mmcli-sms.$(OBJEXT) \
	mmcli-mmcli-call.$(OBJEXT)
mmcli_OBJECTS = $(am_mmcli_OBJECTS)
mmcli_DEPENDENCIES = $(top_builddir)/libmm-glib/libmm-glib.la
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
mmcli_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(mmcli_LDFLAGS) $(LDFLAGS) -o $@
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/mmcli-mmcli-bearer.Po \
	./$(DEPDIR)/mmcli-mmcli-call.Po \
	./$(DEPDIR)/mmcli-mmcli-common.Po \
	./$(DEPDIR)/mmcli-mmcli-manager.Po \
	./$(DEPDIR)/mmcli-mmcli-modem-3gpp.Po \
	./$(DEPDIR)/mmcli-mmcli-modem-cdma.Po \
	./$(DEPDIR)/mmcli-mmcli-modem-firmware.Po \
	./$(DEPDIR)/mmcli-mmcli-modem-location.Po \
	./$(DEPDIR)/mmcli-mmcli-modem-messaging.Po \
	./$(DEPDIR)/mmcli-mmcli-modem-oma.Po \
	./$(DEPDIR)/mmcli-mmcli-modem-signal.Po \
	./$(DEPDIR)/mmcli-mmcli-modem-simple.Po \
	./$(DEPDIR)/mmcli-mmcli-modem-time.Po \
	./$(DEPDIR)/mmcli-mmcli-modem-voice.Po \
	./$(DEPDIR)/mmcli-mmcli-modem.Po \
	./$(DEPDIR)/mmcli-mmcli-output.Po \
	./$(DEPDIR)/mmcli-mmcli-sim.Po ./$(DEPDIR)/mmcli-mmcli-sms.Po \
	./$(DEPDIR)/mmcli-mmcli.Po
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(mmcli_SOURCES)
DIST_SOURCES = $(mmcli_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CODE_COVERAGE_CFLAGS = @CODE_COVERAGE_CFLAGS@
CODE_COVERAGE_ENABLED = @CODE_COVERAGE_ENABLED@
CODE_COVERAGE_LDFLAGS = @CODE_COVERAGE_LDFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CYGPATH_W = @CYGPATH_W@
DBUS_SYS_DIR = @DBUS_SYS_DIR@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
GCOV = @GCOV@
GDBUS_CODEGEN = @GDBUS_CODEGEN@
GENHTML = @GENHTML@
GETTEXT_MACRO_VERSION = @GETTEXT_MACRO_VERSION@
GETTEXT_PACKAGE = @GETTEXT_PACKAGE@
GLIB_MKENUMS = @GLIB_MKENUMS@
GMSGFMT = @GMSGFMT@
GMSGFMT_015 = @GMSGFMT_015@
GREP = @GREP@
GTKDOC_CHECK = @GTKDOC_CHECK@
GTKDOC_CHECK_PATH = @GTKDOC_CHECK_PATH@
GTKDOC_DEPS_CFLAGS = @GTKDOC_DEPS_CFLAGS@
GTKDOC_DEPS_LIBS = @GTKDOC_DEPS_LIBS@
GTKDOC_MKPDF = @GTKDOC_MKPDF@
GTKDOC_REBASE = @GTKDOC_REBASE@
GUDEV_CFLAGS = @GUDEV_CFLAGS@
GUDEV_LIBS = @GUDEV_LIBS@
HTML_DIR = @HTML_DIR@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
INTLLIBS = @INTLLIBS@
INTL_MACOSX_LIBS = @INTL_MACOSX_LIBS@
INTROSPECTION_CFLAGS = @INTROSPECTION_CFLAGS@
INTROSPECTION_COMPILER = @INTROSPECTION_COMPILER@
INTROSPECTION_GENERATE = @INTROSPECTION_GENERATE@
INTROSPECTION_GIRDIR = @INTROSPECTION_GIRDIR@
INTROSPECTION_LIBS = @INTROSPECTION_LIBS@
INTROSPECTION_MAKEFILE = @INTROSPECTION_MAKEFILE@
INTROSPECTION_SCANNER = @INTROSPECTION_SCANNER@
INTROSPECTION_TYPELIBDIR = @INTROSPECTION_TYPELIBDIR@
LCOV = @LCOV@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBICONV = @LIBICONV@
LIBINTL = @LIBINTL@
LIBMM_GLIB_CFLAGS = @LIBMM_GLIB_CFLAGS@
LIBMM_GLIB_LIBS = @LIBMM_GLIB_LIBS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBSYSTEMD_CFLAGS = @LIBSYSTEMD_CFLAGS@
LIBSYSTEMD_LIBS = @LIBSYSTEMD_LIBS@
LIBSYSTEMD_LOGIN_CFLAGS = @LIBSYSTEMD_LOGIN_CFLAGS@
LIBSYSTEMD_LOGIN_LIBS = @LIBSYSTEMD_LOGIN_LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBICONV = @LTLIBICONV@
LTLIBINTL = @LTLIBINTL@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAINT = @MAINT@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MBIM_CFLAGS = @MBIM_CFLAGS@
MBIM_LIBS = @MBIM_LIBS@
MKDIR_P = @MKDIR_P@
MMCLI_CFLAGS = @MMCLI_CFLAGS@
MMCLI_LIBS = @MMCLI_LIBS@
MM_CFLAGS = @MM_CFLAGS@
MM_DEFAULT_USER_POLICY = @MM_DEFAULT_USER_POLICY@
MM_GLIB_LT_AGE = @MM_GLIB_LT_AGE@
MM_GLIB_LT_CURRENT = @MM_GLIB_LT_CURRENT@
MM_GLIB_LT_REVISION = @MM_GLIB_LT_REVISION@
MM_LIBS = @MM_LIBS@
MM_MAJOR_VERSION = @MM_MAJOR_VERSION@
MM_MICRO_VERSION = @MM_MICRO_VERSION@
MM_MINOR_VERSION = @MM_MINOR_VERSION@
MM_POLKIT_SERVICE = @MM_POLKIT_SERVICE@
MM_VERSION = @MM_VERSION@
MSGFMT = @MSGFMT@
MSGFMT_015 = @MSGFMT_015@
MSGMERGE = @MSGMERGE@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PKG_CONFIG = @PKG_CONFIG@
PKG_CONFIG_LIBDIR = @PKG_CONFIG_LIBDIR@
PKG_CONFIG_PATH = @PKG_CONFIG_PATH@
POLKIT_CFLAGS = @POLKIT_CFLAGS@
POLKIT_LIBS = @POLKIT_LIBS@
POSUB = @POSUB@
QMI_CFLAGS = @QMI_CFLAGS@
QMI_LIBS = @QMI_LIBS@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
SYSTEMD_UNIT_DIR = @SYSTEMD_UNIT_DIR@
UDEV_BASE_DIR = @UDEV_BASE_DIR@
USE_NLS = @USE_NLS@
VAPIGEN = @VAPIGEN@
VAPIGEN_MAKEFILE = @VAPIGEN_MAKEFILE@
VAPIGEN_VAPIDIR = @VAPIGEN_VAPIDIR@
VERSION = @VERSION@
WARN_CFLAGS = @WARN_CFLAGS@
WARN_LDFLAGS = @WARN_LDFLAGS@
WARN_SCANNERFLAGS = @WARN_SCANNERFLAGS@
XGETTEXT = @XGETTEXT@
XGETTEXT_015 = @XGETTEXT_015@
XGETTEXT_EXTRA_OPTIONS = @XGETTEXT_EXTRA_OPTIONS@
XSLTPROC_CHECK = @XSLTPROC_CHECK@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
mmcli_CPPFLAGS = $(WARN_CFLAGS) $(MMCLI_CFLAGS) -I$(top_srcdir) \
	-I$(top_srcdir)/include -I$(top_builddir)/include \
	-I$(top_srcdir)/libmm-glib \
	-I${top_srcdir}/libmm-glib/generated \
	-I${top_builddir}/libmm-glib/generated $(NULL) $(am__append_1)
mmcli_SOURCES = \
	mmcli.h \
	mmcli.c \
	mmcli-common.h mmcli-common.c \
	mmcli-output.h mmcli-output.c \
	mmcli-manager.c \
	mmcli-modem.c \
	mmcli-modem-3gpp.c \
	mmcli-modem-cdma.c \
	mmcli-modem-simple.c \
	mmcli-modem-location.c \
	mmcli-modem-messaging.c \
	mmcli-modem-voice.c \
	mmcli-modem-time.c \
	mmcli-modem-firmware.c \
	mmcli-modem-signal.c \
	mmcli-modem-oma.c \
	mmcli-bearer.c \
	mmcli-sim.c \
	mmcli-sms.c \
	mmcli-call.c \
	$(NULL)

mmcli_LDADD = \
	$(top_builddir)/libmm-glib/libmm-glib.la \
	$(NULL)

mmcli_LDFLAGS = $(WARN_LDFLAGS) $(MMCLI_LIBS) $(NULL) $(am__append_2)
completiondir = $(datadir)/bash-completion/completions
EXTRA_DIST = mmcli-completion
all: all-am

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in: @MAINTAINER_MODE_TRUE@ $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu cli/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu cli/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure: @MAINTAINER_MODE_TRUE@ $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4): @MAINTAINER_MODE_TRUE@ $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(bindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(bindir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	 || test -f $$p1 \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	    echo " $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	    $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(bindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(bindir)" && rm -f $$files

clean-binPROGRAMS:
	@list='$(bin_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

mmcli$(EXEEXT): $(mmcli_OBJECTS) $(mmcli_DEPENDENCIES) $(EXTRA_mmcli_DEPENDENCIES) 
	@rm -f mmcli$(EXEEXT)
	$(AM_V_CCLD)$(mmcli_LINK) $(mmcli_OBJECTS) $(mmcli_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-bearer.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-call.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-common.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-manager.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-modem-3gpp.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-modem-cdma.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-modem-firmware.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-modem-location.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-modem-messaging.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-modem-oma.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-modem-signal.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-modem-simple.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-modem-time.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-modem-voice.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-modem.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-output.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-sim.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli-sms.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mmcli-mmcli.Po@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

mmcli-mmcli.o: mmcli.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli.Tpo -c -o mmcli-mmcli.o `test -f 'mmcli.c' || echo '$(srcdir)/'`mmcli.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli.Tpo $(DEPDIR)/mmcli-mmcli.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli.c' object='mmcli-mmcli.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli.o `test -f 'mmcli.c' || echo '$(srcdir)/'`mmcli.c

mmcli-mmcli.obj: mmcli.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli.Tpo -c -o mmcli-mmcli.obj `if test -f 'mmcli.c'; then $(CYGPATH_W) 'mmcli.c'; else $(CYGPATH_W) '$(srcdir)/mmcli.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli.Tpo $(DEPDIR)/mmcli-mmcli.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli.c' object='mmcli-mmcli.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli.obj `if test -f 'mmcli.c'; then $(CYGPATH_W) 'mmcli.c'; else $(CYGPATH_W) '$(srcdir)/mmcli.c'; fi`

mmcli-mmcli-common.o: mmcli-common.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-common.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-common.Tpo -c -o mmcli-mmcli-common.o `test -f 'mmcli-common.c' || echo '$(srcdir)/'`mmcli-common.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-common.Tpo $(DEPDIR)/mmcli-mmcli-common.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-common.c' object='mmcli-mmcli-common.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-common.o `test -f 'mmcli-common.c' || echo '$(srcdir)/'`mmcli-common.c

mmcli-mmcli-common.obj: mmcli-common.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-common.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-common.Tpo -c -o mmcli-mmcli-common.obj `if test -f 'mmcli-common.c'; then $(CYGPATH_W) 'mmcli-common.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-common.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-common.Tpo $(DEPDIR)/mmcli-mmcli-common.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-common.c' object='mmcli-mmcli-common.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-common.obj `if test -f 'mmcli-common.c'; then $(CYGPATH_W) 'mmcli-common.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-common.c'; fi`

mmcli-mmcli-output.o: mmcli-output.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-output.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-output.Tpo -c -o mmcli-mmcli-output.o `test -f 'mmcli-output.c' || echo '$(srcdir)/'`mmcli-output.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-output.Tpo $(DEPDIR)/mmcli-mmcli-output.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-output.c' object='mmcli-mmcli-output.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-output.o `test -f 'mmcli-output.c' || echo '$(srcdir)/'`mmcli-output.c

mmcli-mmcli-output.obj: mmcli-output.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-output.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-output.Tpo -c -o mmcli-mmcli-output.obj `if test -f 'mmcli-output.c'; then $(CYGPATH_W) 'mmcli-output.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-output.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-output.Tpo $(DEPDIR)/mmcli-mmcli-output.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-output.c' object='mmcli-mmcli-output.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-output.obj `if test -f 'mmcli-output.c'; then $(CYGPATH_W) 'mmcli-output.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-output.c'; fi`

mmcli-mmcli-manager.o: mmcli-manager.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-manager.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-manager.Tpo -c -o mmcli-mmcli-manager.o `test -f 'mmcli-manager.c' || echo '$(srcdir)/'`mmcli-manager.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-manager.Tpo $(DEPDIR)/mmcli-mmcli-manager.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-manager.c' object='mmcli-mmcli-manager.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-manager.o `test -f 'mmcli-manager.c' || echo '$(srcdir)/'`mmcli-manager.c

mmcli-mmcli-manager.obj: mmcli-manager.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-manager.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-manager.Tpo -c -o mmcli-mmcli-manager.obj `if test -f 'mmcli-manager.c'; then $(CYGPATH_W) 'mmcli-manager.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-manager.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-manager.Tpo $(DEPDIR)/mmcli-mmcli-manager.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-manager.c' object='mmcli-mmcli-manager.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-manager.obj `if test -f 'mmcli-manager.c'; then $(CYGPATH_W) 'mmcli-manager.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-manager.c'; fi`

mmcli-mmcli-modem.o: mmcli-modem.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem.Tpo -c -o mmcli-mmcli-modem.o `test -f 'mmcli-modem.c' || echo '$(srcdir)/'`mmcli-modem.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem.Tpo $(DEPDIR)/mmcli-mmcli-modem.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem.c' object='mmcli-mmcli-modem.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem.o `test -f 'mmcli-modem.c' || echo '$(srcdir)/'`mmcli-modem.c

mmcli-mmcli-modem.obj: mmcli-modem.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem.Tpo -c -o mmcli-mmcli-modem.obj `if test -f 'mmcli-modem.c'; then $(CYGPATH_W) 'mmcli-modem.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem.Tpo $(DEPDIR)/mmcli-mmcli-modem.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem.c' object='mmcli-mmcli-modem.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem.obj `if test -f 'mmcli-modem.c'; then $(CYGPATH_W) 'mmcli-modem.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem.c'; fi`

mmcli-mmcli-modem-3gpp.o: mmcli-modem-3gpp.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-3gpp.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-3gpp.Tpo -c -o mmcli-mmcli-modem-3gpp.o `test -f 'mmcli-modem-3gpp.c' || echo '$(srcdir)/'`mmcli-modem-3gpp.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-3gpp.Tpo $(DEPDIR)/mmcli-mmcli-modem-3gpp.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-3gpp.c' object='mmcli-mmcli-modem-3gpp.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-3gpp.o `test -f 'mmcli-modem-3gpp.c' || echo '$(srcdir)/'`mmcli-modem-3gpp.c

mmcli-mmcli-modem-3gpp.obj: mmcli-modem-3gpp.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-3gpp.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-3gpp.Tpo -c -o mmcli-mmcli-modem-3gpp.obj `if test -f 'mmcli-modem-3gpp.c'; then $(CYGPATH_W) 'mmcli-modem-3gpp.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-3gpp.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-3gpp.Tpo $(DEPDIR)/mmcli-mmcli-modem-3gpp.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-3gpp.c' object='mmcli-mmcli-modem-3gpp.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-3gpp.obj `if test -f 'mmcli-modem-3gpp.c'; then $(CYGPATH_W) 'mmcli-modem-3gpp.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-3gpp.c'; fi`

mmcli-mmcli-modem-cdma.o: mmcli-modem-cdma.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-cdma.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-cdma.Tpo -c -o mmcli-mmcli-modem-cdma.o `test -f 'mmcli-modem-cdma.c' || echo '$(srcdir)/'`mmcli-modem-cdma.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-cdma.Tpo $(DEPDIR)/mmcli-mmcli-modem-cdma.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-cdma.c' object='mmcli-mmcli-modem-cdma.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-cdma.o `test -f 'mmcli-modem-cdma.c' || echo '$(srcdir)/'`mmcli-modem-cdma.c

mmcli-mmcli-modem-cdma.obj: mmcli-modem-cdma.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-cdma.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-cdma.Tpo -c -o mmcli-mmcli-modem-cdma.obj `if test -f 'mmcli-modem-cdma.c'; then $(CYGPATH_W) 'mmcli-modem-cdma.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-cdma.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-cdma.Tpo $(DEPDIR)/mmcli-mmcli-modem-cdma.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-cdma.c' object='mmcli-mmcli-modem-cdma.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-cdma.obj `if test -f 'mmcli-modem-cdma.c'; then $(CYGPATH_W) 'mmcli-modem-cdma.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-cdma.c'; fi`

mmcli-mmcli-modem-simple.o: mmcli-modem-simple.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-simple.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-simple.Tpo -c -o mmcli-mmcli-modem-simple.o `test -f 'mmcli-modem-simple.c' || echo '$(srcdir)/'`mmcli-modem-simple.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-simple.Tpo $(DEPDIR)/mmcli-mmcli-modem-simple.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-simple.c' object='mmcli-mmcli-modem-simple.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-simple.o `test -f 'mmcli-modem-simple.c' || echo '$(srcdir)/'`mmcli-modem-simple.c

mmcli-mmcli-modem-simple.obj: mmcli-modem-simple.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-simple.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-simple.Tpo -c -o mmcli-mmcli-modem-simple.obj `if test -f 'mmcli-modem-simple.c'; then $(CYGPATH_W) 'mmcli-modem-simple.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-simple.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-simple.Tpo $(DEPDIR)/mmcli-mmcli-modem-simple.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-simple.c' object='mmcli-mmcli-modem-simple.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-simple.obj `if test -f 'mmcli-modem-simple.c'; then $(CYGPATH_W) 'mmcli-modem-simple.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-simple.c'; fi`

mmcli-mmcli-modem-location.o: mmcli-modem-location.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-location.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-location.Tpo -c -o mmcli-mmcli-modem-location.o `test -f 'mmcli-modem-location.c' || echo '$(srcdir)/'`mmcli-modem-location.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-location.Tpo $(DEPDIR)/mmcli-mmcli-modem-location.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-location.c' object='mmcli-mmcli-modem-location.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-location.o `test -f 'mmcli-modem-location.c' || echo '$(srcdir)/'`mmcli-modem-location.c

mmcli-mmcli-modem-location.obj: mmcli-modem-location.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-location.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-location.Tpo -c -o mmcli-mmcli-modem-location.obj `if test -f 'mmcli-modem-location.c'; then $(CYGPATH_W) 'mmcli-modem-location.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-location.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-location.Tpo $(DEPDIR)/mmcli-mmcli-modem-location.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-location.c' object='mmcli-mmcli-modem-location.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-location.obj `if test -f 'mmcli-modem-location.c'; then $(CYGPATH_W) 'mmcli-modem-location.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-location.c'; fi`

mmcli-mmcli-modem-messaging.o: mmcli-modem-messaging.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-messaging.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-messaging.Tpo -c -o mmcli-mmcli-modem-messaging.o `test -f 'mmcli-modem-messaging.c' || echo '$(srcdir)/'`mmcli-modem-messaging.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-messaging.Tpo $(DEPDIR)/mmcli-mmcli-modem-messaging.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-messaging.c' object='mmcli-mmcli-modem-messaging.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-messaging.o `test -f 'mmcli-modem-messaging.c' || echo '$(srcdir)/'`mmcli-modem-messaging.c

mmcli-mmcli-modem-messaging.obj: mmcli-modem-messaging.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-messaging.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-messaging.Tpo -c -o mmcli-mmcli-modem-messaging.obj `if test -f 'mmcli-modem-messaging.c'; then $(CYGPATH_W) 'mmcli-modem-messaging.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-messaging.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-messaging.Tpo $(DEPDIR)/mmcli-mmcli-modem-messaging.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-messaging.c' object='mmcli-mmcli-modem-messaging.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-messaging.obj `if test -f 'mmcli-modem-messaging.c'; then $(CYGPATH_W) 'mmcli-modem-messaging.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-messaging.c'; fi`

mmcli-mmcli-modem-voice.o: mmcli-modem-voice.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-voice.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-voice.Tpo -c -o mmcli-mmcli-modem-voice.o `test -f 'mmcli-modem-voice.c' || echo '$(srcdir)/'`mmcli-modem-voice.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-voice.Tpo $(DEPDIR)/mmcli-mmcli-modem-voice.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-voice.c' object='mmcli-mmcli-modem-voice.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-voice.o `test -f 'mmcli-modem-voice.c' || echo '$(srcdir)/'`mmcli-modem-voice.c

mmcli-mmcli-modem-voice.obj: mmcli-modem-voice.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-voice.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-voice.Tpo -c -o mmcli-mmcli-modem-voice.obj `if test -f 'mmcli-modem-voice.c'; then $(CYGPATH_W) 'mmcli-modem-voice.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-voice.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-voice.Tpo $(DEPDIR)/mmcli-mmcli-modem-voice.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-voice.c' object='mmcli-mmcli-modem-voice.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-voice.obj `if test -f 'mmcli-modem-voice.c'; then $(CYGPATH_W) 'mmcli-modem-voice.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-voice.c'; fi`

mmcli-mmcli-modem-time.o: mmcli-modem-time.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-time.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-time.Tpo -c -o mmcli-mmcli-modem-time.o `test -f 'mmcli-modem-time.c' || echo '$(srcdir)/'`mmcli-modem-time.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-time.Tpo $(DEPDIR)/mmcli-mmcli-modem-time.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-time.c' object='mmcli-mmcli-modem-time.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-time.o `test -f 'mmcli-modem-time.c' || echo '$(srcdir)/'`mmcli-modem-time.c

mmcli-mmcli-modem-time.obj: mmcli-modem-time.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-time.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-time.Tpo -c -o mmcli-mmcli-modem-time.obj `if test -f 'mmcli-modem-time.c'; then $(CYGPATH_W) 'mmcli-modem-time.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-time.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-time.Tpo $(DEPDIR)/mmcli-mmcli-modem-time.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-time.c' object='mmcli-mmcli-modem-time.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-time.obj `if test -f 'mmcli-modem-time.c'; then $(CYGPATH_W) 'mmcli-modem-time.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-time.c'; fi`

mmcli-mmcli-modem-firmware.o: mmcli-modem-firmware.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-firmware.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-firmware.Tpo -c -o mmcli-mmcli-modem-firmware.o `test -f 'mmcli-modem-firmware.c' || echo '$(srcdir)/'`mmcli-modem-firmware.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-firmware.Tpo $(DEPDIR)/mmcli-mmcli-modem-firmware.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-firmware.c' object='mmcli-mmcli-modem-firmware.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-firmware.o `test -f 'mmcli-modem-firmware.c' || echo '$(srcdir)/'`mmcli-modem-firmware.c

mmcli-mmcli-modem-firmware.obj: mmcli-modem-firmware.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-firmware.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-firmware.Tpo -c -o mmcli-mmcli-modem-firmware.obj `if test -f 'mmcli-modem-firmware.c'; then $(CYGPATH_W) 'mmcli-modem-firmware.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-firmware.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-firmware.Tpo $(DEPDIR)/mmcli-mmcli-modem-firmware.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-firmware.c' object='mmcli-mmcli-modem-firmware.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-firmware.obj `if test -f 'mmcli-modem-firmware.c'; then $(CYGPATH_W) 'mmcli-modem-firmware.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-firmware.c'; fi`

mmcli-mmcli-modem-signal.o: mmcli-modem-signal.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-signal.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-signal.Tpo -c -o mmcli-mmcli-modem-signal.o `test -f 'mmcli-modem-signal.c' || echo '$(srcdir)/'`mmcli-modem-signal.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-signal.Tpo $(DEPDIR)/mmcli-mmcli-modem-signal.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-signal.c' object='mmcli-mmcli-modem-signal.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-signal.o `test -f 'mmcli-modem-signal.c' || echo '$(srcdir)/'`mmcli-modem-signal.c

mmcli-mmcli-modem-signal.obj: mmcli-modem-signal.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-signal.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-signal.Tpo -c -o mmcli-mmcli-modem-signal.obj `if test -f 'mmcli-modem-signal.c'; then $(CYGPATH_W) 'mmcli-modem-signal.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-signal.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-signal.Tpo $(DEPDIR)/mmcli-mmcli-modem-signal.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-signal.c' object='mmcli-mmcli-modem-signal.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-signal.obj `if test -f 'mmcli-modem-signal.c'; then $(CYGPATH_W) 'mmcli-modem-signal.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-signal.c'; fi`

mmcli-mmcli-modem-oma.o: mmcli-modem-oma.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-oma.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-oma.Tpo -c -o mmcli-mmcli-modem-oma.o `test -f 'mmcli-modem-oma.c' || echo '$(srcdir)/'`mmcli-modem-oma.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-oma.Tpo $(DEPDIR)/mmcli-mmcli-modem-oma.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-oma.c' object='mmcli-mmcli-modem-oma.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-oma.o `test -f 'mmcli-modem-oma.c' || echo '$(srcdir)/'`mmcli-modem-oma.c

mmcli-mmcli-modem-oma.obj: mmcli-modem-oma.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-modem-oma.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-modem-oma.Tpo -c -o mmcli-mmcli-modem-oma.obj `if test -f 'mmcli-modem-oma.c'; then $(CYGPATH_W) 'mmcli-modem-oma.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-oma.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-modem-oma.Tpo $(DEPDIR)/mmcli-mmcli-modem-oma.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-modem-oma.c' object='mmcli-mmcli-modem-oma.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-modem-oma.obj `if test -f 'mmcli-modem-oma.c'; then $(CYGPATH_W) 'mmcli-modem-oma.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-modem-oma.c'; fi`

mmcli-mmcli-bearer.o: mmcli-bearer.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-bearer.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-bearer.Tpo -c -o mmcli-mmcli-bearer.o `test -f 'mmcli-bearer.c' || echo '$(srcdir)/'`mmcli-bearer.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-bearer.Tpo $(DEPDIR)/mmcli-mmcli-bearer.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-bearer.c' object='mmcli-mmcli-bearer.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-bearer.o `test -f 'mmcli-bearer.c' || echo '$(srcdir)/'`mmcli-bearer.c

mmcli-mmcli-bearer.obj: mmcli-bearer.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-bearer.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-bearer.Tpo -c -o mmcli-mmcli-bearer.obj `if test -f 'mmcli-bearer.c'; then $(CYGPATH_W) 'mmcli-bearer.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-bearer.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-bearer.Tpo $(DEPDIR)/mmcli-mmcli-bearer.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-bearer.c' object='mmcli-mmcli-bearer.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-bearer.obj `if test -f 'mmcli-bearer.c'; then $(CYGPATH_W) 'mmcli-bearer.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-bearer.c'; fi`

mmcli-mmcli-sim.o: mmcli-sim.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-sim.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-sim.Tpo -c -o mmcli-mmcli-sim.o `test -f 'mmcli-sim.c' || echo '$(srcdir)/'`mmcli-sim.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-sim.Tpo $(DEPDIR)/mmcli-mmcli-sim.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-sim.c' object='mmcli-mmcli-sim.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-sim.o `test -f 'mmcli-sim.c' || echo '$(srcdir)/'`mmcli-sim.c

mmcli-mmcli-sim.obj: mmcli-sim.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-sim.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-sim.Tpo -c -o mmcli-mmcli-sim.obj `if test -f 'mmcli-sim.c'; then $(CYGPATH_W) 'mmcli-sim.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-sim.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-sim.Tpo $(DEPDIR)/mmcli-mmcli-sim.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-sim.c' object='mmcli-mmcli-sim.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-sim.obj `if test -f 'mmcli-sim.c'; then $(CYGPATH_W) 'mmcli-sim.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-sim.c'; fi`

mmcli-mmcli-sms.o: mmcli-sms.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-sms.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-sms.Tpo -c -o mmcli-mmcli-sms.o `test -f 'mmcli-sms.c' || echo '$(srcdir)/'`mmcli-sms.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-sms.Tpo $(DEPDIR)/mmcli-mmcli-sms.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-sms.c' object='mmcli-mmcli-sms.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-sms.o `test -f 'mmcli-sms.c' || echo '$(srcdir)/'`mmcli-sms.c

mmcli-mmcli-sms.obj: mmcli-sms.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-sms.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-sms.Tpo -c -o mmcli-mmcli-sms.obj `if test -f 'mmcli-sms.c'; then $(CYGPATH_W) 'mmcli-sms.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-sms.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-sms.Tpo $(DEPDIR)/mmcli-mmcli-sms.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-sms.c' object='mmcli-mmcli-sms.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-sms.obj `if test -f 'mmcli-sms.c'; then $(CYGPATH_W) 'mmcli-sms.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-sms.c'; fi`

mmcli-mmcli-call.o: mmcli-call.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-call.o -MD -MP -MF $(DEPDIR)/mmcli-mmcli-call.Tpo -c -o mmcli-mmcli-call.o `test -f 'mmcli-call.c' || echo '$(srcdir)/'`mmcli-call.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-call.Tpo $(DEPDIR)/mmcli-mmcli-call.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-call.c' object='mmcli-mmcli-call.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-call.o `test -f 'mmcli-call.c' || echo '$(srcdir)/'`mmcli-call.c

mmcli-mmcli-call.obj: mmcli-call.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmcli-mmcli-call.obj -MD -MP -MF $(DEPDIR)/mmcli-mmcli-call.Tpo -c -o mmcli-mmcli-call.obj `if test -f 'mmcli-call.c'; then $(CYGPATH_W) 'mmcli-call.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-call.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mmcli-mmcli-call.Tpo $(DEPDIR)/mmcli-mmcli-call.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='mmcli-call.c' object='mmcli-mmcli-call.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmcli_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmcli-mmcli-call.obj `if test -f 'mmcli-call.c'; then $(CYGPATH_W) 'mmcli-call.c'; else $(CYGPATH_W) '$(srcdir)/mmcli-call.c'; fi`

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(PROGRAMS)
installdirs:
	for dir in "$(DESTDIR)$(bindir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-binPROGRAMS clean-generic clean-libtool mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/mmcli-mmcli-bearer.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-call.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-common.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-manager.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-3gpp.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-cdma.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-firmware.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-location.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-messaging.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-oma.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-signal.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-simple.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-time.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-voice.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-output.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-sim.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-sms.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:
	@$(NORMAL_INSTALL)
	$(MAKE) $(AM_MAKEFLAGS) install-data-hook
install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-binPROGRAMS

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/mmcli-mmcli-bearer.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-call.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-common.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-manager.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-3gpp.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-cdma.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-firmware.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-location.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-messaging.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-oma.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-signal.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-simple.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-time.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem-voice.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-modem.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-output.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-sim.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli-sms.Po
	-rm -f ./$(DEPDIR)/mmcli-mmcli.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-binPROGRAMS
	@$(NORMAL_INSTALL)
	$(MAKE) $(AM_MAKEFLAGS) uninstall-hook
.MAKE: install-am install-data-am install-strip uninstall-am

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-binPROGRAMS clean-generic clean-libtool cscopelist-am \
	ctags ctags-am distclean distclean-compile distclean-generic \
	distclean-libtool distclean-tags distdir dvi dvi-am html \
	html-am info info-am install install-am install-binPROGRAMS \
	install-data install-data-am install-data-hook install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am install-man \
	install-pdf install-pdf-am install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	maintainer-clean maintainer-clean-generic mostlyclean \
	mostlyclean-compile mostlyclean-generic mostlyclean-libtool \
	pdf pdf-am ps ps-am tags tags-am uninstall uninstall-am \
	uninstall-binPROGRAMS uninstall-hook

.PRECIOUS: Makefile


install-data-hook:
	$(mkinstalldirs) $(DESTDIR)$(completiondir)
	$(INSTALL_DATA) $(srcdir)/mmcli-completion $(DESTDIR)$(completiondir)/mmcli

uninstall-hook:
	rm -f $(DESTDIR)$(completiondir)/mmcli

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

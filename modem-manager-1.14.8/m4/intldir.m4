# intldir.m4 serial 2 (gettext-0.18)
dnl Copyright (C) 2006, 2009-2014, 2016 Free Software Foundation, Inc.
dnl This file is free software; the Free Software Foundation
dnl gives unlimited permission to copy and/or distribute it,
dnl with or without modifications, as long as this notice is preserved.
dnl
dnl This file can be used in projects which are not available under
dnl the GNU General Public License or the GNU Library General Public
dnl License but which still want to provide support for the GNU gettext
dnl functionality.
dnl Please note that the actual code of the GNU gettext library is covered
dnl by the GNU Library General Public License, and the rest of the GNU
dnl gettext package is covered by the GNU General Public License.
dnl They are *not* in the public domain.

AC_PREREQ([2.52])

dnl Tells the AM_GNU_GETTEXT macro to consider an intl/ directory.
AC_DEFUN([AM_GNU_GETTEXT_INTL_SUBDIR], [])

# uintmax_t.m4 serial 12
dnl Copyright (C) 1997-2004, 2007-2016 Free Software Foundation, Inc.
dnl This file is free software; the Free Software Foundation
dnl gives unlimited permission to copy and/or distribute it,
dnl with or without modifications, as long as this notice is preserved.

dnl From <PERSON>.

AC_PREREQ([2.13])

# Define uintmax_t to 'unsigned long' or 'unsigned long long'
# if it is not already defined in <stdint.h> or <inttypes.h>.

AC_DEFUN([gl_AC_TYPE_UINTMAX_T],
[
  AC_REQUIRE([gl_AC_HEADER_INTTYPES_H])
  AC_REQUIRE([gl_AC_HEADER_STDINT_H])
  if test $gl_cv_header_inttypes_h = no && test $gl_cv_header_stdint_h = no; then
    AC_REQUIRE([AC_TYPE_UNSIGNED_LONG_LONG_INT])
    test $ac_cv_type_unsigned_long_long_int = yes \
      && ac_type='unsigned long long' \
      || ac_type='unsigned long'
    AC_DEFINE_UNQUOTED([uintmax_t], [$ac_type],
      [Define to unsigned long or unsigned long long
       if <stdint.h> and <inttypes.h> don't define.])
  else
    AC_DEFINE([HAVE_UINTMAX_T], [1],
      [Define if you have the 'uintmax_t' type in <stdint.h> or <inttypes.h>.])
  fi
])

<SECTION>
<FILE>libmm-glib</FILE>
</SECTION>

<SECTION>
<FILE>mm-manager</FILE>
<TITLE>MMManager</TITLE>
MMManager
<SUBSECTION Getters>
mm_manager_peek_proxy
mm_manager_get_proxy
<SUBSECTION New>
mm_manager_new
mm_manager_new_finish
mm_manager_new_sync
<SUBSECTION Methods>
mm_manager_get_version
mm_manager_scan_devices
mm_manager_scan_devices_finish
mm_manager_scan_devices_sync
mm_manager_inhibit_device
mm_manager_inhibit_device_finish
mm_manager_inhibit_device_sync
mm_manager_uninhibit_device
mm_manager_uninhibit_device_finish
mm_manager_uninhibit_device_sync
mm_manager_set_logging
mm_manager_set_logging_finish
mm_manager_set_logging_sync
mm_manager_report_kernel_event
mm_manager_report_kernel_event_finish
mm_manager_report_kernel_event_sync
<SUBSECTION Standard>
MMManagerClass
MMManagerPrivate
MM_IS_MANAGER
MM_IS_MANAGER_CLASS
MM_MANAGER
MM_MANAGER_CLASS
MM_MANAGER_GET_CLASS
MM_TYPE_MANAGER
mm_manager_get_type
</SECTION>

<SECTION>
<FILE>mm-kernel-event-properties</FILE>
<TITLE>MMKernelEventProperties</TITLE>
MMKernelEventProperties
<SUBSECTION Methods>
mm_kernel_event_properties_new
mm_kernel_event_properties_get_action
mm_kernel_event_properties_set_action
mm_kernel_event_properties_get_name
mm_kernel_event_properties_set_name
mm_kernel_event_properties_get_subsystem
mm_kernel_event_properties_set_subsystem
mm_kernel_event_properties_get_uid
mm_kernel_event_properties_set_uid
<SUBSECTION Private>
mm_kernel_event_properties_new_from_string
mm_kernel_event_properties_new_from_dictionary
mm_kernel_event_properties_dup
mm_kernel_event_properties_get_dictionary
<SUBSECTION Standard>
MMKernelEventPropertiesClass
MMKernelEventPropertiesPrivate
MM_KERNEL_EVENT_PROPERTIES
MM_KERNEL_EVENT_PROPERTIES_CLASS
MM_KERNEL_EVENT_PROPERTIES_GET_CLASS
MM_IS_KERNEL_EVENT_PROPERTIES
MM_IS_KERNEL_EVENT_PROPERTIES_CLASS
MM_TYPE_KERNEL_EVENT_PROPERTIES
mm_kernel_event_properties_get_type
</SECTION>

<SECTION>
<FILE>mm-object</FILE>
<TITLE>MMObject</TITLE>
MMObject
<SUBSECTION Getters>
mm_object_get_path
mm_object_dup_path
<SUBSECTION Methods>
mm_object_peek_modem
mm_object_get_modem
mm_object_peek_modem_3gpp
mm_object_get_modem_3gpp
mm_object_peek_modem_3gpp_ussd
mm_object_get_modem_3gpp_ussd
mm_object_peek_modem_cdma
mm_object_get_modem_cdma
mm_object_peek_modem_location
mm_object_get_modem_location
mm_object_peek_modem_messaging
mm_object_get_modem_messaging
mm_object_peek_modem_time
mm_object_get_modem_time
mm_object_peek_modem_firmware
mm_object_get_modem_firmware
mm_object_peek_modem_oma
mm_object_get_modem_oma
mm_object_peek_modem_simple
mm_object_get_modem_simple
mm_object_peek_modem_signal
mm_object_get_modem_signal
mm_object_peek_modem_voice
mm_object_get_modem_voice
<SUBSECTION Standard>
MMObjectClass
MM_IS_OBJECT
MM_IS_OBJECT_CLASS
MM_OBJECT
MM_OBJECT_CLASS
MM_OBJECT_GET_CLASS
MM_TYPE_OBJECT
mm_object_get_type
</SECTION>

<SECTION>
<FILE>mm-modem</FILE>
<TITLE>MMModem</TITLE>
MMModem
MMModemModeCombination
MMModemPortInfo
<SUBSECTION Getters>
mm_modem_get_path
mm_modem_dup_path
mm_modem_get_state
mm_modem_get_state_failed_reason
mm_modem_get_power_state
mm_modem_peek_supported_capabilities
mm_modem_get_supported_capabilities
mm_modem_get_current_capabilities
mm_modem_get_manufacturer
mm_modem_dup_manufacturer
mm_modem_get_model
mm_modem_dup_model
mm_modem_get_revision
mm_modem_dup_revision
mm_modem_get_carrier_configuration
mm_modem_dup_carrier_configuration
mm_modem_get_carrier_configuration_revision
mm_modem_dup_carrier_configuration_revision
mm_modem_get_hardware_revision
mm_modem_dup_hardware_revision
mm_modem_get_drivers
mm_modem_dup_drivers
mm_modem_get_plugin
mm_modem_dup_plugin
mm_modem_get_primary_port
mm_modem_dup_primary_port
mm_modem_peek_ports
mm_modem_get_ports
mm_modem_get_device
mm_modem_dup_device
mm_modem_get_equipment_identifier
mm_modem_dup_equipment_identifier
mm_modem_get_device_identifier
mm_modem_dup_device_identifier
mm_modem_get_unlock_required
mm_modem_peek_unlock_retries
mm_modem_get_unlock_retries
mm_modem_get_max_bearers
mm_modem_get_max_active_bearers
mm_modem_get_bearer_paths
mm_modem_dup_bearer_paths
mm_modem_get_own_numbers
mm_modem_dup_own_numbers
mm_modem_peek_supported_modes
mm_modem_get_supported_modes
mm_modem_get_current_modes
mm_modem_peek_supported_bands
mm_modem_get_supported_bands
mm_modem_peek_current_bands
mm_modem_get_current_bands
mm_modem_get_supported_ip_families
mm_modem_get_signal_quality
mm_modem_get_access_technologies
<SUBSECTION Sim>
mm_modem_get_sim_path
mm_modem_dup_sim_path
mm_modem_get_sim
mm_modem_get_sim_finish
mm_modem_get_sim_sync
<SUBSECTION Methods>
mm_modem_enable
mm_modem_enable_finish
mm_modem_enable_sync
mm_modem_disable
mm_modem_disable_finish
mm_modem_disable_sync
mm_modem_set_power_state
mm_modem_set_power_state_finish
mm_modem_set_power_state_sync
mm_modem_set_current_modes
mm_modem_set_current_modes_finish
mm_modem_set_current_modes_sync
mm_modem_set_current_bands
mm_modem_set_current_bands_finish
mm_modem_set_current_bands_sync
mm_modem_set_current_capabilities
mm_modem_set_current_capabilities_finish
mm_modem_set_current_capabilities_sync
mm_modem_reset
mm_modem_reset_finish
mm_modem_reset_sync
mm_modem_factory_reset
mm_modem_factory_reset_finish
mm_modem_factory_reset_sync
mm_modem_list_bearers
mm_modem_list_bearers_finish
mm_modem_list_bearers_sync
mm_modem_create_bearer
mm_modem_create_bearer_finish
mm_modem_create_bearer_sync
mm_modem_delete_bearer
mm_modem_delete_bearer_finish
mm_modem_delete_bearer_sync
<SUBSECTION DebugMethods>
mm_modem_command
mm_modem_command_finish
mm_modem_command_sync
<SUBSECTION Other>
mm_modem_port_info_array_free
<SUBSECTION Standard>
MMModemClass
MMModemPrivate
MM_IS_MODEM
MM_IS_MODEM_CLASS
MM_MODEM
MM_MODEM_CLASS
MM_MODEM_GET_CLASS
MM_TYPE_MODEM
mm_modem_get_type
</SECTION>

<SECTION>
<FILE>mm-unlock-retries</FILE>
<TITLE>MMUnlockRetries</TITLE>
MMUnlockRetries
MM_UNLOCK_RETRIES_UNKNOWN
<SUBSECTION Getters>
mm_unlock_retries_get
MMUnlockRetriesForeachCb
mm_unlock_retries_foreach
<SUBSECTION Private>
mm_unlock_retries_build_string
mm_unlock_retries_cmp
mm_unlock_retries_new
mm_unlock_retries_new_from_dictionary
mm_unlock_retries_get_dictionary
mm_unlock_retries_set
mm_unlock_retries_unset
<SUBSECTION Standard>
MMUnlockRetriesClass
MMUnlockRetriesPrivate
MM_IS_UNLOCK_RETRIES
MM_IS_UNLOCK_RETRIES_CLASS
MM_TYPE_UNLOCK_RETRIES
MM_UNLOCK_RETRIES
MM_UNLOCK_RETRIES_CLASS
MM_UNLOCK_RETRIES_GET_CLASS
mm_unlock_retries_get_type
</SECTION>

<SECTION>
<FILE>mm-modem-3gpp</FILE>
<TITLE>MMModem3gpp</TITLE>
MMModem3gpp
MMModem3gppNetwork
<SUBSECTION Network>
mm_modem_3gpp_network_get_operator_code
mm_modem_3gpp_network_get_operator_short
mm_modem_3gpp_network_get_operator_long
mm_modem_3gpp_network_get_access_technology
mm_modem_3gpp_network_get_availability
mm_modem_3gpp_network_free
<SUBSECTION Getters>
mm_modem_3gpp_get_path
mm_modem_3gpp_dup_path
mm_modem_3gpp_get_imei
mm_modem_3gpp_dup_imei
mm_modem_3gpp_get_operator_code
mm_modem_3gpp_dup_operator_code
mm_modem_3gpp_get_operator_name
mm_modem_3gpp_dup_operator_name
mm_modem_3gpp_get_enabled_facility_locks
mm_modem_3gpp_get_registration_state
mm_modem_3gpp_get_subscription_state
mm_modem_3gpp_get_pco
mm_modem_3gpp_get_eps_ue_mode_operation
mm_modem_3gpp_get_initial_eps_bearer_path
mm_modem_3gpp_dup_initial_eps_bearer_path
mm_modem_3gpp_get_initial_eps_bearer
mm_modem_3gpp_get_initial_eps_bearer_finish
mm_modem_3gpp_get_initial_eps_bearer_sync
mm_modem_3gpp_get_initial_eps_bearer_settings
mm_modem_3gpp_peek_initial_eps_bearer_settings
<SUBSECTION Methods>
mm_modem_3gpp_register
mm_modem_3gpp_register_finish
mm_modem_3gpp_register_sync
mm_modem_3gpp_scan
mm_modem_3gpp_scan_finish
mm_modem_3gpp_scan_sync
mm_modem_3gpp_set_eps_ue_mode_operation
mm_modem_3gpp_set_eps_ue_mode_operation_finish
mm_modem_3gpp_set_eps_ue_mode_operation_sync
mm_modem_3gpp_set_initial_eps_bearer_settings
mm_modem_3gpp_set_initial_eps_bearer_settings_finish
mm_modem_3gpp_set_initial_eps_bearer_settings_sync
<SUBSECTION Standard>
MMModem3gppClass
MMModem3gppPrivate
MM_IS_MODEM_3GPP
MM_IS_MODEM_3GPP_CLASS
MM_MODEM_3GPP
MM_MODEM_3GPP_CLASS
MM_MODEM_3GPP_GET_CLASS
MM_TYPE_MODEM_3GPP
MM_TYPE_MODEM_3GPP_NETWORK
mm_modem_3gpp_get_type
mm_modem_3gpp_network_get_type
</SECTION>

<SECTION>
<FILE>mm-modem-3gpp-ussd</FILE>
<TITLE>MMModem3gppUssd</TITLE>
MMModem3gppUssd
<SUBSECTION Methods>
mm_modem_3gpp_ussd_get_path
mm_modem_3gpp_ussd_dup_path
mm_modem_3gpp_ussd_get_state
mm_modem_3gpp_ussd_get_network_request
mm_modem_3gpp_ussd_dup_network_request
mm_modem_3gpp_ussd_get_network_notification
mm_modem_3gpp_ussd_dup_network_notification
<SUBSECTION Methods>
mm_modem_3gpp_ussd_initiate
mm_modem_3gpp_ussd_initiate_finish
mm_modem_3gpp_ussd_initiate_sync
mm_modem_3gpp_ussd_respond
mm_modem_3gpp_ussd_respond_finish
mm_modem_3gpp_ussd_respond_sync
mm_modem_3gpp_ussd_cancel
mm_modem_3gpp_ussd_cancel_finish
mm_modem_3gpp_ussd_cancel_sync
<SUBSECTION Standard>
MMModem3gppUssdClass
MM_IS_MODEM_3GPP_USSD
MM_IS_MODEM_3GPP_USSD_CLASS
MM_MODEM_3GPP_USSD
MM_MODEM_3GPP_USSD_CLASS
MM_MODEM_3GPP_USSD_GET_CLASS
MM_TYPE_MODEM_3GPP_USSD
mm_modem_3gpp_ussd_get_type
</SECTION>

<SECTION>
<FILE>mm-cdma-manual-activation-properties</FILE>
<TITLE>MMCdmaManualActivationProperties</TITLE>
MMCdmaManualActivationProperties

<SUBSECTION Methods>
mm_cdma_manual_activation_properties_new
mm_cdma_manual_activation_properties_get_spc
mm_cdma_manual_activation_properties_set_spc
mm_cdma_manual_activation_properties_get_sid
mm_cdma_manual_activation_properties_set_sid
mm_cdma_manual_activation_properties_get_mdn
mm_cdma_manual_activation_properties_set_mdn
mm_cdma_manual_activation_properties_get_min
mm_cdma_manual_activation_properties_set_min
mm_cdma_manual_activation_properties_get_mn_ha_key
mm_cdma_manual_activation_properties_set_mn_ha_key
mm_cdma_manual_activation_properties_get_mn_aaa_key
mm_cdma_manual_activation_properties_set_mn_aaa_key
mm_cdma_manual_activation_properties_get_prl
mm_cdma_manual_activation_properties_peek_prl_bytearray
mm_cdma_manual_activation_properties_get_prl_bytearray
mm_cdma_manual_activation_properties_set_prl
mm_cdma_manual_activation_properties_set_prl_bytearray
<SUBSECTION Private>
mm_cdma_manual_activation_properties_new_from_string
mm_cdma_manual_activation_properties_new_from_dictionary
mm_cdma_manual_activation_properties_get_dictionary
<SUBSECTION Standard>
MMCdmaManualActivationPropertiesClass
MMCdmaManualActivationPropertiesPrivate
MM_CDMA_MANUAL_ACTIVATION_PROPERTIES
MM_CDMA_MANUAL_ACTIVATION_PROPERTIES_CLASS
MM_CDMA_MANUAL_ACTIVATION_PROPERTIES_GET_CLASS
MM_IS_CDMA_MANUAL_ACTIVATION_PROPERTIES
MM_IS_CDMA_MANUAL_ACTIVATION_PROPERTIES_CLASS
MM_TYPE_CDMA_MANUAL_ACTIVATION_PROPERTIES
mm_cdma_manual_activation_properties_get_type
</SECTION>

<SECTION>
<FILE>mm-modem-cdma</FILE>
<TITLE>MMModemCdma</TITLE>
MMModemCdma
MM_MODEM_CDMA_NID_UNKNOWN
MM_MODEM_CDMA_SID_UNKNOWN
<SUBSECTION Getters>
mm_modem_cdma_get_path
mm_modem_cdma_dup_path
mm_modem_cdma_get_esn
mm_modem_cdma_dup_esn
mm_modem_cdma_get_meid
mm_modem_cdma_dup_meid
mm_modem_cdma_get_nid
mm_modem_cdma_get_sid
mm_modem_cdma_get_cdma1x_registration_state
mm_modem_cdma_get_evdo_registration_state
mm_modem_cdma_get_activation_state
<SUBSECTION Methods>
mm_modem_cdma_activate
mm_modem_cdma_activate_finish
mm_modem_cdma_activate_sync
mm_modem_cdma_activate_manual
mm_modem_cdma_activate_manual_finish
mm_modem_cdma_activate_manual_sync
<SUBSECTION Standard>
MMModemCdmaClass
MM_IS_MODEM_CDMA
MM_IS_MODEM_CDMA_CLASS
MM_MODEM_CDMA
MM_MODEM_CDMA_CLASS
MM_MODEM_CDMA_GET_CLASS
MM_TYPE_MODEM_CDMA
mm_modem_cdma_get_type
</SECTION>

<SECTION>
<FILE>mm-modem-location</FILE>
<TITLE>MMModemLocation</TITLE>
MMModemLocation
MM_LOCATION_LONGITUDE_UNKNOWN
MM_LOCATION_LATITUDE_UNKNOWN
MM_LOCATION_ALTITUDE_UNKNOWN
<SUBSECTION Getters>
mm_modem_location_get_path
mm_modem_location_dup_path
mm_modem_location_get_capabilities
mm_modem_location_get_enabled
mm_modem_location_get_gps_refresh_rate
mm_modem_location_signals_location
mm_modem_location_dup_supl_server
mm_modem_location_get_supl_server
mm_modem_location_get_supported_assistance_data
mm_modem_location_dup_assistance_data_servers
mm_modem_location_get_assistance_data_servers
<SUBSECTION Methods>
mm_modem_location_setup
mm_modem_location_setup_finish
mm_modem_location_setup_sync
mm_modem_location_set_supl_server
mm_modem_location_set_supl_server_finish
mm_modem_location_set_supl_server_sync
mm_modem_location_inject_assistance_data
mm_modem_location_inject_assistance_data_finish
mm_modem_location_inject_assistance_data_sync
mm_modem_location_set_gps_refresh_rate
mm_modem_location_set_gps_refresh_rate_finish
mm_modem_location_set_gps_refresh_rate_sync
mm_modem_location_get_3gpp
mm_modem_location_get_3gpp_finish
mm_modem_location_get_3gpp_sync
mm_modem_location_get_gps_nmea
mm_modem_location_get_gps_nmea_finish
mm_modem_location_get_gps_nmea_sync
mm_modem_location_get_gps_raw
mm_modem_location_get_gps_raw_finish
mm_modem_location_get_gps_raw_sync
mm_modem_location_get_cdma_bs
mm_modem_location_get_cdma_bs_finish
mm_modem_location_get_cdma_bs_sync
mm_modem_location_get_full
mm_modem_location_get_full_finish
mm_modem_location_get_full_sync
<SUBSECTION Standard>
MMModemLocationClass
MM_IS_MODEM_LOCATION
MM_IS_MODEM_LOCATION_CLASS
MM_MODEM_LOCATION
MM_MODEM_LOCATION_CLASS
MM_MODEM_LOCATION_GET_CLASS
MM_TYPE_MODEM_LOCATION
mm_modem_location_get_type
</SECTION>

<SECTION>
<FILE>mm-location-3gpp</FILE>
<TITLE>MMLocation3gpp</TITLE>
MMLocation3gpp
<SUBSECTION Getters>
mm_location_3gpp_get_mobile_country_code
mm_location_3gpp_get_mobile_network_code
mm_location_3gpp_get_location_area_code
mm_location_3gpp_get_tracking_area_code
mm_location_3gpp_get_cell_id
<SUBSECTION Private>
mm_location_3gpp_get_string_variant
mm_location_3gpp_new
mm_location_3gpp_new_from_string_variant
mm_location_3gpp_set_cell_id
mm_location_3gpp_set_location_area_code
mm_location_3gpp_set_tracking_area_code
mm_location_3gpp_set_mobile_country_code
mm_location_3gpp_set_mobile_network_code
mm_location_3gpp_reset
<SUBSECTION Standard>
MMLocation3gppClass
MMLocation3gppPrivate
MM_IS_LOCATION_3GPP
MM_IS_LOCATION_3GPP_CLASS
MM_LOCATION_3GPP
MM_LOCATION_3GPP_CLASS
MM_LOCATION_3GPP_GET_CLASS
MM_TYPE_LOCATION_3GPP
mm_location_3gpp_get_type
</SECTION>

<SECTION>
<FILE>mm-location-gps-nmea</FILE>
<TITLE>MMLocationGpsNmea</TITLE>
MMLocationGpsNmea
<SUBSECTION Getters>
mm_location_gps_nmea_get_trace
mm_location_gps_nmea_get_traces
mm_location_gps_nmea_build_full
<SUBSECTION Private>
mm_location_gps_nmea_new
mm_location_gps_nmea_new_from_string_variant
mm_location_gps_nmea_add_trace
mm_location_gps_nmea_get_string_variant
<SUBSECTION Standard>
MMLocationGpsNmeaClass
MMLocationGpsNmeaPrivate
MM_IS_LOCATION_GPS_NMEA
MM_IS_LOCATION_GPS_NMEA_CLASS
MM_LOCATION_GPS_NMEA
MM_LOCATION_GPS_NMEA_CLASS
MM_LOCATION_GPS_NMEA_GET_CLASS
MM_TYPE_LOCATION_GPS_NMEA
mm_location_gps_nmea_get_type
</SECTION>

<SECTION>
<FILE>mm-location-gps-raw</FILE>
<TITLE>MMLocationGpsRaw</TITLE>
MMLocationGpsRaw
<SUBSECTION Getters>
mm_location_gps_raw_get_utc_time
mm_location_gps_raw_get_longitude
mm_location_gps_raw_get_latitude
mm_location_gps_raw_get_altitude
<SUBSECTION Private>
mm_location_gps_raw_new
mm_location_gps_raw_new_from_dictionary
mm_location_gps_raw_get_dictionary
mm_location_gps_raw_add_trace
<SUBSECTION Standard>
MMLocationGpsRawClass
MMLocationGpsRawPrivate
MM_IS_LOCATION_GPS_RAW
MM_IS_LOCATION_GPS_RAW_CLASS
MM_LOCATION_GPS_RAW
MM_LOCATION_GPS_RAW_CLASS
MM_LOCATION_GPS_RAW_GET_CLASS
MM_TYPE_LOCATION_GPS_RAW
mm_location_gps_raw_get_type
</SECTION>

<SECTION>
<FILE>mm-location-cdma-bs</FILE>
<TITLE>MMLocationCdmaBs</TITLE>
MMLocationCdmaBs
<SUBSECTION Getters>
mm_location_cdma_bs_get_latitude
mm_location_cdma_bs_get_longitude
<SUBSECTION Private>
mm_location_cdma_bs_get_dictionary
mm_location_cdma_bs_new
mm_location_cdma_bs_set
mm_location_cdma_bs_new_from_dictionary
<SUBSECTION Standard>
MMLocationCdmaBsClass
MMLocationCdmaBsPrivate
MM_IS_LOCATION_CDMA_BS
MM_IS_LOCATION_CDMA_BS_CLASS
MM_LOCATION_CDMA_BS
MM_LOCATION_CDMA_BS_CLASS
MM_LOCATION_CDMA_BS_GET_CLASS
MM_TYPE_LOCATION_CDMA_BS
mm_location_cdma_bs_get_type
</SECTION>

<SECTION>
<FILE>mm-modem-messaging</FILE>
<TITLE>MMModemMessaging</TITLE>
MMModemMessaging
<SUBSECTION Getters>
mm_modem_messaging_get_path
mm_modem_messaging_dup_path
mm_modem_messaging_peek_supported_storages
mm_modem_messaging_get_supported_storages
mm_modem_messaging_get_default_storage
<SUBSECTION Methods>
mm_modem_messaging_create
mm_modem_messaging_create_finish
mm_modem_messaging_create_sync
mm_modem_messaging_delete
mm_modem_messaging_delete_finish
mm_modem_messaging_delete_sync
mm_modem_messaging_list
mm_modem_messaging_list_finish
mm_modem_messaging_list_sync
<SUBSECTION Standard>
MMModemMessagingClass
MMModemMessagingPrivate
MM_IS_MODEM_MESSAGING
MM_IS_MODEM_MESSAGING_CLASS
MM_MODEM_MESSAGING
MM_MODEM_MESSAGING_CLASS
MM_MODEM_MESSAGING_GET_CLASS
MM_TYPE_MODEM_MESSAGING
mm_modem_messaging_get_type
</SECTION>

<SECTION>
<FILE>mm-modem-time</FILE>
<TITLE>MMModemTime</TITLE>
MMModemTime
<SUBSECTION Getters>
mm_modem_time_get_path
mm_modem_time_dup_path
mm_modem_time_peek_network_timezone
mm_modem_time_get_network_timezone
<SUBSECTION Methods>
mm_modem_time_get_network_time
mm_modem_time_get_network_time_finish
mm_modem_time_get_network_time_sync
<SUBSECTION Standard>
MMModemTimeClass
MMModemTimePrivate
MM_IS_MODEM_TIME
MM_IS_MODEM_TIME_CLASS
MM_MODEM_TIME
MM_MODEM_TIME_CLASS
MM_MODEM_TIME_GET_CLASS
MM_TYPE_MODEM_TIME
mm_modem_time_get_type
</SECTION>

<SECTION>
<FILE>mm-network-timezone</FILE>
<TITLE>MMNetworkTimezone</TITLE>
MMNetworkTimezone
MM_NETWORK_TIMEZONE_OFFSET_UNKNOWN
MM_NETWORK_TIMEZONE_LEAP_SECONDS_UNKNOWN
<SUBSECTION Getters>
mm_network_timezone_get_offset
mm_network_timezone_get_dst_offset
mm_network_timezone_get_leap_seconds
<SUBSECTION Private>
mm_network_timezone_new
mm_network_timezone_new_from_dictionary
mm_network_timezone_set_dst_offset
mm_network_timezone_set_leap_seconds
mm_network_timezone_get_dictionary
mm_network_timezone_set_offset
<SUBSECTION Standard>
MMNetworkTimezoneClass
MMNetworkTimezonePrivate
MM_IS_NETWORK_TIMEZONE
MM_IS_NETWORK_TIMEZONE_CLASS
MM_NETWORK_TIMEZONE
MM_NETWORK_TIMEZONE_CLASS
MM_NETWORK_TIMEZONE_GET_CLASS
MM_TYPE_NETWORK_TIMEZONE
mm_network_timezone_get_type
</SECTION>

<SECTION>
<FILE>mm-modem-firmware</FILE>
<TITLE>MMModemFirmware</TITLE>
MMModemFirmware
<SUBSECTION Getters>
mm_modem_firmware_get_path
mm_modem_firmware_dup_path
<SUBSECTION Methods>
mm_modem_firmware_list
mm_modem_firmware_list_finish
mm_modem_firmware_list_sync
mm_modem_firmware_select
mm_modem_firmware_select_finish
mm_modem_firmware_select_sync
mm_modem_firmware_get_update_settings
mm_modem_firmware_peek_update_settings
<SUBSECTION Standard>
MMModemFirmwarePrivate
MMModemFirmwareClass
MM_IS_MODEM_FIRMWARE
MM_IS_MODEM_FIRMWARE_CLASS
MM_MODEM_FIRMWARE
MM_MODEM_FIRMWARE_CLASS
MM_MODEM_FIRMWARE_GET_CLASS
MM_TYPE_MODEM_FIRMWARE
mm_modem_firmware_get_type
</SECTION>

<SECTION>
<FILE>mm-firmware-properties</FILE>
<TITLE>MMFirmwareProperties</TITLE>
MMFirmwareProperties
<SUBSECTION Getters>
mm_firmware_properties_get_image_type
mm_firmware_properties_get_unique_id
mm_firmware_properties_get_gobi_pri_version
mm_firmware_properties_get_gobi_pri_info
mm_firmware_properties_get_gobi_boot_version
mm_firmware_properties_get_gobi_pri_unique_id
mm_firmware_properties_get_gobi_modem_unique_id
<SUBSECTION Private>
mm_firmware_properties_new
mm_firmware_properties_new_from_dictionary
mm_firmware_properties_get_dictionary
mm_firmware_properties_set_gobi_pri_version
mm_firmware_properties_set_gobi_pri_info
mm_firmware_properties_set_gobi_boot_version
mm_firmware_properties_set_gobi_pri_unique_id
mm_firmware_properties_set_gobi_modem_unique_id
<SUBSECTION Standard>
MMFirmwarePropertiesClass
MMFirmwarePropertiesPrivate
MM_FIRMWARE_PROPERTIES
MM_FIRMWARE_PROPERTIES_CLASS
MM_FIRMWARE_PROPERTIES_GET_CLASS
MM_IS_FIRMWARE_PROPERTIES
MM_IS_FIRMWARE_PROPERTIES_CLASS
MM_TYPE_FIRMWARE_PROPERTIES
mm_firmware_properties_get_type
</SECTION>

<SECTION>
<FILE>mm-firmware-update-settings</FILE>
<TITLE>MMFirmwareUpdateSettings</TITLE>
MMFirmwareUpdateSettings
<SUBSECTION Getters>
mm_firmware_update_settings_get_fastboot_at
mm_firmware_update_settings_get_method
mm_firmware_update_settings_get_device_ids
mm_firmware_update_settings_get_version
<SUBSECTION Private>
mm_firmware_update_settings_get_variant
mm_firmware_update_settings_new
mm_firmware_update_settings_new_from_variant
mm_firmware_update_settings_set_fastboot_at
mm_firmware_update_settings_set_device_ids
mm_firmware_update_settings_set_version
<SUBSECTION Standard>
MMFirmwareUpdateSettingsClass
MMFirmwareUpdateSettingsPrivate
MM_FIRMWARE_UPDATE_SETTINGS
MM_FIRMWARE_UPDATE_SETTINGS_CLASS
MM_FIRMWARE_UPDATE_SETTINGS_GET_CLASS
MM_IS_FIRMWARE_UPDATE_SETTINGS
MM_IS_FIRMWARE_UPDATE_SETTINGS_CLASS
MM_TYPE_FIRMWARE_UPDATE_SETTINGS
mm_firmware_update_settings_get_type
</SECTION>

<SECTION>
<FILE>mm-modem-oma</FILE>
<TITLE>MMModemOma</TITLE>
MMModemOma
MMOmaPendingNetworkInitiatedSession
<SUBSECTION Getters>
mm_modem_oma_get_path
mm_modem_oma_dup_path
<SUBSECTION Methods>
mm_modem_oma_setup
mm_modem_oma_setup_finish
mm_modem_oma_setup_sync
mm_modem_oma_start_client_initiated_session
mm_modem_oma_start_client_initiated_session_finish
mm_modem_oma_start_client_initiated_session_sync
mm_modem_oma_accept_network_initiated_session
mm_modem_oma_accept_network_initiated_session_finish
mm_modem_oma_accept_network_initiated_session_sync
mm_modem_oma_cancel_session
mm_modem_oma_cancel_session_finish
mm_modem_oma_cancel_session_sync
mm_modem_oma_get_features
mm_modem_oma_get_session_type
mm_modem_oma_get_session_state
mm_modem_peek_pending_network_initiated_sessions
mm_modem_get_pending_network_initiated_sessions
<SUBSECTION Standard>
MMModemOmaClass
MMModemOmaPrivate
MM_IS_MODEM_OMA
MM_IS_MODEM_OMA_CLASS
MM_MODEM_OMA
MM_MODEM_OMA_CLASS
MM_MODEM_OMA_GET_CLASS
MM_TYPE_MODEM_OMA
mm_modem_oma_get_type
</SECTION>

<SECTION>
<FILE>mm-modem-simple</FILE>
<TITLE>MMModemSimple</TITLE>
MMModemSimple
<SUBSECTION Getters>
mm_modem_simple_get_path
mm_modem_simple_dup_path
<SUBSECTION Methods>
mm_modem_simple_connect
mm_modem_simple_connect_finish
mm_modem_simple_connect_sync
mm_modem_simple_disconnect
mm_modem_simple_disconnect_finish
mm_modem_simple_disconnect_sync
mm_modem_simple_get_status
mm_modem_simple_get_status_finish
mm_modem_simple_get_status_sync
<SUBSECTION Standard>
MMModemSimpleClass
MM_IS_MODEM_SIMPLE
MM_IS_MODEM_SIMPLE_CLASS
MM_MODEM_SIMPLE
MM_MODEM_SIMPLE_CLASS
MM_MODEM_SIMPLE_GET_CLASS
MM_TYPE_MODEM_SIMPLE
mm_modem_simple_get_type
</SECTION>

<SECTION>
<FILE>mm-simple-connect-properties</FILE>
<TITLE>MMSimpleConnectProperties</TITLE>
MMSimpleConnectProperties
<SUBSECTION New>
mm_simple_connect_properties_new
<SUBSECTION GettersSetters>
mm_simple_connect_properties_get_pin
mm_simple_connect_properties_set_pin
mm_simple_connect_properties_get_operator_id
mm_simple_connect_properties_set_operator_id
mm_simple_connect_properties_get_apn
mm_simple_connect_properties_set_apn
mm_simple_connect_properties_get_allowed_auth
mm_simple_connect_properties_set_allowed_auth
mm_simple_connect_properties_get_user
mm_simple_connect_properties_set_user
mm_simple_connect_properties_get_password
mm_simple_connect_properties_set_password
mm_simple_connect_properties_get_ip_type
mm_simple_connect_properties_set_ip_type
mm_simple_connect_properties_get_allow_roaming
mm_simple_connect_properties_set_allow_roaming
mm_simple_connect_properties_get_number
mm_simple_connect_properties_set_number
<SUBSECTION Private>
mm_simple_connect_properties_get_bearer_properties
mm_simple_connect_properties_new_from_dictionary
mm_simple_connect_properties_new_from_string
mm_simple_connect_properties_get_dictionary
<SUBSECTION Standard>
MMSimpleConnectPropertiesClass
MMSimpleConnectPropertiesPrivate
MM_IS_SIMPLE_CONNECT_PROPERTIES
MM_IS_SIMPLE_CONNECT_PROPERTIES_CLASS
MM_SIMPLE_CONNECT_PROPERTIES
MM_SIMPLE_CONNECT_PROPERTIES_CLASS
MM_SIMPLE_CONNECT_PROPERTIES_GET_CLASS
MM_TYPE_SIMPLE_CONNECT_PROPERTIES
mm_simple_connect_properties_get_type
</SECTION>

<SECTION>
<FILE>mm-simple-status</FILE>
<TITLE>MMSimpleStatus</TITLE>
MMSimpleStatus
<SUBSECTION Getters>
mm_simple_status_get_state
mm_simple_status_get_signal_quality
mm_simple_status_get_access_technologies
mm_simple_status_get_current_bands
mm_simple_status_get_3gpp_registration_state
mm_simple_status_get_3gpp_operator_code
mm_simple_status_get_3gpp_operator_name
mm_simple_status_get_3gpp_subscription_state
mm_simple_status_get_cdma_cdma1x_registration_state
mm_simple_status_get_cdma_evdo_registration_state
mm_simple_status_get_cdma_nid
mm_simple_status_get_cdma_sid
<SUBSECTION Private>
MM_SIMPLE_PROPERTY_3GPP_OPERATOR_CODE
MM_SIMPLE_PROPERTY_3GPP_OPERATOR_NAME
MM_SIMPLE_PROPERTY_3GPP_REGISTRATION_STATE
MM_SIMPLE_PROPERTY_3GPP_SUBSCRIPTION_STATE
MM_SIMPLE_PROPERTY_ACCESS_TECHNOLOGIES
MM_SIMPLE_PROPERTY_CURRENT_BANDS
MM_SIMPLE_PROPERTY_CDMA_CDMA1X_REGISTRATION_STATE
MM_SIMPLE_PROPERTY_CDMA_EVDO_REGISTRATION_STATE
MM_SIMPLE_PROPERTY_CDMA_NID
MM_SIMPLE_PROPERTY_CDMA_SID
MM_SIMPLE_PROPERTY_SIGNAL_QUALITY
MM_SIMPLE_PROPERTY_STATE
mm_simple_status_new
mm_simple_status_new_from_dictionary
mm_simple_status_get_dictionary
<SUBSECTION Standard>
MMSimpleStatusClass
MMSimpleStatusPrivate
MM_IS_SIMPLE_STATUS
MM_IS_SIMPLE_STATUS_CLASS
MM_SIMPLE_STATUS
MM_SIMPLE_STATUS_CLASS
MM_SIMPLE_STATUS_GET_CLASS
MM_TYPE_SIMPLE_STATUS
mm_simple_status_get_type
</SECTION>

<SECTION>
<FILE>mm-modem-signal</FILE>
<TITLE>MMModemSignal</TITLE>
MMModemSignal
<SUBSECTION Getters>
mm_modem_signal_get_path
mm_modem_signal_dup_path
mm_modem_signal_get_rate
mm_modem_signal_peek_cdma
mm_modem_signal_get_cdma
mm_modem_signal_peek_evdo
mm_modem_signal_get_evdo
mm_modem_signal_peek_gsm
mm_modem_signal_get_gsm
mm_modem_signal_peek_umts
mm_modem_signal_get_umts
mm_modem_signal_peek_lte
mm_modem_signal_get_lte
<SUBSECTION Methods>
mm_modem_signal_setup
mm_modem_signal_setup_finish
mm_modem_signal_setup_sync
<SUBSECTION Standard>
MMModemSignalPrivate
MMModemSignalClass
MM_IS_MODEM_SIGNAL
MM_IS_MODEM_SIGNAL_CLASS
MM_MODEM_SIGNAL
MM_MODEM_SIGNAL_CLASS
MM_MODEM_SIGNAL_GET_CLASS
MM_TYPE_MODEM_SIGNAL
mm_modem_signal_get_type
</SECTION>

<SECTION>
<FILE>mm-signal</FILE>
<TITLE>MMSignal</TITLE>
MMSignal
MM_SIGNAL_UNKNOWN
<SUBSECTION Getters>
mm_signal_get_rssi
mm_signal_get_rscp
mm_signal_get_ecio
mm_signal_get_sinr
mm_signal_get_io
mm_signal_get_rsrp
mm_signal_get_rsrq
mm_signal_get_snr
<SUBSECTION Private>
mm_signal_new
mm_signal_new_from_dictionary
mm_signal_get_dictionary
mm_signal_set_rssi
mm_signal_set_rscp
mm_signal_set_ecio
mm_signal_set_sinr
mm_signal_set_io
mm_signal_set_rsrp
mm_signal_set_rsrq
mm_signal_set_snr
<SUBSECTION Standard>
MMSignalClass
MMSignalPrivate
MM_SIGNAL
MM_SIGNAL_CLASS
MM_SIGNAL_GET_CLASS
MM_IS_SIGNAL
MM_IS_SIGNAL_CLASS
MM_TYPE_SIGNAL
mm_signal_get_type
</SECTION>

<SECTION>
<FILE>mm-modem-voice</FILE>
<TITLE>MMModemVoice</TITLE>
MMModemVoice
<SUBSECTION Getters>
mm_modem_voice_get_path
mm_modem_voice_dup_path
mm_modem_voice_get_emergency_only

<SUBSECTION Methods>
mm_modem_voice_create_call
mm_modem_voice_create_call_finish
mm_modem_voice_create_call_sync
mm_modem_voice_delete_call
mm_modem_voice_delete_call_finish
mm_modem_voice_delete_call_sync
mm_modem_voice_list_calls
mm_modem_voice_list_calls_finish
mm_modem_voice_list_calls_sync
mm_modem_voice_hangup_and_accept
mm_modem_voice_hangup_and_accept_finish
mm_modem_voice_hangup_and_accept_sync
mm_modem_voice_hold_and_accept
mm_modem_voice_hold_and_accept_finish
mm_modem_voice_hold_and_accept_sync
mm_modem_voice_hangup_all
mm_modem_voice_hangup_all_finish
mm_modem_voice_hangup_all_sync
mm_modem_voice_transfer
mm_modem_voice_transfer_finish
mm_modem_voice_transfer_sync
mm_modem_voice_call_waiting_query
mm_modem_voice_call_waiting_query_finish
mm_modem_voice_call_waiting_query_sync
mm_modem_voice_call_waiting_setup
mm_modem_voice_call_waiting_setup_finish
mm_modem_voice_call_waiting_setup_sync
<SUBSECTION Standard>
MMModemVoiceClass
MMModemVoicePrivate
MM_IS_MODEM_VOICE
MM_IS_MODEM_VOICE_CLASS
MM_MODEM_VOICE
MM_MODEM_VOICE_CLASS
MM_MODEM_VOICE_GET_CLASS
MM_TYPE_MODEM_VOICE
mm_modem_voice_get_type
</SECTION>

<SECTION>
<FILE>mm-bearer</FILE>
<TITLE>MMBearer</TITLE>
MMBearer
<SUBSECTION Getters>
mm_bearer_get_path
mm_bearer_dup_path
mm_bearer_get_interface
mm_bearer_dup_interface
mm_bearer_get_connected
mm_bearer_get_suspended
mm_bearer_get_ip_timeout
mm_bearer_get_bearer_type
mm_bearer_peek_ipv4_config
mm_bearer_get_ipv4_config
mm_bearer_peek_ipv6_config
mm_bearer_get_ipv6_config
mm_bearer_peek_properties
mm_bearer_get_properties
mm_bearer_peek_stats
mm_bearer_get_stats
<SUBSECTION Methods>
mm_bearer_connect
mm_bearer_connect_finish
mm_bearer_connect_sync
mm_bearer_disconnect
mm_bearer_disconnect_finish
mm_bearer_disconnect_sync
<SUBSECTION Standard>
MMBearerClass
MMBearerPrivate
MM_BEARER
MM_BEARER_CLASS
MM_BEARER_GET_CLASS
MM_IS_BEARER
MM_IS_BEARER_CLASS
MM_TYPE_BEARER
mm_bearer_get_type
</SECTION>

<SECTION>
<FILE>mm-bearer-ip-config</FILE>
<TITLE>MMBearerIpConfig</TITLE>
MMBearerIpConfig
<SUBSECTION Getters>
mm_bearer_ip_config_get_method
mm_bearer_ip_config_get_address
mm_bearer_ip_config_get_prefix
mm_bearer_ip_config_get_dns
mm_bearer_ip_config_get_gateway
mm_bearer_ip_config_get_mtu
<SUBSECTION Private>
mm_bearer_ip_config_get_dictionary
mm_bearer_ip_config_new
mm_bearer_ip_config_new_from_dictionary
mm_bearer_ip_config_set_address
mm_bearer_ip_config_set_dns
mm_bearer_ip_config_set_gateway
mm_bearer_ip_config_set_method
mm_bearer_ip_config_set_prefix
mm_bearer_ip_config_set_mtu
mm_bearer_ip_config_dup
<SUBSECTION Standard>
MMBearerIpConfigClass
MMBearerIpConfigPrivate
MM_BEARER_IP_CONFIG
MM_BEARER_IP_CONFIG_CLASS
MM_BEARER_IP_CONFIG_GET_CLASS
MM_IS_BEARER_IP_CONFIG
MM_IS_BEARER_IP_CONFIG_CLASS
MM_TYPE_BEARER_IP_CONFIG
mm_bearer_ip_config_get_type
</SECTION>

<SECTION>
<FILE>mm-bearer-stats</FILE>
<TITLE>MMBearerStats</TITLE>
MMBearerStats
<SUBSECTION Getters>
mm_bearer_stats_get_duration
mm_bearer_stats_get_rx_bytes
mm_bearer_stats_get_tx_bytes
mm_bearer_stats_get_attempts
mm_bearer_stats_get_failed_attempts
mm_bearer_stats_get_total_duration
mm_bearer_stats_get_total_rx_bytes
mm_bearer_stats_get_total_tx_bytes
<SUBSECTION Private>
mm_bearer_stats_get_dictionary
mm_bearer_stats_new
mm_bearer_stats_new_from_dictionary
mm_bearer_stats_set_duration
mm_bearer_stats_set_rx_bytes
mm_bearer_stats_set_tx_bytes
mm_bearer_stats_set_attempts
mm_bearer_stats_set_failed_attempts
mm_bearer_stats_set_total_duration
mm_bearer_stats_set_total_rx_bytes
mm_bearer_stats_set_total_tx_bytes
<SUBSECTION Standard>
MMBearerStatsClass
MMBearerStatsPrivate
MM_BEARER_STATS
MM_BEARER_STATS_CLASS
MM_BEARER_STATS_GET_CLASS
MM_IS_BEARER_STATS
MM_IS_BEARER_STATS_CLASS
MM_TYPE_BEARER_STATS
mm_bearer_stats_get_type
</SECTION>

<SECTION>
<FILE>mm-bearer-properties</FILE>
<TITLE>MMBearerProperties</TITLE>
MMBearerProperties
<SUBSECTION New>
mm_bearer_properties_new
<SUBSECTION GettersSetters>
mm_bearer_properties_get_apn
mm_bearer_properties_set_apn
mm_bearer_properties_get_allowed_auth
mm_bearer_properties_set_allowed_auth
mm_bearer_properties_get_user
mm_bearer_properties_set_user
mm_bearer_properties_get_password
mm_bearer_properties_set_password
mm_bearer_properties_get_ip_type
mm_bearer_properties_set_ip_type
mm_bearer_properties_get_allow_roaming
mm_bearer_properties_set_allow_roaming
mm_bearer_properties_get_number
mm_bearer_properties_set_number
mm_bearer_properties_get_rm_protocol
mm_bearer_properties_set_rm_protocol
<SUBSECTION Private>
mm_bearer_properties_new_from_dictionary
mm_bearer_properties_new_from_string
mm_bearer_properties_cmp
mm_bearer_properties_consume_string
mm_bearer_properties_consume_variant
mm_bearer_properties_dup
mm_bearer_properties_get_dictionary
<SUBSECTION Standard>
MMBearerPropertiesClass
MMBearerPropertiesPrivate
MM_BEARER_PROPERTIES
MM_BEARER_PROPERTIES_CLASS
MM_BEARER_PROPERTIES_GET_CLASS
MM_IS_BEARER_PROPERTIES
MM_IS_BEARER_PROPERTIES_CLASS
MM_TYPE_BEARER_PROPERTIES
mm_bearer_properties_get_type
</SECTION>

<SECTION>
<FILE>mm-sim</FILE>
<TITLE>MMSim</TITLE>
MMSim
<SUBSECTION Getters>
mm_sim_get_path
mm_sim_dup_path
mm_sim_get_identifier
mm_sim_dup_identifier
mm_sim_get_imsi
mm_sim_dup_imsi
mm_sim_get_operator_identifier
mm_sim_dup_operator_identifier
mm_sim_get_operator_name
mm_sim_dup_operator_name
mm_sim_get_emergency_numbers
mm_sim_dup_emergency_numbers
<SUBSECTION Methods>
mm_sim_send_pin
mm_sim_send_pin_finish
mm_sim_send_pin_sync
mm_sim_send_puk
mm_sim_send_puk_finish
mm_sim_send_puk_sync
mm_sim_enable_pin
mm_sim_enable_pin_finish
mm_sim_enable_pin_sync
mm_sim_disable_pin
mm_sim_disable_pin_finish
mm_sim_disable_pin_sync
mm_sim_change_pin
mm_sim_change_pin_finish
mm_sim_change_pin_sync
<SUBSECTION Standard>
MMSimClass
MM_IS_SIM
MM_IS_SIM_CLASS
MM_SIM
MM_SIM_CLASS
MM_SIM_GET_CLASS
MM_TYPE_SIM
mm_sim_get_type
</SECTION>

<SECTION>
<FILE>mm-sms</FILE>
<TITLE>MMSms</TITLE>
MMSms
<SUBSECTION Getters>
mm_sms_get_path
mm_sms_dup_path
mm_sms_get_state
mm_sms_get_pdu_type
mm_sms_get_storage
mm_sms_get_text
mm_sms_dup_text
mm_sms_get_data
mm_sms_dup_data
mm_sms_get_number
mm_sms_dup_number
mm_sms_get_smsc
mm_sms_dup_smsc
mm_sms_get_message_reference
mm_sms_get_class
mm_sms_get_teleservice_id
mm_sms_get_service_category
mm_sms_get_validity_type
mm_sms_get_validity_relative
mm_sms_get_timestamp
mm_sms_dup_timestamp
mm_sms_get_discharge_timestamp
mm_sms_dup_discharge_timestamp
mm_sms_get_delivery_state
mm_sms_get_delivery_report_request
<SUBSECTION Methods>
mm_sms_send
mm_sms_send_finish
mm_sms_send_sync
mm_sms_store
mm_sms_store_finish
mm_sms_store_sync
<SUBSECTION Standard>
MMSmsClass
MM_IS_SMS
MM_IS_SMS_CLASS
MM_SMS
MM_SMS_CLASS
MM_SMS_GET_CLASS
MM_TYPE_SMS
mm_sms_get_type
</SECTION>

<SECTION>
<FILE>mm-sms-properties</FILE>
<TITLE>MMSmsProperties</TITLE>
MMSmsProperties
<SUBSECTION New>
mm_sms_properties_new
<SUBSECTION GettersSetters>
mm_sms_properties_get_text
mm_sms_properties_set_text
mm_sms_properties_get_data
mm_sms_properties_set_data
mm_sms_properties_peek_data_bytearray
mm_sms_properties_get_data_bytearray
mm_sms_properties_set_data_bytearray
mm_sms_properties_get_number
mm_sms_properties_set_number
mm_sms_properties_get_smsc
mm_sms_properties_set_smsc
mm_sms_properties_get_validity_type
mm_sms_properties_get_validity_relative
mm_sms_properties_set_validity_relative
mm_sms_properties_get_class
mm_sms_properties_set_class
mm_sms_properties_get_delivery_report_request
mm_sms_properties_set_delivery_report_request
mm_sms_properties_get_teleservice_id
mm_sms_properties_set_teleservice_id
mm_sms_properties_get_service_category
mm_sms_properties_set_service_category
<SUBSECTION Private>
mm_sms_properties_get_dictionary
mm_sms_properties_dup
mm_sms_properties_new_from_dictionary
mm_sms_properties_new_from_string
<SUBSECTION Standard>
MMSmsPropertiesClass
MMSmsPropertiesPrivate
MM_IS_SMS_PROPERTIES
MM_IS_SMS_PROPERTIES_CLASS
MM_SMS_PROPERTIES
MM_SMS_PROPERTIES_CLASS
MM_SMS_PROPERTIES_GET_CLASS
MM_TYPE_SMS_PROPERTIES
mm_sms_properties_get_type
</SECTION>

<SECTION>
<FILE>mm-call</FILE>
<TITLE>MMCall</TITLE>
MMCall
<SUBSECTION Getters>
mm_call_get_path
mm_call_dup_path
mm_call_get_number
mm_call_dup_number
mm_call_get_direction
mm_call_get_state
mm_call_get_state_reason
mm_call_get_audio_port
mm_call_dup_audio_port
mm_call_get_audio_format
mm_call_peek_audio_format
mm_call_get_multiparty
<SUBSECTION Methods>
mm_call_start
mm_call_start_finish
mm_call_start_sync
mm_call_accept
mm_call_accept_finish
mm_call_accept_sync
mm_call_hangup
mm_call_hangup_finish
mm_call_hangup_sync
mm_call_send_dtmf
mm_call_send_dtmf_finish
mm_call_send_dtmf_sync
mm_call_deflect
mm_call_deflect_finish
mm_call_deflect_sync
mm_call_join_multiparty
mm_call_join_multiparty_finish
mm_call_join_multiparty_sync
mm_call_leave_multiparty
mm_call_leave_multiparty_finish
mm_call_leave_multiparty_sync
<SUBSECTION Standard>
MMCallClass
MMCallPrivate
MM_IS_CALL
MM_IS_CALL_CLASS
MM_CALL
MM_CALL_CLASS
MM_CALL_GET_CLASS
MM_TYPE_CALL
mm_call_get_type
</SECTION>

<SECTION>
<FILE>mm-call-properties</FILE>
<TITLE>MMCallProperties</TITLE>
MMCallProperties
<SUBSECTION New>
mm_call_properties_new
<SUBSECTION GettersSetters>
mm_call_properties_get_number
mm_call_properties_set_number
mm_call_properties_get_direction
mm_call_properties_set_direction
mm_call_properties_get_state
mm_call_properties_set_state
mm_call_properties_get_state_reason
mm_call_properties_set_state_reason
<SUBSECTION Private>
mm_call_properties_get_dictionary
mm_call_properties_dup
mm_call_properties_new_from_dictionary
mm_call_properties_new_from_string
<SUBSECTION Standard>
MMCallPropertiesClass
MMCallPropertiesPrivate
MM_IS_CALL_PROPERTIES
MM_IS_CALL_PROPERTIES_CLASS
MM_CALL_PROPERTIES
MM_CALL_PROPERTIES_CLASS
MM_CALL_PROPERTIES_GET_CLASS
MM_TYPE_CALL_PROPERTIES
mm_call_properties_get_type
</SECTION>

<SECTION>
<FILE>mm-pco</FILE>
<TITLE>MMPco</TITLE>
MMPco
<SUBSECTION GettersSetters>
mm_pco_get_session_id
mm_pco_is_complete
mm_pco_get_data
<SUBSECTION List>
mm_pco_list_free
<SUBSECTION Private>
mm_pco_new
mm_pco_from_variant
mm_pco_to_variant
mm_pco_set_session_id
mm_pco_set_complete
mm_pco_set_data
mm_pco_list_add
<SUBSECTION Standard>
MMPcoClass
MMPcoPrivate
MM_IS_PCO
MM_IS_PCO_CLASS
MM_PCO
MM_PCO_CLASS
MM_PCO_GET_CLASS
MM_TYPE_PCO
mm_pco_get_type
</SECTION>

<SECTION>
<FILE>mm-call-audio-format</FILE>
<TITLE>MMCallAudioFormat</TITLE>
MMCallAudioFormat
<SUBSECTION Getters>
mm_call_audio_format_get_encoding
mm_call_audio_format_get_resolution
mm_call_audio_format_get_rate
<SUBSECTION Private>
mm_call_audio_format_get_dictionary
mm_call_audio_format_new
mm_call_audio_format_new_from_dictionary
mm_call_audio_format_set_encoding
mm_call_audio_format_set_resolution
mm_call_audio_format_set_rate
mm_call_audio_format_dup
<SUBSECTION Standard>
MMCallAudioFormatClass
MMCallAudioFormatPrivate
MM_CALL_AUDIO_FORMAT
MM_CALL_AUDIO_FORMAT_CLASS
MM_CALL_AUDIO_FORMAT_GET_CLASS
MM_IS_CALL_AUDIO_FORMAT
MM_IS_CALL_AUDIO_FORMAT_CLASS
MM_TYPE_CALL_AUDIO_FORMAT
mm_call_audio_format_get_type
</SECTION>

<SECTION>
<FILE>mm-enums-types</FILE>
<TITLE>Flags and Enumerations</TITLE>
mm_bearer_type_get_string
mm_bearer_ip_method_get_string
mm_bearer_ip_family_get_string
mm_bearer_allowed_auth_build_string_from_mask
mm_modem_capability_build_string_from_mask
mm_modem_state_get_string
mm_modem_state_failed_reason_get_string
mm_modem_state_change_reason_get_string
mm_modem_power_state_get_string
mm_modem_lock_get_string
mm_modem_access_technology_build_string_from_mask
mm_modem_mode_build_string_from_mask
mm_modem_band_get_string
mm_modem_port_type_get_string
mm_modem_3gpp_registration_state_get_string
mm_modem_3gpp_subscription_state_get_string
mm_modem_3gpp_facility_build_string_from_mask
mm_modem_3gpp_network_availability_get_string
mm_modem_3gpp_ussd_session_state_get_string
mm_modem_3gpp_eps_ue_mode_operation_get_string
mm_modem_cdma_registration_state_get_string
mm_modem_cdma_activation_state_get_string
mm_modem_cdma_rm_protocol_get_string
mm_modem_location_source_build_string_from_mask
mm_modem_location_assistance_data_type_build_string_from_mask
mm_modem_contacts_storage_get_string
mm_modem_firmware_update_method_build_string_from_mask
mm_sms_pdu_type_get_string
mm_sms_state_get_string
mm_sms_delivery_state_get_string
mm_sms_storage_get_string
mm_sms_validity_type_get_string
mm_sms_cdma_teleservice_id_get_string
mm_sms_cdma_service_category_get_string
mm_firmware_image_type_get_string
mm_oma_feature_build_string_from_mask
mm_oma_session_type_get_string
mm_oma_session_state_get_string
mm_oma_session_state_failed_reason_get_string
mm_call_direction_get_string
mm_call_state_get_string
mm_call_state_reason_get_string
<SUBSECTION Private>
mm_modem_capability_get_string
mm_modem_lock_build_string_from_mask
mm_modem_state_build_string_from_mask
mm_modem_state_failed_reason_build_string_from_mask
mm_modem_state_change_reason_build_string_from_mask
mm_modem_power_state_build_string_from_mask
mm_modem_access_technology_get_string
mm_modem_mode_get_string
mm_modem_band_build_string_from_mask
mm_sms_pdu_type_build_string_from_mask
mm_sms_state_build_string_from_mask
mm_sms_delivery_state_build_string_from_mask
mm_sms_storage_build_string_from_mask
mm_sms_validity_type_build_string_from_mask
mm_sms_cdma_teleservice_id_build_string_from_mask
mm_sms_cdma_service_category_build_string_from_mask
mm_modem_location_source_get_string
mm_modem_location_assistance_data_type_get_string
mm_modem_contacts_storage_build_string_from_mask
mm_bearer_type_build_string_from_mask
mm_bearer_ip_family_build_string_from_mask
mm_bearer_ip_method_build_string_from_mask
mm_bearer_allowed_auth_get_string
mm_modem_cdma_registration_state_build_string_from_mask
mm_modem_cdma_activation_state_build_string_from_mask
mm_modem_cdma_rm_protocol_build_string_from_mask
mm_modem_3gpp_registration_state_build_string_from_mask
mm_modem_3gpp_subscription_state_build_string_from_mask
mm_modem_3gpp_facility_get_string
mm_modem_3gpp_network_availability_build_string_from_mask
mm_modem_3gpp_ussd_session_state_build_string_from_mask
mm_modem_3gpp_eps_ue_mode_operation_build_string_from_mask
mm_firmware_image_type_build_string_from_mask
mm_modem_port_type_build_string_from_mask
mm_oma_feature_get_string
mm_oma_session_type_build_string_from_mask
mm_oma_session_state_build_string_from_mask
mm_oma_session_state_failed_reason_build_string_from_mask
mm_call_direction_build_string_from_mask
mm_call_state_build_string_from_mask
mm_call_state_reason_build_string_from_mask
mm_modem_firmware_update_method_get_string
<SUBSECTION Standard>
MM_TYPE_BEARER_TYPE
MM_TYPE_BEARER_IP_FAMILY
MM_TYPE_BEARER_IP_METHOD
MM_TYPE_BEARER_ALLOWED_AUTH
MM_TYPE_FIRMWARE_IMAGE_TYPE
MM_TYPE_MODEM_3GPP_FACILITY
MM_TYPE_MODEM_3GPP_NETWORK_AVAILABILITY
MM_TYPE_MODEM_3GPP_REGISTRATION_STATE
MM_TYPE_MODEM_3GPP_SUBSCRIPTION_STATE
MM_TYPE_MODEM_3GPP_USSD_SESSION_STATE
MM_TYPE_MODEM_3GPP_EPS_UE_MODE_OPERATION
MM_TYPE_MODEM_ACCESS_TECHNOLOGY
MM_TYPE_MODEM_BAND
MM_TYPE_MODEM_CAPABILITY
MM_TYPE_MODEM_CDMA_ACTIVATION_STATE
MM_TYPE_MODEM_CDMA_REGISTRATION_STATE
MM_TYPE_MODEM_CDMA_RM_PROTOCOL
MM_TYPE_MODEM_CONTACTS_STORAGE
MM_TYPE_MODEM_LOCATION_SOURCE
MM_TYPE_MODEM_LOCATION_ASSISTANCE_DATA_TYPE
MM_TYPE_MODEM_LOCK
MM_TYPE_MODEM_MODE
MM_TYPE_MODEM_STATE
MM_TYPE_MODEM_STATE_FAILED_REASON
MM_TYPE_MODEM_STATE_CHANGE_REASON
MM_TYPE_MODEM_POWER_STATE
MM_TYPE_MODEM_PORT_TYPE
MM_TYPE_SMS_DELIVERY_STATE
MM_TYPE_SMS_PDU_TYPE
MM_TYPE_SMS_STATE
MM_TYPE_SMS_STORAGE
MM_TYPE_SMS_VALIDITY_TYPE
MM_TYPE_SMS_CDMA_TELESERVICE_ID
MM_TYPE_SMS_CDMA_SERVICE_CATEGORY
MM_TYPE_OMA_FEATURE
MM_TYPE_OMA_SESSION_STATE
MM_TYPE_OMA_SESSION_STATE_FAILED_REASON
MM_TYPE_OMA_SESSION_TYPE
MM_TYPE_CALL_DIRECTION
MM_TYPE_CALL_STATE
MM_TYPE_CALL_STATE_REASON
MM_TYPE_MODEM_FIRMWARE_UPDATE_METHOD
mm_bearer_type_get_type
mm_bearer_ip_family_get_type
mm_bearer_ip_method_get_type
mm_bearer_allowed_auth_get_type
mm_firmware_image_type_get_type
mm_modem_3gpp_facility_get_type
mm_modem_3gpp_network_availability_get_type
mm_modem_3gpp_registration_state_get_type
mm_modem_3gpp_subscription_state_get_type
mm_modem_3gpp_ussd_session_state_get_type
mm_modem_3gpp_eps_ue_mode_operation_get_type
mm_modem_access_technology_get_type
mm_modem_band_get_type
mm_modem_capability_get_type
mm_modem_cdma_activation_state_get_type
mm_modem_cdma_registration_state_get_type
mm_modem_cdma_rm_protocol_get_type
mm_modem_contacts_storage_get_type
mm_modem_location_source_get_type
mm_modem_location_assistance_data_type_get_type
mm_modem_lock_get_type
mm_modem_mode_get_type
mm_modem_state_change_reason_get_type
mm_modem_state_get_type
mm_modem_state_failed_reason_get_type
mm_modem_power_state_get_type
mm_modem_port_type_get_type
mm_sms_delivery_state_get_type
mm_sms_pdu_type_get_type
mm_sms_state_get_type
mm_sms_storage_get_type
mm_sms_validity_type_get_type
mm_sms_cdma_teleservice_id_get_type
mm_sms_cdma_service_category_get_type
mm_oma_feature_get_type
mm_oma_session_state_failed_reason_get_type
mm_oma_session_state_get_type
mm_oma_session_type_get_type
mm_call_direction_get_type
mm_call_state_get_type
mm_call_state_reason_get_type
mm_modem_firmware_update_method_get_type
</SECTION>

<SECTION>
<FILE>mm-errors-types</FILE>
<SUBSECTION Private>
mm_cdma_activation_error_quark
mm_connection_error_quark
mm_core_error_quark
mm_message_error_quark
mm_mobile_equipment_error_quark
mm_serial_error_quark
<SUBSECTION Standard>
MM_CDMA_ACTIVATION_ERROR
MM_CONNECTION_ERROR
MM_CORE_ERROR
MM_MESSAGE_ERROR
MM_MOBILE_EQUIPMENT_ERROR
MM_SERIAL_ERROR
MM_TYPE_CDMA_ACTIVATION_ERROR
MM_TYPE_CONNECTION_ERROR
MM_TYPE_CORE_ERROR
MM_TYPE_MESSAGE_ERROR
MM_TYPE_MOBILE_EQUIPMENT_ERROR
MM_TYPE_SERIAL_ERROR
mm_cdma_activation_error_get_type
mm_connection_error_get_type
mm_core_error_get_type
mm_message_error_get_type
mm_mobile_equipment_error_get_type
mm_serial_error_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusBearer</FILE>
<TITLE>MmGdbusBearer</TITLE>
MmGdbusBearer
MmGdbusBearerIface
<SUBSECTION Getters>
mm_gdbus_bearer_get_interface
mm_gdbus_bearer_dup_interface
mm_gdbus_bearer_get_ip4_config
mm_gdbus_bearer_dup_ip4_config
mm_gdbus_bearer_get_ip6_config
mm_gdbus_bearer_dup_ip6_config
mm_gdbus_bearer_get_ip_timeout
mm_gdbus_bearer_get_properties
mm_gdbus_bearer_dup_properties
mm_gdbus_bearer_get_connected
mm_gdbus_bearer_get_suspended
mm_gdbus_bearer_get_bearer_type
mm_gdbus_bearer_get_stats
mm_gdbus_bearer_dup_stats
<SUBSECTION Methods>
mm_gdbus_bearer_call_connect
mm_gdbus_bearer_call_connect_finish
mm_gdbus_bearer_call_connect_sync
mm_gdbus_bearer_call_disconnect
mm_gdbus_bearer_call_disconnect_finish
mm_gdbus_bearer_call_disconnect_sync
<SUBSECTION Private>
mm_gdbus_bearer_interface_info
mm_gdbus_bearer_set_connected
mm_gdbus_bearer_set_interface
mm_gdbus_bearer_set_ip4_config
mm_gdbus_bearer_set_ip6_config
mm_gdbus_bearer_set_ip_timeout
mm_gdbus_bearer_set_properties
mm_gdbus_bearer_set_suspended
mm_gdbus_bearer_set_bearer_type
mm_gdbus_bearer_set_stats
mm_gdbus_bearer_override_properties
mm_gdbus_bearer_complete_connect
mm_gdbus_bearer_complete_disconnect
<SUBSECTION Standard>
MM_GDBUS_BEARER
MM_GDBUS_BEARER_GET_IFACE
MM_GDBUS_IS_BEARER
MM_GDBUS_TYPE_BEARER
mm_gdbus_bearer_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusBearerProxy</FILE>
<TITLE>MmGdbusBearerProxy</TITLE>
MmGdbusBearerProxy
<SUBSECTION New>
mm_gdbus_bearer_proxy_new
mm_gdbus_bearer_proxy_new_finish
mm_gdbus_bearer_proxy_new_for_bus
mm_gdbus_bearer_proxy_new_for_bus_finish
mm_gdbus_bearer_proxy_new_for_bus_sync
mm_gdbus_bearer_proxy_new_sync
<SUBSECTION Standard>
MmGdbusBearerProxyClass
MM_GDBUS_BEARER_PROXY
MM_GDBUS_BEARER_PROXY_CLASS
MM_GDBUS_BEARER_PROXY_GET_CLASS
MM_GDBUS_IS_BEARER_PROXY
MM_GDBUS_IS_BEARER_PROXY_CLASS
MM_GDBUS_TYPE_BEARER_PROXY
MmGdbusBearerProxyPrivate
mm_gdbus_bearer_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusBearerSkeleton</FILE>
<TITLE>MmGdbusBearerSkeleton</TITLE>
MmGdbusBearerSkeleton
<SUBSECTION New>
mm_gdbus_bearer_skeleton_new
<SUBSECTION Standard>
MmGdbusBearerSkeletonClass
MM_GDBUS_BEARER_SKELETON
MM_GDBUS_BEARER_SKELETON_CLASS
MM_GDBUS_BEARER_SKELETON_GET_CLASS
MM_GDBUS_IS_BEARER_SKELETON
MM_GDBUS_IS_BEARER_SKELETON_CLASS
MM_GDBUS_TYPE_BEARER_SKELETON
MmGdbusBearerSkeletonPrivate
mm_gdbus_bearer_skeleton_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusOrgFreedesktopModemManager1</FILE>
<TITLE>MmGdbusOrgFreedesktopModemManager1</TITLE>
MmGdbusOrgFreedesktopModemManager1
MmGdbusOrgFreedesktopModemManager1Iface
<SUBSECTION Methods>
mm_gdbus_org_freedesktop_modem_manager1_dup_version
mm_gdbus_org_freedesktop_modem_manager1_get_version
mm_gdbus_org_freedesktop_modem_manager1_call_scan_devices
mm_gdbus_org_freedesktop_modem_manager1_call_scan_devices_finish
mm_gdbus_org_freedesktop_modem_manager1_call_scan_devices_sync
mm_gdbus_org_freedesktop_modem_manager1_call_inhibit_device
mm_gdbus_org_freedesktop_modem_manager1_call_inhibit_device_finish
mm_gdbus_org_freedesktop_modem_manager1_call_inhibit_device_sync
mm_gdbus_org_freedesktop_modem_manager1_call_set_logging
mm_gdbus_org_freedesktop_modem_manager1_call_set_logging_finish
mm_gdbus_org_freedesktop_modem_manager1_call_set_logging_sync
mm_gdbus_org_freedesktop_modem_manager1_call_report_kernel_event
mm_gdbus_org_freedesktop_modem_manager1_call_report_kernel_event_finish
mm_gdbus_org_freedesktop_modem_manager1_call_report_kernel_event_sync
<SUBSECTION Private>
mm_gdbus_org_freedesktop_modem_manager1_set_version
mm_gdbus_org_freedesktop_modem_manager1_override_properties
mm_gdbus_org_freedesktop_modem_manager1_complete_inhibit_device
mm_gdbus_org_freedesktop_modem_manager1_complete_scan_devices
mm_gdbus_org_freedesktop_modem_manager1_complete_set_logging
mm_gdbus_org_freedesktop_modem_manager1_complete_report_kernel_event
mm_gdbus_org_freedesktop_modem_manager1_interface_info
<SUBSECTION Standard>
MM_GDBUS_IS_ORG_FREEDESKTOP_MODEM_MANAGER1
MM_GDBUS_ORG_FREEDESKTOP_MODEM_MANAGER1
MM_GDBUS_ORG_FREEDESKTOP_MODEM_MANAGER1_GET_IFACE
MM_GDBUS_TYPE_ORG_FREEDESKTOP_MODEM_MANAGER1
mm_gdbus_org_freedesktop_modem_manager1_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusOrgFreedesktopModemManager1Proxy</FILE>
<TITLE>MmGdbusOrgFreedesktopModemManager1Proxy</TITLE>
MmGdbusOrgFreedesktopModemManager1Proxy
<SUBSECTION New>
mm_gdbus_org_freedesktop_modem_manager1_proxy_new
mm_gdbus_org_freedesktop_modem_manager1_proxy_new_finish
mm_gdbus_org_freedesktop_modem_manager1_proxy_new_for_bus
mm_gdbus_org_freedesktop_modem_manager1_proxy_new_for_bus_finish
mm_gdbus_org_freedesktop_modem_manager1_proxy_new_for_bus_sync
mm_gdbus_org_freedesktop_modem_manager1_proxy_new_sync
<SUBSECTION Standard>
MmGdbusOrgFreedesktopModemManager1ProxyClass
MM_GDBUS_IS_ORG_FREEDESKTOP_MODEM_MANAGER1_PROXY
MM_GDBUS_IS_ORG_FREEDESKTOP_MODEM_MANAGER1_PROXY_CLASS
MM_GDBUS_ORG_FREEDESKTOP_MODEM_MANAGER1_PROXY
MM_GDBUS_ORG_FREEDESKTOP_MODEM_MANAGER1_PROXY_CLASS
MM_GDBUS_ORG_FREEDESKTOP_MODEM_MANAGER1_PROXY_GET_CLASS
MM_GDBUS_TYPE_ORG_FREEDESKTOP_MODEM_MANAGER1_PROXY
MmGdbusOrgFreedesktopModemManager1ProxyPrivate
mm_gdbus_org_freedesktop_modem_manager1_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusOrgFreedesktopModemManager1Skeleton</FILE>
<TITLE>MmGdbusOrgFreedesktopModemManager1Skeleton</TITLE>
MmGdbusOrgFreedesktopModemManager1Skeleton
<SUBSECTION New>
mm_gdbus_org_freedesktop_modem_manager1_skeleton_new
<SUBSECTION Standard>
MmGdbusOrgFreedesktopModemManager1SkeletonClass
MM_GDBUS_IS_ORG_FREEDESKTOP_MODEM_MANAGER1_SKELETON
MM_GDBUS_IS_ORG_FREEDESKTOP_MODEM_MANAGER1_SKELETON_CLASS
MM_GDBUS_ORG_FREEDESKTOP_MODEM_MANAGER1_SKELETON
MM_GDBUS_ORG_FREEDESKTOP_MODEM_MANAGER1_SKELETON_CLASS
MM_GDBUS_ORG_FREEDESKTOP_MODEM_MANAGER1_SKELETON_GET_CLASS
MM_GDBUS_TYPE_ORG_FREEDESKTOP_MODEM_MANAGER1_SKELETON
MmGdbusOrgFreedesktopModemManager1SkeletonPrivate
mm_gdbus_org_freedesktop_modem_manager1_skeleton_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModem3gpp</FILE>
<TITLE>MmGdbusModem3gpp</TITLE>
MmGdbusModem3gpp
MmGdbusModem3gppIface
<SUBSECTION Getters>
mm_gdbus_modem3gpp_get_registration_state
mm_gdbus_modem3gpp_get_imei
mm_gdbus_modem3gpp_dup_imei
mm_gdbus_modem3gpp_get_operator_code
mm_gdbus_modem3gpp_dup_operator_code
mm_gdbus_modem3gpp_get_operator_name
mm_gdbus_modem3gpp_dup_operator_name
mm_gdbus_modem3gpp_get_enabled_facility_locks
mm_gdbus_modem3gpp_get_subscription_state
mm_gdbus_modem3gpp_get_eps_ue_mode_operation
mm_gdbus_modem3gpp_get_pco
mm_gdbus_modem3gpp_dup_pco
mm_gdbus_modem3gpp_get_initial_eps_bearer
mm_gdbus_modem3gpp_dup_initial_eps_bearer
mm_gdbus_modem3gpp_get_initial_eps_bearer_settings
mm_gdbus_modem3gpp_dup_initial_eps_bearer_settings
<SUBSECTION Methods>
mm_gdbus_modem3gpp_call_register
mm_gdbus_modem3gpp_call_register_finish
mm_gdbus_modem3gpp_call_register_sync
mm_gdbus_modem3gpp_call_scan
mm_gdbus_modem3gpp_call_scan_finish
mm_gdbus_modem3gpp_call_scan_sync
mm_gdbus_modem3gpp_call_set_eps_ue_mode_operation
mm_gdbus_modem3gpp_call_set_eps_ue_mode_operation_finish
mm_gdbus_modem3gpp_call_set_eps_ue_mode_operation_sync
mm_gdbus_modem3gpp_call_set_initial_eps_bearer_settings
mm_gdbus_modem3gpp_call_set_initial_eps_bearer_settings_finish
mm_gdbus_modem3gpp_call_set_initial_eps_bearer_settings_sync
<SUBSECTION Private>
mm_gdbus_modem3gpp_complete_register
mm_gdbus_modem3gpp_complete_scan
mm_gdbus_modem3gpp_complete_set_eps_ue_mode_operation
mm_gdbus_modem3gpp_complete_set_initial_eps_bearer_settings
mm_gdbus_modem3gpp_interface_info
mm_gdbus_modem3gpp_override_properties
mm_gdbus_modem3gpp_set_enabled_facility_locks
mm_gdbus_modem3gpp_set_imei
mm_gdbus_modem3gpp_set_operator_code
mm_gdbus_modem3gpp_set_operator_name
mm_gdbus_modem3gpp_set_registration_state
mm_gdbus_modem3gpp_set_subscription_state
mm_gdbus_modem3gpp_set_eps_ue_mode_operation
mm_gdbus_modem3gpp_set_pco
mm_gdbus_modem3gpp_set_initial_eps_bearer
mm_gdbus_modem3gpp_set_initial_eps_bearer_settings
<SUBSECTION Standard>
MM_GDBUS_IS_MODEM3GPP
MM_GDBUS_MODEM3GPP
MM_GDBUS_MODEM3GPP_GET_IFACE
MM_GDBUS_TYPE_MODEM3GPP
mm_gdbus_modem3gpp_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModem3gppProxy</FILE>
<TITLE>MmGdbusModem3gppProxy</TITLE>
MmGdbusModem3gppProxy
<SUBSECTION New>
mm_gdbus_modem3gpp_proxy_new
mm_gdbus_modem3gpp_proxy_new_finish
mm_gdbus_modem3gpp_proxy_new_for_bus
mm_gdbus_modem3gpp_proxy_new_for_bus_finish
mm_gdbus_modem3gpp_proxy_new_for_bus_sync
mm_gdbus_modem3gpp_proxy_new_sync
<SUBSECTION Standard>
MmGdbusModem3gppProxyClass
MM_GDBUS_IS_MODEM3GPP_PROXY
MM_GDBUS_IS_MODEM3GPP_PROXY_CLASS
MM_GDBUS_MODEM3GPP_PROXY
MM_GDBUS_MODEM3GPP_PROXY_CLASS
MM_GDBUS_MODEM3GPP_PROXY_GET_CLASS
MM_GDBUS_TYPE_MODEM3GPP_PROXY
MmGdbusModem3gppProxyPrivate
mm_gdbus_modem3gpp_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModem3gppSkeleton</FILE>
<TITLE>MmGdbusModem3gppSkeleton</TITLE>
MmGdbusModem3gppSkeleton
<SUBSECTION New>
mm_gdbus_modem3gpp_skeleton_new
<SUBSECTION Standard>
MmGdbusModem3gppSkeletonClass
MM_GDBUS_IS_MODEM3GPP_SKELETON
MM_GDBUS_IS_MODEM3GPP_SKELETON_CLASS
MM_GDBUS_MODEM3GPP_SKELETON
MM_GDBUS_MODEM3GPP_SKELETON_CLASS
MM_GDBUS_MODEM3GPP_SKELETON_GET_CLASS
MM_GDBUS_TYPE_MODEM3GPP_SKELETON
MmGdbusModem3gppSkeletonPrivate
mm_gdbus_modem3gpp_skeleton_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModem3gppUssd</FILE>
<TITLE>MmGdbusModem3gppUssd</TITLE>
MmGdbusModem3gppUssd
MmGdbusModem3gppUssdIface
<SUBSECTION Getters>
mm_gdbus_modem3gpp_ussd_get_state
mm_gdbus_modem3gpp_ussd_get_network_request
mm_gdbus_modem3gpp_ussd_dup_network_request
mm_gdbus_modem3gpp_ussd_get_network_notification
mm_gdbus_modem3gpp_ussd_dup_network_notification
<SUBSECTION Methods>
mm_gdbus_modem3gpp_ussd_call_initiate
mm_gdbus_modem3gpp_ussd_call_initiate_finish
mm_gdbus_modem3gpp_ussd_call_initiate_sync
mm_gdbus_modem3gpp_ussd_call_respond
mm_gdbus_modem3gpp_ussd_call_respond_finish
mm_gdbus_modem3gpp_ussd_call_respond_sync
mm_gdbus_modem3gpp_ussd_call_cancel
mm_gdbus_modem3gpp_ussd_call_cancel_finish
mm_gdbus_modem3gpp_ussd_call_cancel_sync
<SUBSECTION Private>
mm_gdbus_modem3gpp_ussd_complete_cancel
mm_gdbus_modem3gpp_ussd_complete_initiate
mm_gdbus_modem3gpp_ussd_complete_respond
mm_gdbus_modem3gpp_ussd_interface_info
mm_gdbus_modem3gpp_ussd_override_properties
mm_gdbus_modem3gpp_ussd_set_network_notification
mm_gdbus_modem3gpp_ussd_set_network_request
mm_gdbus_modem3gpp_ussd_set_state
<SUBSECTION Standard>
MM_GDBUS_IS_MODEM3GPP_USSD
MM_GDBUS_MODEM3GPP_USSD
MM_GDBUS_MODEM3GPP_USSD_GET_IFACE
MM_GDBUS_TYPE_MODEM3GPP_USSD
mm_gdbus_modem3gpp_ussd_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModem3gppUssdProxy</FILE>
<TITLE>MmGdbusModem3gppUssdProxy</TITLE>
MmGdbusModem3gppUssdProxy
<SUBSECTION New>
mm_gdbus_modem3gpp_ussd_proxy_new
mm_gdbus_modem3gpp_ussd_proxy_new_finish
mm_gdbus_modem3gpp_ussd_proxy_new_for_bus
mm_gdbus_modem3gpp_ussd_proxy_new_for_bus_finish
mm_gdbus_modem3gpp_ussd_proxy_new_for_bus_sync
mm_gdbus_modem3gpp_ussd_proxy_new_sync
<SUBSECTION Standard>
MmGdbusModem3gppUssdProxyClass
MM_GDBUS_IS_MODEM3GPP_USSD_PROXY
MM_GDBUS_IS_MODEM3GPP_USSD_PROXY_CLASS
MM_GDBUS_MODEM3GPP_USSD_PROXY
MM_GDBUS_MODEM3GPP_USSD_PROXY_CLASS
MM_GDBUS_MODEM3GPP_USSD_PROXY_GET_CLASS
MM_GDBUS_TYPE_MODEM3GPP_USSD_PROXY
MmGdbusModem3gppUssdProxyPrivate
mm_gdbus_modem3gpp_ussd_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModem3gppUssdSkeleton</FILE>
<TITLE>MmGdbusModem3gppUssdSkeleton</TITLE>
MmGdbusModem3gppUssdSkeleton
<SUBSECTION New>
mm_gdbus_modem3gpp_ussd_skeleton_new
<SUBSECTION Standard>
MmGdbusModem3gppUssdSkeletonClass
MM_GDBUS_IS_MODEM3GPP_USSD_SKELETON
MM_GDBUS_IS_MODEM3GPP_USSD_SKELETON_CLASS
MM_GDBUS_MODEM3GPP_USSD_SKELETON
MM_GDBUS_MODEM3GPP_USSD_SKELETON_CLASS
MM_GDBUS_MODEM3GPP_USSD_SKELETON_GET_CLASS
MM_GDBUS_TYPE_MODEM3GPP_USSD_SKELETON
MmGdbusModem3gppUssdSkeletonPrivate
mm_gdbus_modem3gpp_ussd_skeleton_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModem</FILE>
<TITLE>MmGdbusModem</TITLE>
MmGdbusModem
MmGdbusModemIface
<SUBSECTION Getters>
mm_gdbus_modem_get_access_technologies
mm_gdbus_modem_get_bearers
mm_gdbus_modem_dup_bearers
mm_gdbus_modem_get_current_modes
mm_gdbus_modem_dup_current_modes
mm_gdbus_modem_get_current_bands
mm_gdbus_modem_dup_current_bands
mm_gdbus_modem_get_current_capabilities
mm_gdbus_modem_get_device
mm_gdbus_modem_dup_device
mm_gdbus_modem_get_device_identifier
mm_gdbus_modem_dup_device_identifier
mm_gdbus_modem_get_drivers
mm_gdbus_modem_dup_drivers
mm_gdbus_modem_get_equipment_identifier
mm_gdbus_modem_dup_equipment_identifier
mm_gdbus_modem_get_manufacturer
mm_gdbus_modem_dup_manufacturer
mm_gdbus_modem_get_max_active_bearers
mm_gdbus_modem_get_max_bearers
mm_gdbus_modem_get_model
mm_gdbus_modem_dup_model
mm_gdbus_modem_get_own_numbers
mm_gdbus_modem_dup_own_numbers
mm_gdbus_modem_get_plugin
mm_gdbus_modem_dup_plugin
mm_gdbus_modem_get_power_state
mm_gdbus_modem_get_primary_port
mm_gdbus_modem_dup_primary_port
mm_gdbus_modem_get_ports
mm_gdbus_modem_dup_ports
mm_gdbus_modem_get_revision
mm_gdbus_modem_dup_revision
mm_gdbus_modem_get_carrier_configuration
mm_gdbus_modem_dup_carrier_configuration
mm_gdbus_modem_get_carrier_configuration_revision
mm_gdbus_modem_dup_carrier_configuration_revision
mm_gdbus_modem_get_hardware_revision
mm_gdbus_modem_dup_hardware_revision
mm_gdbus_modem_get_signal_quality
mm_gdbus_modem_dup_signal_quality
mm_gdbus_modem_get_sim
mm_gdbus_modem_dup_sim
mm_gdbus_modem_get_supported_capabilities
mm_gdbus_modem_dup_supported_capabilities
mm_gdbus_modem_get_state
mm_gdbus_modem_get_state_failed_reason
mm_gdbus_modem_get_supported_bands
mm_gdbus_modem_dup_supported_bands
mm_gdbus_modem_get_supported_ip_families
mm_gdbus_modem_get_supported_modes
mm_gdbus_modem_dup_supported_modes
mm_gdbus_modem_get_unlock_required
mm_gdbus_modem_get_unlock_retries
mm_gdbus_modem_dup_unlock_retries
<SUBSECTION Methods>
mm_gdbus_modem_call_enable
mm_gdbus_modem_call_enable_finish
mm_gdbus_modem_call_enable_sync
mm_gdbus_modem_call_set_power_state
mm_gdbus_modem_call_set_power_state_finish
mm_gdbus_modem_call_set_power_state_sync
mm_gdbus_modem_call_create_bearer
mm_gdbus_modem_call_create_bearer_finish
mm_gdbus_modem_call_create_bearer_sync
mm_gdbus_modem_call_delete_bearer
mm_gdbus_modem_call_delete_bearer_finish
mm_gdbus_modem_call_delete_bearer_sync
mm_gdbus_modem_call_list_bearers
mm_gdbus_modem_call_list_bearers_finish
mm_gdbus_modem_call_list_bearers_sync
mm_gdbus_modem_call_reset
mm_gdbus_modem_call_reset_finish
mm_gdbus_modem_call_reset_sync
mm_gdbus_modem_call_factory_reset
mm_gdbus_modem_call_factory_reset_finish
mm_gdbus_modem_call_factory_reset_sync
mm_gdbus_modem_call_set_current_modes
mm_gdbus_modem_call_set_current_modes_finish
mm_gdbus_modem_call_set_current_modes_sync
mm_gdbus_modem_call_set_current_bands
mm_gdbus_modem_call_set_current_bands_finish
mm_gdbus_modem_call_set_current_bands_sync
mm_gdbus_modem_call_set_current_capabilities
mm_gdbus_modem_call_set_current_capabilities_finish
mm_gdbus_modem_call_set_current_capabilities_sync
mm_gdbus_modem_call_command
mm_gdbus_modem_call_command_finish
mm_gdbus_modem_call_command_sync
<SUBSECTION Private>
mm_gdbus_modem_set_access_technologies
mm_gdbus_modem_set_bearers
mm_gdbus_modem_set_current_modes
mm_gdbus_modem_set_current_bands
mm_gdbus_modem_set_current_capabilities
mm_gdbus_modem_set_device
mm_gdbus_modem_set_device_identifier
mm_gdbus_modem_set_drivers
mm_gdbus_modem_set_equipment_identifier
mm_gdbus_modem_set_manufacturer
mm_gdbus_modem_set_max_active_bearers
mm_gdbus_modem_set_max_bearers
mm_gdbus_modem_set_model
mm_gdbus_modem_set_own_numbers
mm_gdbus_modem_set_plugin
mm_gdbus_modem_set_primary_port
mm_gdbus_modem_set_ports
mm_gdbus_modem_set_revision
mm_gdbus_modem_set_carrier_configuration
mm_gdbus_modem_set_carrier_configuration_revision
mm_gdbus_modem_set_hardware_revision
mm_gdbus_modem_set_signal_quality
mm_gdbus_modem_set_sim
mm_gdbus_modem_set_supported_capabilities
mm_gdbus_modem_set_state
mm_gdbus_modem_set_state_failed_reason
mm_gdbus_modem_set_power_state
mm_gdbus_modem_set_supported_bands
mm_gdbus_modem_set_supported_ip_families
mm_gdbus_modem_set_supported_modes
mm_gdbus_modem_set_unlock_required
mm_gdbus_modem_set_unlock_retries
mm_gdbus_modem_emit_state_changed
mm_gdbus_modem_complete_command
mm_gdbus_modem_complete_create_bearer
mm_gdbus_modem_complete_delete_bearer
mm_gdbus_modem_complete_enable
mm_gdbus_modem_complete_set_power_state
mm_gdbus_modem_complete_factory_reset
mm_gdbus_modem_complete_list_bearers
mm_gdbus_modem_complete_reset
mm_gdbus_modem_complete_set_current_modes
mm_gdbus_modem_complete_set_current_bands
mm_gdbus_modem_complete_set_current_capabilities
mm_gdbus_modem_interface_info
mm_gdbus_modem_override_properties
<SUBSECTION Standard>
MM_GDBUS_IS_MODEM
MM_GDBUS_MODEM
MM_GDBUS_MODEM_GET_IFACE
MM_GDBUS_TYPE_MODEM
mm_gdbus_modem_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemProxy</FILE>
<TITLE>MmGdbusModemProxy</TITLE>
MmGdbusModemProxy
<SUBSECTION New>
mm_gdbus_modem_proxy_new
mm_gdbus_modem_proxy_new_finish
mm_gdbus_modem_proxy_new_for_bus
mm_gdbus_modem_proxy_new_for_bus_finish
mm_gdbus_modem_proxy_new_for_bus_sync
mm_gdbus_modem_proxy_new_sync
<SUBSECTION Standard>
MmGdbusModemProxyClass
MM_GDBUS_IS_MODEM_PROXY
MM_GDBUS_IS_MODEM_PROXY_CLASS
MM_GDBUS_MODEM_PROXY
MM_GDBUS_MODEM_PROXY_CLASS
MM_GDBUS_MODEM_PROXY_GET_CLASS
MM_GDBUS_TYPE_MODEM_PROXY
MmGdbusModemProxyPrivate
mm_gdbus_modem_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemSkeleton</FILE>
<TITLE>MmGdbusModemSkeleton</TITLE>
MmGdbusModemSkeleton
<SUBSECTION New>
mm_gdbus_modem_skeleton_new
<SUBSECTION Standard>
MmGdbusModemSkeletonClass
MM_GDBUS_IS_MODEM_SKELETON
MM_GDBUS_IS_MODEM_SKELETON_CLASS
MM_GDBUS_MODEM_SKELETON
MM_GDBUS_MODEM_SKELETON_CLASS
MM_GDBUS_MODEM_SKELETON_GET_CLASS
MM_GDBUS_TYPE_MODEM_SKELETON
MmGdbusModemSkeletonPrivate
mm_gdbus_modem_skeleton_get_type
</SECTION>


<SECTION>
<FILE>MmGdbusModemCdma</FILE>
<TITLE>MmGdbusModemCdma</TITLE>
MmGdbusModemCdma
MmGdbusModemCdmaIface
<SUBSECTION Getters>
mm_gdbus_modem_cdma_get_cdma1x_registration_state
mm_gdbus_modem_cdma_get_evdo_registration_state
mm_gdbus_modem_cdma_get_activation_state
mm_gdbus_modem_cdma_get_esn
mm_gdbus_modem_cdma_dup_esn
mm_gdbus_modem_cdma_get_meid
mm_gdbus_modem_cdma_dup_meid
mm_gdbus_modem_cdma_get_nid
mm_gdbus_modem_cdma_get_sid
<SUBSECTION Methods>
mm_gdbus_modem_cdma_call_activate
mm_gdbus_modem_cdma_call_activate_finish
mm_gdbus_modem_cdma_call_activate_sync
mm_gdbus_modem_cdma_call_activate_manual
mm_gdbus_modem_cdma_call_activate_manual_finish
mm_gdbus_modem_cdma_call_activate_manual_sync
<SUBSECTION Private>
mm_gdbus_modem_cdma_set_activation_state
mm_gdbus_modem_cdma_set_cdma1x_registration_state
mm_gdbus_modem_cdma_set_esn
mm_gdbus_modem_cdma_set_evdo_registration_state
mm_gdbus_modem_cdma_set_meid
mm_gdbus_modem_cdma_set_nid
mm_gdbus_modem_cdma_set_sid
mm_gdbus_modem_cdma_emit_activation_state_changed
mm_gdbus_modem_cdma_complete_activate
mm_gdbus_modem_cdma_complete_activate_manual
mm_gdbus_modem_cdma_interface_info
mm_gdbus_modem_cdma_override_properties
<SUBSECTION Standard>
MM_GDBUS_IS_MODEM_CDMA
MM_GDBUS_MODEM_CDMA
MM_GDBUS_MODEM_CDMA_GET_IFACE
MM_GDBUS_TYPE_MODEM_CDMA
mm_gdbus_modem_cdma_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemCdmaProxy</FILE>
<TITLE>MmGdbusModemCdmaProxy</TITLE>
MmGdbusModemCdmaProxy
<SUBSECTION New>
mm_gdbus_modem_cdma_proxy_new
mm_gdbus_modem_cdma_proxy_new_finish
mm_gdbus_modem_cdma_proxy_new_for_bus
mm_gdbus_modem_cdma_proxy_new_for_bus_finish
mm_gdbus_modem_cdma_proxy_new_for_bus_sync
mm_gdbus_modem_cdma_proxy_new_sync
<SUBSECTION Standard>
MmGdbusModemCdmaProxyClass
MM_GDBUS_IS_MODEM_CDMA_PROXY
MM_GDBUS_IS_MODEM_CDMA_PROXY_CLASS
MM_GDBUS_MODEM_CDMA_PROXY
MM_GDBUS_MODEM_CDMA_PROXY_CLASS
MM_GDBUS_MODEM_CDMA_PROXY_GET_CLASS
MM_GDBUS_TYPE_MODEM_CDMA_PROXY
MmGdbusModemCdmaProxyPrivate
mm_gdbus_modem_cdma_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemCdmaSkeleton</FILE>
<TITLE>MmGdbusModemCdmaSkeleton</TITLE>
MmGdbusModemCdmaSkeleton
<SUBSECTION New>
mm_gdbus_modem_cdma_skeleton_new
<SUBSECTION Standard>
MmGdbusModemCdmaSkeletonClass
MM_GDBUS_IS_MODEM_CDMA_SKELETON
MM_GDBUS_IS_MODEM_CDMA_SKELETON_CLASS
MM_GDBUS_MODEM_CDMA_SKELETON
MM_GDBUS_MODEM_CDMA_SKELETON_CLASS
MM_GDBUS_MODEM_CDMA_SKELETON_GET_CLASS
MM_GDBUS_TYPE_MODEM_CDMA_SKELETON
MmGdbusModemCdmaSkeletonPrivate
mm_gdbus_modem_cdma_skeleton_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemLocation</FILE>
<TITLE>MmGdbusModemLocation</TITLE>
MmGdbusModemLocation
MmGdbusModemLocationIface
<SUBSECTION Getters>
mm_gdbus_modem_location_get_enabled
mm_gdbus_modem_location_get_capabilities
mm_gdbus_modem_location_get_signals_location
mm_gdbus_modem_location_get_location
mm_gdbus_modem_location_dup_location
mm_gdbus_modem_location_dup_supl_server
mm_gdbus_modem_location_get_supl_server
mm_gdbus_modem_location_get_gps_refresh_rate
mm_gdbus_modem_location_get_supported_assistance_data
mm_gdbus_modem_location_dup_assistance_data_servers
mm_gdbus_modem_location_get_assistance_data_servers
<SUBSECTION Methods>
mm_gdbus_modem_location_call_get_location
mm_gdbus_modem_location_call_get_location_finish
mm_gdbus_modem_location_call_get_location_sync
mm_gdbus_modem_location_call_setup
mm_gdbus_modem_location_call_setup_finish
mm_gdbus_modem_location_call_setup_sync
mm_gdbus_modem_location_call_set_supl_server
mm_gdbus_modem_location_call_set_supl_server_finish
mm_gdbus_modem_location_call_set_supl_server_sync
mm_gdbus_modem_location_call_inject_assistance_data
mm_gdbus_modem_location_call_inject_assistance_data_finish
mm_gdbus_modem_location_call_inject_assistance_data_sync
mm_gdbus_modem_location_call_set_gps_refresh_rate
mm_gdbus_modem_location_call_set_gps_refresh_rate_finish
mm_gdbus_modem_location_call_set_gps_refresh_rate_sync
<SUBSECTION Private>
mm_gdbus_modem_location_set_capabilities
mm_gdbus_modem_location_set_enabled
mm_gdbus_modem_location_set_location
mm_gdbus_modem_location_set_signals_location
mm_gdbus_modem_location_set_supl_server
mm_gdbus_modem_location_set_supported_assistance_data
mm_gdbus_modem_location_set_gps_refresh_rate
mm_gdbus_modem_location_set_assistance_data_servers
mm_gdbus_modem_location_complete_get_location
mm_gdbus_modem_location_complete_setup
mm_gdbus_modem_location_complete_set_supl_server
mm_gdbus_modem_location_complete_inject_assistance_data
mm_gdbus_modem_location_complete_set_gps_refresh_rate
mm_gdbus_modem_location_interface_info
mm_gdbus_modem_location_override_properties
<SUBSECTION Standard>
MM_GDBUS_IS_MODEM_LOCATION
MM_GDBUS_MODEM_LOCATION
MM_GDBUS_MODEM_LOCATION_GET_IFACE
MM_GDBUS_TYPE_MODEM_LOCATION
mm_gdbus_modem_location_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemLocationProxy</FILE>
<TITLE>MmGdbusModemLocationProxy</TITLE>
MmGdbusModemLocationProxy
<SUBSECTION New>
mm_gdbus_modem_location_proxy_new
mm_gdbus_modem_location_proxy_new_finish
mm_gdbus_modem_location_proxy_new_for_bus
mm_gdbus_modem_location_proxy_new_for_bus_finish
mm_gdbus_modem_location_proxy_new_for_bus_sync
mm_gdbus_modem_location_proxy_new_sync
<SUBSECTION Standard>
MmGdbusModemLocationProxyClass
MM_GDBUS_IS_MODEM_LOCATION_PROXY
MM_GDBUS_IS_MODEM_LOCATION_PROXY_CLASS
MM_GDBUS_MODEM_LOCATION_PROXY
MM_GDBUS_MODEM_LOCATION_PROXY_CLASS
MM_GDBUS_MODEM_LOCATION_PROXY_GET_CLASS
MM_GDBUS_TYPE_MODEM_LOCATION_PROXY
MmGdbusModemLocationProxyPrivate
mm_gdbus_modem_location_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemLocationSkeleton</FILE>
<TITLE>MmGdbusModemLocationSkeleton</TITLE>
MmGdbusModemLocationSkeleton
<SUBSECTION New>
mm_gdbus_modem_location_skeleton_new
<SUBSECTION Standard>
MmGdbusModemLocationSkeletonClass
MM_GDBUS_IS_MODEM_LOCATION_SKELETON
MM_GDBUS_IS_MODEM_LOCATION_SKELETON_CLASS
MM_GDBUS_MODEM_LOCATION_SKELETON
MM_GDBUS_MODEM_LOCATION_SKELETON_CLASS
MM_GDBUS_MODEM_LOCATION_SKELETON_GET_CLASS
MM_GDBUS_TYPE_MODEM_LOCATION_SKELETON
MmGdbusModemLocationSkeletonPrivate
mm_gdbus_modem_location_skeleton_get_type
</SECTION>


<SECTION>
<FILE>MmGdbusModemMessaging</FILE>
<TITLE>MmGdbusModemMessaging</TITLE>
MmGdbusModemMessaging
MmGdbusModemMessagingIface
<SUBSECTION Getters>
mm_gdbus_modem_messaging_get_messages
mm_gdbus_modem_messaging_dup_messages
mm_gdbus_modem_messaging_get_supported_storages
mm_gdbus_modem_messaging_dup_supported_storages
mm_gdbus_modem_messaging_get_default_storage
<SUBSECTION Methods>
mm_gdbus_modem_messaging_call_create
mm_gdbus_modem_messaging_call_create_finish
mm_gdbus_modem_messaging_call_create_sync
mm_gdbus_modem_messaging_call_delete
mm_gdbus_modem_messaging_call_delete_finish
mm_gdbus_modem_messaging_call_delete_sync
mm_gdbus_modem_messaging_call_list
mm_gdbus_modem_messaging_call_list_finish
mm_gdbus_modem_messaging_call_list_sync
<SUBSECTION Private>
mm_gdbus_modem_messaging_set_messages
mm_gdbus_modem_messaging_set_default_storage
mm_gdbus_modem_messaging_set_supported_storages
mm_gdbus_modem_messaging_emit_added
mm_gdbus_modem_messaging_emit_deleted
mm_gdbus_modem_messaging_complete_create
mm_gdbus_modem_messaging_complete_delete
mm_gdbus_modem_messaging_complete_list
mm_gdbus_modem_messaging_interface_info
mm_gdbus_modem_messaging_override_properties
<SUBSECTION Standard>
MM_GDBUS_IS_MODEM_MESSAGING
MM_GDBUS_MODEM_MESSAGING
MM_GDBUS_MODEM_MESSAGING_GET_IFACE
MM_GDBUS_TYPE_MODEM_MESSAGING
mm_gdbus_modem_messaging_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemMessagingProxy</FILE>
<TITLE>MmGdbusModemMessagingProxy</TITLE>
MmGdbusModemMessagingProxy
<SUBSECTION New>
mm_gdbus_modem_messaging_proxy_new
mm_gdbus_modem_messaging_proxy_new_finish
mm_gdbus_modem_messaging_proxy_new_for_bus
mm_gdbus_modem_messaging_proxy_new_for_bus_finish
mm_gdbus_modem_messaging_proxy_new_for_bus_sync
mm_gdbus_modem_messaging_proxy_new_sync
<SUBSECTION Standard>
MmGdbusModemMessagingProxyClass
MM_GDBUS_IS_MODEM_MESSAGING_PROXY
MM_GDBUS_IS_MODEM_MESSAGING_PROXY_CLASS
MM_GDBUS_MODEM_MESSAGING_PROXY
MM_GDBUS_MODEM_MESSAGING_PROXY_CLASS
MM_GDBUS_MODEM_MESSAGING_PROXY_GET_CLASS
MM_GDBUS_TYPE_MODEM_MESSAGING_PROXY
MmGdbusModemMessagingProxyPrivate
mm_gdbus_modem_messaging_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemMessagingSkeleton</FILE>
<TITLE>MmGdbusModemMessagingSkeleton</TITLE>
MmGdbusModemMessagingSkeleton
<SUBSECTION New>
mm_gdbus_modem_messaging_skeleton_new
<SUBSECTION Standard>
MmGdbusModemMessagingSkeletonClass
MM_GDBUS_IS_MODEM_MESSAGING_SKELETON
MM_GDBUS_IS_MODEM_MESSAGING_SKELETON_CLASS
MM_GDBUS_MODEM_MESSAGING_SKELETON
MM_GDBUS_MODEM_MESSAGING_SKELETON_CLASS
MM_GDBUS_MODEM_MESSAGING_SKELETON_GET_CLASS
MM_GDBUS_TYPE_MODEM_MESSAGING_SKELETON
MmGdbusModemMessagingSkeletonPrivate
mm_gdbus_modem_messaging_skeleton_get_type
</SECTION>


<SECTION>
<FILE>MmGdbusModemTime</FILE>
<TITLE>MmGdbusModemTime</TITLE>
MmGdbusModemTime
MmGdbusModemTimeIface
<SUBSECTION Getters>
mm_gdbus_modem_time_get_network_timezone
mm_gdbus_modem_time_dup_network_timezone
<SUBSECTION Methods>
mm_gdbus_modem_time_call_get_network_time
mm_gdbus_modem_time_call_get_network_time_finish
mm_gdbus_modem_time_call_get_network_time_sync
<SUBSECTION Private>
mm_gdbus_modem_time_set_network_timezone
mm_gdbus_modem_time_emit_network_time_changed
mm_gdbus_modem_time_complete_get_network_time
mm_gdbus_modem_time_interface_info
mm_gdbus_modem_time_override_properties
<SUBSECTION Standard>
MM_GDBUS_IS_MODEM_TIME
MM_GDBUS_MODEM_TIME
MM_GDBUS_MODEM_TIME_GET_IFACE
MM_GDBUS_TYPE_MODEM_TIME
mm_gdbus_modem_time_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemTimeProxy</FILE>
<TITLE>MmGdbusModemTimeProxy</TITLE>
MmGdbusModemTimeProxy
<SUBSECTION New>
mm_gdbus_modem_time_proxy_new
mm_gdbus_modem_time_proxy_new_finish
mm_gdbus_modem_time_proxy_new_for_bus
mm_gdbus_modem_time_proxy_new_for_bus_finish
mm_gdbus_modem_time_proxy_new_for_bus_sync
mm_gdbus_modem_time_proxy_new_sync
<SUBSECTION Standard>
MmGdbusModemTimeProxyClass
MM_GDBUS_IS_MODEM_TIME_PROXY
MM_GDBUS_IS_MODEM_TIME_PROXY_CLASS
MM_GDBUS_MODEM_TIME_PROXY
MM_GDBUS_MODEM_TIME_PROXY_CLASS
MM_GDBUS_MODEM_TIME_PROXY_GET_CLASS
MM_GDBUS_TYPE_MODEM_TIME_PROXY
MmGdbusModemTimeProxyPrivate
mm_gdbus_modem_time_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemTimeSkeleton</FILE>
<TITLE>MmGdbusModemTimeSkeleton</TITLE>
MmGdbusModemTimeSkeleton
<SUBSECTION New>
mm_gdbus_modem_time_skeleton_new
<SUBSECTION Standard>
MmGdbusModemTimeSkeletonClass
MM_GDBUS_IS_MODEM_TIME_SKELETON
MM_GDBUS_IS_MODEM_TIME_SKELETON_CLASS
MM_GDBUS_MODEM_TIME_SKELETON
MM_GDBUS_MODEM_TIME_SKELETON_CLASS
MM_GDBUS_MODEM_TIME_SKELETON_GET_CLASS
MM_GDBUS_TYPE_MODEM_TIME_SKELETON
MmGdbusModemTimeSkeletonPrivate
mm_gdbus_modem_time_skeleton_get_type
</SECTION>


<SECTION>
<FILE>MmGdbusModemFirmware</FILE>
<TITLE>MmGdbusModemFirmware</TITLE>
MmGdbusModemFirmware
MmGdbusModemFirmwareIface
<SUBSECTION Getters>
mm_gdbus_modem_firmware_dup_update_settings
mm_gdbus_modem_firmware_get_update_settings
<SUBSECTION Methods>
mm_gdbus_modem_firmware_call_list
mm_gdbus_modem_firmware_call_list_finish
mm_gdbus_modem_firmware_call_list_sync
mm_gdbus_modem_firmware_call_select
mm_gdbus_modem_firmware_call_select_finish
mm_gdbus_modem_firmware_call_select_sync
<SUBSECTION Private>
mm_gdbus_modem_firmware_set_update_settings
mm_gdbus_modem_firmware_complete_list
mm_gdbus_modem_firmware_complete_select
mm_gdbus_modem_firmware_interface_info
mm_gdbus_modem_firmware_override_properties
<SUBSECTION Standard>
MM_GDBUS_IS_MODEM_FIRMWARE
MM_GDBUS_MODEM_FIRMWARE
MM_GDBUS_MODEM_FIRMWARE_GET_IFACE
MM_GDBUS_TYPE_MODEM_FIRMWARE
mm_gdbus_modem_firmware_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemFirmwareProxy</FILE>
<TITLE>MmGdbusModemFirmwareProxy</TITLE>
MmGdbusModemFirmwareProxy
<SUBSECTION New>
mm_gdbus_modem_firmware_proxy_new
mm_gdbus_modem_firmware_proxy_new_finish
mm_gdbus_modem_firmware_proxy_new_for_bus
mm_gdbus_modem_firmware_proxy_new_for_bus_finish
mm_gdbus_modem_firmware_proxy_new_for_bus_sync
mm_gdbus_modem_firmware_proxy_new_sync
<SUBSECTION Standard>
MmGdbusModemFirmwareProxyClass
MM_GDBUS_IS_MODEM_FIRMWARE_PROXY
MM_GDBUS_IS_MODEM_FIRMWARE_PROXY_CLASS
MM_GDBUS_MODEM_FIRMWARE_PROXY
MM_GDBUS_MODEM_FIRMWARE_PROXY_CLASS
MM_GDBUS_MODEM_FIRMWARE_PROXY_GET_CLASS
MM_GDBUS_TYPE_MODEM_FIRMWARE_PROXY
MmGdbusModemFirmwareProxyPrivate
mm_gdbus_modem_firmware_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemFirmwareSkeleton</FILE>
<TITLE>MmGdbusModemFirmwareSkeleton</TITLE>
MmGdbusModemFirmwareSkeleton
<SUBSECTION New>
mm_gdbus_modem_firmware_skeleton_new
<SUBSECTION Standard>
MmGdbusModemFirmwareSkeletonClass
MM_GDBUS_IS_MODEM_FIRMWARE_SKELETON
MM_GDBUS_IS_MODEM_FIRMWARE_SKELETON_CLASS
MM_GDBUS_MODEM_FIRMWARE_SKELETON
MM_GDBUS_MODEM_FIRMWARE_SKELETON_CLASS
MM_GDBUS_MODEM_FIRMWARE_SKELETON_GET_CLASS
MM_GDBUS_TYPE_MODEM_FIRMWARE_SKELETON
MmGdbusModemFirmwareSkeletonPrivate
mm_gdbus_modem_firmware_skeleton_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemOma</FILE>
<TITLE>MmGdbusModemOma</TITLE>
MmGdbusModemOma
MmGdbusModemOmaIface
<SUBSECTION Getters>
<SUBSECTION Methods>
mm_gdbus_modem_oma_call_accept_network_initiated_session
mm_gdbus_modem_oma_call_accept_network_initiated_session_finish
mm_gdbus_modem_oma_call_accept_network_initiated_session_sync
mm_gdbus_modem_oma_call_cancel_session
mm_gdbus_modem_oma_call_cancel_session_finish
mm_gdbus_modem_oma_call_cancel_session_sync
mm_gdbus_modem_oma_call_setup
mm_gdbus_modem_oma_call_setup_finish
mm_gdbus_modem_oma_call_setup_sync
mm_gdbus_modem_oma_call_start_client_initiated_session
mm_gdbus_modem_oma_call_start_client_initiated_session_finish
mm_gdbus_modem_oma_call_start_client_initiated_session_sync
mm_gdbus_modem_oma_get_features
mm_gdbus_modem_oma_get_session_state
mm_gdbus_modem_oma_get_session_type
mm_gdbus_modem_oma_get_pending_network_initiated_sessions
mm_gdbus_modem_oma_dup_pending_network_initiated_sessions
<SUBSECTION Private>
mm_gdbus_modem_oma_set_pending_network_initiated_sessions
mm_gdbus_modem_oma_set_features
mm_gdbus_modem_oma_set_session_state
mm_gdbus_modem_oma_set_session_type
mm_gdbus_modem_oma_emit_session_state_changed
mm_gdbus_modem_oma_complete_accept_network_initiated_session
mm_gdbus_modem_oma_complete_cancel_session
mm_gdbus_modem_oma_complete_setup
mm_gdbus_modem_oma_complete_start_client_initiated_session
mm_gdbus_modem_oma_interface_info
mm_gdbus_modem_oma_override_properties
<SUBSECTION Standard>
MM_GDBUS_IS_MODEM_OMA
MM_GDBUS_MODEM_OMA
MM_GDBUS_MODEM_OMA_GET_IFACE
MM_GDBUS_TYPE_MODEM_OMA
mm_gdbus_modem_oma_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemOmaProxy</FILE>
<TITLE>MmGdbusModemOmaProxy</TITLE>
MmGdbusModemOmaProxy
<SUBSECTION New>
mm_gdbus_modem_oma_proxy_new
mm_gdbus_modem_oma_proxy_new_finish
mm_gdbus_modem_oma_proxy_new_for_bus
mm_gdbus_modem_oma_proxy_new_for_bus_finish
mm_gdbus_modem_oma_proxy_new_for_bus_sync
mm_gdbus_modem_oma_proxy_new_sync
<SUBSECTION Standard>
MmGdbusModemOmaProxyClass
MM_GDBUS_IS_MODEM_OMA_PROXY
MM_GDBUS_IS_MODEM_OMA_PROXY_CLASS
MM_GDBUS_MODEM_OMA_PROXY
MM_GDBUS_MODEM_OMA_PROXY_CLASS
MM_GDBUS_MODEM_OMA_PROXY_GET_CLASS
MM_GDBUS_TYPE_MODEM_OMA_PROXY
MmGdbusModemOmaProxyPrivate
mm_gdbus_modem_oma_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemOmaSkeleton</FILE>
<TITLE>MmGdbusModemOmaSkeleton</TITLE>
MmGdbusModemOmaSkeleton
<SUBSECTION New>
mm_gdbus_modem_oma_skeleton_new
<SUBSECTION Standard>
MmGdbusModemOmaSkeletonClass
MM_GDBUS_IS_MODEM_OMA_SKELETON
MM_GDBUS_IS_MODEM_OMA_SKELETON_CLASS
MM_GDBUS_MODEM_OMA_SKELETON
MM_GDBUS_MODEM_OMA_SKELETON_CLASS
MM_GDBUS_MODEM_OMA_SKELETON_GET_CLASS
MM_GDBUS_TYPE_MODEM_OMA_SKELETON
MmGdbusModemOmaSkeletonPrivate
mm_gdbus_modem_oma_skeleton_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemSimple</FILE>
<TITLE>MmGdbusModemSimple</TITLE>
MmGdbusModemSimple
MmGdbusModemSimpleIface
<SUBSECTION Methods>
mm_gdbus_modem_simple_call_connect
mm_gdbus_modem_simple_call_connect_finish
mm_gdbus_modem_simple_call_connect_sync
mm_gdbus_modem_simple_call_disconnect
mm_gdbus_modem_simple_call_disconnect_finish
mm_gdbus_modem_simple_call_disconnect_sync
mm_gdbus_modem_simple_call_get_status
mm_gdbus_modem_simple_call_get_status_finish
mm_gdbus_modem_simple_call_get_status_sync
<SUBSECTION Private>
mm_gdbus_modem_simple_complete_connect
mm_gdbus_modem_simple_complete_disconnect
mm_gdbus_modem_simple_complete_get_status
mm_gdbus_modem_simple_interface_info
mm_gdbus_modem_simple_override_properties
<SUBSECTION Standard>
MM_GDBUS_IS_MODEM_SIMPLE
MM_GDBUS_MODEM_SIMPLE
MM_GDBUS_MODEM_SIMPLE_GET_IFACE
MM_GDBUS_TYPE_MODEM_SIMPLE
mm_gdbus_modem_simple_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemSimpleProxy</FILE>
<TITLE>MmGdbusModemSimpleProxy</TITLE>
MmGdbusModemSimpleProxy
<SUBSECTION New>
mm_gdbus_modem_simple_proxy_new
mm_gdbus_modem_simple_proxy_new_finish
mm_gdbus_modem_simple_proxy_new_for_bus
mm_gdbus_modem_simple_proxy_new_for_bus_finish
mm_gdbus_modem_simple_proxy_new_for_bus_sync
mm_gdbus_modem_simple_proxy_new_sync
<SUBSECTION Standard>
MmGdbusModemSimpleProxyClass
MM_GDBUS_IS_MODEM_SIMPLE_PROXY
MM_GDBUS_IS_MODEM_SIMPLE_PROXY_CLASS
MM_GDBUS_MODEM_SIMPLE_PROXY
MM_GDBUS_MODEM_SIMPLE_PROXY_CLASS
MM_GDBUS_MODEM_SIMPLE_PROXY_GET_CLASS
MM_GDBUS_TYPE_MODEM_SIMPLE_PROXY
MmGdbusModemSimpleProxyPrivate
mm_gdbus_modem_simple_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemSimpleSkeleton</FILE>
<TITLE>MmGdbusModemSimpleSkeleton</TITLE>
MmGdbusModemSimpleSkeleton
<SUBSECTION New>
mm_gdbus_modem_simple_skeleton_new
<SUBSECTION Standard>
MmGdbusModemSimpleSkeletonClass
MM_GDBUS_IS_MODEM_SIMPLE_SKELETON
MM_GDBUS_IS_MODEM_SIMPLE_SKELETON_CLASS
MM_GDBUS_MODEM_SIMPLE_SKELETON
MM_GDBUS_MODEM_SIMPLE_SKELETON_CLASS
MM_GDBUS_MODEM_SIMPLE_SKELETON_GET_CLASS
MM_GDBUS_TYPE_MODEM_SIMPLE_SKELETON
MmGdbusModemSimpleSkeletonPrivate
mm_gdbus_modem_simple_skeleton_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemSignal</FILE>
<TITLE>MmGdbusModemSignal</TITLE>
MmGdbusModemSignal
MmGdbusModemSignalIface
<SUBSECTION Getters>
mm_gdbus_modem_signal_get_rate
mm_gdbus_modem_signal_get_cdma
mm_gdbus_modem_signal_get_evdo
mm_gdbus_modem_signal_get_gsm
mm_gdbus_modem_signal_get_umts
mm_gdbus_modem_signal_get_lte
mm_gdbus_modem_signal_dup_cdma
mm_gdbus_modem_signal_dup_evdo
mm_gdbus_modem_signal_dup_gsm
mm_gdbus_modem_signal_dup_umts
mm_gdbus_modem_signal_dup_lte
<SUBSECTION Methods>
mm_gdbus_modem_signal_call_setup
mm_gdbus_modem_signal_call_setup_finish
mm_gdbus_modem_signal_call_setup_sync
<SUBSECTION Private>
mm_gdbus_modem_signal_set_cdma
mm_gdbus_modem_signal_set_evdo
mm_gdbus_modem_signal_set_gsm
mm_gdbus_modem_signal_set_lte
mm_gdbus_modem_signal_set_rate
mm_gdbus_modem_signal_set_umts
mm_gdbus_modem_signal_complete_setup
mm_gdbus_modem_signal_interface_info
mm_gdbus_modem_signal_override_properties
<SUBSECTION Standard>
MM_GDBUS_IS_MODEM_SIGNAL
MM_GDBUS_MODEM_SIGNAL
MM_GDBUS_MODEM_SIGNAL_GET_IFACE
MM_GDBUS_TYPE_MODEM_SIGNAL
mm_gdbus_modem_signal_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemSignalProxy</FILE>
<TITLE>MmGdbusModemSignalProxy</TITLE>
MmGdbusModemSignalProxy
<SUBSECTION New>
mm_gdbus_modem_signal_proxy_new
mm_gdbus_modem_signal_proxy_new_finish
mm_gdbus_modem_signal_proxy_new_for_bus
mm_gdbus_modem_signal_proxy_new_for_bus_finish
mm_gdbus_modem_signal_proxy_new_for_bus_sync
mm_gdbus_modem_signal_proxy_new_sync
<SUBSECTION Standard>
MmGdbusModemSignalProxyClass
MM_GDBUS_IS_MODEM_SIGNAL_PROXY
MM_GDBUS_IS_MODEM_SIGNAL_PROXY_CLASS
MM_GDBUS_MODEM_SIGNAL_PROXY
MM_GDBUS_MODEM_SIGNAL_PROXY_CLASS
MM_GDBUS_MODEM_SIGNAL_PROXY_GET_CLASS
MM_GDBUS_TYPE_MODEM_SIGNAL_PROXY
MmGdbusModemSignalProxyPrivate
mm_gdbus_modem_signal_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemSignalSkeleton</FILE>
<TITLE>MmGdbusModemSignalSkeleton</TITLE>
MmGdbusModemSignalSkeleton
<SUBSECTION New>
mm_gdbus_modem_signal_skeleton_new
<SUBSECTION Standard>
MmGdbusModemSignalSkeletonClass
MM_GDBUS_IS_MODEM_SIGNAL_SKELETON
MM_GDBUS_IS_MODEM_SIGNAL_SKELETON_CLASS
MM_GDBUS_MODEM_SIGNAL_SKELETON
MM_GDBUS_MODEM_SIGNAL_SKELETON_CLASS
MM_GDBUS_MODEM_SIGNAL_SKELETON_GET_CLASS
MM_GDBUS_TYPE_MODEM_SIGNAL_SKELETON
MmGdbusModemSignalSkeletonPrivate
mm_gdbus_modem_signal_skeleton_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemVoice</FILE>
<TITLE>MmGdbusModemVoice</TITLE>
MmGdbusModemVoice
MmGdbusModemVoiceIface
<SUBSECTION Getters>
mm_gdbus_modem_voice_get_calls
mm_gdbus_modem_voice_dup_calls
mm_gdbus_modem_voice_get_emergency_only
<SUBSECTION Methods>
mm_gdbus_modem_voice_call_create_call
mm_gdbus_modem_voice_call_create_call_finish
mm_gdbus_modem_voice_call_create_call_sync
mm_gdbus_modem_voice_call_delete_call
mm_gdbus_modem_voice_call_delete_call_finish
mm_gdbus_modem_voice_call_delete_call_sync
mm_gdbus_modem_voice_call_list_calls
mm_gdbus_modem_voice_call_list_calls_finish
mm_gdbus_modem_voice_call_list_calls_sync
mm_gdbus_modem_voice_call_hangup_and_accept
mm_gdbus_modem_voice_call_hangup_and_accept_finish
mm_gdbus_modem_voice_call_hangup_and_accept_sync
mm_gdbus_modem_voice_call_hold_and_accept
mm_gdbus_modem_voice_call_hold_and_accept_finish
mm_gdbus_modem_voice_call_hold_and_accept_sync
mm_gdbus_modem_voice_call_hangup_all
mm_gdbus_modem_voice_call_hangup_all_finish
mm_gdbus_modem_voice_call_hangup_all_sync
mm_gdbus_modem_voice_call_transfer
mm_gdbus_modem_voice_call_transfer_finish
mm_gdbus_modem_voice_call_transfer_sync
mm_gdbus_modem_voice_call_call_waiting_query
mm_gdbus_modem_voice_call_call_waiting_query_finish
mm_gdbus_modem_voice_call_call_waiting_query_sync
mm_gdbus_modem_voice_call_call_waiting_setup
mm_gdbus_modem_voice_call_call_waiting_setup_finish
mm_gdbus_modem_voice_call_call_waiting_setup_sync
<SUBSECTION Private>
mm_gdbus_modem_voice_set_calls
mm_gdbus_modem_voice_set_emergency_only
mm_gdbus_modem_voice_emit_call_added
mm_gdbus_modem_voice_emit_call_deleted
mm_gdbus_modem_voice_complete_create_call
mm_gdbus_modem_voice_complete_delete_call
mm_gdbus_modem_voice_complete_list_calls
mm_gdbus_modem_voice_complete_hangup_and_accept
mm_gdbus_modem_voice_complete_hold_and_accept
mm_gdbus_modem_voice_complete_hangup_all
mm_gdbus_modem_voice_complete_transfer
mm_gdbus_modem_voice_complete_call_waiting_query
mm_gdbus_modem_voice_complete_call_waiting_setup
mm_gdbus_modem_voice_interface_info
mm_gdbus_modem_voice_override_properties
<SUBSECTION Standard>
MM_GDBUS_IS_MODEM_VOICE
MM_GDBUS_MODEM_VOICE
MM_GDBUS_MODEM_VOICE_GET_IFACE
MM_GDBUS_TYPE_MODEM_VOICE
mm_gdbus_modem_voice_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemVoiceProxy</FILE>
<TITLE>MmGdbusModemVoiceProxy</TITLE>
MmGdbusModemVoiceProxy
<SUBSECTION New>
mm_gdbus_modem_voice_proxy_new
mm_gdbus_modem_voice_proxy_new_finish
mm_gdbus_modem_voice_proxy_new_for_bus
mm_gdbus_modem_voice_proxy_new_for_bus_finish
mm_gdbus_modem_voice_proxy_new_for_bus_sync
mm_gdbus_modem_voice_proxy_new_sync
<SUBSECTION Standard>
MmGdbusModemVoiceProxyClass
MM_GDBUS_IS_MODEM_VOICE_PROXY
MM_GDBUS_IS_MODEM_VOICE_PROXY_CLASS
MM_GDBUS_MODEM_VOICE_PROXY
MM_GDBUS_MODEM_VOICE_PROXY_CLASS
MM_GDBUS_MODEM_VOICE_PROXY_GET_CLASS
MM_GDBUS_TYPE_MODEM_VOICE_PROXY
MmGdbusModemVoiceProxyPrivate
mm_gdbus_modem_voice_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusModemVoiceSkeleton</FILE>
<TITLE>MmGdbusModemVoiceSkeleton</TITLE>
MmGdbusModemVoiceSkeleton
<SUBSECTION New>
mm_gdbus_modem_voice_skeleton_new
<SUBSECTION Standard>
MmGdbusModemVoiceSkeletonClass
MM_GDBUS_IS_MODEM_VOICE_SKELETON
MM_GDBUS_IS_MODEM_VOICE_SKELETON_CLASS
MM_GDBUS_MODEM_VOICE_SKELETON
MM_GDBUS_MODEM_VOICE_SKELETON_CLASS
MM_GDBUS_MODEM_VOICE_SKELETON_GET_CLASS
MM_GDBUS_TYPE_MODEM_VOICE_SKELETON
MmGdbusModemVoiceSkeletonPrivate
mm_gdbus_modem_voice_skeleton_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusObject</FILE>
<TITLE>MmGdbusObject</TITLE>
MmGdbusObject
MmGdbusObjectIface
<SUBSECTION Getters>
mm_gdbus_object_peek_modem
mm_gdbus_object_get_modem
mm_gdbus_object_peek_modem3gpp
mm_gdbus_object_get_modem3gpp
mm_gdbus_object_peek_modem3gpp_ussd
mm_gdbus_object_get_modem3gpp_ussd
mm_gdbus_object_peek_modem_cdma
mm_gdbus_object_get_modem_cdma
mm_gdbus_object_peek_modem_location
mm_gdbus_object_get_modem_location
mm_gdbus_object_peek_modem_messaging
mm_gdbus_object_get_modem_messaging
mm_gdbus_object_peek_modem_time
mm_gdbus_object_get_modem_time
mm_gdbus_object_peek_modem_firmware
mm_gdbus_object_get_modem_firmware
mm_gdbus_object_peek_modem_oma
mm_gdbus_object_get_modem_oma
mm_gdbus_object_peek_modem_simple
mm_gdbus_object_get_modem_simple
mm_gdbus_object_peek_modem_signal
mm_gdbus_object_get_modem_signal
mm_gdbus_object_peek_modem_voice
mm_gdbus_object_get_modem_voice
<SUBSECTION Methods>
<SUBSECTION Private>
<SUBSECTION Standard>
MM_GDBUS_IS_OBJECT
MM_GDBUS_OBJECT
MM_GDBUS_OBJECT_GET_IFACE
MM_GDBUS_TYPE_OBJECT
mm_gdbus_object_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusObjectProxy</FILE>
<TITLE>MmGdbusObjectProxy</TITLE>
MmGdbusObjectProxy
<SUBSECTION New>
mm_gdbus_object_proxy_new
<SUBSECTION Standard>
MmGdbusObjectProxyClass
MM_GDBUS_IS_OBJECT_PROXY
MM_GDBUS_IS_OBJECT_PROXY_CLASS
MM_GDBUS_OBJECT_PROXY
MM_GDBUS_OBJECT_PROXY_CLASS
MM_GDBUS_OBJECT_PROXY_GET_CLASS
MM_GDBUS_TYPE_OBJECT_PROXY
MmGdbusObjectProxyPrivate
mm_gdbus_object_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusObjectSkeleton</FILE>
<TITLE>MmGdbusObjectSkeleton</TITLE>
MmGdbusObjectSkeleton
<SUBSECTION New>
mm_gdbus_object_skeleton_new
<SUBSECTION Methods>
mm_gdbus_object_skeleton_set_modem
mm_gdbus_object_skeleton_set_modem3gpp
mm_gdbus_object_skeleton_set_modem3gpp_ussd
mm_gdbus_object_skeleton_set_modem_cdma
mm_gdbus_object_skeleton_set_modem_firmware
mm_gdbus_object_skeleton_set_modem_oma
mm_gdbus_object_skeleton_set_modem_location
mm_gdbus_object_skeleton_set_modem_messaging
mm_gdbus_object_skeleton_set_modem_simple
mm_gdbus_object_skeleton_set_modem_time
mm_gdbus_object_skeleton_set_modem_signal
mm_gdbus_object_skeleton_set_modem_voice
<SUBSECTION Standard>
MmGdbusObjectSkeletonClass
MM_GDBUS_IS_OBJECT_SKELETON
MM_GDBUS_IS_OBJECT_SKELETON_CLASS
MM_GDBUS_OBJECT_SKELETON
MM_GDBUS_OBJECT_SKELETON_CLASS
MM_GDBUS_OBJECT_SKELETON_GET_CLASS
MM_GDBUS_TYPE_OBJECT_SKELETON
MmGdbusObjectSkeletonPrivate
mm_gdbus_object_skeleton_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusObjectManagerClient</FILE>
<TITLE>MmGdbusObjectManagerClient</TITLE>
MmGdbusObjectManagerClient
<SUBSECTION New>
mm_gdbus_object_manager_client_new
mm_gdbus_object_manager_client_new_finish
mm_gdbus_object_manager_client_new_sync
mm_gdbus_object_manager_client_new_for_bus
mm_gdbus_object_manager_client_new_for_bus_finish
mm_gdbus_object_manager_client_new_for_bus_sync
<SUBSECTION Methods>
mm_gdbus_object_manager_client_get_proxy_type
<SUBSECTION Standard>
MmGdbusObjectManagerClientClass
MM_GDBUS_IS_OBJECT_MANAGER_CLIENT
MM_GDBUS_IS_OBJECT_MANAGER_CLIENT_CLASS
MM_GDBUS_OBJECT_MANAGER_CLIENT
MM_GDBUS_OBJECT_MANAGER_CLIENT_CLASS
MM_GDBUS_OBJECT_MANAGER_CLIENT_GET_CLASS
MM_GDBUS_TYPE_OBJECT_MANAGER_CLIENT
MmGdbusObjectManagerClientPrivate
mm_gdbus_object_manager_client_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusSim</FILE>
<TITLE>MmGdbusSim</TITLE>
MmGdbusSim
MmGdbusSimIface
<SUBSECTION Getters>
mm_gdbus_sim_get_imsi
mm_gdbus_sim_dup_imsi
mm_gdbus_sim_get_sim_identifier
mm_gdbus_sim_dup_sim_identifier
mm_gdbus_sim_get_operator_identifier
mm_gdbus_sim_dup_operator_identifier
mm_gdbus_sim_get_operator_name
mm_gdbus_sim_dup_operator_name
mm_gdbus_sim_get_emergency_numbers
mm_gdbus_sim_dup_emergency_numbers
<SUBSECTION Methods>
mm_gdbus_sim_call_send_pin
mm_gdbus_sim_call_send_pin_finish
mm_gdbus_sim_call_send_pin_sync
mm_gdbus_sim_call_send_puk
mm_gdbus_sim_call_send_puk_finish
mm_gdbus_sim_call_send_puk_sync
mm_gdbus_sim_call_enable_pin
mm_gdbus_sim_call_enable_pin_finish
mm_gdbus_sim_call_enable_pin_sync
mm_gdbus_sim_call_change_pin
mm_gdbus_sim_call_change_pin_finish
mm_gdbus_sim_call_change_pin_sync
<SUBSECTION Private>
mm_gdbus_sim_set_imsi
mm_gdbus_sim_set_operator_identifier
mm_gdbus_sim_set_operator_name
mm_gdbus_sim_set_sim_identifier
mm_gdbus_sim_set_emergency_numbers
mm_gdbus_sim_complete_change_pin
mm_gdbus_sim_complete_enable_pin
mm_gdbus_sim_complete_send_pin
mm_gdbus_sim_complete_send_puk
mm_gdbus_sim_interface_info
mm_gdbus_sim_override_properties
<SUBSECTION Standard>
MM_GDBUS_IS_SIM
MM_GDBUS_SIM
MM_GDBUS_SIM_GET_IFACE
MM_GDBUS_TYPE_SIM
mm_gdbus_sim_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusSimProxy</FILE>
<TITLE>MmGdbusSimProxy</TITLE>
MmGdbusSimProxy
<SUBSECTION New>
mm_gdbus_sim_proxy_new
mm_gdbus_sim_proxy_new_finish
mm_gdbus_sim_proxy_new_for_bus
mm_gdbus_sim_proxy_new_for_bus_finish
mm_gdbus_sim_proxy_new_for_bus_sync
mm_gdbus_sim_proxy_new_sync
<SUBSECTION Standard>
MmGdbusSimProxyClass
MM_GDBUS_IS_SIM_PROXY
MM_GDBUS_IS_SIM_PROXY_CLASS
MM_GDBUS_SIM_PROXY
MM_GDBUS_SIM_PROXY_CLASS
MM_GDBUS_SIM_PROXY_GET_CLASS
MM_GDBUS_TYPE_SIM_PROXY
MmGdbusSimProxyPrivate
mm_gdbus_sim_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusSimSkeleton</FILE>
<TITLE>MmGdbusSimSkeleton</TITLE>
MmGdbusSimSkeleton
<SUBSECTION New>
mm_gdbus_sim_skeleton_new
<SUBSECTION Standard>
MmGdbusSimSkeletonClass
MM_GDBUS_IS_SIM_SKELETON
MM_GDBUS_IS_SIM_SKELETON_CLASS
MM_GDBUS_SIM_SKELETON
MM_GDBUS_SIM_SKELETON_CLASS
MM_GDBUS_SIM_SKELETON_GET_CLASS
MM_GDBUS_TYPE_SIM_SKELETON
MmGdbusSimSkeletonPrivate
mm_gdbus_sim_skeleton_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusSms</FILE>
<TITLE>MmGdbusSms</TITLE>
MmGdbusSms
MmGdbusSmsIface
<SUBSECTION Getters>
mm_gdbus_sms_get_state
mm_gdbus_sms_get_pdu_type
mm_gdbus_sms_get_message_reference
mm_gdbus_sms_get_storage
mm_gdbus_sms_get_text
mm_gdbus_sms_dup_text
mm_gdbus_sms_get_data
mm_gdbus_sms_dup_data
mm_gdbus_sms_get_number
mm_gdbus_sms_dup_number
mm_gdbus_sms_get_smsc
mm_gdbus_sms_dup_smsc
mm_gdbus_sms_get_validity
mm_gdbus_sms_dup_validity
mm_gdbus_sms_get_class
mm_gdbus_sms_get_teleservice_id
mm_gdbus_sms_get_service_category
mm_gdbus_sms_get_timestamp
mm_gdbus_sms_dup_timestamp
mm_gdbus_sms_get_discharge_timestamp
mm_gdbus_sms_dup_discharge_timestamp
mm_gdbus_sms_get_delivery_report_request
mm_gdbus_sms_get_delivery_state
<SUBSECTION Methods>
mm_gdbus_sms_call_send
mm_gdbus_sms_call_send_finish
mm_gdbus_sms_call_send_sync
mm_gdbus_sms_call_store
mm_gdbus_sms_call_store_finish
mm_gdbus_sms_call_store_sync
<SUBSECTION Private>
mm_gdbus_sms_set_class
mm_gdbus_sms_set_teleservice_id
mm_gdbus_sms_set_service_category
mm_gdbus_sms_set_data
mm_gdbus_sms_set_delivery_report_request
mm_gdbus_sms_set_delivery_state
mm_gdbus_sms_set_discharge_timestamp
mm_gdbus_sms_set_message_reference
mm_gdbus_sms_set_number
mm_gdbus_sms_set_pdu_type
mm_gdbus_sms_set_smsc
mm_gdbus_sms_set_state
mm_gdbus_sms_set_storage
mm_gdbus_sms_set_text
mm_gdbus_sms_set_timestamp
mm_gdbus_sms_set_validity
mm_gdbus_sms_complete_send
mm_gdbus_sms_complete_store
mm_gdbus_sms_interface_info
mm_gdbus_sms_override_properties
<SUBSECTION Standard>
MM_GDBUS_IS_SMS
MM_GDBUS_SMS
MM_GDBUS_SMS_GET_IFACE
MM_GDBUS_TYPE_SMS
mm_gdbus_sms_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusSmsProxy</FILE>
<TITLE>MmGdbusSmsProxy</TITLE>
MmGdbusSmsProxy
<SUBSECTION New>
mm_gdbus_sms_proxy_new
mm_gdbus_sms_proxy_new_finish
mm_gdbus_sms_proxy_new_for_bus
mm_gdbus_sms_proxy_new_for_bus_finish
mm_gdbus_sms_proxy_new_for_bus_sync
mm_gdbus_sms_proxy_new_sync
<SUBSECTION Standard>
MmGdbusSmsProxyClass
MM_GDBUS_IS_SMS_PROXY
MM_GDBUS_IS_SMS_PROXY_CLASS
MM_GDBUS_SMS_PROXY
MM_GDBUS_SMS_PROXY_CLASS
MM_GDBUS_SMS_PROXY_GET_CLASS
MM_GDBUS_TYPE_SMS_PROXY
MmGdbusSmsProxyPrivate
mm_gdbus_sms_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusSmsSkeleton</FILE>
<TITLE>MmGdbusSmsSkeleton</TITLE>
MmGdbusSmsSkeleton
<SUBSECTION New>
mm_gdbus_sms_skeleton_new
<SUBSECTION Standard>
MmGdbusSmsSkeletonClass
MM_GDBUS_IS_SMS_SKELETON
MM_GDBUS_IS_SMS_SKELETON_CLASS
MM_GDBUS_SMS_SKELETON
MM_GDBUS_SMS_SKELETON_CLASS
MM_GDBUS_SMS_SKELETON_GET_CLASS
MM_GDBUS_TYPE_SMS_SKELETON
MmGdbusSmsSkeletonPrivate
mm_gdbus_sms_skeleton_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusCall</FILE>
<TITLE>MmGdbusCall</TITLE>
MmGdbusCall
MmGdbusCallIface
<SUBSECTION Getters>
mm_gdbus_call_get_direction
mm_gdbus_call_get_number
mm_gdbus_call_dup_number
mm_gdbus_call_get_state
mm_gdbus_call_get_state_reason
mm_gdbus_call_dup_audio_format
mm_gdbus_call_dup_audio_port
mm_gdbus_call_get_audio_format
mm_gdbus_call_get_audio_port
mm_gdbus_call_get_multiparty
<SUBSECTION Methods>
mm_gdbus_call_call_accept
mm_gdbus_call_call_accept_finish
mm_gdbus_call_call_accept_sync
mm_gdbus_call_call_start
mm_gdbus_call_call_start_finish
mm_gdbus_call_call_start_sync
mm_gdbus_call_call_hangup
mm_gdbus_call_call_hangup_finish
mm_gdbus_call_call_hangup_sync
mm_gdbus_call_call_send_dtmf
mm_gdbus_call_call_send_dtmf_finish
mm_gdbus_call_call_send_dtmf_sync
mm_gdbus_call_call_deflect
mm_gdbus_call_call_deflect_finish
mm_gdbus_call_call_deflect_sync
mm_gdbus_call_call_join_multiparty
mm_gdbus_call_call_join_multiparty_finish
mm_gdbus_call_call_join_multiparty_sync
mm_gdbus_call_call_leave_multiparty
mm_gdbus_call_call_leave_multiparty_finish
mm_gdbus_call_call_leave_multiparty_sync
<SUBSECTION Private>
mm_gdbus_call_set_direction
mm_gdbus_call_set_number
mm_gdbus_call_set_state
mm_gdbus_call_set_state_reason
mm_gdbus_call_set_audio_format
mm_gdbus_call_set_audio_port
mm_gdbus_call_set_multiparty
mm_gdbus_call_complete_accept
mm_gdbus_call_complete_hangup
mm_gdbus_call_complete_send_dtmf
mm_gdbus_call_complete_start
mm_gdbus_call_complete_deflect
mm_gdbus_call_complete_join_multiparty
mm_gdbus_call_complete_leave_multiparty
mm_gdbus_call_interface_info
mm_gdbus_call_override_properties
mm_gdbus_call_emit_dtmf_received
mm_gdbus_call_emit_state_changed
<SUBSECTION Standard>
MM_GDBUS_IS_CALL
MM_GDBUS_CALL
MM_GDBUS_CALL_GET_IFACE
MM_GDBUS_TYPE_CALL
mm_gdbus_call_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusCallProxy</FILE>
<TITLE>MmGdbusCallProxy</TITLE>
MmGdbusCallProxy
<SUBSECTION New>
mm_gdbus_call_proxy_new
mm_gdbus_call_proxy_new_finish
mm_gdbus_call_proxy_new_for_bus
mm_gdbus_call_proxy_new_for_bus_finish
mm_gdbus_call_proxy_new_for_bus_sync
mm_gdbus_call_proxy_new_sync
<SUBSECTION Standard>
MmGdbusCallProxyClass
MM_GDBUS_IS_CALL_PROXY
MM_GDBUS_IS_CALL_PROXY_CLASS
MM_GDBUS_CALL_PROXY
MM_GDBUS_CALL_PROXY_CLASS
MM_GDBUS_CALL_PROXY_GET_CLASS
MM_GDBUS_TYPE_CALL_PROXY
MmGdbusCallProxyPrivate
mm_gdbus_call_proxy_get_type
</SECTION>

<SECTION>
<FILE>MmGdbusCallSkeleton</FILE>
<TITLE>MmGdbusCallSkeleton</TITLE>
MmGdbusCallSkeleton
<SUBSECTION New>
mm_gdbus_call_skeleton_new
<SUBSECTION Standard>
MmGdbusCallSkeletonClass
MM_GDBUS_IS_CALL_SKELETON
MM_GDBUS_IS_CALL_SKELETON_CLASS
MM_GDBUS_CALL_SKELETON
MM_GDBUS_CALL_SKELETON_CLASS
MM_GDBUS_CALL_SKELETON_GET_CLASS
MM_GDBUS_TYPE_CALL_SKELETON
MmGdbusCallSkeletonPrivate
mm_gdbus_call_skeleton_get_type
</SECTION>

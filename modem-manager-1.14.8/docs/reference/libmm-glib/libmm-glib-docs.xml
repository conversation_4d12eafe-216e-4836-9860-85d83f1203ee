<?xml version="1.0"?>
<!DOCTYPE book PUBLIC "-//OASIS//DTD DocBook XML V4.3//EN"
"http://www.oasis-open.org/docbook/xml/4.3/docbookx.dtd" [
<!ENTITY version SYSTEM "version.xml">
]>
<book id="libmm-glib"  xmlns:xi="http://www.w3.org/2003/XInclude">
  <bookinfo>
    <title>libmm-glib Reference Manual</title>
    <subtitle>
      <inlinemediaobject>
        <imageobject>
          <imagedata fileref="ModemManager-logo-wide.png" format="PNG" align="center"/>
        </imageobject>
      </inlinemediaobject>
    </subtitle>
    <releaseinfo>
      For libmm-glib version &version;
    </releaseinfo>

    <authorgroup>
      <author>
        <firstname>Aleksander</firstname>
        <surname>Morgado</surname>
        <affiliation>
          <address>
            <email><EMAIL></email>
          </address>
        </affiliation>
      </author>
    </authorgroup>

    <copyright>
      <year>2011</year>
      <year>2012</year>
      <year>2013</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2017</year>
      <year>2018</year>
      <year>2019</year>
      <year>2020</year>
      <holder>The ModemManager Authors</holder>
    </copyright>

    <legalnotice>
      <para>
        Permission is granted to copy, distribute and/or modify this
        document under the terms of the <citetitle>GNU Free
        Documentation License</citetitle>, Version 1.3 or any later
        version published by the Free Software Foundation with no
        Invariant Sections, no Front-Cover Texts, and no Back-Cover
        Texts. You may obtain a copy of the <citetitle>GNU Free
        Documentation License</citetitle> from the Free Software
        Foundation by visiting <ulink type="http"
        url="http://www.fsf.org">their Web site</ulink> or by writing
        to:
        <address>
          The Free Software Foundation, Inc.
          <street>51 Franklin Street</street>, Suite 500
          <city>Boston</city>, <state>MA</state> <postcode>02110-1335</postcode>
          <country>USA</country>
        </address>
      </para>
    </legalnotice>
  </bookinfo>

  <part>
    <title>High level API</title>

    <chapter>
      <title>Common enums and flags helpers</title>
      <xi:include href="xml/mm-enums-types.xml"/>
    </chapter>

    <chapter>
      <title>The Manager object</title>
      <xi:include href="xml/mm-manager.xml"/>
      <xi:include href="xml/mm-kernel-event-properties.xml"/>
    </chapter>

    <chapter>
      <title>The Modem object</title>
      <xi:include href="xml/mm-object.xml"/>
      <section>
        <title>Generic interfaces</title>
        <xi:include href="xml/mm-modem.xml"/>
        <xi:include href="xml/mm-modem-3gpp.xml"/>
        <xi:include href="xml/mm-modem-3gpp-ussd.xml"/>
        <xi:include href="xml/mm-modem-cdma.xml"/>
        <xi:include href="xml/mm-cdma-manual-activation-properties.xml"/>
        <xi:include href="xml/mm-unlock-retries.xml"/>
      </section>
      <section>
        <title>Simple interface support</title>
        <xi:include href="xml/mm-modem-simple.xml"/>
        <xi:include href="xml/mm-simple-connect-properties.xml"/>
        <xi:include href="xml/mm-simple-status.xml"/>
      </section>
      <section>
        <title>Location support</title>
        <xi:include href="xml/mm-modem-location.xml"/>
        <xi:include href="xml/mm-location-3gpp.xml"/>
        <xi:include href="xml/mm-location-gps-nmea.xml"/>
        <xi:include href="xml/mm-location-gps-raw.xml"/>
        <xi:include href="xml/mm-location-cdma-bs.xml"/>
      </section>
      <section>
        <title>Messaging support</title>
        <xi:include href="xml/mm-modem-messaging.xml"/>
      </section>
      <section>
        <title>Time support</title>
        <xi:include href="xml/mm-modem-time.xml"/>
        <xi:include href="xml/mm-network-timezone.xml"/>
      </section>
      <section>
        <title>Firmware support</title>
        <xi:include href="xml/mm-modem-firmware.xml"/>
        <xi:include href="xml/mm-firmware-properties.xml"/>
        <xi:include href="xml/mm-firmware-update-settings.xml"/>
      </section>
      <section>
        <title>Extended signal information</title>
        <xi:include href="xml/mm-modem-signal.xml"/>
        <xi:include href="xml/mm-signal.xml"/>
      </section>
      <section>
        <title>OMA support</title>
        <xi:include href="xml/mm-modem-oma.xml"/>
      </section>
      <section>
        <title>Voice support</title>
        <xi:include href="xml/mm-modem-voice.xml"/>
      </section>
      <section>
        <title>PCO support</title>
        <xi:include href="xml/mm-pco.xml"/>
      </section>
    </chapter>

    <chapter>
      <title>The Bearer object</title>
      <xi:include href="xml/mm-bearer.xml"/>
      <xi:include href="xml/mm-bearer-properties.xml"/>
      <xi:include href="xml/mm-bearer-ip-config.xml"/>
      <xi:include href="xml/mm-bearer-stats.xml"/>
    </chapter>

    <chapter>
      <title>The SIM object</title>
      <xi:include href="xml/mm-sim.xml"/>
    </chapter>

    <chapter>
      <title>The SMS object</title>
      <xi:include href="xml/mm-sms.xml"/>
      <xi:include href="xml/mm-sms-properties.xml"/>
    </chapter>

    <chapter>
      <title>The Call object</title>
      <xi:include href="xml/mm-call.xml"/>
      <xi:include href="xml/mm-call-properties.xml"/>
      <xi:include href="xml/mm-call-audio-format.xml"/>
    </chapter>

  </part>

  <part>
    <title>Low level API</title>
    <xi:include href="xml/MmGdbusOrgFreedesktopModemManager1.xml"/>
    <xi:include href="xml/MmGdbusOrgFreedesktopModemManager1Proxy.xml"/>
    <xi:include href="xml/MmGdbusOrgFreedesktopModemManager1Skeleton.xml"/>
    <xi:include href="xml/MmGdbusObjectManagerClient.xml"/>

    <xi:include href="xml/MmGdbusObject.xml"/>
    <xi:include href="xml/MmGdbusObjectProxy.xml"/>
    <xi:include href="xml/MmGdbusObjectSkeleton.xml"/>

    <xi:include href="xml/MmGdbusModem.xml"/>
    <xi:include href="xml/MmGdbusModemProxy.xml"/>
    <xi:include href="xml/MmGdbusModemSkeleton.xml"/>

    <xi:include href="xml/MmGdbusModem3gpp.xml"/>
    <xi:include href="xml/MmGdbusModem3gppProxy.xml"/>
    <xi:include href="xml/MmGdbusModem3gppSkeleton.xml"/>

    <xi:include href="xml/MmGdbusModem3gppUssd.xml"/>
    <xi:include href="xml/MmGdbusModem3gppUssdProxy.xml"/>
    <xi:include href="xml/MmGdbusModem3gppUssdSkeleton.xml"/>

    <xi:include href="xml/MmGdbusModemCdma.xml"/>
    <xi:include href="xml/MmGdbusModemCdmaProxy.xml"/>
    <xi:include href="xml/MmGdbusModemCdmaSkeleton.xml"/>

    <xi:include href="xml/MmGdbusModemLocation.xml"/>
    <xi:include href="xml/MmGdbusModemLocationProxy.xml"/>
    <xi:include href="xml/MmGdbusModemLocationSkeleton.xml"/>

    <xi:include href="xml/MmGdbusModemMessaging.xml"/>
    <xi:include href="xml/MmGdbusModemMessagingProxy.xml"/>
    <xi:include href="xml/MmGdbusModemMessagingSkeleton.xml"/>

    <xi:include href="xml/MmGdbusModemTime.xml"/>
    <xi:include href="xml/MmGdbusModemTimeProxy.xml"/>
    <xi:include href="xml/MmGdbusModemTimeSkeleton.xml"/>

    <xi:include href="xml/MmGdbusModemFirmware.xml"/>
    <xi:include href="xml/MmGdbusModemFirmwareProxy.xml"/>
    <xi:include href="xml/MmGdbusModemFirmwareSkeleton.xml"/>

    <xi:include href="xml/MmGdbusModemSignal.xml"/>
    <xi:include href="xml/MmGdbusModemSignalProxy.xml"/>
    <xi:include href="xml/MmGdbusModemSignalSkeleton.xml"/>

    <xi:include href="xml/MmGdbusModemOma.xml"/>
    <xi:include href="xml/MmGdbusModemOmaProxy.xml"/>
    <xi:include href="xml/MmGdbusModemOmaSkeleton.xml"/>

    <xi:include href="xml/MmGdbusModemVoice.xml"/>
    <xi:include href="xml/MmGdbusModemVoiceProxy.xml"/>
    <xi:include href="xml/MmGdbusModemVoiceSkeleton.xml"/>

    <!--xi:include href="xml/MmGdbusModemContacts.xml"/>
    <xi:include href="xml/MmGdbusModemContactsProxy.xml"/>
    <xi:include href="xml/MmGdbusModemContactsSkeleton.xml"/-->

    <xi:include href="xml/MmGdbusModemSimple.xml"/>
    <xi:include href="xml/MmGdbusModemSimpleProxy.xml"/>
    <xi:include href="xml/MmGdbusModemSimpleSkeleton.xml"/>

    <xi:include href="xml/MmGdbusBearer.xml"/>
    <xi:include href="xml/MmGdbusBearerProxy.xml"/>
    <xi:include href="xml/MmGdbusBearerSkeleton.xml"/>

    <xi:include href="xml/MmGdbusSim.xml"/>
    <xi:include href="xml/MmGdbusSimProxy.xml"/>
    <xi:include href="xml/MmGdbusSimSkeleton.xml"/>

    <xi:include href="xml/MmGdbusSms.xml"/>
    <xi:include href="xml/MmGdbusSmsProxy.xml"/>
    <xi:include href="xml/MmGdbusSmsSkeleton.xml"/>

    <xi:include href="xml/MmGdbusCall.xml"/>
    <xi:include href="xml/MmGdbusCallProxy.xml"/>
    <xi:include href="xml/MmGdbusCallSkeleton.xml"/>
  </part>

  <chapter id="object-tree">
    <title>Object Hierarchy</title>
    <xi:include href="xml/tree_index.sgml"/>
  </chapter>
  <chapter id="api-index-full">
    <title>Index</title>
    <xi:include href="xml/api-index-full.xml"></xi:include>
  </chapter>
  <chapter id="deprecated-api-index" role="deprecated">
    <title>Index of deprecated symbols</title>
    <xi:include href="xml/api-index-deprecated.xml"></xi:include>
  </chapter>
  <chapter id="api-index-1-0" role="1.0">
    <title>Index of new symbols in 1.0</title>
    <xi:include href="xml/api-index-1.0.xml"></xi:include>
  </chapter>
  <chapter id="api-index-1-2" role="1.2">
    <title>Index of new symbols in 1.2</title>
    <xi:include href="xml/api-index-1.2.xml"></xi:include>
  </chapter>
  <chapter id="api-index-1-4" role="1.4">
    <title>Index of new symbols in 1.4</title>
    <xi:include href="xml/api-index-1.4.xml"></xi:include>
  </chapter>
  <chapter id="api-index-1-6" role="1.6">
    <title>Index of new symbols in 1.6</title>
    <xi:include href="xml/api-index-1.6.xml"></xi:include>
  </chapter>
  <chapter id="api-index-1-8" role="1.8">
    <title>Index of new symbols in 1.8</title>
    <xi:include href="xml/api-index-1.8.xml"></xi:include>
  </chapter>
  <chapter id="api-index-1-10" role="1.10">
    <title>Index of new symbols in 1.10</title>
    <xi:include href="xml/api-index-1.10.xml"></xi:include>
  </chapter>
  <chapter id="api-index-1-12" role="1.12">
    <title>Index of new symbols in 1.12</title>
    <xi:include href="xml/api-index-1.12.xml"></xi:include>
  </chapter>
  <chapter id="api-index-1-14" role="1.14">
    <title>Index of new symbols in 1.14</title>
    <xi:include href="xml/api-index-1.14.xml"></xi:include>
  </chapter>

  <xi:include href="xml/annotation-glossary.xml"></xi:include>
</book>

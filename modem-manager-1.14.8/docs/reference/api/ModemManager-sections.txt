<SECTION>
<FILE>mm-version</FILE>
<TITLE>Version checks</TITLE>
MM_MAJOR_VERSION
MM_MINOR_VERSION
MM_MICRO_VERSION
MM_CHECK_VERSION
</SECTION>

<SECTION>
<FILE>mm-enums</FILE>
<TITLE>Flags and Enumerations</TITLE>
MMBearerType
MMBearerIpFamily
MMBearerIpMethod
MMBearerAllowedAuth
MMCallDirection
MMCallState
MMCallStateReason
MMFirmwareImageType
MMModem3gppFacility
MMModem3gppNetworkAvailability
MMModem3gppSubscriptionState
MMModem3gppRegistrationState
MMModem3gppUssdSessionState
MMModem3gppEpsUeModeOperation
MMModemAccessTechnology
MMModemBand
MMModemCapability
MMModemCdmaActivationState
MMModemCdmaRegistrationState
MMModemCdmaRmProtocol
MMModemContactsStorage
MMModemLocationSource
MMModemLocationAssistanceDataType
MMModemLock
MMModemMode
MMModemState
MMModemStateFailedReason
MMModemStateChangeReason
MMModemPowerState
MMModemPortType
MMModemFirmwareUpdateMethod
MMOmaFeature
MMOmaSessionState
MMOmaSessionStateFailedReason
MMOmaSessionType
MMSmsPduType
MMSmsState
MMSmsDeliveryState
MMSmsStorage
MMSmsValidityType
MMSmsCdmaTeleserviceId
MMSmsCdmaServiceCategory
</SECTION>

<SECTION>
<FILE>mm-errors</FILE>
<TITLE>Errors</TITLE>
MMConnectionError
MMCoreError
MMMessageError
MMMobileEquipmentError
MMSerialError
MMCdmaActivationError
<SUBSECTION Private>
MM_CDMA_ACTIVATION_ERROR_DBUS_PREFIX
MM_CONNECTION_ERROR_DBUS_PREFIX
MM_CORE_ERROR_DBUS_PREFIX
MM_MESSAGE_ERROR_DBUS_PREFIX
MM_MOBILE_EQUIPMENT_ERROR_DBUS_PREFIX
MM_SERIAL_ERROR_DBUS_PREFIX
</SECTION>

<SECTION>
<FILE>mm-compat</FILE>
MM_MODEM_BAND_U2100
MM_MODEM_BAND_U1900
MM_MODEM_BAND_U1800
MM_MODEM_BAND_U17IV
MM_MODEM_BAND_U850
MM_MODEM_BAND_U800
MM_MODEM_BAND_U2600
MM_MODEM_BAND_U900
MM_MODEM_BAND_U17IX
MM_MODEM_BAND_EUTRAN_I
MM_MODEM_BAND_EUTRAN_II
MM_MODEM_BAND_EUTRAN_III
MM_MODEM_BAND_EUTRAN_IV
MM_MODEM_BAND_EUTRAN_V
MM_MODEM_BAND_EUTRAN_VI
MM_MODEM_BAND_EUTRAN_VII
MM_MODEM_BAND_EUTRAN_VIII
MM_MODEM_BAND_EUTRAN_IX
MM_MODEM_BAND_EUTRAN_X
MM_MODEM_BAND_EUTRAN_XI
MM_MODEM_BAND_EUTRAN_XII
MM_MODEM_BAND_EUTRAN_XIII
MM_MODEM_BAND_EUTRAN_XIV
MM_MODEM_BAND_EUTRAN_XVII
MM_MODEM_BAND_EUTRAN_XVIII
MM_MODEM_BAND_EUTRAN_XIX
MM_MODEM_BAND_EUTRAN_XX
MM_MODEM_BAND_EUTRAN_XXI
MM_MODEM_BAND_EUTRAN_XXII
MM_MODEM_BAND_EUTRAN_XXIII
MM_MODEM_BAND_EUTRAN_XXIV
MM_MODEM_BAND_EUTRAN_XXV
MM_MODEM_BAND_EUTRAN_XXVI
MM_MODEM_BAND_EUTRAN_XXXIII
MM_MODEM_BAND_EUTRAN_XXXIV
MM_MODEM_BAND_EUTRAN_XXXV
MM_MODEM_BAND_EUTRAN_XXXVI
MM_MODEM_BAND_EUTRAN_XXXVII
MM_MODEM_BAND_EUTRAN_XXXVIII
MM_MODEM_BAND_EUTRAN_XXXIX
MM_MODEM_BAND_EUTRAN_XL
MM_MODEM_BAND_EUTRAN_XLI
MM_MODEM_BAND_EUTRAN_XLII
MM_MODEM_BAND_EUTRAN_XLIII
MM_MODEM_BAND_EUTRAN_XLIV
MM_MODEM_BAND_CDMA_BC0_CELLULAR_800
MM_MODEM_BAND_CDMA_BC1_PCS_1900
MM_MODEM_BAND_CDMA_BC2_TACS
MM_MODEM_BAND_CDMA_BC3_JTACS
MM_MODEM_BAND_CDMA_BC4_KOREAN_PCS
MM_MODEM_BAND_CDMA_BC5_NMT450
MM_MODEM_BAND_CDMA_BC6_IMT2000
MM_MODEM_BAND_CDMA_BC7_CELLULAR_700
MM_MODEM_BAND_CDMA_BC8_1800
MM_MODEM_BAND_CDMA_BC9_900
MM_MODEM_BAND_CDMA_BC10_SECONDARY_800
MM_MODEM_BAND_CDMA_BC11_PAMR_400
MM_MODEM_BAND_CDMA_BC12_PAMR_800
MM_MODEM_BAND_CDMA_BC13_IMT2000_2500
MM_MODEM_BAND_CDMA_BC14_PCS2_1900
MM_MODEM_BAND_CDMA_BC15_AWS
MM_MODEM_BAND_CDMA_BC16_US_2500
MM_MODEM_BAND_CDMA_BC17_US_FLO_2500
MM_MODEM_BAND_CDMA_BC18_US_PS_700
MM_MODEM_BAND_CDMA_BC19_US_LOWER_700
MM_MODEM_LOCATION_SOURCE_AGPS
MM_MODEM_CAPABILITY_LTE_ADVANCED
<SUBSECTION Private>
MMModemBandDeprecated
MMModemLocationSourceDeprecated
MMModemCapabilityDeprecated
MM_DEPRECATED
</SECTION>

<SECTION>
<FILE>mm-tags</FILE>
<TITLE>Common udev tags</TITLE>
ID_MM_CANDIDATE
ID_MM_PHYSDEV_UID
ID_MM_DEVICE_PROCESS
ID_MM_DEVICE_IGNORE
ID_MM_PORT_IGNORE
ID_MM_TTY_BLACKLIST
ID_MM_TTY_MANUAL_SCAN_ONLY
ID_MM_PORT_TYPE_AT_PPP
ID_MM_PORT_TYPE_AT_PRIMARY
ID_MM_PORT_TYPE_AT_SECONDARY
ID_MM_PORT_TYPE_GPS
ID_MM_PORT_TYPE_QCDM
ID_MM_PORT_TYPE_AUDIO
ID_MM_TTY_BAUDRATE
ID_MM_TTY_FLOW_CONTROL
</SECTION>

<?xml version="1.0"?>
<!DOCTYPE book PUBLIC "-//OASIS//DTD DocBook XML V4.1.2//EN"
"http://www.oasis-open.org/docbook/xml/4.1.2/docbookx.dtd" [
<!ENTITY version SYSTEM "version.xml">
]>
<book id="ModemManager" xmlns:xi="http://www.w3.org/2003/XInclude">
  <bookinfo>
    <title>ModemManager Reference Manual</title>
    <subtitle>
      <inlinemediaobject>
        <imageobject>
          <imagedata fileref="ModemManager-logo-wide.png" format="PNG" align="center"/>
        </imageobject>
      </inlinemediaobject>
    </subtitle>
    <releaseinfo>
      For ModemManager version &version;
    </releaseinfo>

    <authorgroup>
      <author>
        <firstname>Dan</firstname>
        <surname>Williams</surname>
        <affiliation>
          <address>
            <email><EMAIL></email>
          </address>
        </affiliation>
      </author>
      <author>
        <firstname>Al<PERSON><PERSON><PERSON></firstname>
        <surname>Mo<PERSON>do</surname>
        <affiliation>
          <address>
            <email><EMAIL></email>
          </address>
        </affiliation>
      </author>
    </authorgroup>

    <copyright>
      <year>2008</year>
      <year>2009</year>
      <year>2010</year>
      <year>2011</year>
      <year>2012</year>
      <year>2013</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2017</year>
      <year>2018</year>
      <year>2019</year>
      <year>2020</year>
      <holder>The ModemManager Authors</holder>
    </copyright>

    <legalnotice>
      <para>
        Permission is granted to copy, distribute and/or modify this
        document under the terms of the <citetitle>GNU Free
        Documentation License</citetitle>, Version 1.3 or any later
        version published by the Free Software Foundation with no
        Invariant Sections, no Front-Cover Texts, and no Back-Cover
        Texts. You may obtain a copy of the <citetitle>GNU Free
        Documentation License</citetitle> from the Free Software
        Foundation by visiting <ulink type="http"
        url="http://www.fsf.org">their Web site</ulink> or by writing
        to:
        <address>
          The Free Software Foundation, Inc.
          <street>51 Franklin Street</street>, Suite 500
          <city>Boston</city>, <state>MA</state> <postcode>02110-1335</postcode>
          <country>USA</country>
        </address>
      </para>
    </legalnotice>
  </bookinfo>

  <!-- Part 1, overview -->
  <xi:include href="xml/ModemManager-overview.xml"/>

  <part id="ref-common-types">
    <title>Common types and definitions</title>
    <xi:include href="xml/mm-version.xml"/>
    <xi:include href="xml/mm-enums.xml"/>
    <xi:include href="xml/mm-errors.xml"/>
  </part>

  <part id="ref-udev">
    <title>Common udev tag definitions</title>
    <xi:include href="xml/mm-tags.xml"/>
  </part>

  <!-- Part 2, DBus reference -->
  <xi:include href="xml/ModemManager-dbus-reference.xml"/>

  <!-- Part 3, Migration reference -->
  <xi:include href="xml/ModemManager-migration-reference.xml"/>

  <part id="ref-compat">
    <title>Compatibility with older versions</title>
    <xi:include href="xml/mm-compat.xml"/>
  </part>

  <chapter id="api-index-full">
    <title>Index</title>
    <xi:include href="xml/api-index-full.xml"></xi:include>
  </chapter>
  <chapter id="api-index-deprecated" role="deprecated">
    <title>Index of deprecated symbols</title>
    <xi:include href="xml/api-index-deprecated.xml"></xi:include>
  </chapter>
  <chapter id="api-index-1-0" role="1.0">
    <title>Index of new symbols in 1.0</title>
    <xi:include href="xml/api-index-1.0.xml"></xi:include>
  </chapter>
  <chapter id="api-index-1-2" role="1.2">
    <title>Index of new symbols in 1.2</title>
    <xi:include href="xml/api-index-1.2.xml"></xi:include>
  </chapter>
  <chapter id="api-index-1-4" role="1.4">
    <title>Index of new symbols in 1.4</title>
    <xi:include href="xml/api-index-1.4.xml"></xi:include>
  </chapter>
  <chapter id="api-index-1-6" role="1.6">
    <title>Index of new symbols in 1.6</title>
    <xi:include href="xml/api-index-1.6.xml"></xi:include>
  </chapter>
  <chapter id="api-index-1-8" role="1.8">
    <title>Index of new symbols in 1.8</title>
    <xi:include href="xml/api-index-1.8.xml"></xi:include>
  </chapter>
  <chapter id="api-index-1-10" role="1.10">
    <title>Index of new symbols in 1.10</title>
    <xi:include href="xml/api-index-1.10.xml"></xi:include>
  </chapter>
  <chapter id="api-index-1-12" role="1.12">
    <title>Index of new symbols in 1.12</title>
    <xi:include href="xml/api-index-1.12.xml"></xi:include>
  </chapter>
</book>

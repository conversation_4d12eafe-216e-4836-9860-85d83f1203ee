# Makefile.in generated by automake 1.16.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANT<PERSON><PERSON>ITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

# -*- mode: makefile -*-

####################################
# Everything below here is generic #
####################################
VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
subdir = docs/reference/api
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_code_coverage.m4 \
	$(top_srcdir)/m4/gettext.m4 $(top_srcdir)/m4/gtk-doc.m4 \
	$(top_srcdir)/m4/iconv.m4 $(top_srcdir)/m4/intlmacosx.m4 \
	$(top_srcdir)/m4/introspection.m4 $(top_srcdir)/m4/lib-ld.m4 \
	$(top_srcdir)/m4/lib-link.m4 $(top_srcdir)/m4/lib-prefix.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/mm-enable-plugin.m4 $(top_srcdir)/m4/nls.m4 \
	$(top_srcdir)/m4/po.m4 $(top_srcdir)/m4/progtest.m4 \
	$(top_srcdir)/m4/vapigen.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES = version.xml
CONFIG_CLEAN_VPATH_FILES =
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
SOURCES =
DIST_SOURCES =
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
am__DIST_COMMON = $(srcdir)/Makefile.in $(srcdir)/version.xml.in \
	$(top_srcdir)/gtk-doc.make
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CODE_COVERAGE_CFLAGS = @CODE_COVERAGE_CFLAGS@
CODE_COVERAGE_ENABLED = @CODE_COVERAGE_ENABLED@
CODE_COVERAGE_LDFLAGS = @CODE_COVERAGE_LDFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CYGPATH_W = @CYGPATH_W@
DBUS_SYS_DIR = @DBUS_SYS_DIR@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
GCOV = @GCOV@
GDBUS_CODEGEN = @GDBUS_CODEGEN@
GENHTML = @GENHTML@
GETTEXT_MACRO_VERSION = @GETTEXT_MACRO_VERSION@
GETTEXT_PACKAGE = @GETTEXT_PACKAGE@
GLIB_MKENUMS = @GLIB_MKENUMS@
GMSGFMT = @GMSGFMT@
GMSGFMT_015 = @GMSGFMT_015@
GREP = @GREP@
GTKDOC_CHECK = @GTKDOC_CHECK@
GTKDOC_CHECK_PATH = @GTKDOC_CHECK_PATH@
GTKDOC_DEPS_CFLAGS = @GTKDOC_DEPS_CFLAGS@
GTKDOC_DEPS_LIBS = @GTKDOC_DEPS_LIBS@
GTKDOC_MKPDF = @GTKDOC_MKPDF@
GTKDOC_REBASE = @GTKDOC_REBASE@
GUDEV_CFLAGS = @GUDEV_CFLAGS@
GUDEV_LIBS = @GUDEV_LIBS@
HTML_DIR = @HTML_DIR@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
INTLLIBS = @INTLLIBS@
INTL_MACOSX_LIBS = @INTL_MACOSX_LIBS@
INTROSPECTION_CFLAGS = @INTROSPECTION_CFLAGS@
INTROSPECTION_COMPILER = @INTROSPECTION_COMPILER@
INTROSPECTION_GENERATE = @INTROSPECTION_GENERATE@
INTROSPECTION_GIRDIR = @INTROSPECTION_GIRDIR@
INTROSPECTION_LIBS = @INTROSPECTION_LIBS@
INTROSPECTION_MAKEFILE = @INTROSPECTION_MAKEFILE@
INTROSPECTION_SCANNER = @INTROSPECTION_SCANNER@
INTROSPECTION_TYPELIBDIR = @INTROSPECTION_TYPELIBDIR@
LCOV = @LCOV@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBICONV = @LIBICONV@
LIBINTL = @LIBINTL@
LIBMM_GLIB_CFLAGS = @LIBMM_GLIB_CFLAGS@
LIBMM_GLIB_LIBS = @LIBMM_GLIB_LIBS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBSYSTEMD_CFLAGS = @LIBSYSTEMD_CFLAGS@
LIBSYSTEMD_LIBS = @LIBSYSTEMD_LIBS@
LIBSYSTEMD_LOGIN_CFLAGS = @LIBSYSTEMD_LOGIN_CFLAGS@
LIBSYSTEMD_LOGIN_LIBS = @LIBSYSTEMD_LOGIN_LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBICONV = @LTLIBICONV@
LTLIBINTL = @LTLIBINTL@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAINT = @MAINT@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MBIM_CFLAGS = @MBIM_CFLAGS@
MBIM_LIBS = @MBIM_LIBS@
MKDIR_P = @MKDIR_P@
MMCLI_CFLAGS = @MMCLI_CFLAGS@
MMCLI_LIBS = @MMCLI_LIBS@
MM_CFLAGS = @MM_CFLAGS@
MM_DEFAULT_USER_POLICY = @MM_DEFAULT_USER_POLICY@
MM_GLIB_LT_AGE = @MM_GLIB_LT_AGE@
MM_GLIB_LT_CURRENT = @MM_GLIB_LT_CURRENT@
MM_GLIB_LT_REVISION = @MM_GLIB_LT_REVISION@
MM_LIBS = @MM_LIBS@
MM_MAJOR_VERSION = @MM_MAJOR_VERSION@
MM_MICRO_VERSION = @MM_MICRO_VERSION@
MM_MINOR_VERSION = @MM_MINOR_VERSION@
MM_POLKIT_SERVICE = @MM_POLKIT_SERVICE@
MM_VERSION = @MM_VERSION@
MSGFMT = @MSGFMT@
MSGFMT_015 = @MSGFMT_015@
MSGMERGE = @MSGMERGE@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PKG_CONFIG = @PKG_CONFIG@
PKG_CONFIG_LIBDIR = @PKG_CONFIG_LIBDIR@
PKG_CONFIG_PATH = @PKG_CONFIG_PATH@
POLKIT_CFLAGS = @POLKIT_CFLAGS@
POLKIT_LIBS = @POLKIT_LIBS@
POSUB = @POSUB@
QMI_CFLAGS = @QMI_CFLAGS@
QMI_LIBS = @QMI_LIBS@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
SYSTEMD_UNIT_DIR = @SYSTEMD_UNIT_DIR@
UDEV_BASE_DIR = @UDEV_BASE_DIR@
USE_NLS = @USE_NLS@
VAPIGEN = @VAPIGEN@
VAPIGEN_MAKEFILE = @VAPIGEN_MAKEFILE@
VAPIGEN_VAPIDIR = @VAPIGEN_VAPIDIR@
VERSION = @VERSION@
WARN_CFLAGS = @WARN_CFLAGS@
WARN_LDFLAGS = @WARN_LDFLAGS@
WARN_SCANNERFLAGS = @WARN_SCANNERFLAGS@
XGETTEXT = @XGETTEXT@
XGETTEXT_015 = @XGETTEXT_015@
XGETTEXT_EXTRA_OPTIONS = @XGETTEXT_EXTRA_OPTIONS@
XSLTPROC_CHECK = @XSLTPROC_CHECK@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@

# Logos
LOGOS_PNG = \
	$(top_srcdir)/data/ModemManager-logo-square.png \
	$(top_srcdir)/data/ModemManager-logo-wide.png \
	$(top_srcdir)/data/ModemManager-logo-wide-text.png


# Diagrams
DIAGRAMS_PNG = \
	$(top_srcdir)/data/ModemManager-states.png \
	$(top_srcdir)/data/ModemManager-interface-initialization-sequence.png \
	$(top_srcdir)/data/ModemManager-interface-initialization-sequence-subclassed.png


# The name of the module.
DOC_MODULE = ModemManager

# The top-level SGML file.
DOC_MAIN_SGML_FILE = $(DOC_MODULE)-docs.xml

# Extra options to supply to gtkdoc-scan
SCAN_OPTIONS = --deprecated-guards="MM_DISABLE_DEPRECATED"

# The directory containing the source code.
DOC_SOURCE_DIR = $(top_srcdir)/include
HFILE_GLOB = 
CFILE_GLOB = 

# Headers to ignore
IGNORE_HFILES = \
	ModemManager.h \
	ModemManager-names.h \
	$(NULL)


# CFLAGS and LDFLAGS for compiling scan program. Only needed
# if $(DOC_MODULE).types is non-empty.
AM_CPPFLAGS = \
	-I$(srcdir) \
	-I$(top_srcdir) \
	-I$(top_builddir) \
	$(MM_CFLAGS) \
	$(NULL)

GTKDOC_LIBS = \
	$(MM_LIBS) \
	$(NULL)


# Extra options to supply to gtkdoc-mkdb
MKDB_OPTIONS = --output-format=xml --sgml-mode --name-space=mm

# Images to copy into HTML directory
HTML_IMAGES = \
	$(DIAGRAMS_PNG) \
	$(LOGOS_PNG) \
	$(NULL)

content_files = 
expand_content_files = \
	ModemManager-overview.xml \
	ModemManager-dbus-reference.xml \
	ModemManager-migration-reference.xml \
	$(top_builddir)/libmm-glib/generated/mm-gdbus-doc-org.freedesktop.ModemManager1.xml \
	$(top_builddir)/libmm-glib/generated/mm-gdbus-doc-org.freedesktop.ModemManager1.Sim.xml \
	$(top_builddir)/libmm-glib/generated/mm-gdbus-doc-org.freedesktop.ModemManager1.Sms.xml \
	$(top_builddir)/libmm-glib/generated/mm-gdbus-doc-org.freedesktop.ModemManager1.Bearer.xml \
	$(top_builddir)/libmm-glib/generated/mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.xml \
	$(top_builddir)/libmm-glib/generated/mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Messaging.xml \
	$(top_builddir)/libmm-glib/generated/mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Location.xml \
	$(top_builddir)/libmm-glib/generated/mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Time.xml \
	$(top_builddir)/libmm-glib/generated/mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Firmware.xml \
	$(top_builddir)/libmm-glib/generated/mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Oma.xml \
	$(top_builddir)/libmm-glib/generated/mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.ModemCdma.xml \
	$(top_builddir)/libmm-glib/generated/mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Modem3gpp.xml \
	$(top_builddir)/libmm-glib/generated/mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Modem3gpp.Ussd.xml \
	$(top_builddir)/libmm-glib/generated/mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Simple.xml \
	$(top_builddir)/libmm-glib/generated/mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Signal.xml \
	$(NULL)

extra_files = \
	$(NULL)

@ENABLE_GTK_DOC_TRUE@@GTK_DOC_USE_LIBTOOL_FALSE@GTKDOC_CC = $(CC) $(INCLUDES) $(GTKDOC_DEPS_CFLAGS) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
@ENABLE_GTK_DOC_TRUE@@GTK_DOC_USE_LIBTOOL_TRUE@GTKDOC_CC = $(LIBTOOL) --tag=CC --mode=compile $(CC) $(INCLUDES) $(GTKDOC_DEPS_CFLAGS) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
@ENABLE_GTK_DOC_TRUE@@GTK_DOC_USE_LIBTOOL_FALSE@GTKDOC_LD = $(CC) $(GTKDOC_DEPS_LIBS) $(AM_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS)
@ENABLE_GTK_DOC_TRUE@@GTK_DOC_USE_LIBTOOL_TRUE@GTKDOC_LD = $(LIBTOOL) --tag=CC --mode=link $(CC) $(GTKDOC_DEPS_LIBS) $(AM_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS)
@ENABLE_GTK_DOC_TRUE@@GTK_DOC_USE_LIBTOOL_FALSE@GTKDOC_RUN = 
@ENABLE_GTK_DOC_TRUE@@GTK_DOC_USE_LIBTOOL_TRUE@GTKDOC_RUN = $(LIBTOOL) --mode=execute

# We set GPATH here; this gives us semantics for GNU make
# which are more like other make's VPATH, when it comes to
# whether a source that is a target of one rule is then
# searched for in VPATH/GPATH.
#
@ENABLE_GTK_DOC_TRUE@GPATH = $(srcdir)
@ENABLE_GTK_DOC_TRUE@TARGET_DIR = $(HTML_DIR)/$(DOC_MODULE)
@ENABLE_GTK_DOC_TRUE@SETUP_FILES = \
@ENABLE_GTK_DOC_TRUE@	$(content_files)		\
@ENABLE_GTK_DOC_TRUE@	$(expand_content_files)		\
@ENABLE_GTK_DOC_TRUE@	$(DOC_MAIN_SGML_FILE)		\
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE)-sections.txt	\
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE)-overrides.txt

@ENABLE_GTK_DOC_FALSE@EXTRA_DIST = ModemManager-overview.xml \
@ENABLE_GTK_DOC_FALSE@	ModemManager-dbus-reference.xml \
@ENABLE_GTK_DOC_FALSE@	ModemManager-migration-reference.xml \
@ENABLE_GTK_DOC_FALSE@	version.xml.in $(DIAGRAMS) $(NULL)
@ENABLE_GTK_DOC_TRUE@EXTRA_DIST = $(HTML_IMAGES) $(SETUP_FILES) \
@ENABLE_GTK_DOC_TRUE@	ModemManager-overview.xml \
@ENABLE_GTK_DOC_TRUE@	ModemManager-dbus-reference.xml \
@ENABLE_GTK_DOC_TRUE@	ModemManager-migration-reference.xml \
@ENABLE_GTK_DOC_TRUE@	version.xml.in $(DIAGRAMS) $(NULL)
@ENABLE_GTK_DOC_TRUE@DOC_STAMPS = setup-build.stamp scan-build.stamp sgml-build.stamp \
@ENABLE_GTK_DOC_TRUE@	html-build.stamp pdf-build.stamp \
@ENABLE_GTK_DOC_TRUE@	sgml.stamp html.stamp pdf.stamp

@ENABLE_GTK_DOC_TRUE@SCANOBJ_FILES = \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE).args 	 \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE).hierarchy  \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE).interfaces \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE).prerequisites \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE).signals

@ENABLE_GTK_DOC_TRUE@REPORT_FILES = \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE)-undocumented.txt \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE)-undeclared.txt \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE)-unused.txt

@ENABLE_GTK_DOC_FALSE@CLEANFILES = $(DOC_MODULE)-decl-list.txt \
@ENABLE_GTK_DOC_FALSE@	$(DOC_MODULE)-decl.txt \
@ENABLE_GTK_DOC_FALSE@	$(DOC_MODULE)-overrides.txt \
@ENABLE_GTK_DOC_FALSE@	$(DOC_MODULE)-undeclared.txt \
@ENABLE_GTK_DOC_FALSE@	$(DOC_MODULE)-undocumented.txt \
@ENABLE_GTK_DOC_FALSE@	$(DOC_MODULE)-overrides.txt \
@ENABLE_GTK_DOC_FALSE@	$(DOC_MODULE)-unused.txt \
@ENABLE_GTK_DOC_FALSE@	$(DOC_MODULE).args \
@ENABLE_GTK_DOC_FALSE@	$(DOC_MODULE).hierarchy \
@ENABLE_GTK_DOC_FALSE@	$(DOC_MODULE).interfaces \
@ENABLE_GTK_DOC_FALSE@	$(DOC_MODULE).prerequisites \
@ENABLE_GTK_DOC_FALSE@	$(DOC_MODULE).signals *.stamp -rf xml \
@ENABLE_GTK_DOC_FALSE@	html tmpl $(NULL)
@ENABLE_GTK_DOC_TRUE@CLEANFILES = $(SCANOBJ_FILES) $(REPORT_FILES) \
@ENABLE_GTK_DOC_TRUE@	$(DOC_STAMPS) gtkdoc-check.test \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE)-decl-list.txt \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE)-decl.txt \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE)-overrides.txt \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE)-undeclared.txt \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE)-undocumented.txt \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE)-overrides.txt \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE)-unused.txt \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE).args \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE).hierarchy \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE).interfaces \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE).prerequisites \
@ENABLE_GTK_DOC_TRUE@	$(DOC_MODULE).signals *.stamp -rf xml \
@ENABLE_GTK_DOC_TRUE@	html tmpl $(NULL)
@ENABLE_GTK_DOC_TRUE@@GTK_DOC_BUILD_HTML_FALSE@HTML_BUILD_STAMP = 
@ENABLE_GTK_DOC_TRUE@@GTK_DOC_BUILD_HTML_TRUE@HTML_BUILD_STAMP = html-build.stamp
@ENABLE_GTK_DOC_TRUE@@GTK_DOC_BUILD_PDF_FALSE@PDF_BUILD_STAMP = 
@ENABLE_GTK_DOC_TRUE@@GTK_DOC_BUILD_PDF_TRUE@PDF_BUILD_STAMP = pdf-build.stamp

#### setup ####
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_SETUP = $(GTK_DOC_V_SETUP_$(V))
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_SETUP_ = $(GTK_DOC_V_SETUP_$(AM_DEFAULT_VERBOSITY))
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_SETUP_0 = @echo "  DOC   Preparing build";

#### scan ####
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_SCAN = $(GTK_DOC_V_SCAN_$(V))
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_SCAN_ = $(GTK_DOC_V_SCAN_$(AM_DEFAULT_VERBOSITY))
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_SCAN_0 = @echo "  DOC   Scanning header files";
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_INTROSPECT = $(GTK_DOC_V_INTROSPECT_$(V))
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_INTROSPECT_ = $(GTK_DOC_V_INTROSPECT_$(AM_DEFAULT_VERBOSITY))
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_INTROSPECT_0 = @echo "  DOC   Introspecting gobjects";

#### xml ####
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_XML = $(GTK_DOC_V_XML_$(V))
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_XML_ = $(GTK_DOC_V_XML_$(AM_DEFAULT_VERBOSITY))
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_XML_0 = @echo "  DOC   Building XML";

#### html ####
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_HTML = $(GTK_DOC_V_HTML_$(V))
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_HTML_ = $(GTK_DOC_V_HTML_$(AM_DEFAULT_VERBOSITY))
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_HTML_0 = @echo "  DOC   Building HTML";
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_XREF = $(GTK_DOC_V_XREF_$(V))
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_XREF_ = $(GTK_DOC_V_XREF_$(AM_DEFAULT_VERBOSITY))
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_XREF_0 = @echo "  DOC   Fixing cross-references";

#### pdf ####
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_PDF = $(GTK_DOC_V_PDF_$(V))
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_PDF_ = $(GTK_DOC_V_PDF_$(AM_DEFAULT_VERBOSITY))
@ENABLE_GTK_DOC_TRUE@GTK_DOC_V_PDF_0 = @echo "  DOC   Building PDF";
all: all-am

.SUFFIXES:
$(srcdir)/Makefile.in: @MAINTAINER_MODE_TRUE@ $(srcdir)/Makefile.am $(top_srcdir)/gtk-doc.make $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu docs/reference/api/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu docs/reference/api/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;
$(top_srcdir)/gtk-doc.make $(am__empty):

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure: @MAINTAINER_MODE_TRUE@ $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4): @MAINTAINER_MODE_TRUE@ $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
version.xml: $(top_builddir)/config.status $(srcdir)/version.xml.in
	cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
tags TAGS:

ctags CTAGS:

cscope cscopelist:

@ENABLE_GTK_DOC_FALSE@dist-hook:

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	$(MAKE) $(AM_MAKEFLAGS) \
	  top_distdir="$(top_distdir)" distdir="$(distdir)" \
	  dist-hook
check-am: all-am
check: check-am
@ENABLE_GTK_DOC_FALSE@all-local:
all-am: Makefile all-local
installdirs:
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
@ENABLE_GTK_DOC_FALSE@distclean-local:
@ENABLE_GTK_DOC_FALSE@maintainer-clean-local:
@ENABLE_GTK_DOC_FALSE@clean-local:
@ENABLE_GTK_DOC_FALSE@install-data-local:
@ENABLE_GTK_DOC_FALSE@uninstall-local:
clean: clean-am

clean-am: clean-generic clean-libtool clean-local mostlyclean-am

distclean: distclean-am
	-rm -f Makefile
distclean-am: clean-am distclean-generic distclean-local

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-data-local

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic \
	maintainer-clean-local

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-generic mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-local

.MAKE: install-am install-strip

.PHONY: all all-am all-local check check-am clean clean-generic \
	clean-libtool clean-local cscopelist-am ctags-am dist-hook \
	distclean distclean-generic distclean-libtool distclean-local \
	distdir dvi dvi-am html html-am info info-am install \
	install-am install-data install-data-am install-data-local \
	install-dvi install-dvi-am install-exec install-exec-am \
	install-html install-html-am install-info install-info-am \
	install-man install-pdf install-pdf-am install-ps \
	install-ps-am install-strip installcheck installcheck-am \
	installdirs maintainer-clean maintainer-clean-generic \
	maintainer-clean-local mostlyclean mostlyclean-generic \
	mostlyclean-libtool pdf pdf-am ps ps-am tags-am uninstall \
	uninstall-am uninstall-local

.PRECIOUS: Makefile


@<EMAIL>: Makefile
@ENABLE_GTK_DOC_TRUE@	$(AM_V_GEN)echo "#!/bin/sh -e" > $@; \
@ENABLE_GTK_DOC_TRUE@		echo "$(GTKDOC_CHECK_PATH) || exit 1" >> $@; \
@ENABLE_GTK_DOC_TRUE@		chmod +x $@

@ENABLE_GTK_DOC_TRUE@all-gtk-doc: $(HTML_BUILD_STAMP) $(PDF_BUILD_STAMP)
@ENABLE_GTK_DOC_TRUE@.PHONY: all-gtk-doc

@ENABLE_GTK_DOC_TRUE@all-local: all-gtk-doc

@ENABLE_GTK_DOC_TRUE@docs: $(HTML_BUILD_STAMP) $(PDF_BUILD_STAMP)

@ENABLE_GTK_DOC_TRUE@$(REPORT_FILES): sgml-build.stamp

@<EMAIL>:
@ENABLE_GTK_DOC_TRUE@	-$(GTK_DOC_V_SETUP)if test "$(abs_srcdir)" != "$(abs_builddir)" ; then \
@ENABLE_GTK_DOC_TRUE@	    files=`echo $(SETUP_FILES) $(DOC_MODULE).types`; \
@ENABLE_GTK_DOC_TRUE@	    if test "x$$files" != "x" ; then \
@ENABLE_GTK_DOC_TRUE@	        for file in $$files ; do \
@ENABLE_GTK_DOC_TRUE@	            destdir=`dirname $(abs_builddir)/$$file`; \
@ENABLE_GTK_DOC_TRUE@	            test -d "$$destdir" || mkdir -p "$$destdir"; \
@ENABLE_GTK_DOC_TRUE@	            test -f $(abs_srcdir)/$$file && \
@ENABLE_GTK_DOC_TRUE@	                cp -pf $(abs_srcdir)/$$file $(abs_builddir)/$$file || true; \
@ENABLE_GTK_DOC_TRUE@	        done; \
@ENABLE_GTK_DOC_TRUE@	    fi; \
@ENABLE_GTK_DOC_TRUE@	fi
@ENABLE_GTK_DOC_TRUE@	$(AM_V_at)touch setup-build.stamp

@<EMAIL>: setup-build.stamp $(HFILE_GLOB) $(CFILE_GLOB)
@ENABLE_GTK_DOC_TRUE@	$(GTK_DOC_V_SCAN)_source_dir='' ; \
@ENABLE_GTK_DOC_TRUE@	for i in $(DOC_SOURCE_DIR) ; do \
@ENABLE_GTK_DOC_TRUE@	    _source_dir="$${_source_dir} --source-dir=$$i" ; \
@ENABLE_GTK_DOC_TRUE@	done ; \
@ENABLE_GTK_DOC_TRUE@	gtkdoc-scan --module=$(DOC_MODULE) --ignore-headers="$(IGNORE_HFILES)" $${_source_dir} $(SCAN_OPTIONS) $(EXTRA_HFILES)
@ENABLE_GTK_DOC_TRUE@	$(GTK_DOC_V_INTROSPECT)if grep -l '^..*$$' $(DOC_MODULE).types > /dev/null 2>&1 ; then \
@ENABLE_GTK_DOC_TRUE@	    scanobj_options=""; \
@ENABLE_GTK_DOC_TRUE@	    gtkdoc-scangobj 2>&1 --help | grep  >/dev/null "\-\-verbose"; \
@ENABLE_GTK_DOC_TRUE@	    if test "$$?" = "0"; then \
@ENABLE_GTK_DOC_TRUE@	        if test "x$(V)" = "x1"; then \
@ENABLE_GTK_DOC_TRUE@	            scanobj_options="--verbose"; \
@ENABLE_GTK_DOC_TRUE@	        fi; \
@ENABLE_GTK_DOC_TRUE@	    fi; \
@ENABLE_GTK_DOC_TRUE@	    CC="$(GTKDOC_CC)" LD="$(GTKDOC_LD)" RUN="$(GTKDOC_RUN)" CFLAGS="$(GTKDOC_CFLAGS) $(CFLAGS)" LDFLAGS="$(GTKDOC_LIBS) $(LDFLAGS)" \
@ENABLE_GTK_DOC_TRUE@	    gtkdoc-scangobj $(SCANGOBJ_OPTIONS) $$scanobj_options --module=$(DOC_MODULE); \
@ENABLE_GTK_DOC_TRUE@	else \
@ENABLE_GTK_DOC_TRUE@	    for i in $(SCANOBJ_FILES) ; do \
@ENABLE_GTK_DOC_TRUE@	        test -f $$i || touch $$i ; \
@ENABLE_GTK_DOC_TRUE@	    done \
@ENABLE_GTK_DOC_TRUE@	fi
@ENABLE_GTK_DOC_TRUE@	$(AM_V_at)touch scan-build.stamp

@ENABLE_GTK_DOC_TRUE@$(DOC_MODULE)-decl.txt $(SCANOBJ_FILES) $(DOC_MODULE)-sections.txt $(DOC_MODULE)-overrides.txt: scan-build.stamp
@ENABLE_GTK_DOC_TRUE@	@true

@<EMAIL>: setup-build.stamp $(DOC_MODULE)-decl.txt $(SCANOBJ_FILES) $(HFILE_GLOB) $(CFILE_GLOB) $(DOC_MODULE)-sections.txt $(DOC_MODULE)-overrides.txt $(expand_content_files) xml/gtkdocentities.ent
@ENABLE_GTK_DOC_TRUE@	$(GTK_DOC_V_XML)_source_dir='' ; \
@ENABLE_GTK_DOC_TRUE@	for i in $(DOC_SOURCE_DIR) ; do \
@ENABLE_GTK_DOC_TRUE@	    _source_dir="$${_source_dir} --source-dir=$$i" ; \
@ENABLE_GTK_DOC_TRUE@	done ; \
@ENABLE_GTK_DOC_TRUE@	gtkdoc-mkdb --module=$(DOC_MODULE) --output-format=xml --expand-content-files="$(expand_content_files)" --main-sgml-file=$(DOC_MAIN_SGML_FILE) $${_source_dir} $(MKDB_OPTIONS)
@ENABLE_GTK_DOC_TRUE@	$(AM_V_at)touch sgml-build.stamp

@<EMAIL>: sgml-build.stamp
@ENABLE_GTK_DOC_TRUE@	@true

@ENABLE_GTK_DOC_TRUE@xml/gtkdocentities.ent: Makefile
@ENABLE_GTK_DOC_TRUE@	$(GTK_DOC_V_XML)$(MKDIR_P) $(@D) && ( \
@ENABLE_GTK_DOC_TRUE@		echo "<!ENTITY package \"$(PACKAGE)\">"; \
@ENABLE_GTK_DOC_TRUE@		echo "<!ENTITY package_bugreport \"$(PACKAGE_BUGREPORT)\">"; \
@ENABLE_GTK_DOC_TRUE@		echo "<!ENTITY package_name \"$(PACKAGE_NAME)\">"; \
@ENABLE_GTK_DOC_TRUE@		echo "<!ENTITY package_string \"$(PACKAGE_STRING)\">"; \
@ENABLE_GTK_DOC_TRUE@		echo "<!ENTITY package_tarname \"$(PACKAGE_TARNAME)\">"; \
@ENABLE_GTK_DOC_TRUE@		echo "<!ENTITY package_url \"$(PACKAGE_URL)\">"; \
@ENABLE_GTK_DOC_TRUE@		echo "<!ENTITY package_version \"$(PACKAGE_VERSION)\">"; \
@ENABLE_GTK_DOC_TRUE@	) > $@

@<EMAIL>: sgml.stamp $(DOC_MAIN_SGML_FILE) $(content_files) $(expand_content_files)
@ENABLE_GTK_DOC_TRUE@	$(GTK_DOC_V_HTML)rm -rf html && mkdir html && \
@ENABLE_GTK_DOC_TRUE@	mkhtml_options=""; \
@ENABLE_GTK_DOC_TRUE@	gtkdoc-mkhtml 2>&1 --help | grep  >/dev/null "\-\-verbose"; \
@ENABLE_GTK_DOC_TRUE@	if test "$$?" = "0"; then \
@ENABLE_GTK_DOC_TRUE@	  if test "x$(V)" = "x1"; then \
@ENABLE_GTK_DOC_TRUE@	    mkhtml_options="$$mkhtml_options --verbose"; \
@ENABLE_GTK_DOC_TRUE@	  fi; \
@ENABLE_GTK_DOC_TRUE@	fi; \
@ENABLE_GTK_DOC_TRUE@	gtkdoc-mkhtml 2>&1 --help | grep  >/dev/null "\-\-path"; \
@ENABLE_GTK_DOC_TRUE@	if test "$$?" = "0"; then \
@ENABLE_GTK_DOC_TRUE@	  mkhtml_options="$$mkhtml_options --path=\"$(abs_srcdir)\""; \
@ENABLE_GTK_DOC_TRUE@	fi; \
@ENABLE_GTK_DOC_TRUE@	cd html && gtkdoc-mkhtml $$mkhtml_options $(MKHTML_OPTIONS) $(DOC_MODULE) ../$(DOC_MAIN_SGML_FILE)
@ENABLE_GTK_DOC_TRUE@	-@test "x$(HTML_IMAGES)" = "x" || \
@ENABLE_GTK_DOC_TRUE@	for file in $(HTML_IMAGES) ; do \
@ENABLE_GTK_DOC_TRUE@	  if test -f $(abs_srcdir)/$$file ; then \
@ENABLE_GTK_DOC_TRUE@	    cp $(abs_srcdir)/$$file $(abs_builddir)/html; \
@ENABLE_GTK_DOC_TRUE@	  fi; \
@ENABLE_GTK_DOC_TRUE@	  if test -f $(abs_builddir)/$$file ; then \
@ENABLE_GTK_DOC_TRUE@	    cp $(abs_builddir)/$$file $(abs_builddir)/html; \
@ENABLE_GTK_DOC_TRUE@	  fi; \
@ENABLE_GTK_DOC_TRUE@	done;
@ENABLE_GTK_DOC_TRUE@	$(GTK_DOC_V_XREF)gtkdoc-fixxref --module=$(DOC_MODULE) --module-dir=html --html-dir=$(HTML_DIR) $(FIXXREF_OPTIONS)
@ENABLE_GTK_DOC_TRUE@	$(AM_V_at)touch html-build.stamp

@<EMAIL>: sgml.stamp $(DOC_MAIN_SGML_FILE) $(content_files) $(expand_content_files)
@ENABLE_GTK_DOC_TRUE@	$(GTK_DOC_V_PDF)rm -f $(DOC_MODULE).pdf && \
@ENABLE_GTK_DOC_TRUE@	mkpdf_options=""; \
@ENABLE_GTK_DOC_TRUE@	gtkdoc-mkpdf 2>&1 --help | grep  >/dev/null "\-\-verbose"; \
@ENABLE_GTK_DOC_TRUE@	if test "$$?" = "0"; then \
@ENABLE_GTK_DOC_TRUE@	  if test "x$(V)" = "x1"; then \
@ENABLE_GTK_DOC_TRUE@	    mkpdf_options="$$mkpdf_options --verbose"; \
@ENABLE_GTK_DOC_TRUE@	  fi; \
@ENABLE_GTK_DOC_TRUE@	fi; \
@ENABLE_GTK_DOC_TRUE@	if test "x$(HTML_IMAGES)" != "x"; then \
@ENABLE_GTK_DOC_TRUE@	  for img in $(HTML_IMAGES); do \
@ENABLE_GTK_DOC_TRUE@	    part=`dirname $$img`; \
@ENABLE_GTK_DOC_TRUE@	    echo $$mkpdf_options | grep >/dev/null "\-\-imgdir=$$part "; \
@ENABLE_GTK_DOC_TRUE@	    if test $$? != 0; then \
@ENABLE_GTK_DOC_TRUE@	      mkpdf_options="$$mkpdf_options --imgdir=$$part"; \
@ENABLE_GTK_DOC_TRUE@	    fi; \
@ENABLE_GTK_DOC_TRUE@	  done; \
@ENABLE_GTK_DOC_TRUE@	fi; \
@ENABLE_GTK_DOC_TRUE@	gtkdoc-mkpdf --path="$(abs_srcdir)" $$mkpdf_options $(DOC_MODULE) $(DOC_MAIN_SGML_FILE) $(MKPDF_OPTIONS)
@ENABLE_GTK_DOC_TRUE@	$(AM_V_at)touch pdf-build.stamp

##############

@ENABLE_GTK_DOC_TRUE@clean-local:
@ENABLE_GTK_DOC_TRUE@	@rm -f *~ *.bak
@ENABLE_GTK_DOC_TRUE@	@rm -rf .libs
@ENABLE_GTK_DOC_TRUE@	@if echo $(SCAN_OPTIONS) | grep -q "\-\-rebuild-types" ; then \
@ENABLE_GTK_DOC_TRUE@	  rm -f $(DOC_MODULE).types; \
@ENABLE_GTK_DOC_TRUE@	fi
@ENABLE_GTK_DOC_TRUE@	@if echo $(SCAN_OPTIONS) | grep -q "\-\-rebuild-sections" ; then \
@ENABLE_GTK_DOC_TRUE@	  rm -f $(DOC_MODULE)-sections.txt; \
@ENABLE_GTK_DOC_TRUE@	fi

@ENABLE_GTK_DOC_TRUE@distclean-local:
@ENABLE_GTK_DOC_TRUE@	@rm -rf xml html $(REPORT_FILES) $(DOC_MODULE).pdf \
@ENABLE_GTK_DOC_TRUE@	    $(DOC_MODULE)-decl-list.txt $(DOC_MODULE)-decl.txt
@ENABLE_GTK_DOC_TRUE@	@if test "$(abs_srcdir)" != "$(abs_builddir)" ; then \
@ENABLE_GTK_DOC_TRUE@	    rm -f $(SETUP_FILES) $(DOC_MODULE).types; \
@ENABLE_GTK_DOC_TRUE@	fi

@ENABLE_GTK_DOC_TRUE@maintainer-clean-local:
@ENABLE_GTK_DOC_TRUE@	@rm -rf xml html

@ENABLE_GTK_DOC_TRUE@install-data-local:
@ENABLE_GTK_DOC_TRUE@	@installfiles=`echo $(builddir)/html/*`; \
@ENABLE_GTK_DOC_TRUE@	if test "$$installfiles" = '$(builddir)/html/*'; \
@ENABLE_GTK_DOC_TRUE@	then echo 1>&2 'Nothing to install' ; \
@ENABLE_GTK_DOC_TRUE@	else \
@ENABLE_GTK_DOC_TRUE@	  if test -n "$(DOC_MODULE_VERSION)"; then \
@ENABLE_GTK_DOC_TRUE@	    installdir="$(DESTDIR)$(TARGET_DIR)-$(DOC_MODULE_VERSION)"; \
@ENABLE_GTK_DOC_TRUE@	  else \
@ENABLE_GTK_DOC_TRUE@	    installdir="$(DESTDIR)$(TARGET_DIR)"; \
@ENABLE_GTK_DOC_TRUE@	  fi; \
@ENABLE_GTK_DOC_TRUE@	  $(mkinstalldirs) $${installdir} ; \
@ENABLE_GTK_DOC_TRUE@	  for i in $$installfiles; do \
@ENABLE_GTK_DOC_TRUE@	    echo ' $(INSTALL_DATA) '$$i ; \
@ENABLE_GTK_DOC_TRUE@	    $(INSTALL_DATA) $$i $${installdir}; \
@ENABLE_GTK_DOC_TRUE@	  done; \
@ENABLE_GTK_DOC_TRUE@	  if test -n "$(DOC_MODULE_VERSION)"; then \
@ENABLE_GTK_DOC_TRUE@	    mv -f $${installdir}/$(DOC_MODULE).devhelp2 \
@ENABLE_GTK_DOC_TRUE@	      $${installdir}/$(DOC_MODULE)-$(DOC_MODULE_VERSION).devhelp2; \
@ENABLE_GTK_DOC_TRUE@	  fi; \
@ENABLE_GTK_DOC_TRUE@	  $(GTKDOC_REBASE) --relative --dest-dir=$(DESTDIR) --html-dir=$${installdir}; \
@ENABLE_GTK_DOC_TRUE@	fi

@ENABLE_GTK_DOC_TRUE@uninstall-local:
@ENABLE_GTK_DOC_TRUE@	@if test -n "$(DOC_MODULE_VERSION)"; then \
@ENABLE_GTK_DOC_TRUE@	  installdir="$(DESTDIR)$(TARGET_DIR)-$(DOC_MODULE_VERSION)"; \
@ENABLE_GTK_DOC_TRUE@	else \
@ENABLE_GTK_DOC_TRUE@	  installdir="$(DESTDIR)$(TARGET_DIR)"; \
@ENABLE_GTK_DOC_TRUE@	fi; \
@ENABLE_GTK_DOC_TRUE@	rm -rf $${installdir}

#
# Require gtk-doc when making dist
#
@ENABLE_GTK_DOC_TRUE@@HAVE_GTK_DOC_TRUE@dist-check-gtkdoc: docs
@ENABLE_GTK_DOC_TRUE@@HAVE_GTK_DOC_FALSE@dist-check-gtkdoc:
@ENABLE_GTK_DOC_TRUE@@HAVE_GTK_DOC_FALSE@	@echo "*** gtk-doc is needed to run 'make dist'.         ***"
@ENABLE_GTK_DOC_TRUE@@HAVE_GTK_DOC_FALSE@	@echo "*** gtk-doc was not found when 'configure' ran.   ***"
@ENABLE_GTK_DOC_TRUE@@HAVE_GTK_DOC_FALSE@	@echo "*** please install gtk-doc and rerun 'configure'. ***"
@ENABLE_GTK_DOC_TRUE@@HAVE_GTK_DOC_FALSE@	@false

@ENABLE_GTK_DOC_TRUE@dist-hook: dist-check-gtkdoc all-gtk-doc dist-hook-local
@ENABLE_GTK_DOC_TRUE@	@mkdir $(distdir)/html
@ENABLE_GTK_DOC_TRUE@	@cp ./html/* $(distdir)/html
@ENABLE_GTK_DOC_TRUE@	@-cp ./$(DOC_MODULE).pdf $(distdir)/
@ENABLE_GTK_DOC_TRUE@	@-cp ./$(DOC_MODULE).types $(distdir)/
@ENABLE_GTK_DOC_TRUE@	@-cp ./$(DOC_MODULE)-sections.txt $(distdir)/
@ENABLE_GTK_DOC_TRUE@	@cd $(distdir) && rm -f $(DISTCLEANFILES)
@ENABLE_GTK_DOC_TRUE@	@$(GTKDOC_REBASE) --online --relative --html-dir=$(distdir)/html

@ENABLE_GTK_DOC_TRUE@.PHONY : dist-hook-local docs

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

<?xml version="1.0" encoding="UTF-8" ?>

<!--
 ModemManager 1.0 Interface Specification

   Copyright (C) 2011-2013 Red Hat, Inc.
   Copyright (C) 2011-2013 Google, Inc.
   Copyright (C) 2011-2013 Lanedo GmbH
-->

<node name="/" xmlns:doc="http://www.freedesktop.org/dbus/1.0/doc.dtd">

  <!--
      org.freedesktop.ModemManager1.Sms:
      @short_description: The ModemManager SMS interface.

      The SMS interface Defines operations and properties of a single SMS message.
  -->
  <interface name="org.freedesktop.ModemManager1.Sms">

    <!--
        Send:

        If the message has not yet been sent, queue it for delivery.
    -->
    <method name="Send" />

    <!--
        Store:

        Store the message in the device if not already done.

        This method requires a <link linkend="MMSmsStorage">MMSmsStorage</link>
        value, describing the storage where this message is to be kept; or
        <link linkend="MM-SMS-STORAGE-UNKNOWN:CAPS"><constant>MM_SMS_STORAGE_UNKNOWN</constant></link>
        if the default storage should be used.
    -->
    <method name="Store">
      <arg name="storage" type="u" direction="in" />
    </method>

    <!--
        State:

        A <link linkend="MMSmsState">MMSmsState</link> value,
        describing the state of the message.
    -->
    <property name="State" type="u" access="read" />

    <!--
        PduType:

        A <link linkend="MMSmsPduType">MMSmsPduType</link> value,
        describing the type of PDUs used in the SMS message.
    -->
    <property name="PduType" type="u" access="read" />

    <!--
        Number:

        Number to which the message is addressed.
    -->
    <property name="Number" type="s" access="read" />

    <!--
        Text:

        Message text, in UTF-8.

        When sending, if the text is larger than the limit of the technology or
        modem, the message will be broken into multiple parts or messages.

        Note that Text and Data are never given at the same time.
    -->
    <property name="Text" type="s" access="read" />

    <!--
        Data:

        Message data.

        When sending, if the data is larger than the limit of the technology or
        modem, the message will be broken into multiple parts or messages.

        Note that Text and Data are never given at the same time.
    -->
    <property name="Data" type="ay" access="read" />

    <!--
        SMSC:

        Indicates the SMS service center number.

        Always empty for 3GPP2/CDMA.
    -->
    <property name="SMSC" type="s" access="read" />

    <!--
        Validity:

        Indicates when the SMS expires in the SMSC.

        This value is composed of a
        <link linkend="MMSmsValidityType">MMSmsValidityType</link>
        key, with an associated data which contains type-specific validity
        information:

        <variablelist>
         <varlistentry><term><link linkend="MM-SMS-VALIDITY-TYPE-RELATIVE:CAPS">MM_SMS_VALIDITY_TYPE_RELATIVE</link></term>
          <listitem>
           <para>
             The value is the length of the validity period in minutes, given
             as an unsigned integer (D-Bus signature <literal>'u'</literal>).
            </para>
          </listitem>
         </varlistentry>
        </variablelist>
    -->
    <property name="Validity" type="(uv)" access="read" />

    <!--
        Class:

        3GPP message class (-1..3).  -1 means class is not available or
        is not used for this message, otherwise the 3GPP SMS message class.

        Always -1 for 3GPP2/CDMA.
    -->
    <property name="Class" type="i" access="read" />

    <!--
        TeleserviceId:

        A <link linkend="MMSmsCdmaTeleserviceId">MMSmsCdmaTeleserviceId</link> value.

        Always <link linkend="MM-SMS-CDMA-TELESERVICE-ID-UNKNOWN:CAPS">MM_SMS_CDMA_TELESERVICE_ID_UNKNOWN</link> for 3GPP.
    -->
    <property name="TeleserviceId" type="u" access="read" />

    <!--
        ServiceCategory:

        A <link linkend="MMSmsCdmaServiceCategory">MMSmsCdmaServiceCategory</link> value.

        Always <link linkend="MM-SMS-CDMA-SERVICE-CATEGORY-UNKNOWN:CAPS">MM_SMS_CDMA_SERVICE_CATEGORY_UNKNOWN</link> for 3GPP.
    -->
    <property name="ServiceCategory" type="u" access="read" />

    <!--
        DeliveryReportRequest:

        #TRUE if delivery report request is required, #FALSE otherwise.
    -->
    <property name="DeliveryReportRequest" type="b" access="read" />

    <!--
        MessageReference:

        Message Reference of the last PDU sent/received within this SMS.

        If the PDU type is
        <link linkend="MM-SMS-PDU-TYPE-STATUS-REPORT:CAPS"><constant>MM_SMS_PDU_TYPE_STATUS_REPORT</constant></link>,
        this field identifies the Message Reference of the PDU associated to the status report.
    -->
    <property name="MessageReference" type="u" access="read" />

    <!--
        Timestamp:

        Time when the first PDU of the SMS message arrived the SMSC, in
        <ulink url="http://en.wikipedia.org/wiki/ISO_8601">ISO8601</ulink>
        format.
        This field is only applicable if the PDU type is
        <link linkend="MM-SMS-PDU-TYPE-DELIVER:CAPS"><constant>MM_SMS_PDU_TYPE_DELIVER</constant></link>.
        or
        <link linkend="MM-SMS-PDU-TYPE-STATUS-REPORT:CAPS"><constant>MM_SMS_PDU_TYPE_STATUS_REPORT</constant></link>.
    -->
    <property name="Timestamp" type="s" access="read" />

    <!--
        DischargeTimestamp:

        Time when the first PDU of the SMS message left the SMSC, in
        <ulink url="http://en.wikipedia.org/wiki/ISO_8601">ISO8601</ulink>
        format.

        This field is only applicable if the PDU type is
        <link linkend="MM-SMS-PDU-TYPE-STATUS-REPORT:CAPS"><constant>MM_SMS_PDU_TYPE_STATUS_REPORT</constant></link>.
    -->
    <property name="DischargeTimestamp" type="s" access="read" />

    <!--
        DeliveryState:

        A <link linkend="MMSmsDeliveryState">MMSmsDeliveryState</link> value,
        describing the state of the delivery reported in the Status Report message.

        This field is only applicable if the PDU type is
        <link linkend="MM-SMS-PDU-TYPE-STATUS-REPORT:CAPS"><constant>MM_SMS_PDU_TYPE_STATUS_REPORT</constant></link>.
    -->
    <property name="DeliveryState" type="u" access="read" />

    <!--
        Storage:

        A <link linkend="MMSmsStorage">MMSmsStorage</link> value,
        describing the storage where this message is kept.
    -->
    <property name="Storage" type="u" access="read" />

  </interface>
</node>

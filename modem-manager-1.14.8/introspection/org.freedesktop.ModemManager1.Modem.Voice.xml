<?xml version="1.0" encoding="UTF-8" ?>

<!--
 ModemManager 1.0 Interface Specification

   Copyright (C) 2015 <PERSON> <<EMAIL>>
   Copyright (C) 2015 <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
   Copyright (C) 2019 Purism SPC
-->

<node name="/" xmlns:doc="http://www.freedesktop.org/dbus/1.0/doc.dtd">

  <!--
      org.freedesktop.ModemManager1.Modem.Voice:
      @short_description: The ModemManager Voice interface.

      The Voice interface handles Calls.

      This interface will only be available once the modem is ready to be
      registered in the cellular network. 3GPP devices will require a valid
      unlocked SIM card before any of the features in the interface can be
      used.
  -->
  <interface name="org.freedesktop.ModemManager1.Modem.Voice">

    <!--
        ListCalls:
        @result: The list of call object paths.

        Retrieve all Calls.

        This method should only be used once and subsequent information
        retrieved either by listening for the
        #org.freedesktop.ModemManager1.Modem.Voice::Added signal,
        or by querying the specific Call object of interest.
    -->
    <method name="ListCalls">
      <arg name="result" type="ao" direction="out" />
    </method>

    <!--
        DeleteCall:
        @path: The object path of the Call to delete.

        Delete a Call from the list of calls.

        The call will be hangup if it is still active.
    -->
    <method name="DeleteCall">
      <arg name="path" type="o" direction="in" />
    </method>

    <!--
        CreateCall:
        @properties: Call properties from the <link linkend="gdbus-org.freedesktop.ModemManager1.Call">Call D-Bus interface</link>.
        @path: The object path of the new call object.

        Creates a new call object for a new outgoing call.

        The '<link linkend="gdbus-property-org-freedesktop-ModemManager1-Call.Number">Number</link>' is the only
        expected property to set by the user.
    -->
    <method name="CreateCall">
      <arg name="properties" type="a{sv}" direction="in" />
      <arg name="path" type="o" direction="out" />
    </method>

    <!--
        HoldAndAccept:

        Place all active calls on hold, if any, and accept the next
        call.

        Waiting calls have preference over held calls, so the next
        call being active will be any waiting call, or otherwise,
        any held call.

        The user should monitor the state of all available ongoing
        calls to be reported of which one becomes active.

        No error is returned if there are no waiting or held calls.
    -->
    <method name="HoldAndAccept" />

    <!--
        HangupAndAccept:

        Hangup all active calls, if any, and accept the next call.

        Waiting calls have preference over held calls, so the next
        call being active will be any waiting call, or otherwise,
        any held call.

        The user should monitor the state of all available ongoing
        calls to be reported of which one becomes active.

        No error is returned if there are no waiting or held calls.
        In this case, this method would be equivalent to calling
        <link linkend="gdbus-method-org-freedesktop-ModemManager1-Call.Hangup">Hangup()</link>
        on the active call.
    -->
    <method name="HangupAndAccept" />

    <!--
        HangupAll:

        Hangup all active calls.

        Depending on how the device implements the action, calls on
        hold or in waiting state may also be terminated.

        No error is returned if there are no ongoing calls.
    -->
    <method name="HangupAll" />

    <!--
        Transfer:

        Join the currently active and held calls together into a single
        multiparty call, but disconnects from them.

        The affected calls will be considered terminated from the point of
        view of the subscriber.
    -->
    <method name="Transfer" />

    <!--
        CallWaitingSetup:

        Activates or deactivates the call waiting network service, as per
        3GPP TS 22.083.

        This operation requires communication with the network in order to
        complete, so the modem must be successfully registered.
    -->
    <method name="CallWaitingSetup">
      <arg name="enable" type="b" direction="in" />
    </method>

    <!--
        CallWaitingQuery:

        Queries the status of the call waiting network service, as per
        3GPP TS 22.083.

        This operation requires communication with the network in order to
        complete, so the modem must be successfully registered.
    -->
    <method name="CallWaitingQuery">
      <arg name="status" type="b" direction="out" />
    </method>

    <!--
        CallAdded:
        @path: Object path of the new call.

        Emitted when a call has been added.
    -->
    <signal name="CallAdded">
      <arg name="path" type="o" />
    </signal>

    <!--
        CallDeleted:
        @path: Object path of the now deleted Call.

        Emitted when a call has been deleted.
    -->
    <signal name="CallDeleted">
      <arg name="path" type="o" />
    </signal>

    <!--
        Calls:

        The list of calls object paths.
    -->
    <property name="Calls" type="ao" access="read" />

    <!--
        EmergencyOnly:

        A flag indicating whether emergency calls are the only allowed ones.

        If this flag is set, users should only attempt voice calls to
        emergency numbers, as standard voice calls will likely fail.
    -->
    <property name="EmergencyOnly" type="b" access="read" />

  </interface>
</node>

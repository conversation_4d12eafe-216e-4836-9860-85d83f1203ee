<?xml version="1.0" encoding="UTF-8" ?>

<!--
 ModemManager 1.0 Interface Specification

   Copyright (C) 2008 Novell, Inc.
   Copyright (C) 2008-2013 Red Hat, Inc.
   Copyright (C) 2011-2013 Google, Inc.
   Copyright (C) 2011-2013 Lanedo GmbH
-->

<node name="/" xmlns:doc="http://www.freedesktop.org/dbus/1.0/doc.dtd">

  <!--
      org.freedesktop.ModemManager1.Modem.Modem3gpp:
      @short_description: The ModemManager 3GPP interface.

      This interface provides access to specific actions that may be performed
      in modems with 3GPP capabilities.

      This interface will only be available once the modem is ready to be
      registered in the cellular network. 3GPP devices will require a valid
      unlocked SIM card before any of the features in the interface can be
      used.
  -->
  <interface name="org.freedesktop.ModemManager1.Modem.Modem3gpp">

    <!--
        Register:
        @operator_id: The operator ID (ie, <literal>"MCCMNC"</literal>, like <literal>"310260"</literal>) to register. An empty string can be used to register to the home network.

        Request registration with a given mobile network.
    -->
    <method name="Register">
      <arg name="operator_id" type="s" direction="in" />
    </method>

    <!--
        Scan:
        @results: Array of dictionaries with the found networks.

        Scan for available networks.

        @results is an array of dictionaries with each array element describing
        a mobile network found in the scan. Each dictionary may include one or
        more of the following keys:
        <variablelist>
          <varlistentry><term><literal>"status"</literal></term>
            <listitem>
              A <link linkend="MMModem3gppNetworkAvailability">MMModem3gppNetworkAvailability</link>
              value representing network availability status, given as an
              unsigned integer (signature <literal>"u"</literal>). This key will
              always be present.
            </listitem>
          </varlistentry>
          <varlistentry><term><literal>"operator-long"</literal></term>
            <listitem>
              Long-format name of operator, given as a string value (signature
              <literal>"s"</literal>). If the name is unknown, this field
              should not be present.
            </listitem>
          </varlistentry>
          <varlistentry><term><literal>"operator-short"</literal></term>
            <listitem>
              Short-format name of operator, given as a string value (signature
              <literal>"s"</literal>). If the name is unknown, this field
              should not be present.
            </listitem>
          </varlistentry>
          <varlistentry><term><literal>"operator-code"</literal></term>
            <listitem>
              Mobile code of the operator, given as a string value (signature
              <literal>"s"</literal>). Returned in the format
              <literal>"MCCMNC"</literal>, where <literal>MCC</literal> is the
              three-digit ITU E.212 Mobile Country Code and <literal>MNC</literal>
              is the two- or three-digit GSM Mobile Network Code. e.g.
              <literal>"31026"</literal> or <literal>"310260"</literal>.
            </listitem>
          </varlistentry>
          <varlistentry><term><literal>"access-technology"</literal></term>
            <listitem>
              A <link linkend="MMModemAccessTechnology">MMModemAccessTechnology</link> value
              representing the generic access technology used by this mobile network,
              given as an unsigned integer (signature <literal>"u"</literal>).
            </listitem>
          </varlistentry>
        </variablelist>
    -->
    <method name="Scan">
      <arg name="results" type="aa{sv}" direction="out" />
    </method>

    <!--
        SetEpsUeModeOperation:
        @mode: a <link linkend="MMModem3gppEpsUeModeOperation">MMModem3gppEpsUeModeOperation</link>.

        Sets the UE mode of operation for EPS.
    -->
    <method name="SetEpsUeModeOperation">
      <arg name="mode" type="u" direction="in" />
    </method>

    <!--
        SetInitialEpsBearerSettings:
        @settings: List of properties to use when requesting the LTE attach procedure.

        Updates the default settings to be used in the initial default EPS bearer when registering to the LTE network.

        Allowed properties are:
        <variablelist>
        <varlistentry><term><literal>"apn"</literal></term>
          <listitem><para>Access Point Name, given as a string value (signature <literal>"s"</literal>).</para></listitem></varlistentry>
        <varlistentry><term><literal>"ip-type"</literal></term>
          <listitem><para>Addressing type, given as a <link linkend="MMBearerIpFamily">MMBearerIpFamily</link> value (signature <literal>"u"</literal>).</para></listitem></varlistentry>
        <varlistentry><term><literal>"allowed-auth"</literal></term>
          <listitem><para>The authentication method to use, given as a <link linkend="MMBearerAllowedAuth">MMBearerAllowedAuth</link> value (signature <literal>"u"</literal>).</para></listitem></varlistentry>
        <varlistentry><term><literal>"user"</literal></term>
          <listitem><para>User name (if any) required by the network, given as a string value (signature <literal>"s"</literal>).</para></listitem></varlistentry>
        <varlistentry><term><literal>"password"</literal></term>
          <listitem><para>Password (if any) required by the network, given as a string value (signature <literal>"s"</literal>).</para></listitem></varlistentry>
        </variablelist>
    -->
    <method name="SetInitialEpsBearerSettings">
      <arg name="settings" type="a{sv}" direction="in" />
    </method>

    <!--
        Imei:

        The <ulink url="http://en.wikipedia.org/wiki/Imei">IMEI</ulink> of the device.
    -->
    <property name="Imei" type="s" access="read" />

    <!--
        RegistrationState:

        A <link linkend="MMModem3gppRegistrationState">MMModem3gppRegistrationState</link>
        value specifying the mobile registration status as defined in 3GPP TS 27.007
        section 10.1.19.
    -->
    <property name="RegistrationState" type="u" access="read" />

    <!--
        OperatorCode:

        Code of the operator to which the mobile is currently registered.

        Returned in the format <literal>"MCCMNC"</literal>, where
        <literal>MCC</literal> is the three-digit ITU E.212 Mobile Country Code
        and <literal>MNC</literal> is the two- or three-digit GSM Mobile Network
        Code. e.g. e<literal>"31026"</literal> or <literal>"310260"</literal>.

        If the <literal>MCC</literal> and <literal>MNC</literal> are not known
        or the mobile is not registered to a mobile network, this property will
        be a zero-length (blank) string.
    -->
    <property name="OperatorCode" type="s" access="read" />

    <!--
        OperatorName:

        Name of the operator to which the mobile is currently registered.

        If the operator name is not known or the mobile is not
        registered to a mobile network, this property will be a zero-length
        (blank) string.
    -->
    <property name="OperatorName" type="s" access="read" />

    <!--
        EnabledFacilityLocks:

        Bitmask of <link linkend="MMModem3gppFacility">MMModem3gppFacility</link> values
        for which PIN locking is enabled.
    -->
    <property name="EnabledFacilityLocks" type="u" access="read" />

    <!--
        SubscriptionState:

        A <link linkend="MMModem3gppSubscriptionState">MMModem3gppSubscriptionState</link>
        value representing the subscription status of the account and whether there
        is any data remaining, given as an unsigned integer (signature <literal>"u"</literal>).

        Deprecated: 1.10.0. The value of this property can only be obtained with operator
        specific logic (e.g. processing specific PCO info), and therefore it doesn't make sense
        to expose it in the ModemManager interface.
    -->
    <property name="SubscriptionState" type="u" access="read" />

    <!--
        EpsUeModeOperation:

        A <link linkend="MMModem3gppEpsUeModeOperation">MMModem3gppEpsUeModeOperation</link>
        value representing the UE mode of operation for EPS, given as an unsigned integer
        (signature <literal>"u"</literal>).
    -->
    <property name="EpsUeModeOperation" type="u" access="read" />

    <!--
        Pco:

        The raw PCOs received from the network, given as array of PCO
        elements (signature <literal>"a(ubay)"</literal>).

        Each PCO is defined as a sequence of 3 fields:
        <orderedlist>
          <listitem>
            The session ID associated with the PCO, given as an
            unsigned integer value (signature <literal>"u"</literal>).
          </listitem>
          <listitem>
            The flag that indicates whether the PCO data contains the
            complete PCO structure received from the network, given as
            a boolean value (signature <literal>"b"</literal>).
          </listitem>
          <listitem>
            The raw  PCO data, given as an array of bytes (signature
            <literal>"ay"</literal>).
          </listitem>
        </orderedlist>
    -->
    <property name="Pco" type="a(ubay)" access="read" />

    <!--
        InitialEpsBearer:

        The object path for the initial default EPS bearer.
    -->
    <property name="InitialEpsBearer" type="o" access="read" />

    <!--
        InitialEpsBearerSettings:

        List of properties requested by the device for the initial EPS bearer during
        LTE network attach procedure.

        The network may decide to use different settings during the actual device attach
        procedure, e.g. if the device is roaming or no explicit settings were requested,
        so the values shown in the
        #org.freedesktop.ModemManager1.Modem.Modem3gpp:InitialEpsBearer
        bearer object may be totally different.

        This is a read-only property, updating these settings should be done using the
        <link linkend="gdbus-method-org-freedesktop-ModemManager1-Modem-Modem3gpp.SetInitialEpsBearerSettings">SetInitialEpsBearerSettings()</link>
        method.
    -->
    <property name="InitialEpsBearerSettings" type="a{sv}" access="read" />

  </interface>
</node>

<?xml version="1.0" encoding="UTF-8" ?>

<!--
 ModemManager 1.0 Interface Specification

   Copyright (C) 2015 <PERSON> <<EMAIL>>
   Copyright (C) 2015 <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
   Copyright (C) 2019 Purism SPC
-->

<node name="/" xmlns:doc="http://www.freedesktop.org/dbus/1.0/doc.dtd">

  <!--
      org.freedesktop.ModemManager1.Call:
      @short_description: The ModemManager Call interface.

      The Call interface Defines operations and properties of a single Call.
  -->
  <interface name="org.freedesktop.ModemManager1.Call">

    <!--
        Start:

        If the outgoing call has not yet been started, start it.

        Applicable only if state is <link linkend="MM-CALL-STATE-UNKNOWN:CAPS"><constant>MM_CALL_STATE_UNKNOWN</constant></link> and direction is
        <link linkend="MM-CALL-DIRECTION-OUTGOING:CAPS"><constant>MM_CALL_DIRECTION_OUTGOING</constant></link>.
    -->
    <method name="Start" />

    <!--
        Accept:

        Accept incoming call (answer).

        Applicable only if state is <link linkend="MM-CALL-STATE-RINGING-IN:CAPS"><constant>MM_CALL_STATE_RINGING_IN</constant></link> and direction is
        <link linkend="MM-CALL-DIRECTION-INCOMING:CAPS"><constant>MM_CALL_DIRECTION_INCOMING</constant></link>.
    -->
    <method name="Accept" />

    <!--
        Deflect:
        @number: new number where the call will be deflected.

        Deflect an incoming or waiting call to a new number. This call will be
        considered terminated once the deflection is performed.

        Applicable only if state is <link linkend="MM-CALL-STATE-RINGING-IN:CAPS"><constant>MM_CALL_STATE_RINGING_IN</constant></link> or
        <link linkend="MM-CALL-STATE-WAITING:CAPS"><constant>MM_CALL_STATE_WAITING</constant></link> and direction is
        <link linkend="MM-CALL-DIRECTION-INCOMING:CAPS"><constant>MM_CALL_DIRECTION_INCOMING</constant></link>.
    -->
    <method name="Deflect">
      <arg name="number" type="s" />
    </method>

    <!--
        JoinMultiparty:

        Join the currently held call into a single multiparty call with another
        already active call.

        The calls will be flagged with the
        '<link linkend="gdbus-property-org-freedesktop-ModemManager1-Call.Multiparty">Multiparty</link>'
        property while they are part of the multiparty call.

        Applicable only if state is <link linkend="MM-CALL-STATE-HELD:CAPS"><constant>MM_CALL_STATE_HELD</constant></link>.
    -->
    <method name="JoinMultiparty" />

    <!--
        LeaveMultiparty:

        If this call is part of an ongoing multiparty call, detach it from the multiparty call,
        put the multiparty call on hold, and activate this one alone. This operation makes this
        call private again between both ends of the call.

        Applicable only if state is <link linkend="MM-CALL-STATE-ACTIVE:CAPS"><constant>MM_CALL_STATE_ACTIVE</constant></link> or
        <link linkend="MM-CALL-STATE-HELD:CAPS"><constant>MM_CALL_STATE_HELD</constant></link> and
        the call is a multiparty call.
    -->
    <method name="LeaveMultiparty"/>

    <!--
        Hangup:

        Hangup the active call.

        Applicable only if state is <link linkend="MM-CALL-STATE-UNKNOWN:CAPS"><constant>MM_CALL_STATE_UNKNOWN</constant></link>.
    -->
    <method name="Hangup"/>

    <!--
        SendDtmf:
        @dtmf: DTMF tone identifier [0-9A-D*#].

        Send a DTMF tone (Dual Tone Multi-Frequency) (only on supported modem).

        Applicable only if state is <link linkend="MM-CALL-STATE-ACTIVE:CAPS"><constant>MM_CALL_STATE_ACTIVE</constant></link>.
    -->
    <method name="SendDtmf">
      <arg name="dtmf" type="s" direction="in"/>
    </method>

    <!--
        DtmfReceived:
        @dtmf: DTMF tone identifier [0-9A-D*#].

        Emitted when a DTMF tone is received (only on supported modem)
    -->
    <signal name="DtmfReceived">
      <arg name="dtmf" type="s" />
    </signal>

    <!--
        StateChanged:
        @old: Old state MMCallState
        @new: New state MMCallState
        @reason: A <link linkend="MMCallStateReason">MMCallStateReason</link> value, specifying the reason for this state change.

        Emitted when call changes state
    -->
    <signal name="StateChanged">
      <arg name="old" type="i" />
      <arg name="new" type="i" />
      <arg name="reason" type="u" />
    </signal>

    <!--
        State:

        A <link linkend="MMCallState">MMCallState</link> value,
        describing the state of the call.
    -->
    <property name="State" type="i" access="read" />

    <!--
        StateReason:

        A <link linkend="MMCallStateReason">MMCallStateReason</link> value, describing why the state is changed.
    -->
    <property name="StateReason" type="i" access="read" />

    <!--
        Direction:

        A <link linkend="MMCallDirection">MMCallDirection</link> value,
        describing the direction of the call.
    -->
    <property name="Direction" type="i" access="read" />

    <!--
        Number:

        The remote phone number.
    -->
    <property name="Number" type="s" access="read" />

    <!--
        Multiparty:

        Whether the call is currently part of a multiparty conference call.
    -->
    <property name="Multiparty" type="b" access="read" />

    <!--
        AudioPort:

        If call audio is routed via the host, the name of the kernel device that
        provides the audio.  For example, with certain Huawei USB modems, this
        property might be "ttyUSB2" indicating audio is available via ttyUSB2 in
        the format described by the AudioFormat property.
    -->
    <property name="AudioPort" type="s" access="read" />

    <!--
        AudioFormat:

        If call audio is routed via the host, a description of the audio format
        supported by the audio port.

        This property may include the following items:
        <variablelist>
          <varlistentry><term><literal>"encoding"</literal></term>
            <listitem>
              The audio encoding format.  For example, "pcm" for PCM audio.
            </listitem>
          </varlistentry>
          <varlistentry><term><literal>"resolution"</literal></term>
            <listitem>
              The sampling precision and its encoding format. For example,
              "s16le" for signed 16-bit little-endian samples.
            </listitem>
          </varlistentry>
          <varlistentry><term><literal>"rate"</literal></term>
            <listitem>
              The sampling rate as an unsigned integer.  For example, 8000 for
              8000hz.
            </listitem>
          </varlistentry>
        </variablelist>
    -->
    <property name="AudioFormat" type="a{sv}" access="read" />
  </interface>
</node>

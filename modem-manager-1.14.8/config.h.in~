/* config.h.in.  Generated from configure.ac by autoheader.  */

/* Define to 1 if translation of program messages to the user's native
   language is requested. */
#undef ENABLE_NLS

/* Define if altair-lte plugin is enabled */
#undef ENABLE_PLUGIN_ALTAIR_LTE

/* Define if anydata plugin is enabled */
#undef ENABLE_PLUGIN_ANYDATA

/* Define if broadmobi plugin is enabled */
#undef ENABLE_PLUGIN_BROADMOBI

/* Define if cinterion plugin is enabled */
#undef ENABLE_PLUGIN_CINTERION

/* Define if dell plugin is enabled */
#undef ENABLE_PLUGIN_DELL

/* Define if dlink plugin is enabled */
#undef ENABLE_PLUGIN_DLINK

/* Define if fibocom plugin is enabled */
#undef ENABLE_PLUGIN_FIBOCOM

/* Define if foxconn plugin is enabled */
#undef ENABLE_PLUGIN_FOXCONN

/* Define if generic plugin is enabled */
#undef ENABLE_PLUGIN_GENERIC

/* Define if haier plugin is enabled */
#undef ENABLE_PLUGIN_HAIER

/* Define if huawei plugin is enabled */
#undef ENABLE_PLUGIN_HUAWEI

/* Define if iridium plugin is enabled */
#undef ENABLE_PLUGIN_IRIDIUM

/* Define if linktop plugin is enabled */
#undef ENABLE_PLUGIN_LINKTOP

/* Define if longcheer plugin is enabled */
#undef ENABLE_PLUGIN_LONGCHEER

/* Define if mbm plugin is enabled */
#undef ENABLE_PLUGIN_MBM

/* Define if me3630 plugin is enabled */
#undef ENABLE_PLUGIN_ME3630

/* Define if motorola plugin is enabled */
#undef ENABLE_PLUGIN_MOTOROLA

/* Define if mtk plugin is enabled */
#undef ENABLE_PLUGIN_MTK

/* Define if nokia plugin is enabled */
#undef ENABLE_PLUGIN_NOKIA

/* Define if nokia-icera plugin is enabled */
#undef ENABLE_PLUGIN_NOKIA_ICERA

/* Define if novatel plugin is enabled */
#undef ENABLE_PLUGIN_NOVATEL

/* Define if novatel-lte plugin is enabled */
#undef ENABLE_PLUGIN_NOVATEL_LTE

/* Define if option plugin is enabled */
#undef ENABLE_PLUGIN_OPTION

/* Define if option-hso plugin is enabled */
#undef ENABLE_PLUGIN_OPTION_HSO

/* Define if pantech plugin is enabled */
#undef ENABLE_PLUGIN_PANTECH

/* Define if quectel plugin is enabled */
#undef ENABLE_PLUGIN_QUECTEL

/* Define if samsung plugin is enabled */
#undef ENABLE_PLUGIN_SAMSUNG

/* Define if sierra plugin is enabled */
#undef ENABLE_PLUGIN_SIERRA

/* Define if sierra-legacy plugin is enabled */
#undef ENABLE_PLUGIN_SIERRA_LEGACY

/* Define if simtech plugin is enabled */
#undef ENABLE_PLUGIN_SIMTECH

/* Define if telit plugin is enabled */
#undef ENABLE_PLUGIN_TELIT

/* Define if thuraya plugin is enabled */
#undef ENABLE_PLUGIN_THURAYA

/* Define if tplink plugin is enabled */
#undef ENABLE_PLUGIN_TPLINK

/* Define if ublox plugin is enabled */
#undef ENABLE_PLUGIN_UBLOX

/* Define if via plugin is enabled */
#undef ENABLE_PLUGIN_VIA

/* Define if wavecom plugin is enabled */
#undef ENABLE_PLUGIN_WAVECOM

/* Define if x22x plugin is enabled */
#undef ENABLE_PLUGIN_X22X

/* Define if zte plugin is enabled */
#undef ENABLE_PLUGIN_ZTE

/* Gettext package */
#undef GETTEXT_PACKAGE

/* Define to 1 if you have the Mac OS X function CFLocaleCopyCurrent in the
   CoreFoundation framework. */
#undef HAVE_CFLOCALECOPYCURRENT

/* Define to 1 if you have the Mac OS X function CFPreferencesCopyAppValue in
   the CoreFoundation framework. */
#undef HAVE_CFPREFERENCESCOPYAPPVALUE

/* Define if the GNU dcgettext() function is already present or preinstalled.
   */
#undef HAVE_DCGETTEXT

/* Define to 1 if you have the <dlfcn.h> header file. */
#undef HAVE_DLFCN_H

/* Define if the GNU gettext() function is already present or preinstalled. */
#undef HAVE_GETTEXT

/* Define if you have the iconv() function and it works. */
#undef HAVE_ICONV

/* Define to 1 if you have the <inttypes.h> header file. */
#undef HAVE_INTTYPES_H

/* Define to 1 if you have the <memory.h> header file. */
#undef HAVE_MEMORY_H

/* Define to 1 if you have the <stdint.h> header file. */
#undef HAVE_STDINT_H

/* Define to 1 if you have the <stdlib.h> header file. */
#undef HAVE_STDLIB_H

/* Define to 1 if you have the <strings.h> header file. */
#undef HAVE_STRINGS_H

/* Define to 1 if you have the <string.h> header file. */
#undef HAVE_STRING_H

/* Define to 1 if you have the <sys/stat.h> header file. */
#undef HAVE_SYS_STAT_H

/* Define to 1 if you have the <sys/types.h> header file. */
#undef HAVE_SYS_TYPES_H

/* Define to 1 if you have the <unistd.h> header file. */
#undef HAVE_UNISTD_H

/* Define to the sub-directory where libtool stores uninstalled libraries. */
#undef LT_OBJDIR

/* Define the distribution version string */
#undef MM_DIST_VERSION

/* Name of package */
#undef PACKAGE

/* Define to the address where bug reports for this package should be sent. */
#undef PACKAGE_BUGREPORT

/* Define to the full name of this package. */
#undef PACKAGE_NAME

/* Define to the full name and version of this package. */
#undef PACKAGE_STRING

/* Define to the one symbol short name of this package. */
#undef PACKAGE_TARNAME

/* Define to the home page for this package. */
#undef PACKAGE_URL

/* Define to the version of this package. */
#undef PACKAGE_VERSION

/* Define to 1 if you have the ANSI C header files. */
#undef STDC_HEADERS

/* Enable extensions on AIX 3, Interix.  */
#ifndef _ALL_SOURCE
# undef _ALL_SOURCE
#endif
/* Enable GNU extensions on systems that have them.  */
#ifndef _GNU_SOURCE
# undef _GNU_SOURCE
#endif
/* Enable threading extensions on Solaris.  */
#ifndef _POSIX_PTHREAD_SEMANTICS
# undef _POSIX_PTHREAD_SEMANTICS
#endif
/* Enable extensions on HP NonStop.  */
#ifndef _TANDEM_SOURCE
# undef _TANDEM_SOURCE
#endif
/* Enable general extensions on Solaris.  */
#ifndef __EXTENSIONS__
# undef __EXTENSIONS__
#endif


/* Version number of package */
#undef VERSION

/* Define if you want to enable AT commands via DBus */
#undef WITH_AT_COMMAND_VIA_DBUS

/* Define if you want MBIM support */
#undef WITH_MBIM

/* Define if you have PolicyKit support */
#undef WITH_POLKIT

/* Define if you want QMI support */
#undef WITH_QMI

/* Define if foxconn utils are built */
#undef WITH_SHARED_FOXCONN

/* Define if icera utils are built */
#undef WITH_SHARED_ICERA

/* Define if novatel utils are built */
#undef WITH_SHARED_NOVATEL

/* Define if option utils are built */
#undef WITH_SHARED_OPTION

/* Define if sierra utils are built */
#undef WITH_SHARED_SIERRA

/* Define if telit utils are built */
#undef WITH_SHARED_TELIT

/* Define if xmm utils are built */
#undef WITH_SHARED_XMM

/* Define if you want systemd journal support */
#undef WITH_SYSTEMD_JOURNAL

/* Define if you have systemd suspend-resume support */
#undef WITH_SYSTEMD_SUSPEND_RESUME

/* Define if you want udev support */
#undef WITH_UDEV

/* Define to 1 if on MINIX. */
#undef _MINIX

/* Define to 2 if the system does not provide POSIX.1 features except with
   this defined. */
#undef _POSIX_1_SOURCE

/* Define to 1 if you need to in order for `stat' and other things to work. */
#undef _POSIX_SOURCE

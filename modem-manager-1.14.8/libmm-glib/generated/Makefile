# Makefile.in generated by automake 1.16.1 from Makefile.am.
# libmm-glib/generated/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.





am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/ModemManager
pkgincludedir = $(includedir)/ModemManager
pkglibdir = $(libdir)/ModemManager
pkglibexecdir = $(libexecdir)/ModemManager
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-linux-gnu
host_triplet = arm-buildroot-linux-gnueabihf
subdir = libmm-glib/generated
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_code_coverage.m4 \
	$(top_srcdir)/m4/gettext.m4 $(top_srcdir)/m4/gtk-doc.m4 \
	$(top_srcdir)/m4/iconv.m4 $(top_srcdir)/m4/intlmacosx.m4 \
	$(top_srcdir)/m4/introspection.m4 $(top_srcdir)/m4/lib-ld.m4 \
	$(top_srcdir)/m4/lib-link.m4 $(top_srcdir)/m4/lib-prefix.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/mm-enable-plugin.m4 $(top_srcdir)/m4/nls.m4 \
	$(top_srcdir)/m4/po.m4 $(top_srcdir)/m4/progtest.m4 \
	$(top_srcdir)/m4/vapigen.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
LTLIBRARIES = $(noinst_LTLIBRARIES)
libmm_generated_la_LIBADD =
am__objects_1 =
am__objects_2 = libmm_generated_la-mm-enums-types.lo \
	libmm_generated_la-mm-errors-types.lo \
	libmm_generated_la-mm-errors-quarks.lo \
	libmm_generated_la-mm-gdbus-manager.lo \
	libmm_generated_la-mm-gdbus-sim.lo \
	libmm_generated_la-mm-gdbus-sms.lo \
	libmm_generated_la-mm-gdbus-call.lo \
	libmm_generated_la-mm-gdbus-bearer.lo \
	libmm_generated_la-mm-gdbus-modem.lo
nodist_libmm_generated_la_OBJECTS = $(am__objects_1) $(am__objects_2)
libmm_generated_la_OBJECTS = $(nodist_libmm_generated_la_OBJECTS)
AM_V_lt = $(am__v_lt_$(V))
am__v_lt_ = $(am__v_lt_$(AM_DEFAULT_VERBOSITY))
am__v_lt_0 = --silent
am__v_lt_1 = 
libmm_generated_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(libmm_generated_la_CFLAGS) $(CFLAGS) \
	$(libmm_generated_la_LDFLAGS) $(LDFLAGS) -o $@
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I. -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade =  \
	./$(DEPDIR)/libmm_generated_la-mm-enums-types.Plo \
	./$(DEPDIR)/libmm_generated_la-mm-errors-quarks.Plo \
	./$(DEPDIR)/libmm_generated_la-mm-errors-types.Plo \
	./$(DEPDIR)/libmm_generated_la-mm-gdbus-bearer.Plo \
	./$(DEPDIR)/libmm_generated_la-mm-gdbus-call.Plo \
	./$(DEPDIR)/libmm_generated_la-mm-gdbus-manager.Plo \
	./$(DEPDIR)/libmm_generated_la-mm-gdbus-modem.Plo \
	./$(DEPDIR)/libmm_generated_la-mm-gdbus-sim.Plo \
	./$(DEPDIR)/libmm_generated_la-mm-gdbus-sms.Plo
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_$(V))
am__v_CC_ = $(am__v_CC_$(AM_DEFAULT_VERBOSITY))
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_$(V))
am__v_CCLD_ = $(am__v_CCLD_$(AM_DEFAULT_VERBOSITY))
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(nodist_libmm_generated_la_SOURCES)
DIST_SOURCES =
RECURSIVE_TARGETS = all-recursive check-recursive cscopelist-recursive \
	ctags-recursive dvi-recursive html-recursive info-recursive \
	install-data-recursive install-dvi-recursive \
	install-exec-recursive install-html-recursive \
	install-info-recursive install-pdf-recursive \
	install-ps-recursive install-recursive installcheck-recursive \
	installdirs-recursive pdf-recursive ps-recursive \
	tags-recursive uninstall-recursive
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(includedir)"
HEADERS = $(nodist_include_HEADERS)
RECURSIVE_CLEAN_TARGETS = mostlyclean-recursive clean-recursive	\
  distclean-recursive maintainer-clean-recursive
am__recursive_targets = \
  $(RECURSIVE_TARGETS) \
  $(RECURSIVE_CLEAN_TARGETS) \
  $(am__extra_recursive_targets)
AM_RECURSIVE_TARGETS = $(am__recursive_targets:-recursive=) TAGS CTAGS \
	distdir distdir-am
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
DIST_SUBDIRS = $(SUBDIRS)
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
am__relativize = \
  dir0=`pwd`; \
  sed_first='s,^\([^/]*\)/.*$$,\1,'; \
  sed_rest='s,^[^/]*/*,,'; \
  sed_last='s,^.*/\([^/]*\)$$,\1,'; \
  sed_butlast='s,/*[^/]*$$,,'; \
  while test -n "$$dir1"; do \
    first=`echo "$$dir1" | sed -e "$$sed_first"`; \
    if test "$$first" != "."; then \
      if test "$$first" = ".."; then \
        dir2=`echo "$$dir0" | sed -e "$$sed_last"`/"$$dir2"; \
        dir0=`echo "$$dir0" | sed -e "$$sed_butlast"`; \
      else \
        first2=`echo "$$dir2" | sed -e "$$sed_first"`; \
        if test "$$first2" = "$$first"; then \
          dir2=`echo "$$dir2" | sed -e "$$sed_rest"`; \
        else \
          dir2="../$$dir2"; \
        fi; \
        dir0="$$dir0"/"$$first"; \
      fi; \
    fi; \
    dir1=`echo "$$dir1" | sed -e "$$sed_rest"`; \
  done; \
  reldir="$$dir2"
ACLOCAL = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 0
AR = ar
AUTOCONF = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing autoconf
AUTOHEADER = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing autoheader
AUTOMAKE = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing automake-1.16
AWK = gawk
CC = /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc
CCDEPMODE = depmode=none
CFLAGS = -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -std=gnu89
CODE_COVERAGE_CFLAGS = 
CODE_COVERAGE_ENABLED = no
CODE_COVERAGE_LDFLAGS = 
CPP = /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-cpp
CPPFLAGS = -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64
CYGPATH_W = echo
DBUS_SYS_DIR = /etc/dbus-1/system.d
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
DLLTOOL = false
DSYMUTIL = 
DUMPBIN = :
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
EXEEXT = 
FGREP = /usr/bin/grep -F
GCOV = 
GDBUS_CODEGEN = gdbus-codegen
GENHTML = 
GETTEXT_MACRO_VERSION = 0.19
GETTEXT_PACKAGE = ModemManager
GLIB_MKENUMS = glib-mkenums
GMSGFMT = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
GMSGFMT_015 = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
GREP = /usr/bin/grep
GTKDOC_CHECK = 
GTKDOC_CHECK_PATH = 
GTKDOC_DEPS_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include 
GTKDOC_DEPS_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgobject-2.0 -lglib-2.0 
GTKDOC_MKPDF = 
GTKDOC_REBASE = true
GUDEV_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gudev-1.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include 
GUDEV_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgudev-1.0 -lgobject-2.0 -lglib-2.0 
HTML_DIR = ${datadir}/gtk-doc/html
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
INTLLIBS = 
INTL_MACOSX_LIBS = 
INTROSPECTION_CFLAGS = 
INTROSPECTION_COMPILER = 
INTROSPECTION_GENERATE = 
INTROSPECTION_GIRDIR = 
INTROSPECTION_LIBS = 
INTROSPECTION_MAKEFILE = 
INTROSPECTION_SCANNER = 
INTROSPECTION_TYPELIBDIR = 
LCOV = 
LD = /opt/gcc-arm/arm-none-linux-gnueabihf/bin/ld
LDFLAGS = 
LIBICONV = -liconv
LIBINTL = 
LIBMM_GLIB_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gio-unix-2.0 -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS
LIBMM_GLIB_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgio-2.0 -lgobject-2.0 -lglib-2.0 
LIBOBJS = 
LIBS = 
LIBSYSTEMD_CFLAGS = 
LIBSYSTEMD_LIBS = 
LIBSYSTEMD_LOGIN_CFLAGS = 
LIBSYSTEMD_LOGIN_LIBS = 
LIBTOOL = $(SHELL) $(top_builddir)/libtool
LIPO = 
LN_S = ln -s
LTLIBICONV = -liconv
LTLIBINTL = 
LTLIBOBJS = 
LT_SYS_LIBRARY_PATH = 
MAINT = 
MAKEINFO = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing makeinfo
MANIFEST_TOOL = :
MBIM_CFLAGS = 
MBIM_LIBS = 
MKDIR_P = /usr/bin/mkdir -p
MMCLI_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS
MMCLI_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgio-2.0 -lgobject-2.0 -lglib-2.0 
MM_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gio-unix-2.0 -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS
MM_DEFAULT_USER_POLICY = 
MM_GLIB_LT_AGE = 6
MM_GLIB_LT_CURRENT = 6
MM_GLIB_LT_REVISION = 0
MM_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -Wl,--export-dynamic -lgmodule-2.0 -pthread -lglib-2.0 -lgio-2.0 -lgobject-2.0 -lglib-2.0 
MM_MAJOR_VERSION = 1
MM_MICRO_VERSION = 8
MM_MINOR_VERSION = 14
MM_POLKIT_SERVICE = 
MM_VERSION = 1.14.8
MSGFMT = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
MSGFMT_015 = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
MSGMERGE = /root/buildroot-2021.02/dev_out/host/bin/msgmerge
NM = nm
NMEDIT = 
OBJDUMP = objdump
OBJEXT = o
OTOOL = 
OTOOL64 = 
PACKAGE = ModemManager
PACKAGE_BUGREPORT = https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues
PACKAGE_NAME = ModemManager
PACKAGE_STRING = ModemManager 1.14.8
PACKAGE_TARNAME = ModemManager
PACKAGE_URL = 
PACKAGE_VERSION = 1.14.8
PATH_SEPARATOR = :
PKG_CONFIG = /root/buildroot-2021.02/dev_out/host/bin/pkg-config
PKG_CONFIG_LIBDIR = 
PKG_CONFIG_PATH = 
POLKIT_CFLAGS = 
POLKIT_LIBS = 
POSUB = po
QMI_CFLAGS = 
QMI_LIBS = 
RANLIB = ranlib
SED = /usr/bin/sed
SET_MAKE = 
SHELL = /bin/bash
STRIP = strip
SYSTEMD_UNIT_DIR = 
UDEV_BASE_DIR = /lib/udev
USE_NLS = yes
VAPIGEN = 
VAPIGEN_MAKEFILE = 
VAPIGEN_VAPIDIR = 
VERSION = 1.14.8
WARN_CFLAGS = -fno-strict-aliasing -Wnested-externs -Wmissing-prototypes -Wstrict-prototypes -Wdeclaration-after-statement -Wimplicit-function-declaration -Wold-style-definition -Wjump-misses-init -Wall -Wextra -Wundef -Wwrite-strings -Wpointer-arith -Wmissing-declarations -Wredundant-decls -Wno-unused-parameter -Wno-missing-field-initializers -Wformat=2 -Wcast-align -Wformat-nonliteral -Wformat-security -Wsign-compare -Wstrict-aliasing -Wshadow -Winline -Wpacked -Wmissing-format-attribute -Wmissing-noreturn -Winit-self -Wmissing-include-dirs -Wunused-but-set-variable -Warray-bounds -Wreturn-type -Wswitch-enum -Wswitch-default -Wduplicated-cond -Wduplicated-branches -Wlogical-op -Wrestrict -Wnull-dereference -Wdouble-promotion -Wno-error=unused-parameter -Wno-error=missing-field-initializers -Wno-cast-function-type -Wno-packed -Wno-error=cast-function-type -Wno-error=packed
WARN_LDFLAGS = -Wl,--no-as-needed
WARN_SCANNERFLAGS =               --warn-all                                                             
XGETTEXT = /root/buildroot-2021.02/dev_out/host/bin/xgettext
XGETTEXT_015 = /root/buildroot-2021.02/dev_out/host/bin/xgettext
XGETTEXT_EXTRA_OPTIONS = 
XSLTPROC_CHECK = yes
abs_builddir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/libmm-glib/generated
abs_srcdir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/libmm-glib/generated
abs_top_builddir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8
abs_top_srcdir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8
ac_ct_AR = ar
ac_ct_CC = 
ac_ct_DUMPBIN = link -dump
am__include = include
am__leading_dot = .
am__quote = 
am__tar = tar --format=ustar -chf - "$$tardir"
am__untar = tar -xf -
bindir = ${exec_prefix}/bin
build = x86_64-pc-linux-gnu
build_alias = x86_64-pc-linux-gnu
build_cpu = x86_64
build_os = linux-gnu
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = /usr
host = arm-buildroot-linux-gnueabihf
host_alias = arm-buildroot-linux-gnueabihf
host_cpu = arm
host_os = linux-gnueabihf
host_vendor = buildroot
htmldir = ${docdir}
includedir = ${prefix}/include/libmm-glib
infodir = ${datarootdir}/info
install_sh = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
localedir = ${datarootdir}/locale
localstatedir = /var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /usr
program_transform_name = s&^&&
psdir = ${docdir}
runstatedir = ${localstatedir}/run
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = .
sysconfdir = /etc
target_alias = arm-buildroot-linux-gnueabihf
top_build_prefix = ../../
top_builddir = ../..
top_srcdir = ../..
SUBDIRS = . tests
AM_CFLAGS = $(CODE_COVERAGE_CFLAGS)
AM_LDFLAGS = $(CODE_COVERAGE_LDFLAGS)
noinst_LTLIBRARIES = libmm-generated.la
GENERATED_H = \
	mm-enums-types.h \
	mm-errors-types.h \
	mm-gdbus-manager.h \
	mm-gdbus-sim.h \
	mm-gdbus-sms.h \
	mm-gdbus-call.h \
	mm-gdbus-bearer.h \
	mm-gdbus-modem.h \
	$(NULL)

GENERATED_C = \
	mm-enums-types.c \
	mm-errors-types.c \
	mm-errors-quarks.c \
	mm-gdbus-manager.c \
	mm-gdbus-sim.c \
	mm-gdbus-sms.c \
	mm-gdbus-call.c \
	mm-gdbus-bearer.c \
	mm-gdbus-modem.c   \
	$(NULL)

GENERATED_DOC = \
	mm-gdbus-doc-org.freedesktop.ModemManager1.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Sim.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Sms.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Call.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Bearer.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Messaging.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Voice.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Location.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Time.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Firmware.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Oma.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.ModemCdma.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Modem3gpp.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Modem3gpp.Ussd.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Simple.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Signal.xml \
	$(NULL)

BUILT_SOURCES = $(GENERATED_H) $(GENERATED_C) $(GENERATED_DOC)

# Manager interface
mm_gdbus_manager_generated = \
	mm-gdbus-manager.h \
	mm-gdbus-manager.c \
	mm-gdbus-doc-org.freedesktop.ModemManager1.xml \
	$(NULL)

mm_gdbus_manager_deps = \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.xml \
	$(NULL)


# Modem interfaces
mm_gdbus_modem_generated = \
	mm-gdbus-modem.h \
	mm-gdbus-modem.c \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Messaging.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Voice.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Location.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Time.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Firmware.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Oma.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.ModemCdma.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Modem3gpp.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Modem3gpp.Ussd.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Simple.xml \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Modem.Signal.xml \
	$(NULL)

mm_gdbus_modem_deps = \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Modem.xml \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Modem.Messaging.xml \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Modem.Voice.xml \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Modem.Location.xml \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Modem.Time.xml \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Modem.Firmware.xml \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Modem.Oma.xml \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Modem.ModemCdma.xml \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Modem.Modem3gpp.xml \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Modem.Modem3gpp.Ussd.xml \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Modem.Simple.xml \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Modem.Signal.xml \
	$(NULL)


# SIM interface
mm_gdbus_sim_generated = \
	mm-gdbus-sim.h \
	mm-gdbus-sim.c \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Sim.xml \
	$(NULL)

mm_gdbus_sim_deps = \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Sim.xml \
	$(NULL)


# Bearer interface
mm_gdbus_bearer_generated = \
	mm-gdbus-bearer.h \
	mm-gdbus-bearer.c \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Bearer.xml \
	$(NULL)

mm_gdbus_bearer_deps = \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Bearer.xml \
	$(NULL)


# SMS interface
mm_gdbus_sms_generated = \
	mm-gdbus-sms.h \
	mm-gdbus-sms.c \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Sms.xml \
	$(NULL)

mm_gdbus_sms_deps = \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Sms.xml \
	$(NULL)


# Call interface
mm_gdbus_call_generated = \
	mm-gdbus-call.h \
	mm-gdbus-call.c \
	mm-gdbus-doc-org.freedesktop.ModemManager1.Call.xml \
	$(NULL)

mm_gdbus_call_deps = \
	$(top_srcdir)/introspection/org.freedesktop.ModemManager1.Call.xml \
	$(NULL)

nodist_libmm_generated_la_SOURCES = $(GENERATED_H) $(GENERATED_C)
libmm_generated_la_CPPFLAGS = \
	$(LIBMM_GLIB_CFLAGS) \
	-I$(top_srcdir) \
	-I$(top_srcdir)/include \
	-I$(top_builddir)/include \
	-Wno-unused-function \
	-Wno-float-equal \
	-Wno-shadow \
	$(NULL)

libmm_generated_la_CFLAGS = \
	$(WARN_CFLAGS) \
	$(NULL)

libmm_generated_la_LDFLAGS = \
	$(WARN_LDFLAGS) \
	$(LIBMM_GLIB_LIBS) \
	$(NULL)

nodist_include_HEADERS = $(GENERATED_H)
CLEANFILES = $(GENERATED_H) $(GENERATED_C) $(GENERATED_DOC)
all: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) all-recursive

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu libmm-glib/generated/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu libmm-glib/generated/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

clean-noinstLTLIBRARIES:
	-test -z "$(noinst_LTLIBRARIES)" || rm -f $(noinst_LTLIBRARIES)
	@list='$(noinst_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}

libmm-generated.la: $(libmm_generated_la_OBJECTS) $(libmm_generated_la_DEPENDENCIES) $(EXTRA_libmm_generated_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_generated_la_LINK)  $(libmm_generated_la_OBJECTS) $(libmm_generated_la_LIBADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

#include ./$(DEPDIR)/libmm_generated_la-mm-enums-types.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_generated_la-mm-errors-quarks.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_generated_la-mm-errors-types.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_generated_la-mm-gdbus-bearer.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_generated_la-mm-gdbus-call.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_generated_la-mm-gdbus-manager.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_generated_la-mm-gdbus-modem.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_generated_la-mm-gdbus-sim.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_generated_la-mm-gdbus-sms.Plo # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
#	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
#	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
#	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(COMPILE) -c -o $@ $<

.c.obj:
#	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
#	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
#	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
#	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
#	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
#	$(am__mv) $$depbase.Tpo $$depbase.Plo
#	$(AM_V_CC)source='$<' object='$@' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LTCOMPILE) -c -o $@ $<

libmm_generated_la-mm-enums-types.lo: mm-enums-types.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -MT libmm_generated_la-mm-enums-types.lo -MD -MP -MF $(DEPDIR)/libmm_generated_la-mm-enums-types.Tpo -c -o libmm_generated_la-mm-enums-types.lo `test -f 'mm-enums-types.c' || echo '$(srcdir)/'`mm-enums-types.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_generated_la-mm-enums-types.Tpo $(DEPDIR)/libmm_generated_la-mm-enums-types.Plo
#	$(AM_V_CC)source='mm-enums-types.c' object='libmm_generated_la-mm-enums-types.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -c -o libmm_generated_la-mm-enums-types.lo `test -f 'mm-enums-types.c' || echo '$(srcdir)/'`mm-enums-types.c

libmm_generated_la-mm-errors-types.lo: mm-errors-types.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -MT libmm_generated_la-mm-errors-types.lo -MD -MP -MF $(DEPDIR)/libmm_generated_la-mm-errors-types.Tpo -c -o libmm_generated_la-mm-errors-types.lo `test -f 'mm-errors-types.c' || echo '$(srcdir)/'`mm-errors-types.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_generated_la-mm-errors-types.Tpo $(DEPDIR)/libmm_generated_la-mm-errors-types.Plo
#	$(AM_V_CC)source='mm-errors-types.c' object='libmm_generated_la-mm-errors-types.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -c -o libmm_generated_la-mm-errors-types.lo `test -f 'mm-errors-types.c' || echo '$(srcdir)/'`mm-errors-types.c

libmm_generated_la-mm-errors-quarks.lo: mm-errors-quarks.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -MT libmm_generated_la-mm-errors-quarks.lo -MD -MP -MF $(DEPDIR)/libmm_generated_la-mm-errors-quarks.Tpo -c -o libmm_generated_la-mm-errors-quarks.lo `test -f 'mm-errors-quarks.c' || echo '$(srcdir)/'`mm-errors-quarks.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_generated_la-mm-errors-quarks.Tpo $(DEPDIR)/libmm_generated_la-mm-errors-quarks.Plo
#	$(AM_V_CC)source='mm-errors-quarks.c' object='libmm_generated_la-mm-errors-quarks.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -c -o libmm_generated_la-mm-errors-quarks.lo `test -f 'mm-errors-quarks.c' || echo '$(srcdir)/'`mm-errors-quarks.c

libmm_generated_la-mm-gdbus-manager.lo: mm-gdbus-manager.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -MT libmm_generated_la-mm-gdbus-manager.lo -MD -MP -MF $(DEPDIR)/libmm_generated_la-mm-gdbus-manager.Tpo -c -o libmm_generated_la-mm-gdbus-manager.lo `test -f 'mm-gdbus-manager.c' || echo '$(srcdir)/'`mm-gdbus-manager.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_generated_la-mm-gdbus-manager.Tpo $(DEPDIR)/libmm_generated_la-mm-gdbus-manager.Plo
#	$(AM_V_CC)source='mm-gdbus-manager.c' object='libmm_generated_la-mm-gdbus-manager.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -c -o libmm_generated_la-mm-gdbus-manager.lo `test -f 'mm-gdbus-manager.c' || echo '$(srcdir)/'`mm-gdbus-manager.c

libmm_generated_la-mm-gdbus-sim.lo: mm-gdbus-sim.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -MT libmm_generated_la-mm-gdbus-sim.lo -MD -MP -MF $(DEPDIR)/libmm_generated_la-mm-gdbus-sim.Tpo -c -o libmm_generated_la-mm-gdbus-sim.lo `test -f 'mm-gdbus-sim.c' || echo '$(srcdir)/'`mm-gdbus-sim.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_generated_la-mm-gdbus-sim.Tpo $(DEPDIR)/libmm_generated_la-mm-gdbus-sim.Plo
#	$(AM_V_CC)source='mm-gdbus-sim.c' object='libmm_generated_la-mm-gdbus-sim.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -c -o libmm_generated_la-mm-gdbus-sim.lo `test -f 'mm-gdbus-sim.c' || echo '$(srcdir)/'`mm-gdbus-sim.c

libmm_generated_la-mm-gdbus-sms.lo: mm-gdbus-sms.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -MT libmm_generated_la-mm-gdbus-sms.lo -MD -MP -MF $(DEPDIR)/libmm_generated_la-mm-gdbus-sms.Tpo -c -o libmm_generated_la-mm-gdbus-sms.lo `test -f 'mm-gdbus-sms.c' || echo '$(srcdir)/'`mm-gdbus-sms.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_generated_la-mm-gdbus-sms.Tpo $(DEPDIR)/libmm_generated_la-mm-gdbus-sms.Plo
#	$(AM_V_CC)source='mm-gdbus-sms.c' object='libmm_generated_la-mm-gdbus-sms.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -c -o libmm_generated_la-mm-gdbus-sms.lo `test -f 'mm-gdbus-sms.c' || echo '$(srcdir)/'`mm-gdbus-sms.c

libmm_generated_la-mm-gdbus-call.lo: mm-gdbus-call.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -MT libmm_generated_la-mm-gdbus-call.lo -MD -MP -MF $(DEPDIR)/libmm_generated_la-mm-gdbus-call.Tpo -c -o libmm_generated_la-mm-gdbus-call.lo `test -f 'mm-gdbus-call.c' || echo '$(srcdir)/'`mm-gdbus-call.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_generated_la-mm-gdbus-call.Tpo $(DEPDIR)/libmm_generated_la-mm-gdbus-call.Plo
#	$(AM_V_CC)source='mm-gdbus-call.c' object='libmm_generated_la-mm-gdbus-call.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -c -o libmm_generated_la-mm-gdbus-call.lo `test -f 'mm-gdbus-call.c' || echo '$(srcdir)/'`mm-gdbus-call.c

libmm_generated_la-mm-gdbus-bearer.lo: mm-gdbus-bearer.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -MT libmm_generated_la-mm-gdbus-bearer.lo -MD -MP -MF $(DEPDIR)/libmm_generated_la-mm-gdbus-bearer.Tpo -c -o libmm_generated_la-mm-gdbus-bearer.lo `test -f 'mm-gdbus-bearer.c' || echo '$(srcdir)/'`mm-gdbus-bearer.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_generated_la-mm-gdbus-bearer.Tpo $(DEPDIR)/libmm_generated_la-mm-gdbus-bearer.Plo
#	$(AM_V_CC)source='mm-gdbus-bearer.c' object='libmm_generated_la-mm-gdbus-bearer.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -c -o libmm_generated_la-mm-gdbus-bearer.lo `test -f 'mm-gdbus-bearer.c' || echo '$(srcdir)/'`mm-gdbus-bearer.c

libmm_generated_la-mm-gdbus-modem.lo: mm-gdbus-modem.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -MT libmm_generated_la-mm-gdbus-modem.lo -MD -MP -MF $(DEPDIR)/libmm_generated_la-mm-gdbus-modem.Tpo -c -o libmm_generated_la-mm-gdbus-modem.lo `test -f 'mm-gdbus-modem.c' || echo '$(srcdir)/'`mm-gdbus-modem.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_generated_la-mm-gdbus-modem.Tpo $(DEPDIR)/libmm_generated_la-mm-gdbus-modem.Plo
#	$(AM_V_CC)source='mm-gdbus-modem.c' object='libmm_generated_la-mm-gdbus-modem.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_generated_la_CPPFLAGS) $(CPPFLAGS) $(libmm_generated_la_CFLAGS) $(CFLAGS) -c -o libmm_generated_la-mm-gdbus-modem.lo `test -f 'mm-gdbus-modem.c' || echo '$(srcdir)/'`mm-gdbus-modem.c

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
install-nodist_includeHEADERS: $(nodist_include_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(nodist_include_HEADERS)'; test -n "$(includedir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(includedir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(includedir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(includedir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(includedir)" || exit $$?; \
	done

uninstall-nodist_includeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(nodist_include_HEADERS)'; test -n "$(includedir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(includedir)'; $(am__uninstall_files_from_dir)

# This directory's subdirectories are mostly independent; you can cd
# into them and run 'make' without going through this Makefile.
# To change the values of 'make' variables: instead of editing Makefiles,
# (1) if the variable is set in 'config.status', edit 'config.status'
#     (which will cause the Makefiles to be regenerated when you run 'make');
# (2) otherwise, pass the desired values on the 'make' command line.
$(am__recursive_targets):
	@fail=; \
	if $(am__make_keepgoing); then \
	  failcom='fail=yes'; \
	else \
	  failcom='exit 1'; \
	fi; \
	dot_seen=no; \
	target=`echo $@ | sed s/-recursive//`; \
	case "$@" in \
	  distclean-* | maintainer-clean-*) list='$(DIST_SUBDIRS)' ;; \
	  *) list='$(SUBDIRS)' ;; \
	esac; \
	for subdir in $$list; do \
	  echo "Making $$target in $$subdir"; \
	  if test "$$subdir" = "."; then \
	    dot_seen=yes; \
	    local_target="$$target-am"; \
	  else \
	    local_target="$$target"; \
	  fi; \
	  ($(am__cd) $$subdir && $(MAKE) $(AM_MAKEFLAGS) $$local_target) \
	  || eval $$failcom; \
	done; \
	if test "$$dot_seen" = "no"; then \
	  $(MAKE) $(AM_MAKEFLAGS) "$$target-am" || exit 1; \
	fi; test -z "$$fail"

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-recursive
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	if ($(ETAGS) --etags-include --version) >/dev/null 2>&1; then \
	  include_option=--etags-include; \
	  empty_fix=.; \
	else \
	  include_option=--include; \
	  empty_fix=; \
	fi; \
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    test ! -f $$subdir/TAGS || \
	      set "$$@" "$$include_option=$$here/$$subdir/TAGS"; \
	  fi; \
	done; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-recursive

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-recursive

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	@list='$(DIST_SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    $(am__make_dryrun) \
	      || test -d "$(distdir)/$$subdir" \
	      || $(MKDIR_P) "$(distdir)/$$subdir" \
	      || exit 1; \
	    dir1=$$subdir; dir2="$(distdir)/$$subdir"; \
	    $(am__relativize); \
	    new_distdir=$$reldir; \
	    dir1=$$subdir; dir2="$(top_distdir)"; \
	    $(am__relativize); \
	    new_top_distdir=$$reldir; \
	    echo " (cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) top_distdir="$$new_top_distdir" distdir="$$new_distdir" \\"; \
	    echo "     am__remove_distdir=: am__skip_length_check=: am__skip_mode_fix=: distdir)"; \
	    ($(am__cd) $$subdir && \
	      $(MAKE) $(AM_MAKEFLAGS) \
	        top_distdir="$$new_top_distdir" \
	        distdir="$$new_distdir" \
		am__remove_distdir=: \
		am__skip_length_check=: \
		am__skip_mode_fix=: \
	        distdir) \
	      || exit 1; \
	  fi; \
	done
check-am: all-am
check: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) check-recursive
all-am: Makefile $(LTLIBRARIES) $(HEADERS)
installdirs: installdirs-recursive
installdirs-am:
	for dir in "$(DESTDIR)$(includedir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) install-recursive
install-exec: install-exec-recursive
install-data: install-data-recursive
uninstall: uninstall-recursive

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-recursive
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
	-test -z "$(BUILT_SOURCES)" || rm -f $(BUILT_SOURCES)
clean: clean-recursive

clean-am: clean-generic clean-libtool clean-noinstLTLIBRARIES \
	mostlyclean-am

distclean: distclean-recursive
		-rm -f ./$(DEPDIR)/libmm_generated_la-mm-enums-types.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-errors-quarks.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-errors-types.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-gdbus-bearer.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-gdbus-call.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-gdbus-manager.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-gdbus-modem.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-gdbus-sim.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-gdbus-sms.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-recursive

dvi-am:

html: html-recursive

html-am:

info: info-recursive

info-am:

install-data-am: install-nodist_includeHEADERS

install-dvi: install-dvi-recursive

install-dvi-am:

install-exec-am:

install-html: install-html-recursive

install-html-am:

install-info: install-info-recursive

install-info-am:

install-man:

install-pdf: install-pdf-recursive

install-pdf-am:

install-ps: install-ps-recursive

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-recursive
		-rm -f ./$(DEPDIR)/libmm_generated_la-mm-enums-types.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-errors-quarks.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-errors-types.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-gdbus-bearer.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-gdbus-call.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-gdbus-manager.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-gdbus-modem.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-gdbus-sim.Plo
	-rm -f ./$(DEPDIR)/libmm_generated_la-mm-gdbus-sms.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-recursive

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-recursive

pdf-am:

ps: ps-recursive

ps-am:

uninstall-am: uninstall-nodist_includeHEADERS

.MAKE: $(am__recursive_targets) all check install install-am \
	install-strip

.PHONY: $(am__recursive_targets) CTAGS GTAGS TAGS all all-am \
	am--depfiles check check-am clean clean-generic clean-libtool \
	clean-noinstLTLIBRARIES cscopelist-am ctags ctags-am distclean \
	distclean-compile distclean-generic distclean-libtool \
	distclean-tags distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am install-man \
	install-nodist_includeHEADERS install-pdf install-pdf-am \
	install-ps install-ps-am install-strip installcheck \
	installcheck-am installdirs installdirs-am maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags tags-am uninstall uninstall-am \
	uninstall-nodist_includeHEADERS

.PRECIOUS: Makefile


# Enum types
mm-enums-types.h: Makefile.am $(top_srcdir)/include/ModemManager-enums.h $(top_srcdir)/build-aux/mm-enums-template.h
	$(AM_V_GEN) $(GLIB_MKENUMS) \
		--fhead "#include <ModemManager.h>\n#ifndef __MM_ENUMS_TYPES_H__\n#define __MM_ENUMS_TYPES_H__\n" \
		--template $(top_srcdir)/build-aux/mm-enums-template.h \
		--ftail "#endif /* __MM_ENUMS_TYPES_H__ */\n" \
		$(top_srcdir)/include/ModemManager-enums.h > $@

mm-enums-types.c: Makefile.am $(top_srcdir)/include/ModemManager-enums.h $(top_srcdir)/build-aux/mm-enums-template.c mm-enums-types.h
	$(AM_V_GEN) $(GLIB_MKENUMS) \
		--fhead "#include \"mm-enums-types.h\"\n" \
		--template $(top_srcdir)/build-aux/mm-enums-template.c \
		$(top_srcdir)/include/ModemManager-enums.h > $@

# Error types & quarks
mm-errors-types.h: Makefile.am $(top_srcdir)/include/ModemManager-errors.h $(top_srcdir)/build-aux/mm-errors-template.h
	$(AM_V_GEN) $(GLIB_MKENUMS) \
		--fhead "#ifndef __MM_ERRORS_TYPES_H__\n#define __MM_ERRORS_TYPES_H__\n" \
		--template $(top_srcdir)/build-aux/mm-errors-template.h \
		--ftail "#endif /* __MM_ERRORS_TYPES_H__ */\n" \
		$(top_srcdir)/include/ModemManager-errors.h > $@

mm-errors-types.c: Makefile.am $(top_srcdir)/include/ModemManager-errors.h $(top_srcdir)/build-aux/mm-errors-template.c mm-errors-types.h
	$(AM_V_GEN) $(GLIB_MKENUMS) \
		--fhead "#include <ModemManager.h>\n#include \"mm-errors-types.h\"\n" \
		--template $(top_srcdir)/build-aux/mm-errors-template.c \
		$(top_srcdir)/include/ModemManager-errors.h > $@

mm-errors-quarks.c: Makefile.am $(top_srcdir)/include/ModemManager-errors.h $(top_srcdir)/build-aux/mm-errors-quarks-template.c $(top_builddir)/include/ModemManager-names.h mm-errors-types.h
	$(AM_V_GEN) $(GLIB_MKENUMS) \
		--fhead "#include <ModemManager.h>\n#include \"mm-errors-types.h\"\n" \
		--template $(top_srcdir)/build-aux/mm-errors-quarks-template.c \
		$(top_srcdir)/include/ModemManager-errors.h > $@
mm-gdbus-manager.c: $(mm_gdbus_manager_deps)
	$(AM_V_GEN) $(GDBUS_CODEGEN) \
		--interface-prefix org.freedesktop.ModemManager1. \
		--c-namespace=MmGdbus \
		--generate-docbook mm-gdbus-doc \
		--generate-c-code mm-gdbus-manager \
		$< \
		$(NULL)
$(filter-out mm-gdbus-manager.c, $(mm_gdbus_manager_generated)): $(mm_gdbus_manager_deps) mm-gdbus-manager.c
	@: # nothing to do, generated as a side-effect of the .c
mm-gdbus-modem.c: $(mm_gdbus_modem_deps)
	$(AM_V_GEN) $(GDBUS_CODEGEN) \
		--interface-prefix org.freedesktop.ModemManager1. \
		--c-namespace=MmGdbus \
		--generate-docbook mm-gdbus-doc \
		--generate-c-code mm-gdbus-modem \
		--c-generate-object-manager \
		--annotate "org.freedesktop.ModemManager1.Modem.ModemCdma" org.gtk.GDBus.C.Name ModemCdma \
		--annotate "org.freedesktop.ModemManager1.Modem.Modem3gpp" org.gtk.GDBus.C.Name Modem3gpp \
		--annotate "org.freedesktop.ModemManager1.Modem.Modem3gpp.Ussd" org.gtk.GDBus.C.Name Modem3gppUssd \
		$^ \
		$(NULL)
$(filter-out mm-gdbus-modem.c, $(mm_gdbus_modem_generated)): $(mm_gdbus_modem_deps) mm-gdbus-modem.c
	@: # nothing to do, generated as a side-effect of the .c
mm-gdbus-sim.c: $(mm_gdbus_sim_deps)
	$(AM_V_GEN) $(GDBUS_CODEGEN) \
		--interface-prefix org.freedesktop.ModemManager1. \
		--c-namespace=MmGdbus \
		--generate-docbook mm-gdbus-doc \
		--generate-c-code mm-gdbus-sim \
		$< \
		$(NULL)
$(filter-out mm-gdbus-sim.c, $(mm_gdbus_sim_generated)): $(mm_gdbus_sim_deps) mm-gdbus-sim.c
	@: # nothing to do, generated as a side-effect of the .c
mm-gdbus-bearer.c: $(mm_gdbus_bearer_deps)
	$(AM_V_GEN) $(GDBUS_CODEGEN) \
		--interface-prefix org.freedesktop.ModemManager1. \
		--c-namespace=MmGdbus \
		--generate-docbook mm-gdbus-doc \
		--generate-c-code mm-gdbus-bearer \
		$< \
		$(NULL)
$(filter-out mm-gdbus-bearer.c, $(mm_gdbus_bearer_generated)): $(mm_gdbus_bearer_deps) mm-gdbus-bearer.c
	@: # nothing to do, generated as a side-effect of the .c
mm-gdbus-sms.c: $(mm_gdbus_sms_deps)
	$(AM_V_GEN) $(GDBUS_CODEGEN) \
		--interface-prefix org.freedesktop.ModemManager1. \
		--c-namespace=MmGdbus \
		--generate-docbook mm-gdbus-doc \
		--generate-c-code mm-gdbus-sms \
		--annotate "org.freedesktop.ModemManager1.Sms:Data" org.gtk.GDBus.C.ForceGVariant True \
		$< \
		$(NULL)
$(filter-out mm-gdbus-sms.c, $(mm_gdbus_sms_generated)): $(mm_gdbus_sms_deps) mm-gdbus-sms.c
	@: # nothing to do, generated as a side-effect of the .c
mm-gdbus-call.c: $(mm_gdbus_call_deps)
	$(AM_V_GEN) $(GDBUS_CODEGEN) \
		--interface-prefix org.freedesktop.ModemManager1. \
		--c-namespace=MmGdbus \
		--generate-docbook mm-gdbus-doc \
		--generate-c-code mm-gdbus-call \
		$< \
		$(NULL)
$(filter-out mm-gdbus-call.c, $(mm_gdbus_call_generated)): $(mm_gdbus_call_deps) mm-gdbus-call.c
	@: # nothing to do, generated as a side-effect of the .c

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

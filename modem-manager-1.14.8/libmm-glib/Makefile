# Makefile.in generated by automake 1.16.1 from Makefile.am.
# libmm-glib/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.






am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/ModemManager
pkgincludedir = $(includedir)/ModemManager
pkglibdir = $(libdir)/ModemManager
pkglibexecdir = $(libexecdir)/ModemManager
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-linux-gnu
host_triplet = arm-buildroot-linux-gnueabihf
#am__append_1 = $(nodist_gir_DATA) $(nodist_typelib_DATA)
subdir = libmm-glib
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_code_coverage.m4 \
	$(top_srcdir)/m4/gettext.m4 $(top_srcdir)/m4/gtk-doc.m4 \
	$(top_srcdir)/m4/iconv.m4 $(top_srcdir)/m4/intlmacosx.m4 \
	$(top_srcdir)/m4/introspection.m4 $(top_srcdir)/m4/lib-ld.m4 \
	$(top_srcdir)/m4/lib-link.m4 $(top_srcdir)/m4/lib-prefix.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/mm-enable-plugin.m4 $(top_srcdir)/m4/nls.m4 \
	$(top_srcdir)/m4/po.m4 $(top_srcdir)/m4/progtest.m4 \
	$(top_srcdir)/m4/vapigen.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(include_HEADERS) \
	$(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(libdir)" "$(DESTDIR)$(girdir)" \
	"$(DESTDIR)$(typelibdir)" "$(DESTDIR)$(includedir)"
LTLIBRARIES = $(lib_LTLIBRARIES)
libmm_glib_la_DEPENDENCIES =  \
	${top_builddir}/libmm-glib/generated/libmm-generated.la
am_libmm_glib_la_OBJECTS = libmm_glib_la-mm-helper-types.lo \
	libmm_glib_la-mm-manager.lo libmm_glib_la-mm-object.lo \
	libmm_glib_la-mm-modem.lo libmm_glib_la-mm-modem-3gpp.lo \
	libmm_glib_la-mm-modem-3gpp-ussd.lo \
	libmm_glib_la-mm-modem-cdma.lo \
	libmm_glib_la-mm-modem-simple.lo \
	libmm_glib_la-mm-modem-location.lo \
	libmm_glib_la-mm-modem-time.lo \
	libmm_glib_la-mm-modem-firmware.lo \
	libmm_glib_la-mm-modem-signal.lo libmm_glib_la-mm-modem-oma.lo \
	libmm_glib_la-mm-sim.lo libmm_glib_la-mm-sms.lo \
	libmm_glib_la-mm-modem-messaging.lo \
	libmm_glib_la-mm-modem-voice.lo libmm_glib_la-mm-call.lo \
	libmm_glib_la-mm-bearer.lo libmm_glib_la-mm-common-helpers.lo \
	libmm_glib_la-mm-simple-status.lo \
	libmm_glib_la-mm-simple-connect-properties.lo \
	libmm_glib_la-mm-bearer-properties.lo \
	libmm_glib_la-mm-sms-properties.lo \
	libmm_glib_la-mm-call-properties.lo \
	libmm_glib_la-mm-bearer-ip-config.lo \
	libmm_glib_la-mm-bearer-stats.lo \
	libmm_glib_la-mm-location-3gpp.lo \
	libmm_glib_la-mm-location-gps-raw.lo \
	libmm_glib_la-mm-location-gps-nmea.lo \
	libmm_glib_la-mm-location-cdma-bs.lo \
	libmm_glib_la-mm-unlock-retries.lo \
	libmm_glib_la-mm-network-timezone.lo \
	libmm_glib_la-mm-firmware-properties.lo \
	libmm_glib_la-mm-firmware-update-settings.lo \
	libmm_glib_la-mm-cdma-manual-activation-properties.lo \
	libmm_glib_la-mm-signal.lo \
	libmm_glib_la-mm-kernel-event-properties.lo \
	libmm_glib_la-mm-pco.lo libmm_glib_la-mm-call-audio-format.lo
libmm_glib_la_OBJECTS = $(am_libmm_glib_la_OBJECTS)
AM_V_lt = $(am__v_lt_$(V))
am__v_lt_ = $(am__v_lt_$(AM_DEFAULT_VERBOSITY))
am__v_lt_0 = --silent
am__v_lt_1 = 
libmm_glib_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(libmm_glib_la_LDFLAGS) $(LDFLAGS) -o $@
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I. -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade =  \
	./$(DEPDIR)/libmm_glib_la-mm-bearer-ip-config.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-bearer-properties.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-bearer-stats.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-bearer.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-call-audio-format.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-call-properties.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-call.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-cdma-manual-activation-properties.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-common-helpers.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-firmware-properties.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-firmware-update-settings.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-helper-types.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-kernel-event-properties.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-location-3gpp.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-location-cdma-bs.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-location-gps-nmea.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-location-gps-raw.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-manager.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-modem-3gpp-ussd.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-modem-3gpp.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-modem-cdma.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-modem-firmware.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-modem-location.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-modem-messaging.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-modem-oma.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-modem-signal.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-modem-simple.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-modem-time.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-modem-voice.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-modem.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-network-timezone.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-object.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-pco.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-signal.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-sim.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-simple-connect-properties.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-simple-status.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-sms-properties.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-sms.Plo \
	./$(DEPDIR)/libmm_glib_la-mm-unlock-retries.Plo
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_$(V))
am__v_CC_ = $(am__v_CC_$(AM_DEFAULT_VERBOSITY))
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_$(V))
am__v_CCLD_ = $(am__v_CCLD_$(AM_DEFAULT_VERBOSITY))
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(libmm_glib_la_SOURCES)
DIST_SOURCES = $(libmm_glib_la_SOURCES)
RECURSIVE_TARGETS = all-recursive check-recursive cscopelist-recursive \
	ctags-recursive dvi-recursive html-recursive info-recursive \
	install-data-recursive install-dvi-recursive \
	install-exec-recursive install-html-recursive \
	install-info-recursive install-pdf-recursive \
	install-ps-recursive install-recursive installcheck-recursive \
	installdirs-recursive pdf-recursive ps-recursive \
	tags-recursive uninstall-recursive
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
DATA = $(nodist_gir_DATA) $(nodist_typelib_DATA)
HEADERS = $(include_HEADERS)
RECURSIVE_CLEAN_TARGETS = mostlyclean-recursive clean-recursive	\
  distclean-recursive maintainer-clean-recursive
am__recursive_targets = \
  $(RECURSIVE_TARGETS) \
  $(RECURSIVE_CLEAN_TARGETS) \
  $(am__extra_recursive_targets)
AM_RECURSIVE_TARGETS = $(am__recursive_targets:-recursive=) TAGS CTAGS \
	distdir distdir-am
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
DIST_SUBDIRS = $(SUBDIRS)
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
am__relativize = \
  dir0=`pwd`; \
  sed_first='s,^\([^/]*\)/.*$$,\1,'; \
  sed_rest='s,^[^/]*/*,,'; \
  sed_last='s,^.*/\([^/]*\)$$,\1,'; \
  sed_butlast='s,/*[^/]*$$,,'; \
  while test -n "$$dir1"; do \
    first=`echo "$$dir1" | sed -e "$$sed_first"`; \
    if test "$$first" != "."; then \
      if test "$$first" = ".."; then \
        dir2=`echo "$$dir0" | sed -e "$$sed_last"`/"$$dir2"; \
        dir0=`echo "$$dir0" | sed -e "$$sed_butlast"`; \
      else \
        first2=`echo "$$dir2" | sed -e "$$sed_first"`; \
        if test "$$first2" = "$$first"; then \
          dir2=`echo "$$dir2" | sed -e "$$sed_rest"`; \
        else \
          dir2="../$$dir2"; \
        fi; \
        dir0="$$dir0"/"$$first"; \
      fi; \
    fi; \
    dir1=`echo "$$dir1" | sed -e "$$sed_rest"`; \
  done; \
  reldir="$$dir2"
ACLOCAL = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 0
AR = ar
AUTOCONF = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing autoconf
AUTOHEADER = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing autoheader
AUTOMAKE = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing automake-1.16
AWK = gawk
CC = /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc
CCDEPMODE = depmode=none
CFLAGS = -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -std=gnu89
CODE_COVERAGE_CFLAGS = 
CODE_COVERAGE_ENABLED = no
CODE_COVERAGE_LDFLAGS = 
CPP = /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-cpp
CPPFLAGS = -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64
CYGPATH_W = echo
DBUS_SYS_DIR = /etc/dbus-1/system.d
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
DLLTOOL = false
DSYMUTIL = 
DUMPBIN = :
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
EXEEXT = 
FGREP = /usr/bin/grep -F
GCOV = 
GDBUS_CODEGEN = gdbus-codegen
GENHTML = 
GETTEXT_MACRO_VERSION = 0.19
GETTEXT_PACKAGE = ModemManager
GLIB_MKENUMS = glib-mkenums
GMSGFMT = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
GMSGFMT_015 = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
GREP = /usr/bin/grep
GTKDOC_CHECK = 
GTKDOC_CHECK_PATH = 
GTKDOC_DEPS_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include 
GTKDOC_DEPS_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgobject-2.0 -lglib-2.0 
GTKDOC_MKPDF = 
GTKDOC_REBASE = true
GUDEV_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gudev-1.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include 
GUDEV_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgudev-1.0 -lgobject-2.0 -lglib-2.0 
HTML_DIR = ${datadir}/gtk-doc/html
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
INTLLIBS = 
INTL_MACOSX_LIBS = 
INTROSPECTION_CFLAGS = 
INTROSPECTION_COMPILER = 
INTROSPECTION_GENERATE = 
INTROSPECTION_GIRDIR = 
INTROSPECTION_LIBS = 
INTROSPECTION_MAKEFILE = 
INTROSPECTION_SCANNER = 
INTROSPECTION_TYPELIBDIR = 
LCOV = 
LD = /opt/gcc-arm/arm-none-linux-gnueabihf/bin/ld
LDFLAGS = 
LIBICONV = -liconv
LIBINTL = 
LIBMM_GLIB_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gio-unix-2.0 -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS
LIBMM_GLIB_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgio-2.0 -lgobject-2.0 -lglib-2.0 
LIBOBJS = 
LIBS = 
LIBSYSTEMD_CFLAGS = 
LIBSYSTEMD_LIBS = 
LIBSYSTEMD_LOGIN_CFLAGS = 
LIBSYSTEMD_LOGIN_LIBS = 
LIBTOOL = $(SHELL) $(top_builddir)/libtool
LIPO = 
LN_S = ln -s
LTLIBICONV = -liconv
LTLIBINTL = 
LTLIBOBJS = 
LT_SYS_LIBRARY_PATH = 
MAINT = 
MAKEINFO = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing makeinfo
MANIFEST_TOOL = :
MBIM_CFLAGS = 
MBIM_LIBS = 
MKDIR_P = /usr/bin/mkdir -p
MMCLI_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS
MMCLI_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgio-2.0 -lgobject-2.0 -lglib-2.0 
MM_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gio-unix-2.0 -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS
MM_DEFAULT_USER_POLICY = 
MM_GLIB_LT_AGE = 6
MM_GLIB_LT_CURRENT = 6
MM_GLIB_LT_REVISION = 0
MM_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -Wl,--export-dynamic -lgmodule-2.0 -pthread -lglib-2.0 -lgio-2.0 -lgobject-2.0 -lglib-2.0 
MM_MAJOR_VERSION = 1
MM_MICRO_VERSION = 8
MM_MINOR_VERSION = 14
MM_POLKIT_SERVICE = 
MM_VERSION = 1.14.8
MSGFMT = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
MSGFMT_015 = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
MSGMERGE = /root/buildroot-2021.02/dev_out/host/bin/msgmerge
NM = nm
NMEDIT = 
OBJDUMP = objdump
OBJEXT = o
OTOOL = 
OTOOL64 = 
PACKAGE = ModemManager
PACKAGE_BUGREPORT = https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues
PACKAGE_NAME = ModemManager
PACKAGE_STRING = ModemManager 1.14.8
PACKAGE_TARNAME = ModemManager
PACKAGE_URL = 
PACKAGE_VERSION = 1.14.8
PATH_SEPARATOR = :
PKG_CONFIG = /root/buildroot-2021.02/dev_out/host/bin/pkg-config
PKG_CONFIG_LIBDIR = 
PKG_CONFIG_PATH = 
POLKIT_CFLAGS = 
POLKIT_LIBS = 
POSUB = po
QMI_CFLAGS = 
QMI_LIBS = 
RANLIB = ranlib
SED = /usr/bin/sed
SET_MAKE = 
SHELL = /bin/bash
STRIP = strip
SYSTEMD_UNIT_DIR = 
UDEV_BASE_DIR = /lib/udev
USE_NLS = yes
VAPIGEN = 
VAPIGEN_MAKEFILE = 
VAPIGEN_VAPIDIR = 
VERSION = 1.14.8
WARN_CFLAGS = -fno-strict-aliasing -Wnested-externs -Wmissing-prototypes -Wstrict-prototypes -Wdeclaration-after-statement -Wimplicit-function-declaration -Wold-style-definition -Wjump-misses-init -Wall -Wextra -Wundef -Wwrite-strings -Wpointer-arith -Wmissing-declarations -Wredundant-decls -Wno-unused-parameter -Wno-missing-field-initializers -Wformat=2 -Wcast-align -Wformat-nonliteral -Wformat-security -Wsign-compare -Wstrict-aliasing -Wshadow -Winline -Wpacked -Wmissing-format-attribute -Wmissing-noreturn -Winit-self -Wmissing-include-dirs -Wunused-but-set-variable -Warray-bounds -Wreturn-type -Wswitch-enum -Wswitch-default -Wduplicated-cond -Wduplicated-branches -Wlogical-op -Wrestrict -Wnull-dereference -Wdouble-promotion -Wno-error=unused-parameter -Wno-error=missing-field-initializers -Wno-cast-function-type -Wno-packed -Wno-error=cast-function-type -Wno-error=packed
WARN_LDFLAGS = -Wl,--no-as-needed
WARN_SCANNERFLAGS =               --warn-all                                                             
XGETTEXT = /root/buildroot-2021.02/dev_out/host/bin/xgettext
XGETTEXT_015 = /root/buildroot-2021.02/dev_out/host/bin/xgettext
XGETTEXT_EXTRA_OPTIONS = 
XSLTPROC_CHECK = yes
abs_builddir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/libmm-glib
abs_srcdir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/libmm-glib
abs_top_builddir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8
abs_top_srcdir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8
ac_ct_AR = ar
ac_ct_CC = 
ac_ct_DUMPBIN = link -dump
am__include = include
am__leading_dot = .
am__quote = 
am__tar = tar --format=ustar -chf - "$$tardir"
am__untar = tar -xf -
bindir = ${exec_prefix}/bin
build = x86_64-pc-linux-gnu
build_alias = x86_64-pc-linux-gnu
build_cpu = x86_64
build_os = linux-gnu
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = /usr
host = arm-buildroot-linux-gnueabihf
host_alias = arm-buildroot-linux-gnueabihf
host_cpu = arm
host_os = linux-gnueabihf
host_vendor = buildroot
htmldir = ${docdir}
includedir = ${prefix}/include/libmm-glib
infodir = ${datarootdir}/info
install_sh = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
localedir = ${datarootdir}/locale
localstatedir = /var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /usr
program_transform_name = s&^&&
psdir = ${docdir}
runstatedir = ${localstatedir}/run
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = .
sysconfdir = /etc
target_alias = arm-buildroot-linux-gnueabihf
top_build_prefix = ../
top_builddir = ..
top_srcdir = ..
SUBDIRS = generated . tests
AM_CFLAGS = $(CODE_COVERAGE_CFLAGS)
AM_LDFLAGS = $(CODE_COVERAGE_LDFLAGS)
lib_LTLIBRARIES = libmm-glib.la
libmm_glib_la_SOURCES = \
	libmm-glib.h \
	mm-helpers.h \
	mm-helper-types.h \
	mm-helper-types.c \
	mm-manager.h \
	mm-manager.c \
	mm-object.h \
	mm-object.c \
	mm-modem.h \
	mm-modem.c \
	mm-modem-3gpp.h \
	mm-modem-3gpp.c \
	mm-modem-3gpp-ussd.h \
	mm-modem-3gpp-ussd.c \
	mm-modem-cdma.h \
	mm-modem-cdma.c \
	mm-modem-simple.h \
	mm-modem-simple.c \
	mm-modem-location.h \
	mm-modem-location.c \
	mm-modem-time.h \
	mm-modem-time.c \
	mm-modem-firmware.h \
	mm-modem-firmware.c \
	mm-modem-signal.h \
	mm-modem-signal.c \
	mm-modem-oma.h \
	mm-modem-oma.c \
	mm-sim.h \
	mm-sim.c \
	mm-sms.h \
	mm-sms.c \
	mm-modem-messaging.h \
	mm-modem-messaging.c \
	mm-modem-voice.h \
	mm-modem-voice.c \
	mm-call.h \
	mm-call.c \
	mm-bearer.h \
	mm-bearer.c \
	mm-common-helpers.h \
	mm-common-helpers.c \
	mm-simple-status.h \
	mm-simple-status.c \
	mm-simple-connect-properties.h \
	mm-simple-connect-properties.c \
	mm-bearer-properties.h \
	mm-bearer-properties.c \
	mm-sms-properties.h \
	mm-sms-properties.c \
	mm-call-properties.h \
	mm-call-properties.c \
	mm-bearer-ip-config.h \
	mm-bearer-ip-config.c \
	mm-bearer-stats.h \
	mm-bearer-stats.c \
	mm-location-common.h \
	mm-location-3gpp.h \
	mm-location-3gpp.c \
	mm-location-gps-raw.h \
	mm-location-gps-raw.c \
	mm-location-gps-nmea.h \
	mm-location-gps-nmea.c \
	mm-location-cdma-bs.h \
	mm-location-cdma-bs.c \
	mm-unlock-retries.h \
	mm-unlock-retries.c \
	mm-network-timezone.h \
	mm-network-timezone.c \
	mm-firmware-properties.h \
	mm-firmware-properties.c \
	mm-firmware-update-settings.h \
	mm-firmware-update-settings.c \
	mm-cdma-manual-activation-properties.h \
	mm-cdma-manual-activation-properties.c \
	mm-signal.h \
	mm-signal.c \
	mm-kernel-event-properties.h \
	mm-kernel-event-properties.c \
	mm-pco.h \
	mm-pco.c \
	mm-call-audio-format.h \
	mm-call-audio-format.c \
	$(NULL)

libmm_glib_la_CPPFLAGS = \
	$(LIBMM_GLIB_CFLAGS) \
	-I$(srcdir) \
	-I$(top_srcdir) \
	-I$(top_builddir) \
	-I$(top_srcdir)/include \
	-I$(top_builddir)/include \
	-I${top_srcdir}/libmm-glib/generated \
	-I${top_builddir}/libmm-glib/generated \
	-DLIBMM_GLIB_COMPILATION \
	$(NULL)

libmm_glib_gla_CFLAGS = \
	$(WARN_CFLAGS) \
	$(NULL)

libmm_glib_la_LIBADD = \
	${top_builddir}/libmm-glib/generated/libmm-generated.la \
	$(NULL)

libmm_glib_la_LDFLAGS = \
	-version-info $(MM_GLIB_LT_CURRENT):$(MM_GLIB_LT_REVISION):$(MM_GLIB_LT_AGE) \
	$(WARN_LDFLAGS) \
	$(LIBMM_GLIB_LIBS) \
	$(NULL)

include_HEADERS = \
	libmm-glib.h \
	mm-helper-types.h \
	mm-manager.h \
	mm-object.h \
	mm-modem.h \
	mm-modem-3gpp.h \
	mm-modem-3gpp-ussd.h \
	mm-modem-cdma.h \
	mm-modem-messaging.h \
	mm-modem-location.h \
	mm-modem-time.h \
	mm-modem-firmware.h \
	mm-modem-signal.h \
	mm-modem-oma.h \
	mm-modem-simple.h \
	mm-sim.h \
	mm-sms.h \
	mm-modem-voice.h \
	mm-call.h \
	mm-bearer.h \
	mm-simple-status.h \
	mm-simple-connect-properties.h \
	mm-bearer-properties.h \
	mm-sms-properties.h \
	mm-call-properties.h \
	mm-bearer-ip-config.h \
	mm-bearer-stats.h \
	mm-location-common.h \
	mm-location-3gpp.h \
	mm-location-gps-nmea.h \
	mm-location-gps-raw.h \
	mm-location-cdma-bs.h \
	mm-unlock-retries.h \
	mm-network-timezone.h \
	mm-firmware-properties.h \
	mm-firmware-update-settings.h \
	mm-cdma-manual-activation-properties.h \
	mm-signal.h \
	mm-kernel-event-properties.h \
	mm-pco.h \
	mm-call-audio-format.h \
	$(NULL)

CLEANFILES = $(am__append_1)

# Introspection
#GENERATED_H = \
#	mm-enums-types.h \
#	mm-errors-types.h \
#	mm-gdbus-manager.h \
#	mm-gdbus-sim.h \
#	mm-gdbus-sms.h \
#	mm-gdbus-bearer.h \
#	mm-gdbus-modem.h \
#	$(NULL)

#GENERATED_C = \
#	mm-enums-types.c \
#	mm-errors-types.c \
#	mm-errors-quarks.c \
#	mm-gdbus-manager.c \
#	mm-gdbus-sim.c \
#	mm-gdbus-sms.c \
#	mm-gdbus-bearer.c \
#	mm-gdbus-modem.c \
#	$(NULL)

#PUBLIC_H = \
#	ModemManager-names.h \
#	ModemManager-version.h

#INTROSPECTION_GIRS = ModemManager-1.0.gir
#INTROSPECTION_SCANNER_ARGS = --warn-all
#INTROSPECTION_COMPILER_ARGS = 
#ModemManager_1_0_gir_INCLUDES = GLib-2.0 GObject-2.0 Gio-2.0
#ModemManager_1_0_gir_CFLAGS = $(libmm_glib_la_CPPFLAGS)
#ModemManager_1_0_gir_LIBS = libmm-glib.la
#ModemManager_1_0_gir_EXPORT_PACKAGES = libmm-glib
#ModemManager_1_0_gir_SCANNERFLAGS = \
#	$(WARN_SCANNERFLAGS) \
#	--c-include "libmm-glib.h" \
#	--identifier-prefix=MM \
#	--identifier-prefix=Mm \
#	--symbol-prefix=mm \
#	$(NULL)

#ModemManager_1_0_gir_FILES = \
#	$(include_HEADERS) \
#	$(filter-out %.h,$(libmm_glib_la_SOURCES)) \
#	$(filter %.c,$(libmm_glib_la_SOURCES)) \
#	$(addprefix generated/,$(GENERATED_H)) \
#	$(addprefix generated/,$(GENERATED_C)) \
#	$(addprefix ../include/,$(PUBLIC_H)) \
#	$(NULL)

#girdir = $(datadir)/gir-1.0
#nodist_gir_DATA = $(INTROSPECTION_GIRS)
#typelibdir = $(libdir)/girepository-1.0
#nodist_typelib_DATA = $(INTROSPECTION_GIRS:.gir=.typelib)
all: all-recursive

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu libmm-glib/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu libmm-glib/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

install-libLTLIBRARIES: $(lib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)"; \
	}

uninstall-libLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libdir)/$$f"; \
	done

clean-libLTLIBRARIES:
	-test -z "$(lib_LTLIBRARIES)" || rm -f $(lib_LTLIBRARIES)
	@list='$(lib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}

libmm-glib.la: $(libmm_glib_la_OBJECTS) $(libmm_glib_la_DEPENDENCIES) $(EXTRA_libmm_glib_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libmm_glib_la_LINK) -rpath $(libdir) $(libmm_glib_la_OBJECTS) $(libmm_glib_la_LIBADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

#include ./$(DEPDIR)/libmm_glib_la-mm-bearer-ip-config.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-bearer-properties.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-bearer-stats.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-bearer.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-call-audio-format.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-call-properties.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-call.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-cdma-manual-activation-properties.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-common-helpers.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-firmware-properties.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-firmware-update-settings.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-helper-types.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-kernel-event-properties.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-location-3gpp.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-location-cdma-bs.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-location-gps-nmea.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-location-gps-raw.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-manager.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-modem-3gpp-ussd.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-modem-3gpp.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-modem-cdma.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-modem-firmware.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-modem-location.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-modem-messaging.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-modem-oma.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-modem-signal.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-modem-simple.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-modem-time.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-modem-voice.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-modem.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-network-timezone.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-object.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-pco.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-signal.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-sim.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-simple-connect-properties.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-simple-status.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-sms-properties.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-sms.Plo # am--include-marker
#include ./$(DEPDIR)/libmm_glib_la-mm-unlock-retries.Plo # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
#	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
#	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
#	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(COMPILE) -c -o $@ $<

.c.obj:
#	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
#	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
#	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
#	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
#	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
#	$(am__mv) $$depbase.Tpo $$depbase.Plo
#	$(AM_V_CC)source='$<' object='$@' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LTCOMPILE) -c -o $@ $<

libmm_glib_la-mm-helper-types.lo: mm-helper-types.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-helper-types.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-helper-types.Tpo -c -o libmm_glib_la-mm-helper-types.lo `test -f 'mm-helper-types.c' || echo '$(srcdir)/'`mm-helper-types.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-helper-types.Tpo $(DEPDIR)/libmm_glib_la-mm-helper-types.Plo
#	$(AM_V_CC)source='mm-helper-types.c' object='libmm_glib_la-mm-helper-types.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-helper-types.lo `test -f 'mm-helper-types.c' || echo '$(srcdir)/'`mm-helper-types.c

libmm_glib_la-mm-manager.lo: mm-manager.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-manager.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-manager.Tpo -c -o libmm_glib_la-mm-manager.lo `test -f 'mm-manager.c' || echo '$(srcdir)/'`mm-manager.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-manager.Tpo $(DEPDIR)/libmm_glib_la-mm-manager.Plo
#	$(AM_V_CC)source='mm-manager.c' object='libmm_glib_la-mm-manager.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-manager.lo `test -f 'mm-manager.c' || echo '$(srcdir)/'`mm-manager.c

libmm_glib_la-mm-object.lo: mm-object.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-object.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-object.Tpo -c -o libmm_glib_la-mm-object.lo `test -f 'mm-object.c' || echo '$(srcdir)/'`mm-object.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-object.Tpo $(DEPDIR)/libmm_glib_la-mm-object.Plo
#	$(AM_V_CC)source='mm-object.c' object='libmm_glib_la-mm-object.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-object.lo `test -f 'mm-object.c' || echo '$(srcdir)/'`mm-object.c

libmm_glib_la-mm-modem.lo: mm-modem.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-modem.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-modem.Tpo -c -o libmm_glib_la-mm-modem.lo `test -f 'mm-modem.c' || echo '$(srcdir)/'`mm-modem.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-modem.Tpo $(DEPDIR)/libmm_glib_la-mm-modem.Plo
#	$(AM_V_CC)source='mm-modem.c' object='libmm_glib_la-mm-modem.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-modem.lo `test -f 'mm-modem.c' || echo '$(srcdir)/'`mm-modem.c

libmm_glib_la-mm-modem-3gpp.lo: mm-modem-3gpp.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-modem-3gpp.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-modem-3gpp.Tpo -c -o libmm_glib_la-mm-modem-3gpp.lo `test -f 'mm-modem-3gpp.c' || echo '$(srcdir)/'`mm-modem-3gpp.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-modem-3gpp.Tpo $(DEPDIR)/libmm_glib_la-mm-modem-3gpp.Plo
#	$(AM_V_CC)source='mm-modem-3gpp.c' object='libmm_glib_la-mm-modem-3gpp.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-modem-3gpp.lo `test -f 'mm-modem-3gpp.c' || echo '$(srcdir)/'`mm-modem-3gpp.c

libmm_glib_la-mm-modem-3gpp-ussd.lo: mm-modem-3gpp-ussd.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-modem-3gpp-ussd.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-modem-3gpp-ussd.Tpo -c -o libmm_glib_la-mm-modem-3gpp-ussd.lo `test -f 'mm-modem-3gpp-ussd.c' || echo '$(srcdir)/'`mm-modem-3gpp-ussd.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-modem-3gpp-ussd.Tpo $(DEPDIR)/libmm_glib_la-mm-modem-3gpp-ussd.Plo
#	$(AM_V_CC)source='mm-modem-3gpp-ussd.c' object='libmm_glib_la-mm-modem-3gpp-ussd.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-modem-3gpp-ussd.lo `test -f 'mm-modem-3gpp-ussd.c' || echo '$(srcdir)/'`mm-modem-3gpp-ussd.c

libmm_glib_la-mm-modem-cdma.lo: mm-modem-cdma.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-modem-cdma.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-modem-cdma.Tpo -c -o libmm_glib_la-mm-modem-cdma.lo `test -f 'mm-modem-cdma.c' || echo '$(srcdir)/'`mm-modem-cdma.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-modem-cdma.Tpo $(DEPDIR)/libmm_glib_la-mm-modem-cdma.Plo
#	$(AM_V_CC)source='mm-modem-cdma.c' object='libmm_glib_la-mm-modem-cdma.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-modem-cdma.lo `test -f 'mm-modem-cdma.c' || echo '$(srcdir)/'`mm-modem-cdma.c

libmm_glib_la-mm-modem-simple.lo: mm-modem-simple.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-modem-simple.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-modem-simple.Tpo -c -o libmm_glib_la-mm-modem-simple.lo `test -f 'mm-modem-simple.c' || echo '$(srcdir)/'`mm-modem-simple.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-modem-simple.Tpo $(DEPDIR)/libmm_glib_la-mm-modem-simple.Plo
#	$(AM_V_CC)source='mm-modem-simple.c' object='libmm_glib_la-mm-modem-simple.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-modem-simple.lo `test -f 'mm-modem-simple.c' || echo '$(srcdir)/'`mm-modem-simple.c

libmm_glib_la-mm-modem-location.lo: mm-modem-location.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-modem-location.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-modem-location.Tpo -c -o libmm_glib_la-mm-modem-location.lo `test -f 'mm-modem-location.c' || echo '$(srcdir)/'`mm-modem-location.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-modem-location.Tpo $(DEPDIR)/libmm_glib_la-mm-modem-location.Plo
#	$(AM_V_CC)source='mm-modem-location.c' object='libmm_glib_la-mm-modem-location.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-modem-location.lo `test -f 'mm-modem-location.c' || echo '$(srcdir)/'`mm-modem-location.c

libmm_glib_la-mm-modem-time.lo: mm-modem-time.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-modem-time.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-modem-time.Tpo -c -o libmm_glib_la-mm-modem-time.lo `test -f 'mm-modem-time.c' || echo '$(srcdir)/'`mm-modem-time.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-modem-time.Tpo $(DEPDIR)/libmm_glib_la-mm-modem-time.Plo
#	$(AM_V_CC)source='mm-modem-time.c' object='libmm_glib_la-mm-modem-time.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-modem-time.lo `test -f 'mm-modem-time.c' || echo '$(srcdir)/'`mm-modem-time.c

libmm_glib_la-mm-modem-firmware.lo: mm-modem-firmware.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-modem-firmware.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-modem-firmware.Tpo -c -o libmm_glib_la-mm-modem-firmware.lo `test -f 'mm-modem-firmware.c' || echo '$(srcdir)/'`mm-modem-firmware.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-modem-firmware.Tpo $(DEPDIR)/libmm_glib_la-mm-modem-firmware.Plo
#	$(AM_V_CC)source='mm-modem-firmware.c' object='libmm_glib_la-mm-modem-firmware.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-modem-firmware.lo `test -f 'mm-modem-firmware.c' || echo '$(srcdir)/'`mm-modem-firmware.c

libmm_glib_la-mm-modem-signal.lo: mm-modem-signal.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-modem-signal.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-modem-signal.Tpo -c -o libmm_glib_la-mm-modem-signal.lo `test -f 'mm-modem-signal.c' || echo '$(srcdir)/'`mm-modem-signal.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-modem-signal.Tpo $(DEPDIR)/libmm_glib_la-mm-modem-signal.Plo
#	$(AM_V_CC)source='mm-modem-signal.c' object='libmm_glib_la-mm-modem-signal.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-modem-signal.lo `test -f 'mm-modem-signal.c' || echo '$(srcdir)/'`mm-modem-signal.c

libmm_glib_la-mm-modem-oma.lo: mm-modem-oma.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-modem-oma.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-modem-oma.Tpo -c -o libmm_glib_la-mm-modem-oma.lo `test -f 'mm-modem-oma.c' || echo '$(srcdir)/'`mm-modem-oma.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-modem-oma.Tpo $(DEPDIR)/libmm_glib_la-mm-modem-oma.Plo
#	$(AM_V_CC)source='mm-modem-oma.c' object='libmm_glib_la-mm-modem-oma.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-modem-oma.lo `test -f 'mm-modem-oma.c' || echo '$(srcdir)/'`mm-modem-oma.c

libmm_glib_la-mm-sim.lo: mm-sim.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-sim.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-sim.Tpo -c -o libmm_glib_la-mm-sim.lo `test -f 'mm-sim.c' || echo '$(srcdir)/'`mm-sim.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-sim.Tpo $(DEPDIR)/libmm_glib_la-mm-sim.Plo
#	$(AM_V_CC)source='mm-sim.c' object='libmm_glib_la-mm-sim.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-sim.lo `test -f 'mm-sim.c' || echo '$(srcdir)/'`mm-sim.c

libmm_glib_la-mm-sms.lo: mm-sms.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-sms.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-sms.Tpo -c -o libmm_glib_la-mm-sms.lo `test -f 'mm-sms.c' || echo '$(srcdir)/'`mm-sms.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-sms.Tpo $(DEPDIR)/libmm_glib_la-mm-sms.Plo
#	$(AM_V_CC)source='mm-sms.c' object='libmm_glib_la-mm-sms.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-sms.lo `test -f 'mm-sms.c' || echo '$(srcdir)/'`mm-sms.c

libmm_glib_la-mm-modem-messaging.lo: mm-modem-messaging.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-modem-messaging.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-modem-messaging.Tpo -c -o libmm_glib_la-mm-modem-messaging.lo `test -f 'mm-modem-messaging.c' || echo '$(srcdir)/'`mm-modem-messaging.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-modem-messaging.Tpo $(DEPDIR)/libmm_glib_la-mm-modem-messaging.Plo
#	$(AM_V_CC)source='mm-modem-messaging.c' object='libmm_glib_la-mm-modem-messaging.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-modem-messaging.lo `test -f 'mm-modem-messaging.c' || echo '$(srcdir)/'`mm-modem-messaging.c

libmm_glib_la-mm-modem-voice.lo: mm-modem-voice.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-modem-voice.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-modem-voice.Tpo -c -o libmm_glib_la-mm-modem-voice.lo `test -f 'mm-modem-voice.c' || echo '$(srcdir)/'`mm-modem-voice.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-modem-voice.Tpo $(DEPDIR)/libmm_glib_la-mm-modem-voice.Plo
#	$(AM_V_CC)source='mm-modem-voice.c' object='libmm_glib_la-mm-modem-voice.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-modem-voice.lo `test -f 'mm-modem-voice.c' || echo '$(srcdir)/'`mm-modem-voice.c

libmm_glib_la-mm-call.lo: mm-call.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-call.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-call.Tpo -c -o libmm_glib_la-mm-call.lo `test -f 'mm-call.c' || echo '$(srcdir)/'`mm-call.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-call.Tpo $(DEPDIR)/libmm_glib_la-mm-call.Plo
#	$(AM_V_CC)source='mm-call.c' object='libmm_glib_la-mm-call.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-call.lo `test -f 'mm-call.c' || echo '$(srcdir)/'`mm-call.c

libmm_glib_la-mm-bearer.lo: mm-bearer.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-bearer.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-bearer.Tpo -c -o libmm_glib_la-mm-bearer.lo `test -f 'mm-bearer.c' || echo '$(srcdir)/'`mm-bearer.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-bearer.Tpo $(DEPDIR)/libmm_glib_la-mm-bearer.Plo
#	$(AM_V_CC)source='mm-bearer.c' object='libmm_glib_la-mm-bearer.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-bearer.lo `test -f 'mm-bearer.c' || echo '$(srcdir)/'`mm-bearer.c

libmm_glib_la-mm-common-helpers.lo: mm-common-helpers.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-common-helpers.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-common-helpers.Tpo -c -o libmm_glib_la-mm-common-helpers.lo `test -f 'mm-common-helpers.c' || echo '$(srcdir)/'`mm-common-helpers.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-common-helpers.Tpo $(DEPDIR)/libmm_glib_la-mm-common-helpers.Plo
#	$(AM_V_CC)source='mm-common-helpers.c' object='libmm_glib_la-mm-common-helpers.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-common-helpers.lo `test -f 'mm-common-helpers.c' || echo '$(srcdir)/'`mm-common-helpers.c

libmm_glib_la-mm-simple-status.lo: mm-simple-status.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-simple-status.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-simple-status.Tpo -c -o libmm_glib_la-mm-simple-status.lo `test -f 'mm-simple-status.c' || echo '$(srcdir)/'`mm-simple-status.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-simple-status.Tpo $(DEPDIR)/libmm_glib_la-mm-simple-status.Plo
#	$(AM_V_CC)source='mm-simple-status.c' object='libmm_glib_la-mm-simple-status.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-simple-status.lo `test -f 'mm-simple-status.c' || echo '$(srcdir)/'`mm-simple-status.c

libmm_glib_la-mm-simple-connect-properties.lo: mm-simple-connect-properties.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-simple-connect-properties.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-simple-connect-properties.Tpo -c -o libmm_glib_la-mm-simple-connect-properties.lo `test -f 'mm-simple-connect-properties.c' || echo '$(srcdir)/'`mm-simple-connect-properties.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-simple-connect-properties.Tpo $(DEPDIR)/libmm_glib_la-mm-simple-connect-properties.Plo
#	$(AM_V_CC)source='mm-simple-connect-properties.c' object='libmm_glib_la-mm-simple-connect-properties.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-simple-connect-properties.lo `test -f 'mm-simple-connect-properties.c' || echo '$(srcdir)/'`mm-simple-connect-properties.c

libmm_glib_la-mm-bearer-properties.lo: mm-bearer-properties.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-bearer-properties.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-bearer-properties.Tpo -c -o libmm_glib_la-mm-bearer-properties.lo `test -f 'mm-bearer-properties.c' || echo '$(srcdir)/'`mm-bearer-properties.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-bearer-properties.Tpo $(DEPDIR)/libmm_glib_la-mm-bearer-properties.Plo
#	$(AM_V_CC)source='mm-bearer-properties.c' object='libmm_glib_la-mm-bearer-properties.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-bearer-properties.lo `test -f 'mm-bearer-properties.c' || echo '$(srcdir)/'`mm-bearer-properties.c

libmm_glib_la-mm-sms-properties.lo: mm-sms-properties.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-sms-properties.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-sms-properties.Tpo -c -o libmm_glib_la-mm-sms-properties.lo `test -f 'mm-sms-properties.c' || echo '$(srcdir)/'`mm-sms-properties.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-sms-properties.Tpo $(DEPDIR)/libmm_glib_la-mm-sms-properties.Plo
#	$(AM_V_CC)source='mm-sms-properties.c' object='libmm_glib_la-mm-sms-properties.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-sms-properties.lo `test -f 'mm-sms-properties.c' || echo '$(srcdir)/'`mm-sms-properties.c

libmm_glib_la-mm-call-properties.lo: mm-call-properties.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-call-properties.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-call-properties.Tpo -c -o libmm_glib_la-mm-call-properties.lo `test -f 'mm-call-properties.c' || echo '$(srcdir)/'`mm-call-properties.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-call-properties.Tpo $(DEPDIR)/libmm_glib_la-mm-call-properties.Plo
#	$(AM_V_CC)source='mm-call-properties.c' object='libmm_glib_la-mm-call-properties.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-call-properties.lo `test -f 'mm-call-properties.c' || echo '$(srcdir)/'`mm-call-properties.c

libmm_glib_la-mm-bearer-ip-config.lo: mm-bearer-ip-config.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-bearer-ip-config.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-bearer-ip-config.Tpo -c -o libmm_glib_la-mm-bearer-ip-config.lo `test -f 'mm-bearer-ip-config.c' || echo '$(srcdir)/'`mm-bearer-ip-config.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-bearer-ip-config.Tpo $(DEPDIR)/libmm_glib_la-mm-bearer-ip-config.Plo
#	$(AM_V_CC)source='mm-bearer-ip-config.c' object='libmm_glib_la-mm-bearer-ip-config.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-bearer-ip-config.lo `test -f 'mm-bearer-ip-config.c' || echo '$(srcdir)/'`mm-bearer-ip-config.c

libmm_glib_la-mm-bearer-stats.lo: mm-bearer-stats.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-bearer-stats.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-bearer-stats.Tpo -c -o libmm_glib_la-mm-bearer-stats.lo `test -f 'mm-bearer-stats.c' || echo '$(srcdir)/'`mm-bearer-stats.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-bearer-stats.Tpo $(DEPDIR)/libmm_glib_la-mm-bearer-stats.Plo
#	$(AM_V_CC)source='mm-bearer-stats.c' object='libmm_glib_la-mm-bearer-stats.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-bearer-stats.lo `test -f 'mm-bearer-stats.c' || echo '$(srcdir)/'`mm-bearer-stats.c

libmm_glib_la-mm-location-3gpp.lo: mm-location-3gpp.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-location-3gpp.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-location-3gpp.Tpo -c -o libmm_glib_la-mm-location-3gpp.lo `test -f 'mm-location-3gpp.c' || echo '$(srcdir)/'`mm-location-3gpp.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-location-3gpp.Tpo $(DEPDIR)/libmm_glib_la-mm-location-3gpp.Plo
#	$(AM_V_CC)source='mm-location-3gpp.c' object='libmm_glib_la-mm-location-3gpp.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-location-3gpp.lo `test -f 'mm-location-3gpp.c' || echo '$(srcdir)/'`mm-location-3gpp.c

libmm_glib_la-mm-location-gps-raw.lo: mm-location-gps-raw.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-location-gps-raw.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-location-gps-raw.Tpo -c -o libmm_glib_la-mm-location-gps-raw.lo `test -f 'mm-location-gps-raw.c' || echo '$(srcdir)/'`mm-location-gps-raw.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-location-gps-raw.Tpo $(DEPDIR)/libmm_glib_la-mm-location-gps-raw.Plo
#	$(AM_V_CC)source='mm-location-gps-raw.c' object='libmm_glib_la-mm-location-gps-raw.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-location-gps-raw.lo `test -f 'mm-location-gps-raw.c' || echo '$(srcdir)/'`mm-location-gps-raw.c

libmm_glib_la-mm-location-gps-nmea.lo: mm-location-gps-nmea.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-location-gps-nmea.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-location-gps-nmea.Tpo -c -o libmm_glib_la-mm-location-gps-nmea.lo `test -f 'mm-location-gps-nmea.c' || echo '$(srcdir)/'`mm-location-gps-nmea.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-location-gps-nmea.Tpo $(DEPDIR)/libmm_glib_la-mm-location-gps-nmea.Plo
#	$(AM_V_CC)source='mm-location-gps-nmea.c' object='libmm_glib_la-mm-location-gps-nmea.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-location-gps-nmea.lo `test -f 'mm-location-gps-nmea.c' || echo '$(srcdir)/'`mm-location-gps-nmea.c

libmm_glib_la-mm-location-cdma-bs.lo: mm-location-cdma-bs.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-location-cdma-bs.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-location-cdma-bs.Tpo -c -o libmm_glib_la-mm-location-cdma-bs.lo `test -f 'mm-location-cdma-bs.c' || echo '$(srcdir)/'`mm-location-cdma-bs.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-location-cdma-bs.Tpo $(DEPDIR)/libmm_glib_la-mm-location-cdma-bs.Plo
#	$(AM_V_CC)source='mm-location-cdma-bs.c' object='libmm_glib_la-mm-location-cdma-bs.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-location-cdma-bs.lo `test -f 'mm-location-cdma-bs.c' || echo '$(srcdir)/'`mm-location-cdma-bs.c

libmm_glib_la-mm-unlock-retries.lo: mm-unlock-retries.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-unlock-retries.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-unlock-retries.Tpo -c -o libmm_glib_la-mm-unlock-retries.lo `test -f 'mm-unlock-retries.c' || echo '$(srcdir)/'`mm-unlock-retries.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-unlock-retries.Tpo $(DEPDIR)/libmm_glib_la-mm-unlock-retries.Plo
#	$(AM_V_CC)source='mm-unlock-retries.c' object='libmm_glib_la-mm-unlock-retries.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-unlock-retries.lo `test -f 'mm-unlock-retries.c' || echo '$(srcdir)/'`mm-unlock-retries.c

libmm_glib_la-mm-network-timezone.lo: mm-network-timezone.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-network-timezone.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-network-timezone.Tpo -c -o libmm_glib_la-mm-network-timezone.lo `test -f 'mm-network-timezone.c' || echo '$(srcdir)/'`mm-network-timezone.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-network-timezone.Tpo $(DEPDIR)/libmm_glib_la-mm-network-timezone.Plo
#	$(AM_V_CC)source='mm-network-timezone.c' object='libmm_glib_la-mm-network-timezone.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-network-timezone.lo `test -f 'mm-network-timezone.c' || echo '$(srcdir)/'`mm-network-timezone.c

libmm_glib_la-mm-firmware-properties.lo: mm-firmware-properties.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-firmware-properties.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-firmware-properties.Tpo -c -o libmm_glib_la-mm-firmware-properties.lo `test -f 'mm-firmware-properties.c' || echo '$(srcdir)/'`mm-firmware-properties.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-firmware-properties.Tpo $(DEPDIR)/libmm_glib_la-mm-firmware-properties.Plo
#	$(AM_V_CC)source='mm-firmware-properties.c' object='libmm_glib_la-mm-firmware-properties.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-firmware-properties.lo `test -f 'mm-firmware-properties.c' || echo '$(srcdir)/'`mm-firmware-properties.c

libmm_glib_la-mm-firmware-update-settings.lo: mm-firmware-update-settings.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-firmware-update-settings.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-firmware-update-settings.Tpo -c -o libmm_glib_la-mm-firmware-update-settings.lo `test -f 'mm-firmware-update-settings.c' || echo '$(srcdir)/'`mm-firmware-update-settings.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-firmware-update-settings.Tpo $(DEPDIR)/libmm_glib_la-mm-firmware-update-settings.Plo
#	$(AM_V_CC)source='mm-firmware-update-settings.c' object='libmm_glib_la-mm-firmware-update-settings.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-firmware-update-settings.lo `test -f 'mm-firmware-update-settings.c' || echo '$(srcdir)/'`mm-firmware-update-settings.c

libmm_glib_la-mm-cdma-manual-activation-properties.lo: mm-cdma-manual-activation-properties.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-cdma-manual-activation-properties.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-cdma-manual-activation-properties.Tpo -c -o libmm_glib_la-mm-cdma-manual-activation-properties.lo `test -f 'mm-cdma-manual-activation-properties.c' || echo '$(srcdir)/'`mm-cdma-manual-activation-properties.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-cdma-manual-activation-properties.Tpo $(DEPDIR)/libmm_glib_la-mm-cdma-manual-activation-properties.Plo
#	$(AM_V_CC)source='mm-cdma-manual-activation-properties.c' object='libmm_glib_la-mm-cdma-manual-activation-properties.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-cdma-manual-activation-properties.lo `test -f 'mm-cdma-manual-activation-properties.c' || echo '$(srcdir)/'`mm-cdma-manual-activation-properties.c

libmm_glib_la-mm-signal.lo: mm-signal.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-signal.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-signal.Tpo -c -o libmm_glib_la-mm-signal.lo `test -f 'mm-signal.c' || echo '$(srcdir)/'`mm-signal.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-signal.Tpo $(DEPDIR)/libmm_glib_la-mm-signal.Plo
#	$(AM_V_CC)source='mm-signal.c' object='libmm_glib_la-mm-signal.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-signal.lo `test -f 'mm-signal.c' || echo '$(srcdir)/'`mm-signal.c

libmm_glib_la-mm-kernel-event-properties.lo: mm-kernel-event-properties.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-kernel-event-properties.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-kernel-event-properties.Tpo -c -o libmm_glib_la-mm-kernel-event-properties.lo `test -f 'mm-kernel-event-properties.c' || echo '$(srcdir)/'`mm-kernel-event-properties.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-kernel-event-properties.Tpo $(DEPDIR)/libmm_glib_la-mm-kernel-event-properties.Plo
#	$(AM_V_CC)source='mm-kernel-event-properties.c' object='libmm_glib_la-mm-kernel-event-properties.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-kernel-event-properties.lo `test -f 'mm-kernel-event-properties.c' || echo '$(srcdir)/'`mm-kernel-event-properties.c

libmm_glib_la-mm-pco.lo: mm-pco.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-pco.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-pco.Tpo -c -o libmm_glib_la-mm-pco.lo `test -f 'mm-pco.c' || echo '$(srcdir)/'`mm-pco.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-pco.Tpo $(DEPDIR)/libmm_glib_la-mm-pco.Plo
#	$(AM_V_CC)source='mm-pco.c' object='libmm_glib_la-mm-pco.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-pco.lo `test -f 'mm-pco.c' || echo '$(srcdir)/'`mm-pco.c

libmm_glib_la-mm-call-audio-format.lo: mm-call-audio-format.c
#	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libmm_glib_la-mm-call-audio-format.lo -MD -MP -MF $(DEPDIR)/libmm_glib_la-mm-call-audio-format.Tpo -c -o libmm_glib_la-mm-call-audio-format.lo `test -f 'mm-call-audio-format.c' || echo '$(srcdir)/'`mm-call-audio-format.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/libmm_glib_la-mm-call-audio-format.Tpo $(DEPDIR)/libmm_glib_la-mm-call-audio-format.Plo
#	$(AM_V_CC)source='mm-call-audio-format.c' object='libmm_glib_la-mm-call-audio-format.lo' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libmm_glib_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libmm_glib_la-mm-call-audio-format.lo `test -f 'mm-call-audio-format.c' || echo '$(srcdir)/'`mm-call-audio-format.c

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
install-nodist_girDATA: $(nodist_gir_DATA)
	@$(NORMAL_INSTALL)
	@list='$(nodist_gir_DATA)'; test -n "$(girdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(girdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(girdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(girdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(girdir)" || exit $$?; \
	done

uninstall-nodist_girDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(nodist_gir_DATA)'; test -n "$(girdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(girdir)'; $(am__uninstall_files_from_dir)
install-nodist_typelibDATA: $(nodist_typelib_DATA)
	@$(NORMAL_INSTALL)
	@list='$(nodist_typelib_DATA)'; test -n "$(typelibdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(typelibdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(typelibdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(typelibdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(typelibdir)" || exit $$?; \
	done

uninstall-nodist_typelibDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(nodist_typelib_DATA)'; test -n "$(typelibdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(typelibdir)'; $(am__uninstall_files_from_dir)
install-includeHEADERS: $(include_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(include_HEADERS)'; test -n "$(includedir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(includedir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(includedir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(includedir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(includedir)" || exit $$?; \
	done

uninstall-includeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(include_HEADERS)'; test -n "$(includedir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(includedir)'; $(am__uninstall_files_from_dir)

# This directory's subdirectories are mostly independent; you can cd
# into them and run 'make' without going through this Makefile.
# To change the values of 'make' variables: instead of editing Makefiles,
# (1) if the variable is set in 'config.status', edit 'config.status'
#     (which will cause the Makefiles to be regenerated when you run 'make');
# (2) otherwise, pass the desired values on the 'make' command line.
$(am__recursive_targets):
	@fail=; \
	if $(am__make_keepgoing); then \
	  failcom='fail=yes'; \
	else \
	  failcom='exit 1'; \
	fi; \
	dot_seen=no; \
	target=`echo $@ | sed s/-recursive//`; \
	case "$@" in \
	  distclean-* | maintainer-clean-*) list='$(DIST_SUBDIRS)' ;; \
	  *) list='$(SUBDIRS)' ;; \
	esac; \
	for subdir in $$list; do \
	  echo "Making $$target in $$subdir"; \
	  if test "$$subdir" = "."; then \
	    dot_seen=yes; \
	    local_target="$$target-am"; \
	  else \
	    local_target="$$target"; \
	  fi; \
	  ($(am__cd) $$subdir && $(MAKE) $(AM_MAKEFLAGS) $$local_target) \
	  || eval $$failcom; \
	done; \
	if test "$$dot_seen" = "no"; then \
	  $(MAKE) $(AM_MAKEFLAGS) "$$target-am" || exit 1; \
	fi; test -z "$$fail"

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-recursive
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	if ($(ETAGS) --etags-include --version) >/dev/null 2>&1; then \
	  include_option=--etags-include; \
	  empty_fix=.; \
	else \
	  include_option=--include; \
	  empty_fix=; \
	fi; \
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    test ! -f $$subdir/TAGS || \
	      set "$$@" "$$include_option=$$here/$$subdir/TAGS"; \
	  fi; \
	done; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-recursive

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-recursive

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	@list='$(DIST_SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    $(am__make_dryrun) \
	      || test -d "$(distdir)/$$subdir" \
	      || $(MKDIR_P) "$(distdir)/$$subdir" \
	      || exit 1; \
	    dir1=$$subdir; dir2="$(distdir)/$$subdir"; \
	    $(am__relativize); \
	    new_distdir=$$reldir; \
	    dir1=$$subdir; dir2="$(top_distdir)"; \
	    $(am__relativize); \
	    new_top_distdir=$$reldir; \
	    echo " (cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) top_distdir="$$new_top_distdir" distdir="$$new_distdir" \\"; \
	    echo "     am__remove_distdir=: am__skip_length_check=: am__skip_mode_fix=: distdir)"; \
	    ($(am__cd) $$subdir && \
	      $(MAKE) $(AM_MAKEFLAGS) \
	        top_distdir="$$new_top_distdir" \
	        distdir="$$new_distdir" \
		am__remove_distdir=: \
		am__skip_length_check=: \
		am__skip_mode_fix=: \
	        distdir) \
	      || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-recursive
all-am: Makefile $(LTLIBRARIES) $(DATA) $(HEADERS)
installdirs: installdirs-recursive
installdirs-am:
	for dir in "$(DESTDIR)$(libdir)" "$(DESTDIR)$(girdir)" "$(DESTDIR)$(typelibdir)" "$(DESTDIR)$(includedir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-recursive
install-exec: install-exec-recursive
install-data: install-data-recursive
uninstall: uninstall-recursive

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-recursive
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-recursive

clean-am: clean-generic clean-libLTLIBRARIES clean-libtool \
	mostlyclean-am

distclean: distclean-recursive
		-rm -f ./$(DEPDIR)/libmm_glib_la-mm-bearer-ip-config.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-bearer-properties.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-bearer-stats.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-bearer.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-call-audio-format.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-call-properties.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-call.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-cdma-manual-activation-properties.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-common-helpers.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-firmware-properties.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-firmware-update-settings.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-helper-types.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-kernel-event-properties.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-location-3gpp.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-location-cdma-bs.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-location-gps-nmea.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-location-gps-raw.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-manager.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-3gpp-ussd.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-3gpp.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-cdma.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-firmware.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-location.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-messaging.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-oma.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-signal.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-simple.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-time.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-voice.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-network-timezone.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-object.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-pco.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-signal.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-sim.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-simple-connect-properties.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-simple-status.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-sms-properties.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-sms.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-unlock-retries.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-recursive

dvi-am:

html: html-recursive

html-am:

info: info-recursive

info-am:

install-data-am: install-includeHEADERS install-nodist_girDATA \
	install-nodist_typelibDATA

install-dvi: install-dvi-recursive

install-dvi-am:

install-exec-am: install-libLTLIBRARIES

install-html: install-html-recursive

install-html-am:

install-info: install-info-recursive

install-info-am:

install-man:

install-pdf: install-pdf-recursive

install-pdf-am:

install-ps: install-ps-recursive

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-recursive
		-rm -f ./$(DEPDIR)/libmm_glib_la-mm-bearer-ip-config.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-bearer-properties.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-bearer-stats.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-bearer.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-call-audio-format.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-call-properties.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-call.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-cdma-manual-activation-properties.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-common-helpers.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-firmware-properties.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-firmware-update-settings.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-helper-types.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-kernel-event-properties.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-location-3gpp.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-location-cdma-bs.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-location-gps-nmea.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-location-gps-raw.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-manager.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-3gpp-ussd.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-3gpp.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-cdma.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-firmware.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-location.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-messaging.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-oma.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-signal.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-simple.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-time.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem-voice.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-modem.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-network-timezone.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-object.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-pco.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-signal.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-sim.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-simple-connect-properties.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-simple-status.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-sms-properties.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-sms.Plo
	-rm -f ./$(DEPDIR)/libmm_glib_la-mm-unlock-retries.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-recursive

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-recursive

pdf-am:

ps: ps-recursive

ps-am:

uninstall-am: uninstall-includeHEADERS uninstall-libLTLIBRARIES \
	uninstall-nodist_girDATA uninstall-nodist_typelibDATA

.MAKE: $(am__recursive_targets) install-am install-strip

.PHONY: $(am__recursive_targets) CTAGS GTAGS TAGS all all-am \
	am--depfiles check check-am clean clean-generic \
	clean-libLTLIBRARIES clean-libtool cscopelist-am ctags \
	ctags-am distclean distclean-compile distclean-generic \
	distclean-libtool distclean-tags distdir dvi dvi-am html \
	html-am info info-am install install-am install-data \
	install-data-am install-dvi install-dvi-am install-exec \
	install-exec-am install-html install-html-am \
	install-includeHEADERS install-info install-info-am \
	install-libLTLIBRARIES install-man install-nodist_girDATA \
	install-nodist_typelibDATA install-pdf install-pdf-am \
	install-ps install-ps-am install-strip installcheck \
	installcheck-am installdirs installdirs-am maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags tags-am uninstall uninstall-am uninstall-includeHEADERS \
	uninstall-libLTLIBRARIES uninstall-nodist_girDATA \
	uninstall-nodist_typelibDATA

.PRECIOUS: Makefile

#	ModemManager-enums.h \
#	ModemManager-errors.h \
#	ModemManager-compat.h \
#	ModemManager.h \
#	$(NULL)

#ModemManager-1.0.gir: libmm-glib.la

-include $(INTROSPECTION_MAKEFILE)

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

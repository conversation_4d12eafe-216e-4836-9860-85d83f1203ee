# Makefile.in generated by automake 1.16.1 from Makefile.am.
# test/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.




am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/ModemManager
pkgincludedir = $(includedir)/ModemManager
pkglibdir = $(libdir)/ModemManager
pkglibexecdir = $(libexecdir)/ModemManager
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-linux-gnu
host_triplet = arm-buildroot-linux-gnueabihf
noinst_PROGRAMS = $(am__EXEEXT_1) mmtty$(EXEEXT) mmrules$(EXEEXT) \
	mmsmspdu$(EXEEXT) mmsmsmonitor$(EXEEXT)

################################################################################
# lsudev
################################################################################
am__append_1 = lsudev
subdir = test
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_code_coverage.m4 \
	$(top_srcdir)/m4/gettext.m4 $(top_srcdir)/m4/gtk-doc.m4 \
	$(top_srcdir)/m4/iconv.m4 $(top_srcdir)/m4/intlmacosx.m4 \
	$(top_srcdir)/m4/introspection.m4 $(top_srcdir)/m4/lib-ld.m4 \
	$(top_srcdir)/m4/lib-link.m4 $(top_srcdir)/m4/lib-prefix.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/mm-enable-plugin.m4 $(top_srcdir)/m4/nls.m4 \
	$(top_srcdir)/m4/po.m4 $(top_srcdir)/m4/progtest.m4 \
	$(top_srcdir)/m4/vapigen.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__EXEEXT_1 = lsudev$(EXEEXT)
PROGRAMS = $(noinst_PROGRAMS)
am__lsudev_SOURCES_DIST = lsudev.c
am_lsudev_OBJECTS = lsudev-lsudev.$(OBJEXT)
lsudev_OBJECTS = $(am_lsudev_OBJECTS)
am__DEPENDENCIES_1 =
lsudev_DEPENDENCIES = $(am__DEPENDENCIES_1)
AM_V_lt = $(am__v_lt_$(V))
am__v_lt_ = $(am__v_lt_$(AM_DEFAULT_VERBOSITY))
am__v_lt_0 = --silent
am__v_lt_1 = 
am_mmrules_OBJECTS = mmrules-mmrules.$(OBJEXT)
mmrules_OBJECTS = $(am_mmrules_OBJECTS)
mmrules_DEPENDENCIES = $(am__DEPENDENCIES_1) \
	$(top_builddir)/src/libkerneldevice.la
am_mmsmsmonitor_OBJECTS = mmsmsmonitor-mmsmsmonitor.$(OBJEXT)
mmsmsmonitor_OBJECTS = $(am_mmsmsmonitor_OBJECTS)
mmsmsmonitor_DEPENDENCIES = $(am__DEPENDENCIES_1) \
	$(top_builddir)/src/libhelpers.la \
	$(top_builddir)/libmm-glib/libmm-glib.la
am_mmsmspdu_OBJECTS = mmsmspdu-mmsmspdu.$(OBJEXT)
mmsmspdu_OBJECTS = $(am_mmsmspdu_OBJECTS)
mmsmspdu_DEPENDENCIES = $(am__DEPENDENCIES_1) \
	$(top_builddir)/src/libhelpers.la \
	$(top_builddir)/libmm-glib/libmm-glib.la
am_mmtty_OBJECTS = mmtty-mmtty.$(OBJEXT)
mmtty_OBJECTS = $(am_mmtty_OBJECTS)
mmtty_DEPENDENCIES = $(am__DEPENDENCIES_1) \
	$(top_builddir)/src/libport.la
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I. -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/lsudev-lsudev.Po \
	./$(DEPDIR)/mmrules-mmrules.Po \
	./$(DEPDIR)/mmsmsmonitor-mmsmsmonitor.Po \
	./$(DEPDIR)/mmsmspdu-mmsmspdu.Po ./$(DEPDIR)/mmtty-mmtty.Po
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_$(V))
am__v_CC_ = $(am__v_CC_$(AM_DEFAULT_VERBOSITY))
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_$(V))
am__v_CCLD_ = $(am__v_CCLD_$(AM_DEFAULT_VERBOSITY))
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(lsudev_SOURCES) $(mmrules_SOURCES) $(mmsmsmonitor_SOURCES) \
	$(mmsmspdu_SOURCES) $(mmtty_SOURCES)
DIST_SOURCES = $(am__lsudev_SOURCES_DIST) $(mmrules_SOURCES) \
	$(mmsmsmonitor_SOURCES) $(mmsmspdu_SOURCES) $(mmtty_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 0
AR = ar
AUTOCONF = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing autoconf
AUTOHEADER = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing autoheader
AUTOMAKE = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing automake-1.16
AWK = gawk
CC = /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-gcc
CCDEPMODE = depmode=none
CFLAGS = -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64  -Os   -std=gnu89
CODE_COVERAGE_CFLAGS = 
CODE_COVERAGE_ENABLED = no
CODE_COVERAGE_LDFLAGS = 
CPP = /root/buildroot-2021.02/dev_out/host/bin/arm-none-linux-gnueabihf-cpp
CPPFLAGS = -D_LARGEFILE_SOURCE -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64
CYGPATH_W = echo
DBUS_SYS_DIR = /etc/dbus-1/system.d
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
DLLTOOL = false
DSYMUTIL = 
DUMPBIN = :
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
EXEEXT = 
FGREP = /usr/bin/grep -F
GCOV = 
GDBUS_CODEGEN = gdbus-codegen
GENHTML = 
GETTEXT_MACRO_VERSION = 0.19
GETTEXT_PACKAGE = ModemManager
GLIB_MKENUMS = glib-mkenums
GMSGFMT = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
GMSGFMT_015 = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
GREP = /usr/bin/grep
GTKDOC_CHECK = 
GTKDOC_CHECK_PATH = 
GTKDOC_DEPS_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include 
GTKDOC_DEPS_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgobject-2.0 -lglib-2.0 
GTKDOC_MKPDF = 
GTKDOC_REBASE = true
GUDEV_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gudev-1.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include 
GUDEV_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgudev-1.0 -lgobject-2.0 -lglib-2.0 
HTML_DIR = ${datadir}/gtk-doc/html
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
INTLLIBS = 
INTL_MACOSX_LIBS = 
INTROSPECTION_CFLAGS = 
INTROSPECTION_COMPILER = 
INTROSPECTION_GENERATE = 
INTROSPECTION_GIRDIR = 
INTROSPECTION_LIBS = 
INTROSPECTION_MAKEFILE = 
INTROSPECTION_SCANNER = 
INTROSPECTION_TYPELIBDIR = 
LCOV = 
LD = /opt/gcc-arm/arm-none-linux-gnueabihf/bin/ld
LDFLAGS = 
LIBICONV = -liconv
LIBINTL = 
LIBMM_GLIB_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gio-unix-2.0 -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS
LIBMM_GLIB_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgio-2.0 -lgobject-2.0 -lglib-2.0 
LIBOBJS = 
LIBS = 
LIBSYSTEMD_CFLAGS = 
LIBSYSTEMD_LIBS = 
LIBSYSTEMD_LOGIN_CFLAGS = 
LIBSYSTEMD_LOGIN_LIBS = 
LIBTOOL = $(SHELL) $(top_builddir)/libtool
LIPO = 
LN_S = ln -s
LTLIBICONV = -liconv
LTLIBINTL = 
LTLIBOBJS = 
LT_SYS_LIBRARY_PATH = 
MAINT = 
MAKEINFO = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/missing makeinfo
MANIFEST_TOOL = :
MBIM_CFLAGS = 
MBIM_LIBS = 
MKDIR_P = /usr/bin/mkdir -p
MMCLI_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS
MMCLI_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -lgio-2.0 -lgobject-2.0 -lglib-2.0 
MM_CFLAGS = -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/glib-2.0 -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib/glib-2.0/include -I/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/include/gio-unix-2.0 -pthread  -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_48 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_2_48 -DGLIB_DISABLE_DEPRECATION_WARNINGS
MM_DEFAULT_USER_POLICY = 
MM_GLIB_LT_AGE = 6
MM_GLIB_LT_CURRENT = 6
MM_GLIB_LT_REVISION = 0
MM_LIBS = -L/root/buildroot-2021.02/dev_out/host/bin/../arm-buildroot-linux-gnueabihf/sysroot/usr/lib -Wl,--export-dynamic -lgmodule-2.0 -pthread -lglib-2.0 -lgio-2.0 -lgobject-2.0 -lglib-2.0 
MM_MAJOR_VERSION = 1
MM_MICRO_VERSION = 8
MM_MINOR_VERSION = 14
MM_POLKIT_SERVICE = 
MM_VERSION = 1.14.8
MSGFMT = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
MSGFMT_015 = /root/buildroot-2021.02/dev_out/host/bin/msgfmt
MSGMERGE = /root/buildroot-2021.02/dev_out/host/bin/msgmerge
NM = nm
NMEDIT = 
OBJDUMP = objdump
OBJEXT = o
OTOOL = 
OTOOL64 = 
PACKAGE = ModemManager
PACKAGE_BUGREPORT = https://gitlab.freedesktop.org/mobile-broadband/ModemManager/issues
PACKAGE_NAME = ModemManager
PACKAGE_STRING = ModemManager 1.14.8
PACKAGE_TARNAME = ModemManager
PACKAGE_URL = 
PACKAGE_VERSION = 1.14.8
PATH_SEPARATOR = :
PKG_CONFIG = /root/buildroot-2021.02/dev_out/host/bin/pkg-config
PKG_CONFIG_LIBDIR = 
PKG_CONFIG_PATH = 
POLKIT_CFLAGS = 
POLKIT_LIBS = 
POSUB = po
QMI_CFLAGS = 
QMI_LIBS = 
RANLIB = ranlib
SED = /usr/bin/sed
SET_MAKE = 
SHELL = /bin/bash
STRIP = strip
SYSTEMD_UNIT_DIR = 
UDEV_BASE_DIR = /lib/udev
USE_NLS = yes
VAPIGEN = 
VAPIGEN_MAKEFILE = 
VAPIGEN_VAPIDIR = 
VERSION = 1.14.8
WARN_CFLAGS = -fno-strict-aliasing -Wnested-externs -Wmissing-prototypes -Wstrict-prototypes -Wdeclaration-after-statement -Wimplicit-function-declaration -Wold-style-definition -Wjump-misses-init -Wall -Wextra -Wundef -Wwrite-strings -Wpointer-arith -Wmissing-declarations -Wredundant-decls -Wno-unused-parameter -Wno-missing-field-initializers -Wformat=2 -Wcast-align -Wformat-nonliteral -Wformat-security -Wsign-compare -Wstrict-aliasing -Wshadow -Winline -Wpacked -Wmissing-format-attribute -Wmissing-noreturn -Winit-self -Wmissing-include-dirs -Wunused-but-set-variable -Warray-bounds -Wreturn-type -Wswitch-enum -Wswitch-default -Wduplicated-cond -Wduplicated-branches -Wlogical-op -Wrestrict -Wnull-dereference -Wdouble-promotion -Wno-error=unused-parameter -Wno-error=missing-field-initializers -Wno-cast-function-type -Wno-packed -Wno-error=cast-function-type -Wno-error=packed
WARN_LDFLAGS = -Wl,--no-as-needed
WARN_SCANNERFLAGS =               --warn-all                                                             
XGETTEXT = /root/buildroot-2021.02/dev_out/host/bin/xgettext
XGETTEXT_015 = /root/buildroot-2021.02/dev_out/host/bin/xgettext
XGETTEXT_EXTRA_OPTIONS = 
XSLTPROC_CHECK = yes
abs_builddir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/test
abs_srcdir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/test
abs_top_builddir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8
abs_top_srcdir = /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8
ac_ct_AR = ar
ac_ct_CC = 
ac_ct_DUMPBIN = link -dump
am__include = include
am__leading_dot = .
am__quote = 
am__tar = tar --format=ustar -chf - "$$tardir"
am__untar = tar -xf -
bindir = ${exec_prefix}/bin
build = x86_64-pc-linux-gnu
build_alias = x86_64-pc-linux-gnu
build_cpu = x86_64
build_os = linux-gnu
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = /usr
host = arm-buildroot-linux-gnueabihf
host_alias = arm-buildroot-linux-gnueabihf
host_cpu = arm
host_os = linux-gnueabihf
host_vendor = buildroot
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = ${SHELL} /root/buildroot-2021.02/dev_out/build/modem-manager-1.14.8/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
localedir = ${datarootdir}/locale
localstatedir = /var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /usr
program_transform_name = s&^&&
psdir = ${docdir}
runstatedir = ${localstatedir}/run
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = .
sysconfdir = /etc
target_alias = arm-buildroot-linux-gnueabihf
top_build_prefix = ../
top_builddir = ..
top_srcdir = ..

################################################################################
# mmcli-test-sms
################################################################################
EXTRA_DIST = mmcli-test-sms
AM_CFLAGS = \
	$(WARN_CFLAGS) \
	$(NULL)

AM_LDFLAGS = \
	$(WARN_LDFLAGS) \
	$(NULL)

lsudev_SOURCES = lsudev.c
lsudev_CPPFLAGS = $(GUDEV_CFLAGS)
lsudev_LDADD = $(GUDEV_LIBS)
mmtty_SOURCES = mmtty.c
mmtty_CPPFLAGS = \
	$(MM_CFLAGS) \
	-I$(top_srcdir) \
	-I$(top_srcdir)/src \
	-I$(top_srcdir)/src/kerneldevice \
	-I$(top_srcdir)/include \
	-I$(top_builddir)/include \
	-I$(top_srcdir)/libmm-glib \
	-I$(top_srcdir)/libmm-glib/generated \
	-I$(top_builddir)/libmm-glib/generated

mmtty_LDADD = \
	$(MM_LIBS) \
	$(top_builddir)/src/libport.la \
	$(NULL)

mmrules_SOURCES = mmrules.c
mmrules_CPPFLAGS = \
	$(MM_CFLAGS) \
	-I$(top_srcdir) \
	-I$(top_srcdir)/src \
	-I$(top_srcdir)/src/kerneldevice \
	-I$(top_srcdir)/include \
	-I$(top_builddir)/include \
	-I$(top_srcdir)/libmm-glib \
	-I$(top_srcdir)/libmm-glib/generated \
	-I$(top_builddir)/libmm-glib/generated

mmrules_LDADD = \
	$(MM_LIBS) \
	$(top_builddir)/src/libkerneldevice.la \
	$(NULL)

mmsmspdu_SOURCES = mmsmspdu.c
mmsmspdu_CPPFLAGS = \
	$(MM_CFLAGS) \
	-I$(top_srcdir) \
	-I$(top_srcdir)/src \
	-I$(top_srcdir)/include \
	-I$(top_builddir)/include \
	-I$(top_srcdir)/libmm-glib \
	-I$(top_srcdir)/libmm-glib/generated \
	-I$(top_builddir)/libmm-glib/generated

mmsmspdu_LDADD = \
	$(MM_LIBS) \
	$(top_builddir)/src/libhelpers.la \
	$(top_builddir)/libmm-glib/libmm-glib.la \
	$(NULL)

mmsmsmonitor_SOURCES = mmsmsmonitor.c
mmsmsmonitor_CPPFLAGS = \
	$(MM_CFLAGS) \
	-I$(top_srcdir) \
	-I$(top_srcdir)/src \
	-I$(top_srcdir)/include \
	-I$(top_builddir)/include \
	-I$(top_srcdir)/libmm-glib \
	-I$(top_srcdir)/libmm-glib/generated \
	-I$(top_builddir)/libmm-glib/generated

mmsmsmonitor_LDADD = \
	$(MM_LIBS) \
	$(top_builddir)/src/libhelpers.la \
	$(top_builddir)/libmm-glib/libmm-glib.la \
	$(NULL)

all: all-am

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu test/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu test/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

clean-noinstPROGRAMS:
	@list='$(noinst_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

lsudev$(EXEEXT): $(lsudev_OBJECTS) $(lsudev_DEPENDENCIES) $(EXTRA_lsudev_DEPENDENCIES) 
	@rm -f lsudev$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(lsudev_OBJECTS) $(lsudev_LDADD) $(LIBS)

mmrules$(EXEEXT): $(mmrules_OBJECTS) $(mmrules_DEPENDENCIES) $(EXTRA_mmrules_DEPENDENCIES) 
	@rm -f mmrules$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(mmrules_OBJECTS) $(mmrules_LDADD) $(LIBS)

mmsmsmonitor$(EXEEXT): $(mmsmsmonitor_OBJECTS) $(mmsmsmonitor_DEPENDENCIES) $(EXTRA_mmsmsmonitor_DEPENDENCIES) 
	@rm -f mmsmsmonitor$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(mmsmsmonitor_OBJECTS) $(mmsmsmonitor_LDADD) $(LIBS)

mmsmspdu$(EXEEXT): $(mmsmspdu_OBJECTS) $(mmsmspdu_DEPENDENCIES) $(EXTRA_mmsmspdu_DEPENDENCIES) 
	@rm -f mmsmspdu$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(mmsmspdu_OBJECTS) $(mmsmspdu_LDADD) $(LIBS)

mmtty$(EXEEXT): $(mmtty_OBJECTS) $(mmtty_DEPENDENCIES) $(EXTRA_mmtty_DEPENDENCIES) 
	@rm -f mmtty$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(mmtty_OBJECTS) $(mmtty_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

#include ./$(DEPDIR)/lsudev-lsudev.Po # am--include-marker
#include ./$(DEPDIR)/mmrules-mmrules.Po # am--include-marker
#include ./$(DEPDIR)/mmsmsmonitor-mmsmsmonitor.Po # am--include-marker
#include ./$(DEPDIR)/mmsmspdu-mmsmspdu.Po # am--include-marker
#include ./$(DEPDIR)/mmtty-mmtty.Po # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
#	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
#	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
#	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(COMPILE) -c -o $@ $<

.c.obj:
#	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
#	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
#	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
#	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
#	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
#	$(am__mv) $$depbase.Tpo $$depbase.Plo
#	$(AM_V_CC)source='$<' object='$@' libtool=yes 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(LTCOMPILE) -c -o $@ $<

lsudev-lsudev.o: lsudev.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(lsudev_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT lsudev-lsudev.o -MD -MP -MF $(DEPDIR)/lsudev-lsudev.Tpo -c -o lsudev-lsudev.o `test -f 'lsudev.c' || echo '$(srcdir)/'`lsudev.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/lsudev-lsudev.Tpo $(DEPDIR)/lsudev-lsudev.Po
#	$(AM_V_CC)source='lsudev.c' object='lsudev-lsudev.o' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(lsudev_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o lsudev-lsudev.o `test -f 'lsudev.c' || echo '$(srcdir)/'`lsudev.c

lsudev-lsudev.obj: lsudev.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(lsudev_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT lsudev-lsudev.obj -MD -MP -MF $(DEPDIR)/lsudev-lsudev.Tpo -c -o lsudev-lsudev.obj `if test -f 'lsudev.c'; then $(CYGPATH_W) 'lsudev.c'; else $(CYGPATH_W) '$(srcdir)/lsudev.c'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/lsudev-lsudev.Tpo $(DEPDIR)/lsudev-lsudev.Po
#	$(AM_V_CC)source='lsudev.c' object='lsudev-lsudev.obj' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(lsudev_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o lsudev-lsudev.obj `if test -f 'lsudev.c'; then $(CYGPATH_W) 'lsudev.c'; else $(CYGPATH_W) '$(srcdir)/lsudev.c'; fi`

mmrules-mmrules.o: mmrules.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmrules_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmrules-mmrules.o -MD -MP -MF $(DEPDIR)/mmrules-mmrules.Tpo -c -o mmrules-mmrules.o `test -f 'mmrules.c' || echo '$(srcdir)/'`mmrules.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/mmrules-mmrules.Tpo $(DEPDIR)/mmrules-mmrules.Po
#	$(AM_V_CC)source='mmrules.c' object='mmrules-mmrules.o' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmrules_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmrules-mmrules.o `test -f 'mmrules.c' || echo '$(srcdir)/'`mmrules.c

mmrules-mmrules.obj: mmrules.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmrules_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmrules-mmrules.obj -MD -MP -MF $(DEPDIR)/mmrules-mmrules.Tpo -c -o mmrules-mmrules.obj `if test -f 'mmrules.c'; then $(CYGPATH_W) 'mmrules.c'; else $(CYGPATH_W) '$(srcdir)/mmrules.c'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/mmrules-mmrules.Tpo $(DEPDIR)/mmrules-mmrules.Po
#	$(AM_V_CC)source='mmrules.c' object='mmrules-mmrules.obj' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmrules_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmrules-mmrules.obj `if test -f 'mmrules.c'; then $(CYGPATH_W) 'mmrules.c'; else $(CYGPATH_W) '$(srcdir)/mmrules.c'; fi`

mmsmsmonitor-mmsmsmonitor.o: mmsmsmonitor.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmsmsmonitor_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmsmsmonitor-mmsmsmonitor.o -MD -MP -MF $(DEPDIR)/mmsmsmonitor-mmsmsmonitor.Tpo -c -o mmsmsmonitor-mmsmsmonitor.o `test -f 'mmsmsmonitor.c' || echo '$(srcdir)/'`mmsmsmonitor.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/mmsmsmonitor-mmsmsmonitor.Tpo $(DEPDIR)/mmsmsmonitor-mmsmsmonitor.Po
#	$(AM_V_CC)source='mmsmsmonitor.c' object='mmsmsmonitor-mmsmsmonitor.o' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmsmsmonitor_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmsmsmonitor-mmsmsmonitor.o `test -f 'mmsmsmonitor.c' || echo '$(srcdir)/'`mmsmsmonitor.c

mmsmsmonitor-mmsmsmonitor.obj: mmsmsmonitor.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmsmsmonitor_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmsmsmonitor-mmsmsmonitor.obj -MD -MP -MF $(DEPDIR)/mmsmsmonitor-mmsmsmonitor.Tpo -c -o mmsmsmonitor-mmsmsmonitor.obj `if test -f 'mmsmsmonitor.c'; then $(CYGPATH_W) 'mmsmsmonitor.c'; else $(CYGPATH_W) '$(srcdir)/mmsmsmonitor.c'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/mmsmsmonitor-mmsmsmonitor.Tpo $(DEPDIR)/mmsmsmonitor-mmsmsmonitor.Po
#	$(AM_V_CC)source='mmsmsmonitor.c' object='mmsmsmonitor-mmsmsmonitor.obj' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmsmsmonitor_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmsmsmonitor-mmsmsmonitor.obj `if test -f 'mmsmsmonitor.c'; then $(CYGPATH_W) 'mmsmsmonitor.c'; else $(CYGPATH_W) '$(srcdir)/mmsmsmonitor.c'; fi`

mmsmspdu-mmsmspdu.o: mmsmspdu.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmsmspdu_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmsmspdu-mmsmspdu.o -MD -MP -MF $(DEPDIR)/mmsmspdu-mmsmspdu.Tpo -c -o mmsmspdu-mmsmspdu.o `test -f 'mmsmspdu.c' || echo '$(srcdir)/'`mmsmspdu.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/mmsmspdu-mmsmspdu.Tpo $(DEPDIR)/mmsmspdu-mmsmspdu.Po
#	$(AM_V_CC)source='mmsmspdu.c' object='mmsmspdu-mmsmspdu.o' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmsmspdu_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmsmspdu-mmsmspdu.o `test -f 'mmsmspdu.c' || echo '$(srcdir)/'`mmsmspdu.c

mmsmspdu-mmsmspdu.obj: mmsmspdu.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmsmspdu_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmsmspdu-mmsmspdu.obj -MD -MP -MF $(DEPDIR)/mmsmspdu-mmsmspdu.Tpo -c -o mmsmspdu-mmsmspdu.obj `if test -f 'mmsmspdu.c'; then $(CYGPATH_W) 'mmsmspdu.c'; else $(CYGPATH_W) '$(srcdir)/mmsmspdu.c'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/mmsmspdu-mmsmspdu.Tpo $(DEPDIR)/mmsmspdu-mmsmspdu.Po
#	$(AM_V_CC)source='mmsmspdu.c' object='mmsmspdu-mmsmspdu.obj' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmsmspdu_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmsmspdu-mmsmspdu.obj `if test -f 'mmsmspdu.c'; then $(CYGPATH_W) 'mmsmspdu.c'; else $(CYGPATH_W) '$(srcdir)/mmsmspdu.c'; fi`

mmtty-mmtty.o: mmtty.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmtty_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmtty-mmtty.o -MD -MP -MF $(DEPDIR)/mmtty-mmtty.Tpo -c -o mmtty-mmtty.o `test -f 'mmtty.c' || echo '$(srcdir)/'`mmtty.c
#	$(AM_V_at)$(am__mv) $(DEPDIR)/mmtty-mmtty.Tpo $(DEPDIR)/mmtty-mmtty.Po
#	$(AM_V_CC)source='mmtty.c' object='mmtty-mmtty.o' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmtty_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmtty-mmtty.o `test -f 'mmtty.c' || echo '$(srcdir)/'`mmtty.c

mmtty-mmtty.obj: mmtty.c
#	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmtty_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT mmtty-mmtty.obj -MD -MP -MF $(DEPDIR)/mmtty-mmtty.Tpo -c -o mmtty-mmtty.obj `if test -f 'mmtty.c'; then $(CYGPATH_W) 'mmtty.c'; else $(CYGPATH_W) '$(srcdir)/mmtty.c'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/mmtty-mmtty.Tpo $(DEPDIR)/mmtty-mmtty.Po
#	$(AM_V_CC)source='mmtty.c' object='mmtty-mmtty.obj' libtool=no 
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) 
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(mmtty_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o mmtty-mmtty.obj `if test -f 'mmtty.c'; then $(CYGPATH_W) 'mmtty.c'; else $(CYGPATH_W) '$(srcdir)/mmtty.c'; fi`

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(PROGRAMS)
installdirs:
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool clean-noinstPROGRAMS \
	mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/lsudev-lsudev.Po
	-rm -f ./$(DEPDIR)/mmrules-mmrules.Po
	-rm -f ./$(DEPDIR)/mmsmsmonitor-mmsmsmonitor.Po
	-rm -f ./$(DEPDIR)/mmsmspdu-mmsmspdu.Po
	-rm -f ./$(DEPDIR)/mmtty-mmtty.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/lsudev-lsudev.Po
	-rm -f ./$(DEPDIR)/mmrules-mmrules.Po
	-rm -f ./$(DEPDIR)/mmsmsmonitor-mmsmsmonitor.Po
	-rm -f ./$(DEPDIR)/mmsmspdu-mmsmspdu.Po
	-rm -f ./$(DEPDIR)/mmtty-mmtty.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am:

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-generic clean-libtool clean-noinstPROGRAMS cscopelist-am \
	ctags ctags-am distclean distclean-compile distclean-generic \
	distclean-libtool distclean-tags distdir dvi dvi-am html \
	html-am info info-am install install-am install-data \
	install-data-am install-dvi install-dvi-am install-exec \
	install-exec-am install-html install-html-am install-info \
	install-info-am install-man install-pdf install-pdf-am \
	install-ps install-ps-am install-strip installcheck \
	installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags tags-am uninstall uninstall-am

.PRECIOUS: Makefile

	$(NULL)
	$(NULL)
	$(NULL)
	$(NULL)

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

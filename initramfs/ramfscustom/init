#!/bin/sh


mount -t proc proc /proc
mount -t sysfs sysfs /sys
mount -t devtmpfs none /dev

exec < /dev/console > /dev/console 2>&1

for parm in $(cat /proc/cmdline); do
	case $parm in
	ramfs)
		RAMFS_MODE=1
		;;  
	root=*)
		ROOT_DEVICE=`echo $parm | awk -F\= '{print $2}'`
		;;
	esac
done

if [ "x$ROOT_DEVICE" = "x" ]; then
	ROOT_DEVICE=autoconfig
fi
echo [$0]: RootDevice is \"$ROOT_DEVICE\"

# $1: the name of block device
wait_for_ready()
{
	CNT=10
	while true; do
		if [ -b $1 ]; then
			return 0
		fi
		
		echo [$0]: Wait $1 ready ...
		
		CNT=`expr $CNT - 1`
		if [ $CNT -eq 0 ]; then
			echo [$0]: $1 is not available!
			return 1
		fi
		sleep 1
	done
}

# $1: The block device
do_mount()
{
	e2fsck -y $1
	mount -o rw,noatime,nodiratime,norelatime,noauto_da_alloc,barrier=0,data=ordered -t ext4 $1 /mnt
	if [ $? -ne 0 ]; then
		echo [$0]: Failed to mount $1!
	fi
}

load_nand()
{
	echo [$0]: Try to load Nand ...
	
	NAND_MODULE=/lib/modules/$(uname -r)/nand.ko
	if [ ! -f $NAND_MODULE ]; then
		echo [$0]: $NAND_MODULE does not exist!
		return 1
	fi
	
	insmod $NAND_MODULE
	if [ $? -ne 0 ]; then
		echo [$0]: $NAND_MODULE is invalid!
		return 2
	fi
	wait_for_ready /dev/nandd
	if [ $? -eq 0 ]; then
		do_mount /dev/nandd
	fi
}

load_emmc()
{
	echo [$0]: Try to load EMMC ...
	
	wait_for_ready /dev/mmcblk0p7
	if [ $? -eq 0 ]; then
		do_mount /dev/mmcblk0p7
	fi

}

case $ROOT_DEVICE in
	/dev/nand*|/dev/system)
		load_nand
		;;
	/dev/mmc*)
		load_emmc
		;;
	autoconfig*)
		sleep 1;
        if cat /proc/partitions|grep "mmcblk0p7" >/dev/null;then
            magic_num=$(hexdump -s 1292 -n 2 -x /dev/mmcblk0p7|head -1|awk '{print $2 }')
            if echo $magic_num|grep "f30a" >/dev/null;then
				load_emmc
            fi
        else
			load_nand
		fi
		;;
	*)
		echo [$0]: "Use default type"
		;;
esac

if [ -x /mnt/sbin/init ]; then
	mount -n --move /proc /mnt/proc
	mount -n --move /sys /mnt/sys
	mount -n --move /dev /mnt/dev

	exec switch_root /mnt /sbin/init
fi

/sbin/getty -L ttyS0 115200 vt100 -n -l /bin/ash

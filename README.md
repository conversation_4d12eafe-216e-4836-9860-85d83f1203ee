
由于A40i基础软件平台文件较多，仅将必要的改动以及配置文件发布到gitee平台上。SDK、buildroot需要自己下载

准备文件：
1.  使用飞凌提供的SDK
下载地址https://pan.baidu.com/share/init?surl=Ns6dTI6OwF-aaGfIttwjzw
提取密码kphu
编译相关内容可参考《OKA40i-C&OKT3-C-Linux-软件用户手册》第四章 linux编译篇
2.  从buildroot官网下载2021.02版本。
https://buildroot.org/download.html
3.  gitee a40i_platform仓库

编译：
总体思路：由于根文件系统做了升级，使用buildroot2021编译根文件系统。使用SDK编译内核、设备树。编译的根文件系统和内核、设备树一起通过SDK打包

根文件系统：
1.	编译initramfs：
initramfs与内核一起打包，仅用于引导加载真正的根文件系统，可以不进行该步骤，直接使用编译好的rootfs.cpio.gz文件
拷贝a40i_platfrom仓库的initramfs到buildroot2021根目录
执行以下命令：
make initramfs_defconfig
make
buildroot/output/images目录生成rootfs.cpio.gz文件
2.	编译根文件系统：
拷贝a40i_platfrom仓库的rootfs到buildroot2021根目录
sudo make sun8iw11p1_hf_defconfig
sudo make //不加sudo权限可能会编译出现问题
buildroot/output/images目录生成rootfs.ext2文件
将rootfs.ext2拷贝到lichee/out/sun8iw11p1/linux/common目录，重命名为rootfs.ext4

SDK
拷贝a40i_platform仓库的lichee目录到SDK的lichee目录
进入lichee目录
1.  配置编译选项：
./build.sh config
配置编译的板件与分辨率，配置的内容保存在lichee/.buildconfig文件中。也可直接修改.buildconfig文件的BOARD和RESOLUTIN变量
2.  编译内核与设备树：
./build.sh –m kernel
该命令编译内核与设备树,生成Image文件
. packimg/packimg.sh
该命令将rootfs.cpio.gz文件系统与Image一起打包生成boot.img文件，并拷贝到lichee/out/sun8iw11p1/linux/common目录
3.  打包
./build.sh pack
在lichee/tools/pack目录得到img镜像文件，可通过PhoenixSuit工具烧写或者通过PhoenixCard工具制作烧写SD卡

单独更新内核：
将boot.img文件上传到装置的/root目录，执行以下命令
dd if=boot.img of=/dev/mmcblk0p6 bs=512
sync
重启装置，内核完成更新

单独更新设备树：
修改dts文件后，需要执行./build.sh –m kernel，再次./build.sh pack命令获得boot_package.fex文件，目录lichee/tools/pack/out/boot_package.fex
仅修改fex文件，直接执行./build.sh pack命令
将boot_package.fex文件上传到装置的/root目录，执行以下命令
dd if=boot_package.fex of=/dev/mmcblk0 bs=512 seek=32800
dd if=boot_package.fex of=/dev/mmcblk0 bs=512 seek=24576
sync
重启装置，设备树完成更新


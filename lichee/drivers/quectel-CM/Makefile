TARGET         = quectel-CM
DEST_DIR      ?= _install
CC            ?= arm-linux-gcc

build:quectel-CM

quectel-CM: *.c
	@$(CC) -Wall -s QmiWwanCM.c GobiNetCM.c main.c MPQMUX.c QMIThread.c util.c udhcpc.c -o quectel-CM -lpthread -ldl

hold:
	@echo "quectel-CM hold[skip]"

install:
	@mkdir -p $(DEST_DIR)
	@cp $(TARGET) $(DEST_DIR)

uninstall:
	@rm -fr $(DEST_DIR)/$(TARGET)

distclean: uninstall
	@rm -fr $(TARGET)
	@rm -fr _install

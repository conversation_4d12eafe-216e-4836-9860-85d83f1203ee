#include "stdio.h"
#include "unistd.h"
#include "sys/types.h"
#include "sys/stat.h"
#include "sys/ioctl.h"
#include "fcntl.h"
#include "stdlib.h"
#include "string.h"
#include <poll.h>
#include <sys/select.h>
#include <sys/time.h>
#include <signal.h>
#include <fcntl.h>


/*
 * @description		: main主程序
 * @param - argc 	: argv数组元素个数
 * @param - argv 	: 具体参数
 * @return 			: 0 成功;其他 失败
 */
int main(int argc, char *argv[])
{
	int fd;
	unsigned int databuf[2];
	unsigned int chan1_adc;
    unsigned int chan2_adc;

	int ret = 0;

	fd = open("/dev/ad7091r2", O_RDWR);
	if(fd < 0) {
		printf("can't open /dev/ad7091r2\r\n");
		return -1;
	}

	while (1) {
		ret = read(fd, databuf, sizeof(databuf));
		if(ret == 0) { 			/* 数据读取成功 */
			chan1_adc = databuf[0];
			chan2_adc = databuf[1];
			printf("原始值:\r\n");
			printf("chan1 = %d\r\n", chan1_adc);
            printf("chan2 = %d\r\n", chan2_adc);
		}
		usleep(100000); //100ms
	}
	close(fd);		
	return 0;
}


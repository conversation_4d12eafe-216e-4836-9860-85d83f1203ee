#include "stdio.h"
#include "unistd.h"
#include "stdlib.h"
#include <fcntl.h>

#define FCONST 223750.0
#define VRATIO 19705.7

int main(int argc, char *argv[])
{
	int fd;
	unsigned int databuf[2];
	float freq;
    float vrms;
	int ret = 0;

	fd = open("/dev/ade7953", O_RDWR);
	if(fd < 0) {
		printf("can't open /dev/ade7953\r\n");
		return -1;
	}

	while (1) {
		ret = read(fd, databuf, sizeof(databuf));
		if(ret == 0) { 			
			freq = FCONST / (databuf[0] + 0);
			vrms = databuf[1] / VRATIO;
            printf("databuf[0] = %d\r\n", databuf[0]);
            printf("databuf[1] = %d\r\n", databuf[1]);
			printf("freq = %f\r\n", freq);
            printf("vrms = %f\r\n", vrms);
		}
		usleep(100000);
	}
	close(fd);		
	return 0;
}


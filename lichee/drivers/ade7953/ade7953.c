#include <linux/ide.h>
#include <linux/spi/spi.h>
#include <linux/module.h>
#include <linux/errno.h>
#include <linux/cdev.h>
#include <linux/of_gpio.h>
#include <linux/delay.h>


#define	CONFIG_REG		0x102
#define	FREQ_REG		0x10E
#define	VRMS_REG		0x31C
#define FCONST 223750
#define VRATIO 19706

#define ADE7953_CNT	1
#define ADE7953_NAME	"ade7953"

struct ade7953_dev {
	dev_t devid;				/* 设备号 	*/
	struct cdev cdev;			/* cdev 	*/
	struct class *class;		/* 类 		*/
	struct device *device;		/* 设备 	*/
	struct device_node	*nd; 	/* 设备节点 */
	int major;					/* 主设备号 */
	void *private_data;			/* 私有数据 */
    int freq;		            /* 频率数据 */
    int volt;		            /* 电压数据 */
};

static struct ade7953_dev ade7953dev;

void ade7953_spilock(struct spi_device *spi)
{
    unsigned char writebuf[7];

    //write config register
    writebuf[0] = 0x01;
    writebuf[1] = 0x02;
    writebuf[2] = 0x00;
    writebuf[3] = 0x00;
    writebuf[4] = 0x04;
    spi_write(spi, writebuf, 5);

    //write register 0x120
    writebuf[0] = 0x00;
    writebuf[1] = 0xFE;
    writebuf[2] = 0x00;
    writebuf[3] = 0xAD;
    spi_write(spi, writebuf, 4);
    writebuf[0] = 0x01;
    writebuf[1] = 0x20;
    writebuf[2] = 0x00;
    writebuf[3] = 0x00;
    writebuf[4] = 0x30;
    spi_write(spi, writebuf, 5);
}

void ade7953_readdata(struct ade7953_dev *dev)
{
    unsigned char readbuf[4] = {0};
    unsigned char writebuf[3] = {0};
    
    writebuf[0] = 0x01;
    writebuf[1] = 0x0E;
    writebuf[2] = 0x80;
    spi_write_then_read(dev->private_data, writebuf, 3, readbuf, 2);
    dev->freq = (readbuf[0] << 8) + readbuf[1];
    
    writebuf[0] = 0x03;
    writebuf[1] = 0x1C;
    writebuf[2] = 0x80;
    spi_write_then_read(dev->private_data, writebuf, 3, readbuf, 4);
    dev->volt = (readbuf[1] << 16) + (readbuf[2] << 8) + readbuf[3];
}

static int ade7953_open(struct inode *inode, struct file *filp)
{
    filp->private_data = &ade7953dev; /* 设置私有数据 */
	return 0;
}

static ssize_t ade7953_read(struct file *filp, char __user *buf, size_t cnt, loff_t *off)
{
    unsigned int data[2] = {0};
    long err = 0;
	struct ade7953_dev *dev = (struct ade7953_dev *)filp->private_data;
    ade7953_readdata(dev);

    data[0] = dev->freq;
    data[0] = dev->volt;
	err = copy_to_user(buf, data, sizeof(data));

	return 0;
}

static int ade7953_release(struct inode *inode, struct file *filp)
{
	return 0;
}

/* ade7953操作函数 */
static const struct file_operations ade7953_ops = {
	.owner = THIS_MODULE,
	.open = ade7953_open,
	.read = ade7953_read,
	.release = ade7953_release,
};


static ssize_t freq_show(struct device *dev, struct device_attribute *attr, char *buf)
{
    ade7953_readdata(&ade7953dev);
    return sprintf(buf, "freq: %d\n", FCONST * 100 / ade7953dev.freq);
}
static DEVICE_ATTR(freq, S_IRUSR, freq_show, NULL);

static ssize_t volt_show(struct device *dev, struct device_attribute *attr, char *buf)
{
    ade7953_readdata(&ade7953dev);
    return sprintf(buf, "volt: %d\n", ade7953dev.volt * 100 / VRATIO);
}
static DEVICE_ATTR(volt, S_IRUSR, volt_show, NULL);

static struct attribute *attributes[] = {
    &dev_attr_freq.attr,
    &dev_attr_volt.attr,
    NULL
};

static const struct attribute_group ade7953_attr_group = {
    .attrs = attributes,
};
	
static int ade7953_probe(struct spi_device *spi)
{
	/* 构建设备号 */
	if (ade7953dev.major) {
		ade7953dev.devid = MKDEV(ade7953dev.major, 0);
		register_chrdev_region(ade7953dev.devid, ADE7953_CNT, ADE7953_NAME);
	} else {
		alloc_chrdev_region(&ade7953dev.devid, 0, ADE7953_CNT, ADE7953_NAME);
		ade7953dev.major = MAJOR(ade7953dev.devid);
	}

	/* 注册设备 */
	cdev_init(&ade7953dev.cdev, &ade7953_ops);
	cdev_add(&ade7953dev.cdev, ade7953dev.devid, ADE7953_CNT);

	/* 创建类 */
	ade7953dev.class = class_create(THIS_MODULE, ADE7953_NAME);
	if (IS_ERR(ade7953dev.class)) {
		return PTR_ERR(ade7953dev.class);
	}

	/* 创建设备 */
	ade7953dev.device = device_create(ade7953dev.class, NULL, ade7953dev.devid, NULL, ADE7953_NAME);
	if (IS_ERR(ade7953dev.device)) {
		return PTR_ERR(ade7953dev.device);
	}

    if (sysfs_create_group(&ade7953dev.device->kobj, &ade7953_attr_group) < 0) {
        pr_err("ade7953 sysfs_create_group failed\n");
        return -1;
    }

	/*初始化spi_device */
    spi->mode = SPI_MODE_3;	/*CPOL=1，CPHA=1*/
	spi_setup(spi);
	ade7953dev.private_data = spi; /* 设置私有数据 */

	/* 锁定ADE7953 SPI通信方式 */
	ade7953_spilock(spi); 

	return 0;
}

static int ade7953_remove(struct spi_device *spi)
{
    /* 删除设备 */
	cdev_del(&ade7953dev.cdev);
	unregister_chrdev_region(ade7953dev.devid, ADE7953_CNT);

    sysfs_remove_group(&ade7953dev.device->kobj, &ade7953_attr_group);

	/* 注销掉类和设备 */
	device_destroy(ade7953dev.class, ade7953dev.devid);
	class_destroy(ade7953dev.class);
	return 0;
}

/* 传统匹配方式ID列表 */
static const struct spi_device_id ade7953_id[] = {
	{"ecg,ade7953", 0},  
	{}
};

/* 设备树匹配列表 */
static const struct of_device_id ade7953_of_match[] = {
	{ .compatible = "ecg,ade7953" },
	{ /* Sentinel */ }
};

/* SPI驱动结构体 */	
static struct spi_driver ade7953_driver = {
	.probe = ade7953_probe,
	.remove = ade7953_remove,
	.driver = {
			.owner = THIS_MODULE,
		   	.name = "ade7953",
		   	.of_match_table = ade7953_of_match, 
		   },
	.id_table = ade7953_id,
};
		   
static int __init ade7953_init(void)
{
    return spi_register_driver(&ade7953_driver);
}

static void __exit ade7953_exit(void)
{
    spi_unregister_driver(&ade7953_driver);
}

module_init(ade7953_init);
module_exit(ade7953_exit);
MODULE_LICENSE("GPL");
MODULE_AUTHOR("xufei");




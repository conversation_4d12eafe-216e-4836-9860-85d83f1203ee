#include <stdio.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include "drv_display.h"

void  set_backlight(int v)
{
    unsigned long args[3];
    int fd = open("/dev/disp", O_RDONLY);
    if(fd < 0)
    {
        printf("Open backlight fail\n");
        return;
    }
    args[0]  = 0;
    args[1]  = v;
    args[2]  = 0;
    ioctl(fd,DISP_CMD_LCD_SET_BRIGHTNESS,args);
    close(fd);
}


int main(int argc, char *argv[])
{
    set_backlight(atoi(argv[1]));
    return 0;
}

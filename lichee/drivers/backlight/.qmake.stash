QMAKE_CXX.INCDIRS = \
    /root/workspace/allwinner/A40i/bsp/lichee/out/sun8iw11p1/linux/common/buildroot/host/opt/ext-toolchain/arm-linux-gnueabihf/include/c++/5.3.1 \
    /root/workspace/allwinner/A40i/bsp/lichee/out/sun8iw11p1/linux/common/buildroot/host/opt/ext-toolchain/arm-linux-gnueabihf/include/c++/5.3.1/arm-linux-gnueabihf \
    /root/workspace/allwinner/A40i/bsp/lichee/out/sun8iw11p1/linux/common/buildroot/host/opt/ext-toolchain/arm-linux-gnueabihf/include/c++/5.3.1/backward \
    /root/workspace/allwinner/A40i/bsp/lichee/out/sun8iw11p1/linux/common/buildroot/host/opt/ext-toolchain/lib/gcc/arm-linux-gnueabihf/5.3.1/include \
    /root/workspace/allwinner/A40i/bsp/lichee/out/sun8iw11p1/linux/common/buildroot/host/opt/ext-toolchain/lib/gcc/arm-linux-gnueabihf/5.3.1/include-fixed \
    /root/workspace/allwinner/A40i/bsp/lichee/out/sun8iw11p1/linux/common/buildroot/host/opt/ext-toolchain/arm-linux-gnueabihf/include \
    /root/workspace/allwinner/A40i/bsp/lichee/out/sun8iw11p1/linux/common/buildroot/host/usr/arm-buildroot-linux-gnueabihf/sysroot/usr/include
QMAKE_CXX.LIBDIRS = \
    /root/workspace/allwinner/A40i/bsp/lichee/out/sun8iw11p1/linux/common/buildroot/host/opt/ext-toolchain/lib/gcc/arm-linux-gnueabihf/5.3.1 \
    /root/workspace/allwinner/A40i/bsp/lichee/out/sun8iw11p1/linux/common/buildroot/host/opt/ext-toolchain/lib/gcc/arm-linux-gnueabihf \
    /root/workspace/allwinner/A40i/bsp/lichee/out/sun8iw11p1/linux/common/buildroot/host/opt/ext-toolchain/lib/gcc \
    /root/workspace/allwinner/A40i/bsp/lichee/out/sun8iw11p1/linux/common/buildroot/host/opt/ext-toolchain/arm-linux-gnueabihf/lib \
    /root/workspace/allwinner/A40i/bsp/lichee/out/sun8iw11p1/linux/common/buildroot/host/usr/arm-buildroot-linux-gnueabihf/sysroot/lib \
    /root/workspace/allwinner/A40i/bsp/lichee/out/sun8iw11p1/linux/common/buildroot/host/usr/arm-buildroot-linux-gnueabihf/sysroot/usr/lib
QMAKE_CXX.QT_COMPILER_STDCXX = 199711L
QMAKE_CXX.QT_GCC_MAJOR_VERSION = 5
QMAKE_CXX.QT_GCC_MINOR_VERSION = 3
QMAKE_CXX.QT_GCC_PATCH_VERSION = 1
QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QT_GCC_MAJOR_VERSION \
    QT_GCC_MINOR_VERSION \
    QT_GCC_PATCH_VERSION

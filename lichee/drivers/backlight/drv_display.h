#ifndef DRV_DISPLAY_H
#define DRV_DISPLAY_H
typedef enum tag_DISP_CMD
{
    //----disp global----
    DISP_CMD_RESERVE0 = 0x00,
    DISP_CMD_RESERVE1 = 0x01,
    DISP_CMD_SET_BKCOLOR = 0x03,
    DISP_CMD_GET_BKCOLOR = 0x04,
    DISP_CMD_SET_COLORKEY = 0x05,
    DISP_CMD_GET_COLORKEY = 0x06,
    DISP_CMD_GET_SCN_WIDTH = 0x07,
    DISP_CMD_GET_SCN_HEIGHT = 0x08,
    DISP_CMD_GET_OUTPUT_TYPE = 0x09,
    DISP_CMD_SET_EXIT_MODE = 0x0A,
    DISP_CMD_VSYNC_EVENT_EN = 0x0B,
    DISP_CMD_BLANK = 0x0C,
    DISP_CMD_SHADOW_PROTECT = 0x0D,
    DISP_CMD_HWC_COMMIT = 0x0E,
    DISP_CMD_HWC_GET_DISP_READY = 0x0F,

    //----layer----
    DISP_CMD_LAYER_ENABLE = 0x40,
    DISP_CMD_LAYER_DISABLE = 0x41,
    DISP_CMD_LAYER_SET_INFO = 0x42,
    DISP_CMD_LAYER_GET_INFO = 0x43,
    DISP_CMD_LAYER_TOP = 0x44,
    DISP_CMD_LAYER_BOTTOM = 0x45,
    DISP_CMD_LAYER_GET_FRAME_ID = 0x46,

    //----hwc----
    DISP_CMD_CURSOR_ENABLE = 0x80,
    DISP_CMD_CURSOR_DISABLE = 0x81,
    DISP_CMD_CURSOR_SET_POS = 0x82,
    DISP_CMD_CURSOR_GET_POS = 0x83,
    DISP_CMD_CURSOR_SET_FB = 0x84,
    DISP_CMD_CURSOR_SET_PALETTE = 0x85,

    //----hdmi----
    DISP_CMD_HDMI_ENABLE = 0xc0,
    DISP_CMD_HDMI_DISABLE = 0xc1,
    DISP_CMD_HDMI_SET_MODE = 0xc2,
    DISP_CMD_HDMI_GET_MODE = 0xc3,
    DISP_CMD_HDMI_SUPPORT_MODE = 0xc4,
    DISP_CMD_HDMI_GET_HPD_STATUS = 0xc5,
    DISP_CMD_HDMI_SET_SRC = 0xc6,

    //----lcd----
    DISP_CMD_LCD_ENABLE = 0x100,
    DISP_CMD_LCD_DISABLE = 0x101,
    DISP_CMD_LCD_SET_BRIGHTNESS = 0x102,
    DISP_CMD_LCD_GET_BRIGHTNESS = 0x103,
    DISP_CMD_LCD_BACKLIGHT_ENABLE  = 0x104,
    DISP_CMD_LCD_BACKLIGHT_DISABLE  = 0x105,
    DISP_CMD_LCD_SET_SRC = 0x106,
    DISP_CMD_LCD_SET_FPS  = 0x107,
    DISP_CMD_LCD_GET_FPS  = 0x108,
    DISP_CMD_LCD_GET_SIZE = 0x109,
    DISP_CMD_LCD_GET_MODEL_NAME = 0x10a,
    DISP_CMD_LCD_SET_GAMMA_TABLE = 0x10b,
    DISP_CMD_LCD_GAMMA_CORRECTION_ENABLE = 0x10c,
    DISP_CMD_LCD_GAMMA_CORRECTION_DISABLE = 0x10d,
    DISP_CMD_LCD_USER_DEFINED_FUNC = 0x10e,
    DISP_CMD_LCD_CHECK_OPEN_FINISH = 0x10f,
    DISP_CMD_LCD_CHECK_CLOSE_FINISH = 0x110,

    //---- capture ---
    DISP_CMD_CAPTURE_SCREEN = 0x140,//caputre screen and scaler to dram
    DISP_CMD_CAPTURE_SCREEN_STOP = 0x141,//for continue mode

    //---enhance ---
    DISP_CMD_ENHANCE_ENABLE = 0x180,
    DISP_CMD_ENHANCE_DISABLE = 0x181,
    DISP_CMD_GET_ENHANCE_EN = 0x182,
    DISP_CMD_SET_BRIGHT = 0x183,
    DISP_CMD_GET_BRIGHT = 0x184,
    DISP_CMD_SET_CONTRAST = 0x185,
    DISP_CMD_GET_CONTRAST = 0x186,
    DISP_CMD_SET_SATURATION = 0x187,
    DISP_CMD_GET_SATURATION = 0x188,
    DISP_CMD_SET_HUE = 0x189,
    DISP_CMD_GET_HUE = 0x18a,
    DISP_CMD_SET_ENHANCE_WINDOW = 0x18b,
    DISP_CMD_GET_ENHANCE_WINDOW = 0x18c,
    DISP_CMD_SET_ENHANCE_MODE = 0x18d,
    DISP_CMD_GET_ENHANCE_MODE = 0x18e,

    DISP_CMD_LAYER_ENHANCE_ENABLE = 0x1c0,
    DISP_CMD_LAYER_ENHANCE_DISABLE = 0x1c1,
    DISP_CMD_LAYER_GET_ENHANCE_EN = 0x1c2,
    DISP_CMD_LAYER_SET_BRIGHT = 0x1c3,
    DISP_CMD_LAYER_GET_BRIGHT = 0x1c4,
    DISP_CMD_LAYER_SET_CONTRAST = 0x1c5,
    DISP_CMD_LAYER_GET_CONTRAST = 0x1c6,
    DISP_CMD_LAYER_SET_SATURATION = 0x1c7,
    DISP_CMD_LAYER_GET_SATURATION = 0x1c8,
    DISP_CMD_LAYER_SET_HUE = 0x1c9,
    DISP_CMD_LAYER_GET_HUE = 0x1ca,
    DISP_CMD_LAYER_SET_ENHANCE_WINDOW = 0X1cb,
    DISP_CMD_LAYER_GET_ENHANCE_WINDOW = 0X1cc,
    DISP_CMD_LAYER_SET_ENHANCE_MODE = 0x1cd,
    DISP_CMD_LAYER_GET_ENHANCE_MODE = 0x1ce,

    DISP_CMD_DRC_ENABLE = 0x200,
    DISP_CMD_DRC_DISABLE = 0x201,
    DISP_CMD_GET_DRC_EN = 0x202,
    DISP_CMD_DRC_SET_WINDOW = 0x203,
    DISP_CMD_DRC_GET_WINDOW = 0x204,

    //---- for test
    DISP_CMD_FB_REQUEST = 0x280,
    DISP_CMD_FB_RELEASE = 0x281,

    DISP_CMD_MEM_REQUEST = 0x2c0,
    DISP_CMD_MEM_RELEASE = 0x2c1,
    DISP_CMD_MEM_GETADR = 0x2c2,
    DISP_CMD_MEM_SELIDX = 0x2c3,

    DISP_CMD_PRINT_REG = 0x2e0,
}__disp_cmd_t;


#endif // DRV_DISPLAY_H

#include <stdio.h>
#include <fcntl.h>
#include <poll.h>
#include <unistd.h>

int main()
{
    int fd = open("/sys/class/gpio/gpio224/value", O_RDONLY);
    if (fd < 0) {
        perror("open '/sys/class/gpio/gpio224/value' failed!\n");
        return -1;
    }
    struct pollfd fds[1];
    fds[0].fd = fd;
    fds[0].events = POLLPRI;
    char buffer[1];
    int ret;
    ret = read(fd, buffer, sizeof(buffer));
    if (ret == -1) {
        perror("read failed!\n");
        return -1;
    }
    while (1) {
        ret = poll(fds, 1, -1);
        if (ret == -1) {
            perror("poll failed!\n");
            return -1;
        } else if (ret == 0) {
            perror("poll timeout!\n");
            continue;
        }
        if (fds[0].revents & POLLPRI) {
            ret = lseek(fd, 0, SEEK_SET);
            if (ret == -1) {
                perror("lseek failed!\n");
                return -1;
            }
            ret = read(fd, buffer, sizeof(buffer));
            if (ret == -1) {
                perror("read failed!\n");
                return -1;
            }
            printf("PH0 interrupt, val=%c\n", buffer[0]);
            perror("POWER LOST!!!");
        }
    }
    return 0;
}

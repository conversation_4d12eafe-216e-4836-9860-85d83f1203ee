/*******************************************************************************
* COPYRIGHT (c) NACO. nanjing, china
*
*        FILE NAME: rt_wave.c
*
* FILE DESCRIPTION: realtime wave
*
* --DATE--    NAME        REVISION HISTORY
* 20180525    liuchenxin  Original Issue
* 20211119    liuchenxin  for A40I Linux SMP
*******************************************************************************/

#include "wave.h"

static int wave_open(struct inode *inode, struct file *filp)
{
    struct Wave *wave = container_of(inode->i_cdev, struct Wave, cdev);

    if (mutex_trylock(&wave->m_open) == 0)
        return -EBUSY;

    filp->private_data = wave;

    return 0;
}

static int wave_release(struct inode *inode, struct file *filp)
{
    struct Wave *wave = filp->private_data;

    mutex_unlock(&wave->m_open);
    return 0;
}

static int find_ready_cell(struct Wave *self)
{
    struct Wave_Cell *cell = &self->cell[0];
    int c_no, i;

    c_no = -1;
    for (i = 0; i < RT_WAVE_AUX; ++i) {
        if (cell[i].status == RT_CELL_STAT_WRT_RQ) {
            c_no = i;
            break;
        }
    }
    return c_no;
}

static ssize_t wave_read(struct file *filp,
                         char __user *buf, size_t len, loff_t *off)
{
    struct Wave *wave = filp->private_data;
    size_t size = sizeof(struct Wave_Data);
    struct Wave_Cell *cell = &wave->cell[0];
    int c_no;

    if (len != size)
        return -EINVAL;

    c_no = find_ready_cell(wave);
    if (c_no < 0) {
        if (wait_event_interruptible(wave->wq_read, (c_no = find_ready_cell(wave)) >= 0) < 0)
            return -ERESTARTSYS;
    }

    if (copy_to_user(buf, &cell[c_no].data, sizeof(struct Wave_Data)))
        return -EFAULT;

    cell[c_no].status = RT_CELL_STAT_NORMAL;

    return size;
}

static struct file_operations fops = {
    .owner          = THIS_MODULE,
    .read           = wave_read,
    .open           = wave_open,
    .release        = wave_release,
};

static ssize_t status_show(struct device *dev,
                           struct device_attribute *attr, char *buf)
{
    struct Wave *wave = dev_get_drvdata(dev);

    return sprintf(buf, "[rt_wave]\n"
                   "status: %#x\n"
                   "errno: %#x\n",
                   wave->status,
                   wave->err
        );
}

static DEVICE_ATTR(status, S_IRUSR, status_show, NULL);

static ssize_t trig_store(struct device *dev,
                          struct device_attribute *attr,
                          const char *buf, size_t count)
{
    struct Wave *wave = dev_get_drvdata(dev);

	unsigned long cfg = 0;

	if (kstrtoul(buf, 10, &cfg))
		return -EINVAL;

    if (cfg == 1) {
        wave->trigger = 1;
        return count;
    }

    return -EINVAL;
}
static DEVICE_ATTR(trig, S_IWUSR, NULL, trig_store);

static struct attribute *attributes[] = {
    &dev_attr_status.attr,
    &dev_attr_trig.attr,
    NULL
};

static const struct attribute_group wave_attr_group = {
    .attrs = attributes,
};

int wave_init(struct Wave* self, struct Sample* sample, struct class *class)
{
    self->class = class;

    if ((alloc_chrdev_region(&self->devt, 0, 1, "rt_wave")) < 0) {
        pr_err("wave alloc_chrdev_region failed\n");
        return -1;
    }
    pr_info("rt_wave: major=%d, minor=%d\n", MAJOR(self->devt), MINOR(self->devt));

    cdev_init(&self->cdev, &fops);

    if ((cdev_add(&self->cdev, self->devt, 1)) < 0) {
        pr_err("wave cdev_add failed\n");
        goto r_chrdev;
    }

    self->device = device_create(class, NULL, self->devt, self, "wave");
    if (self->device == NULL) {
        pr_err("wave device_create failed\n");
        goto r_cdev;
    }

    if (sysfs_create_group(&self->device->kobj, &wave_attr_group) < 0) {
        pr_err("wave sysfs_create_group failed\n");
        goto r_device;
    }


    mutex_init(&self->m_open);
    init_waitqueue_head(&self->wq_read);

    self->sample = sample;

    //self->write_ch_ready = -1;

    self->cell_rec = -1;
    //self->cell_wrt = -1;
    self->start_last = 0;
    self->status = RT_WAVE_STAT_NORMAL;

    //memset(&self->trigger[0], 0, sizeof(self->trigger));
    self->trigger = 0;
    // memset(&self->bi[0], 0, sizeof(self->bi));
    memset(&self->cell[0], 0, sizeof(self->cell));

    self->err = 0;

    return 0;

r_attr:
    sysfs_remove_group(&self->device->kobj, &wave_attr_group);
r_device:
    device_destroy(self->class, self->devt);
r_cdev:
    cdev_del(&self->cdev);
r_chrdev:
    unregister_chrdev_region(self->devt, 1);

    return -1;
}
EXPORT_SYMBOL(wave_init);

void wave_exit(struct Wave *self)
{
    sysfs_remove_group(&self->device->kobj, &wave_attr_group);
    device_destroy(self->class, self->devt);
    cdev_del(&self->cdev);
    unregister_chrdev_region(self->devt, 1);
}
EXPORT_SYMBOL(wave_exit);

static int find_available_cell(struct Wave* self)
{
    int c_no, i;
    struct Wave_Cell *cell = &self->cell[0];

    c_no = -1;
    // try find normal cell
    for (i = 0; i < RT_WAVE_AUX; i++) {
        if (cell[i].status == RT_CELL_STAT_NORMAL) {
            c_no = i;
            break;
        }
    }
    // no normal cell found
    if (c_no < 0) {
        // try find recording cell
        for (i = 0; i < RT_WAVE_AUX; i++) {
            if ((cell[i].status & RT_CELL_STAT_REC_MASK)) {
                // TODO: should impossible here
                c_no = i;
                break;
            }
        }
    }

    return c_no;
}

static void record_worker(struct Wave* self)
{
    int c_no;
    struct Wave_Cell* c;
    uint32_t cell_status;
    int begin_cnt, trig_cnt;
    int idx, i, j;

    if (self->status == RT_WAVE_STAT_START) {
        c_no = self->cell_rec;
        if (c_no < 0 || c_no >= RT_WAVE_AUX) {
            self->err |= RT_WAVE_ERR_REC;
            return;
        }

        c = &self->cell[c_no];
        cell_status = c->status;
        begin_cnt = c->begin_samp_cnt;
        trig_cnt = c->trig_samp_cnt;
        if ((cell_status & RT_CELL_STAT_REC_RQ)
            && ((cell_status & RT_CELL_STAT_REC_BEF_OK) == 0)) {

            idx = RT_WAVE_ST_BEF * RT_SAMP_POINTS_PER_CYC - (trig_cnt - begin_cnt);
            for (j = 0; j < RT_WAVE_WORKER_POINTS; ++j, ++idx) {
                for (i = 0; i < RT_SAMP_AD_CHANS; ++i) {
                    c->data.buf_ad[i][idx] = self->sample->flex->buf_ad[i][begin_cnt + j];
                }

                /*
                for (i = 0; i < ai_num; ++i) {
                    c->buf_ai[i][idx] = sample->buf_ai[i][begin_cnt + j];
                }
                */

                for (i = 0; i < RT_SAMP_BI_CHANS; ++i) {
                    c->data.buf_bi[i][idx] = self->sample->flex->buf_bi[i][begin_cnt + j];
                }

                //c->buf_freq[idx] = sample->buf_freq[begin_cnt + j];
            }

            c->begin_samp_cnt += RT_WAVE_WORKER_POINTS;
            if (c->begin_samp_cnt >= trig_cnt) {
                c->status |= RT_CELL_STAT_REC_BEF_OK;
            }
        }
    }
}

void wave_run(struct Wave* self)
{
    int i;
    int start;
    int c_no, samp_cnt;
    struct Wave_Cell *cell = &self->cell[0];
    int cell_status;
    //Sample* sample = this->sample;
    //uint32_t status;

    start = 0;
    for (i = 0; i < RT_WAVE_TRG_NUM; ++i) {
        if (self->trigger == 1) {
            start = 1;
            break;
        }
    }

    // recording
    if (self->status == RT_WAVE_STAT_START) {
        // find recording cell
        c_no = self->cell_rec;
        cell_status = cell[c_no].status;

        // recording done
        if ((cell_status & RT_CELL_STAT_REC_BEF_OK)
            && (cell_status & RT_CELL_STAT_REC_AFT_OK)) {
            // prepare writing, only if trigger returned and recording is done
            //self->cell_wrt = c_no;
            self->cell_rec = -1;
            cell[c_no].status = RT_CELL_STAT_WRT_RQ;
            self->status = RT_WAVE_STAT_NORMAL;

            self->trigger = 0;
            wake_up_interruptible(&self->wq_read);
            /*
            if (write_ch_ready == -1) {
                write_ch_ready = cell_wrt + 1;
                write_ch_ready.write();
            }
            */
        } else {
            // wait recording done
            record_worker(self);
        }
    // jump up
    } else if (self->start_last == 0 && start == 1) {
        // new start recording
        if (self->status == RT_WAVE_STAT_NORMAL) {
            samp_cnt = self->sample->flex->cnt + RT_SAMP_POINTS;

            // find available cell
            c_no = find_available_cell(self);
            // not found
            if (c_no < 0) {
                self->err |= RT_WAVE_ERR_AVA;
                return;
            }

            cell[c_no].trig_samp_cnt = samp_cnt;
            //get_current_time(&cell[c_no].trig_time);
            cell[c_no].data.trig_time = ktime_to_timespec(ktime_get_real());
            cell[c_no].begin_samp_cnt = samp_cnt - (RT_WAVE_ST_BEF * RT_SAMP_POINTS_PER_CYC);
            cell[c_no].remain_samp_cnt = RT_WAVE_ST_AFT * RT_SAMP_POINTS_PER_CYC;

            self->cell_rec = c_no;
            //cell_wrt = -1;
            cell[c_no].status = RT_CELL_STAT_REC_RQ;
            self->status = RT_WAVE_STAT_START;
        }
    } else {
        // watch any written cell done
        /*
        if (write_ch_ready > 0) {
            // rewind wave cell, after writing was done
            write_ch_done.try_read();
            int ch_done = write_ch_done;
            if (ch_done > 0 && ch_done <= RT_WAVE_AUX
                && cell[ch_done - 1].status == RT_CELL_STAT_WRT_RQ) {
                cell[ch_done - 1].status = RT_CELL_STAT_NORMAL;

                if (write_ch_ready == ch_done) {
                    write_ch_ready = -1;
                    write_ch_ready.write();
                    cell_wrt = -1;
                    //sem_wrt = 0;
                // have another cell to write
                } else {
                    write_ch_ready = cell_wrt + 1;
                    write_ch_ready.write();
                    cell_wrt = -1;
                    //sem_wrt = 1;
                }
            }
        }
        */
    }

    self->start_last = start;

}
EXPORT_SYMBOL(wave_run);


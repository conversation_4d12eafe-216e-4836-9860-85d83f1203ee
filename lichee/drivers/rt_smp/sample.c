/*******************************************************************************
* COPYRIGHT (c) DERI. nanjing, china
*
*        FILE NAME: sample.cpp
*
* FILE DESCRIPTION: realtime sample
*
* --DATE--    NAME        REVISION HISTORY
* 20180525    liuchenxin  Original Issue
* 20211119    liuchenxin  for rt_smp
*******************************************************************************/

#include <linux/mm.h>
#include <linux/slab.h>

#include "wave.h"
#include "sample.h"

static int sample_open(struct inode *inode, struct file *filp)
{
    struct Sample *sample = container_of(inode->i_cdev, struct Sample, cdev);

    if (mutex_trylock(&sample->m_open) == 0)
        return -EBUSY;

    filp->private_data = sample;

    return 0;
}

static int sample_release(struct inode *inode, struct file *filp)
{
    struct Sample *sample = filp->private_data;

    mutex_unlock(&sample->m_open);
    return 0;
}

static ssize_t sample_read(struct file *filp,
                           char __user *buf, size_t len, loff_t *off)
{
    struct Sample *sample = filp->private_data;
    int i, j;
    int index;
    int16_t *point;
    size_t piece_num = RT_SAMP_POINTS_PER_CYC + (RT_SAMP_POINTS_PER_CYC >> 3);
    size_t piece_size = piece_num * sizeof(int16_t);
    size_t size = piece_size * RT_SAMP_AD_CHANS;

    if (len != size)
        return -EINVAL;

    if (wait_event_interruptible(sample->wq_read, sample->index_last != sample->index) < 0)
        return -ERESTARTSYS;

    sample->reading = 1;

    index = sample->index;
    for (i = 0, j = 0; i < RT_SAMP_AD_CHANS; ++i) {
        point = &sample->flex->buf_ad[i][index + RT_SAMP_POINTS - piece_num];
        if (copy_to_user(buf + j, point, piece_size))
            return -EFAULT;
        j += piece_size;
    }

    sample->index_last = index;
    sample->reading = 0;

    return size;
}

static int sample_mmap(struct file *filp, struct vm_area_struct *vma)
{
    struct Sample *sample = filp->private_data;
    int ret = 0;
    struct page *page = NULL;
    unsigned long size = (unsigned long)(vma->vm_end - vma->vm_start);

    pr_info("sample mmap\n");
    
    if (size < sizeof(struct Sample_Buffer)) {
        pr_err("size: %lu; want: %u\n", size, sizeof(struct Sample_Buffer));
        return -EINVAL;
    }
   
    page = virt_to_page((unsigned long)sample->flex + (vma->vm_pgoff << PAGE_SHIFT)); 
    ret = remap_pfn_range(vma, vma->vm_start, page_to_pfn(page), size, vma->vm_page_prot);
    if (ret != 0)
        return ret;
    
    return 0;
}

static struct file_operations fops = {
    .owner          = THIS_MODULE,
    .read           = sample_read,
    .mmap           = sample_mmap,
    .open           = sample_open,
    .release        = sample_release,
};

int sample_init(struct Sample* self, struct Wave* wave, struct class *class)
{
    self->class = class;

    if ((alloc_chrdev_region(&self->devt, 0, 1, "rt_sample")) < 0) {
        pr_err("sample alloc_chrdev_region failed\n");
        return -1;
    }
    pr_info("rt_sample: major=%d, minor=%d\n", MAJOR(self->devt), MINOR(self->devt));

    cdev_init(&self->cdev, &fops);

    if ((cdev_add(&self->cdev, self->devt, 1)) < 0) {
        pr_err("sample cdev_add failed\n");
        goto r_chrdev;
    }

    self->device = device_create(class, NULL, self->devt, self, "sample");
    if (self->device == NULL) {
        pr_err("sample device_create failed\n");
        goto r_cdev;
    }

    mutex_init(&self->m_open);
    init_waitqueue_head(&self->wq_read);
    self->reading = 0;
    self->index = -1;
    self->index_last = -1;

    self->wave = wave;

    memset(&self->ad[0], 0, sizeof(self->ad));
    memset(&self->bi[0], 0, sizeof(self->bi));

    /*
    for (int i = 0; i < RT_SAMP_AD_CHANS; ++i) {
        ad_idx[i] = i;
    }
    ad_idx.write();
    */

    self->flex = kzalloc(sizeof(*self->flex), GFP_KERNEL);
    if (!self->flex) {
        pr_err("Sample kzalloc flex failed\n");
        goto r_device;
    }
    self->flex->cnt = 0;
    //self->cnt_div = 0;
    memset(&self->flex->buf_ad[0], 0, sizeof(self->flex->buf_ad));
    // memset(buf_ai, 0, sizeof(buf_ai));
    memset(&self->flex->buf_bi[0], 0, sizeof(self->flex->buf_bi));
    // memset(&self->buf_freq[0], 0, sizeof(self->buf_freq));

    self->max_cnt = 0;
    self->err = 0;
    self->calcu_failed_cnt = 0;

    return 0;

r_device:
    device_destroy(self->class, self->devt);
r_cdev:
    cdev_del(&self->cdev);
r_chrdev:
    unregister_chrdev_region(self->devt, 1);

    return -1;
}
EXPORT_SYMBOL(sample_init);

void sample_exit(struct Sample* self)
{
    kfree(self->flex);
    device_destroy(self->class, self->devt);
    cdev_del(&self->cdev);
    unregister_chrdev_region(self->devt, 1);
}
EXPORT_SYMBOL(sample_exit);

void sample_run(struct Sample* self)
{
    int i, new_cnt;
    int c_no;
    struct Wave_Cell* cell;
    int ps, idx1, idx2;
    int loop_cnt;
    // static bool flip_flag = false;

    // block flip
    // TODO: BS flip
    // MIO_Write_BS(flip_flag);
    // flip_flag = !flip_flag;

    // update count
    new_cnt = self->flex->cnt + 1;
    if (new_cnt >= RT_SAMP_POINTS) {
        new_cnt = 0;
    }

    // update ad buffer
    for (i = 0; i < RT_SAMP_AD_CHANS; ++i) {
        self->flex->buf_ad[i][new_cnt] = self->ad[i];
        self->flex->buf_ad[i][new_cnt + RT_SAMP_POINTS] = self->ad[i];
    }

    // update bi buffer
    for (i = 0; i < RT_SAMP_BI_CHANS; ++i) {
        self->flex->buf_bi[i][new_cnt] = self->bi[i];
        self->flex->buf_bi[i][new_cnt + RT_SAMP_POINTS] = self->bi[i];
    }

    // update timeval buffer
    self->flex->buf_tv[new_cnt] = self->tv;
    self->flex->buf_tv[new_cnt + RT_SAMP_POINTS] = self->tv;

    // update frequency buffer
    /*
    uint32_t freq = get_freq_isr1();
    buf_freq[new_cnt] = freq;
    buf_freq[new_cnt + RT_SAMP_POINTS] = freq;
    */

    // wave record
    //Wave* wave = this->wave;
    if (self->wave && self->wave->status == RT_WAVE_STAT_START) {
        c_no = self->wave->cell_rec;
        if (c_no < 0 || c_no >= RT_WAVE_AUX) {
            self->err |= RT_SAMP_ERR_WAV;
            return;
        }

        cell = &self->wave->cell[c_no];
        // int16_t(*wv_buf_ad)[RT_WAVE_POINTS] = &cell->buf_ad[0];
        // float(*wv_buf_ai)[RT_WAVE_POINTS] = &cell->buf_ai[0];
        // uint8_t(*wv_buf_bi)[RT_WAVE_POINTS] = &cell->buf_bi[0];
        // uint16_t(*wv_buf_freq)[RT_WAVE_POINTS] = &cell->buf_freq[0];
        if ((cell->status & RT_CELL_STAT_REC_RQ)
            && ((cell->status & RT_CELL_STAT_REC_AFT_OK) == 0)) {

            loop_cnt = 0;
            do {
                ps = RT_WAVE_ST_AFT * RT_SAMP_POINTS_PER_CYC - cell->remain_samp_cnt;
                idx1 = RT_WAVE_ST_BEF * RT_SAMP_POINTS_PER_CYC + ps;
                // idx2 = (cell->trig_samp_cnt + ps) % RT_MAX_SAMP_POINTS;
                idx2 = (cell->trig_samp_cnt - RT_SAMP_POINTS + ps) % RT_SAMP_POINTS;

                for (i = 0; i < RT_SAMP_AD_CHANS; i++) {
                    cell->data.buf_ad[i][idx1] = self->flex->buf_ad[i][idx2];
                }

                /*
                for (i = 0; i < wave->ai_num; i++) {
                    cell->buf_ai[i][idx1] = buf_ai[i][idx2];
                }
                */

                for (i = 0; i < RT_SAMP_BI_CHANS; i++) {
                    cell->data.buf_bi[i][idx1] = self->flex->buf_bi[i][idx2];
                }

                //cell->buf_freq[idx1] = buf_freq[idx2];

                loop_cnt++;
            // } while (--cell->remain_samp_cnt > 0 && idx2 != dp->cnt + RT_SAMP_POINTS);
            } while (--cell->remain_samp_cnt > 0 && idx2 < new_cnt);

            if (cell->remain_samp_cnt <= 0) {
                cell->status |= RT_CELL_STAT_REC_AFT_OK;
            }

            if (loop_cnt > self->max_cnt)
                self->max_cnt = loop_cnt;
        }
    }

    // update count
    self->flex->cnt = new_cnt;
    //cnt_div = cnt / 3;

    if (self->flex->cnt % (RT_SAMP_POINTS_PER_CYC) == 0) {
        if (self->reading)
            ++self->calcu_failed_cnt;
        self->index = self->flex->cnt;
        wake_up_interruptible(&self->wq_read);
    }

    /*
    if (cnt == 0) {
        cnt_div = 0;
    }else if (cnt % RT_SAMP_CNT_DIV == 0) {
        ++cnt_div;
    }
    */

}
EXPORT_SYMBOL(sample_run);

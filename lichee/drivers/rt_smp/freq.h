/*******************************************************************************
* COPYRIGHT (c) DERI. nanjing, china
*
*        FILE NAME: prot.h
*
* FILE DESCRIPTION: realtime frequency calculation header
*
* --DATE--    NAME        REVISION HISTORY
* 20180625    liuchenxin  Original Issue
* 20211206    liuchenxin  for A40I Linux SMP
*******************************************************************************/

#pragma once

#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/module.h>
#include <linux/kdev_t.h>
#include <linux/fs.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/hrtimer.h>

#include "sample_conf.h"
#include "sample.h"

#define FREQ_CYCLE_AVE_NUM 2
#define FREQ_CYCLE_READY_DELAY_NUM (10 * RT_SAMP_POINTS_PER_CYC)
#define K_UAB_THRESHOLD_VALUE 364 // about 10% Un
//#define K_FREQ_JUMP_THRESHOLD_VALUE = 1.0;

// #define FREQ_INTERVL_NSEC (20 * 1000 * 1000 / RT_SAMP_POINTS_PER_CYC)

//constexpr float TICKS_1HZ = 191666671.75;

// constexpr uint32_t TICKS_50HZ = 3833333;
// constexpr uint32_t TICKS_60HZ = 3194445;
// constexpr uint32_t TICKS_55HZ = 3484849;
// constexpr uint32_t TICKS_40HZ = 4791667;

struct FreqCycle {
    dev_t devt;
    struct cdev cdev;
    struct device *device;
    struct class *class;

    struct mutex m_open;

    /* input */
    struct Sample* sample;
    int16_t* chan_Ua; // Ua ad channal
    int16_t* chan_Ub; // Ub ad channal
    int* sample_cnt;

    /* private */
    int32_t ad_Uab_last;
    int32_t ad_Uab_now;
    uint8_t ad_ready;

    // int cycle_cnt;
    //uint64_t t_last; // last time cnt
    uint32_t dt;     // total delta time cnt

    //float freq_prot[FREQ_AVE_NUM];
    uint32_t cycle_buff[FREQ_CYCLE_AVE_NUM];
    int cycle_buff_cnt;

    /* output */
    //float freq_now, freq_last;
    //float freq_ava, freq_ava_last;
    uint32_t cycle_now, cycle_last;
    uint32_t cycle_ava, cycle_ava_last;
    //drp::SignalOut<float> freq;
    uint32_t cycle;
    uint8_t cycle_err;

    uint8_t ready;
    int ready_cnt;
    uint8_t cycle_overflow;
    int8_t cycle_jump;

    /* para */
    // drp::Para<float> rating;
    uint32_t rating;
    uint32_t chanA, chanB;

    uint32_t ns_up;
    uint32_t ns_rating;
    uint32_t ns_down;
    uint32_t ns_interval;

    // ktime_t t_last;

    // debug
    /*
    drp::SignalOut<uint8_t> tg;
    drp::SignalOut<float> d_freq_now;
    drp::SignalOut<float> d_ad_Uab;
    drp::SignalOut<float> d_dt;
    drp::SignalOut<uint32_t> d_val;
    */

};

int freq_init(struct FreqCycle* self, const char* n, struct Sample* sample, struct class *class);
void freq_exit(struct FreqCycle* self);
void freq_run(struct FreqCycle* self);


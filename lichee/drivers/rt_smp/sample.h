/*******************************************************************************
* COPYRIGHT (c) DERI. nanjing, china
*
*        FILE NAME: sample.h
*
* FILE DESCRIPTION: realtime sample header
*
* --DATE--    NAME        REVISION HISTORY
* 20180525    liuchenxin  Original Issue
* 20211119    liuchenxin  for rt_smp
*******************************************************************************/

#pragma once

#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/module.h>
#include <linux/kdev_t.h>
#include <linux/fs.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/hrtimer.h>
#include <linux/uaccess.h>
#include <linux/kthread.h>
#include <linux/wait.h>

#include "sample_conf.h"

struct Wave;

#define RT_SAMP_ERR_WAV 0x01

struct Sample_Buffer {
    int cnt;
    //int cnt_div;

    /* samp data buffers */
    int16_t buf_ad[RT_SAMP_AD_CHANS][RT_MAX_SAMP_POINTS];
    // float buf_ai[RT_SAMP_AI_CHANS][RT_MAX_SAMP_POINTS];
    uint8_t buf_bi[RT_SAMP_BI_CHANS][RT_MAX_SAMP_POINTS];
    // uint16_t buf_freq[RT_MAX_SAMP_POINTS];
    struct timeval buf_tv[RT_MAX_SAMP_POINTS];
};

struct Sample {
    dev_t devt;
    struct cdev cdev;
    struct device *device;
    struct class *class;

    struct mutex m_open;
    wait_queue_head_t wq_read;
    uint8_t reading;
    int index;
    int index_last;

    /* input */
    struct Wave* wave;
    int16_t ad[RT_SAMP_AD_CHANS];
    uint8_t bi[RT_SAMP_BI_CHANS];
    struct timeval tv;

    /* output */
    //drp::SignalOutArray<uint8_t, RT_SAMP_AD_CHANS> ad_idx;
    int max_cnt;

    struct Sample_Buffer *flex;

    uint32_t err;

    // statsu
    int calcu_failed_cnt;
};

// typedef int16_t (*Sample_Buffer)[RT_MAX_SAMP_POINTS];

int sample_init(struct Sample*, struct Wave*, struct class *class);
void sample_exit(struct Sample*);
void sample_run(struct Sample*);


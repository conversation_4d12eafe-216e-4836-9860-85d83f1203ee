/*******************************************************************************
* COPYRIGHT (c) DERI. nanjing, china
*
*        FILE NAME: sample_conf.h
*
* FILE DESCRIPTION: realtime config
*
* --DATE--    NAME        REVISION HISTORY
* 20180525    liuchenxin  Original Issue
* 20211119    liuchenxin  for A40I Linux SMP
*******************************************************************************/

#pragma once

/* normal frequency and sample points */
#define RT_FREQ_BASE_UP          55
#define RT_FREQ_BASE             50
#define RT_FREQ_BASE_DOWN        45
#define RT_SAMP_POINTS_PER_CYC   32 // sample points per cycle

/* rt frequency */
/*
#define RT_FREQ_LEVEL1 (RT_FREQ_BASE * RT_SAMP_POINTS_PER_CYC)  // 625us
#define RT_FREQ_LEVEL2 1000  // 1ms
*/
#define RT_FREQ_LEVEL3 200   // 5ms

/* wave configure */
#define RT_WAVE_TRG_NUM 16    // trigger num
#define RT_WAVE_ST_BEF  5     // cycle num before start point
#define RT_WAVE_ST_AFT  9     // cycle num after start point
// sample points of total wave
#define RT_WAVE_POINTS ((RT_WAVE_ST_BEF + RT_WAVE_ST_AFT) * RT_SAMP_POINTS_PER_CYC)

/* sample configure */
#define RT_SAMP_AD_CHANS 64   // ad sample chan num
#define RT_SAMP_AI_CHANS 0    // ai sample chan num
#define RT_SAMP_BI_CHANS 84   // yx sample chan num, 14 * 6
// total sample chan num
#define RT_SAMP_CHANS (RT_SAMP_AD_CHANS + RT_SAMP_AI_CHANS + RT_SAMP_BI_CHANS + 1)

// sample buffer cycle num
#define RT_SAMP_CYC_NUM (RT_WAVE_ST_BEF + 1)
// total sample points
#define RT_SAMP_POINTS (RT_SAMP_CYC_NUM * RT_SAMP_POINTS_PER_CYC)
#define RT_MAX_SAMP_POINTS (2 * RT_SAMP_POINTS)
// sample count divider
#define RT_SAMP_CNT_DIV 1

// rated digital value, hardware related
// constexpr float AC_VOLT_CHAN_RATIO = 220.0 / 13805.0;
// constexpr float AC_VOLT_CHAN_RATED_0P1_DIGIT = (13805.0 * 0.1);
// constexpr float AC_CURR_CHAN_RATIO = 5.0 / 1498.0;

// tianrui CT&PT
#define AC_VOLT_CHAN_RATIO (220.0 / 22563.33)
#define AC_VOLT_CHAN_RATED_0P1_DIGIT (22563.33 * 0.1)
#define AC_CURR_CHAN_RATIO (5.0 / 3037.5) 


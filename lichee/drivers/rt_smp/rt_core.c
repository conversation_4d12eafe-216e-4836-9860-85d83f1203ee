/*******************************************************************************
 * COPYRIGHT (c) DERI. nanjing, china
 *
 *        FILE NAME: rt_core.c
 *
 * FILE DESCRIPTION: realtime core driver module for Linux SMP
 *
 * --DATE--    NAME        REVISION HISTORY
 * 20211101    liuchenxin  Original Issue
 *******************************************************************************/

#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/module.h>
#include <linux/kdev_t.h>
#include <linux/fs.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/hrtimer.h>
#include <linux/ktime.h>
#include <linux/gpio.h>
#include <linux/workqueue.h>
#include <linux/io.h>
#include <linux/of.h>
#include <linux/spi/spi.h>
#include <linux/delay.h>

#include "sample.h"
#include "wave.h"
#include "freq.h"

MODULE_LICENSE("GPL");
MODULE_AUTHOR("liuchenxin @ DERI");
MODULE_DESCRIPTION("Real Time Drvier in SMP");
MODULE_VERSION("1.0");

#define DRIVER_NAME "rt_smp"

#define PWM_ADDR          0x01c23400
#define GPIO_ADDR         0x01c20800

#define PWM_TICK_PER_US   24

#define TIMEOUT_DEFAULT   ktime_set(1, 0)                                // 1s

/* configurable settings */
/*
#define TIMEOUT_CNVT      (50 * 1000)      // (ns)
#define TIMEOUT_WAIT      (200 * 1000)     // (ns)
*/

/*
#define GPIO_CNVT_BANK    4
#define GPIO_CNVT_OFST    9
#define GPIO_CNVT_NUM     (GPIO_CNVT_BANK * 32 + GPIO_CNVT_OFST)         // PE9
#define GPIO_CNVT_ADDR    (GPIO_ADDR + 0x0010 + GPIO_CNVT_BANK * 0x24)   // 0x01c208A0
*/

// #define GPIO_FLAG_BANK    1
// #define GPIO_FLAG_OFST    2
// #define GPIO_FLAG_NUM     (GPIO_FLAG_BANK * 32 + GPIO_FLAG_OFST)         // PB2
// #define GPIO_FLAG_ADDR    (GPIO_ADDR + 0x0010 + GPIO_FLAG_BANK * 0x24)   

#define PWM_NUM           4
#define PWM_PER_ADDR      (PWM_ADDR + 0x0040)
#define PWM_PCR_ADDR      (PWM_ADDR + 0x0060 + PWM_NUM * 0x20)
#define PWM_PPR_ADDR      (PWM_ADDR + 0x0064 + PWM_NUM * 0x20)
#define PWM_PCNTR_ADDR    (PWM_ADDR + 0x0068 + PWM_NUM * 0x20)

#define GPIO_RST          262   // PI6
#define GPIO_CS0          272   // PI16
#define GPIO_CS1          268   // PI12
#define GPIO_CS2          36    // PB4
#define GPIO_CS3          53    // PB21
#define GPIO_BUSY0        237   // PH13
#define GPIO_BUSY1        41    // PB9
#define GPIO_BUSY2        229   // PH5
#define GPIO_BUSY3        226   // PH2
const u32 gpio_cs[4] = {GPIO_CS0, GPIO_CS1, GPIO_CS2, GPIO_CS3};
const u32 gpio_busy[4] = {GPIO_BUSY0, GPIO_BUSY1, GPIO_BUSY2, GPIO_BUSY3};
#define MAX_BUSY_CNT      300

#define GPIO_BS              40   // PB8
#define HW1_BI_CHAN          7
#define HW1_BI_CS            12
#define HW1_GPIO_BI1         137   // PE9
#define HW1_GPIO_BI2         138   // PE10
#define HW1_GPIO_BI3         129   // PE1
#define HW1_GPIO_BI4         139   // PE11
#define HW1_GPIO_BI5         130   // PE2
#define HW1_GPIO_BI6         39    // PB7
#define HW1_GPIO_BI7         131   // PE3
#define HW1_BI_CS1           197   // PG5
#define HW1_BI_CS2           196   // PG4
#define HW1_BI_CS3           195   // PG3
#define HW1_BI_CS4           194   // PG2
#define HW1_BI_CS5           192   // PG0
#define HW1_BI_CS6           193   // PG1
#define HW1_BI_CS7           201   // PG9
#define HW1_BI_CS8           200   // PG8
#define HW1_BI_CS9           264   // PI8
#define HW1_BI_CS10          265   // PI9
#define HW1_BI_CS11          260   // PI4
#define HW1_BI_CS12          261   // PI5
const u32 hw1_gpio_bi[HW1_BI_CHAN] = {HW1_GPIO_BI1, HW1_GPIO_BI2, HW1_GPIO_BI3, HW1_GPIO_BI4, HW1_GPIO_BI5, HW1_GPIO_BI6, HW1_GPIO_BI7};
const u32 hw1_bi_cs[HW1_BI_CS] = {HW1_BI_CS1, HW1_BI_CS2, HW1_BI_CS3, HW1_BI_CS4, HW1_BI_CS5, HW1_BI_CS6, HW1_BI_CS7, HW1_BI_CS8, HW1_BI_CS9, HW1_BI_CS10, HW1_BI_CS11, HW1_BI_CS12};

#define HW2_BI_CHAN          8
#define HW2_BI_CS            7
#define HW2_GPIO_BI1         38    // PB6
#define HW2_GPIO_BI2         131   // PE3
#define HW2_GPIO_BI3         39   // PB7
#define HW2_GPIO_BI4         130   // PE2
#define HW2_GPIO_BI5         139   // PE11
#define HW2_GPIO_BI6         129    // PE1
#define HW2_GPIO_BI7         138   // PE10
#define HW2_GPIO_BI8         137   // PE9
#define HW2_BI_CS1           201   // PG9
#define HW2_BI_CS2           193   // PG1
#define HW2_BI_CS3           192   // PG0
#define HW2_BI_CS4           194   // PG2
#define HW2_BI_CS5           195   // PG3
#define HW2_BI_CS6           196   // PG4
#define HW2_BI_CS7           197   // PG5
const u32 hw2_gpio_bi[HW2_BI_CHAN] = {HW2_GPIO_BI1, HW2_GPIO_BI2, HW2_GPIO_BI3, HW2_GPIO_BI4, HW2_GPIO_BI5, HW2_GPIO_BI6, HW2_GPIO_BI7, HW2_GPIO_BI8};
const u32 hw2_bi_cs[HW2_BI_CS] = {HW2_BI_CS1, HW2_BI_CS2, HW2_BI_CS3, HW2_BI_CS4, HW2_BI_CS5, HW2_BI_CS6, HW2_BI_CS7};

struct rt_drvdata {
    struct class *class;
    struct spi_device *spi;

    // void __iomem *cnvt_base;
    void __iomem *flag_base;
    void __iomem *pwm_per;
    void __iomem *pwm_pcr;
    void __iomem *pwm_ppr;
    void __iomem *pwm_pcntr;

    struct hrtimer timer;
    // int pulse;
    // ktime_t timeout;

    // struct workqueue_struct *wq;
    // struct work_struct work;
    struct mutex mutex;

    struct spi_message spi_msg[4];
    struct spi_transfer spi_xfer[4];
    u32 cur_cs;
    u8 buf[4][32];

    struct Sample sample;
    struct Wave wave;
    struct FreqCycle freq0, freq1;

    // status
    u8 enable;
    u32 pwm_entire_cycle;
    u32 pwm_act_cycle;
    u32 pulse_miss_cnt;
    u32 pulse_all_cnt;
    u32 queue_failed_cnt;
    u32 spi_read_usec_min;
    u32 spi_read_usec_max;
    bool isMGC2;
};

// static struct rt_drvdata drvdata;
static struct class *rt_smp_class;

/*
struct rt_fdata {
    struct rt_drvdata *drvdata;
};
*/

static void flip_BS(void){
    static bool flip_flag = false;
    gpio_set_value(GPIO_BS, flip_flag);
    flip_flag = !flip_flag;
}

static void bi_worker(struct rt_drvdata *drvdata){
    u8 cs_num;
    u8 chan_num;
    if(drvdata->isMGC2){
        for(cs_num = 0; cs_num < HW2_BI_CS; cs_num++){
            gpio_set_value(hw2_bi_cs[cs_num], 0);
            for(chan_num = 0; chan_num < HW2_BI_CHAN; chan_num++){
                drvdata->sample.bi[cs_num * HW2_BI_CHAN + chan_num] = !gpio_get_value(hw2_gpio_bi[chan_num]);
            }
            gpio_set_value(hw2_bi_cs[cs_num], 1);
        }
    }else{
        for(cs_num = 0; cs_num < HW1_BI_CS; cs_num++){
            gpio_set_value(hw1_bi_cs[cs_num], 0);
            for(chan_num = 0; chan_num < HW1_BI_CHAN; chan_num++){
                drvdata->sample.bi[cs_num * HW1_BI_CHAN + chan_num] = !gpio_get_value(hw1_gpio_bi[chan_num]);
            }
            gpio_set_value(hw1_bi_cs[cs_num], 1);
        }
    }

    do_gettimeofday(&(drvdata->sample.tv));
}

static void spi_worker(void *_drvdata)
{
    // struct rt_drvdata *drvdata = container_of(work, struct rt_drvdata, work);
    struct rt_drvdata *drvdata = _drvdata;
    struct spi_device *spi = drvdata->spi;
	// struct spi_message msg;
    /*
	struct spi_transfer xfer = {
        .speed_hz = spi->max_speed_hz,
        .bits_per_word = 8,
    };
    */
    // u32 val = 0;
    // unsigned char buf[32];
    int i;
    u32 cs = drvdata->cur_cs;
    u32 st;
    u8 *buf;
    u32 cnt = 0;
    static ktime_t begin, end, ps;
    u32 usec;

    if (cs >= 4) {
        buf = &drvdata->buf[3][0];
        /*A0~A7通道*/
        st = 3 * 16;
        for (i = st; i < st + 8; ++i)
            drvdata->sample.ad[i] = (buf[(i - st) * 4] << 8) + buf[(i - st) * 4 + 1];
        /*B0~B7通道*/
        st = 3 * 16 + 8;
        for (i = st; i < st + 8; ++i)
            drvdata->sample.ad[i] = (buf[(i - st) * 4 + 2] << 8) + buf[(i - st) * 4 + 3];

        sample_run(&drvdata->sample);
        wave_run(&drvdata->wave);
        freq_run(&drvdata->freq0);
        freq_run(&drvdata->freq1);

        drvdata->cur_cs = 0;

        // gpio_set_value(GPIO_FLAG_NUM, 0);
        // val = readl(drvdata->flag_base);
        // val = (val & ~(1 << GPIO_FLAG_OFST));
        // writel(val, drvdata->flag_base);

        end = ktime_get();
        ps = ktime_sub(end, begin);
        usec = (u32)(ps.tv64 >> 10);
        if (usec < drvdata->spi_read_usec_min)
            drvdata->spi_read_usec_min = usec;
            
        if (usec > drvdata->spi_read_usec_max)
            drvdata->spi_read_usec_max = usec;
        
        return;

    } else if (cs > 0) {
        buf = &drvdata->buf[cs - 1][0];
        /*A0~A7通道*/
        st = (cs - 1) * 16;
        for (i = st; i < st + 8; ++i)
            drvdata->sample.ad[i] = (buf[(i - st) * 4] << 8) + buf[(i - st) * 4 + 1];
        /*B0~B7通道*/
        st = (cs - 1) * 16 + 8;
        for (i = st; i < st + 8; ++i)
            drvdata->sample.ad[i] = (buf[(i - st) * 4 + 2] << 8) + buf[(i - st) * 4 + 3];
    } else {
        begin = ktime_get();
    }

    /*BUSY信号变成高电平表示转换正在进行，从高电平变成低电平表示转换已完成*/
    while(gpio_get_value(gpio_busy[cs])){
        if(cnt++ > MAX_BUSY_CNT){
            pr_info("max busy cnt\n");
            break;
        }         
    }

    spi_message_init(&drvdata->spi_msg[cs]);
    drvdata->spi_msg[cs].complete = &spi_worker;
    drvdata->spi_msg[cs].context = drvdata;

    spi_message_add_tail(&drvdata->spi_xfer[cs], &drvdata->spi_msg[cs]);

    // spi->chip_select = 0;
    /*手动控制CS引脚电平*/
    gpio_set_value(gpio_cs[(cs+1)%4], 1);
    gpio_set_value(gpio_cs[(cs+2)%4], 1);
    gpio_set_value(gpio_cs[(cs+3)%4], 1);
    gpio_set_value(gpio_cs[cs], 0); 
    spi_async(spi, &drvdata->spi_msg[cs]);

    ++drvdata->cur_cs;

    flip_BS();
    bi_worker(drvdata);

    /*
    if (mutex_trylock(&drvdata->mutex) == 0) {
        ++drvdata->locked_cnt;
        return;
    }
    //pr_info("drvdata: %#x; timer: %#x\n", drvdata, &drvdata->timer);
    //pr_info("spi: %#x\n", drvdata->spi);
    spi_bus_lock(spi->master);

    for (i = 0; i < 4; ++i) {
        // xfer.cs_change = 1; // keep cs
        spi->chip_select = 0;
        if (spi_sync_locked(spi, &msg)) {
            spi_bus_unlock(spi->master);
            mutex_unlock(&drvdata->mutex);
            pr_err("spi_sync_locked failed\n");
            return;
        }
    }

    spi_bus_unlock(spi->master);
    mutex_unlock(&drvdata->mutex);
    */
}

//Timer Callback function. This will be called when timer expires
static enum hrtimer_restart timer_callback(struct hrtimer *timer)
{
    struct rt_drvdata *drvdata = container_of(timer, struct rt_drvdata, timer);
    u32 val, diff_cnt, diff_ns;
    //ktime_t now;

    //now = ktime_get();
    if (unlikely(drvdata->enable == 0)) {
        hrtimer_forward_now(timer, TIMEOUT_DEFAULT);
        return HRTIMER_RESTART;
    }

    ++drvdata->pulse_all_cnt;

    val = readl(drvdata->pwm_ppr);
    drvdata->pwm_entire_cycle = (val >> 16);
    drvdata->pwm_act_cycle = (val & 0xffff);

    val = readl(drvdata->pwm_pcntr);
    val &= 0xffff;
    diff_cnt = drvdata->pwm_entire_cycle - val;
    diff_ns = (diff_cnt << 5) + (diff_cnt << 3) + (diff_cnt << 1) + 5000;

    //hrtimer_forward(timer, now, ktime_set(0, diff));
    hrtimer_forward_now(timer, ktime_set(0, diff_ns));

    /*
    pr_info("pcnt: %d\n", val);
    pr_info("diff cnt: %d\n", diff_cnt);
    pr_info("diff tm: %d(ns)\n", diff_ns);
    */

    if (unlikely(drvdata->pwm_entire_cycle - val <= drvdata->pwm_act_cycle)) {
        ++drvdata->pulse_miss_cnt;
        return HRTIMER_RESTART;
    }

    // gpio_set_value(GPIO_FLAG_NUM, 1);
    // val = readl(drvdata->flag_base);
    // val = (val | (1 << GPIO_FLAG_OFST));
    // writel(val, drvdata->flag_base);

    // TODO: check BUSY GPIO

    /*
    if (!queue_work(drvdata->wq, &drvdata->work))
        ++drvdata->queue_failed_cnt;
    */
    if (drvdata->cur_cs != 0)
        ++drvdata->queue_failed_cnt;
    else
        //drvdata->cur_cs = 0;
        spi_worker(drvdata);
    
    return HRTIMER_RESTART;
}

static ssize_t status_show(struct device *dev,
                           struct device_attribute *attr, char *buf)
{
    struct Sample *sample = dev_get_drvdata(dev);
    struct rt_drvdata *drvdata = container_of(sample, struct rt_drvdata, sample);

    return sprintf(buf, "[rt_sample]\n"
                   "enable: %d\n"
                   "pwm_entire_cycle: %d\n"
                   "pwm_act_cycle: %d\n"
                   "err_pulse: %d/%d\n"
                   "queue_failed_cnt: %d\n"
                   "spi_read_usec_min: %u\n"
                   "spi_read_usec_max: %u\n"
                   "calcu_failed_cnt: %d\n",
                   drvdata->enable,
                   drvdata->pwm_entire_cycle,
                   drvdata->pwm_act_cycle,
                   drvdata->pulse_miss_cnt,
                   drvdata->pulse_all_cnt,
                   drvdata->queue_failed_cnt,
                   drvdata->spi_read_usec_min,
                   drvdata->spi_read_usec_max,
                   drvdata->sample.calcu_failed_cnt
        );
}

static DEVICE_ATTR(status, S_IRUSR, status_show, NULL);

static ssize_t enable_store(struct device *dev,
                            struct device_attribute *attr,
                            const char *buf, size_t count)
{
    struct Sample *sample = dev_get_drvdata(dev);
    struct rt_drvdata *drvdata = container_of(sample, struct rt_drvdata, sample);

	unsigned long cfg = 0;
    u32 val = 0;

	if (kstrtoul(buf, 10, &cfg))
		return -EINVAL;

    if (cfg == 1) {
        val = readl(drvdata->pwm_ppr);
        drvdata->pwm_entire_cycle = (val >> 16);
        drvdata->pwm_act_cycle = (val & 0xffff);

        drvdata->enable = 1;
        return count;
    } else if (cfg == 0) {
        drvdata->enable = 0;
        drvdata->pwm_entire_cycle = 0;
        drvdata->pwm_act_cycle = 0;
        drvdata->pulse_miss_cnt = 0;
        drvdata->pulse_all_cnt = 0;
        drvdata->sample.calcu_failed_cnt = 0;
        return count;
    }

    return -EINVAL;
}
static DEVICE_ATTR(enable, S_IWUSR, NULL, enable_store);

static struct attribute *attributes[] = {
    &dev_attr_status.attr,
    &dev_attr_enable.attr,
    NULL
};

static const struct attribute_group sample_attr_group = {
    .attrs = attributes,
};


static int rt_smp_drv_probe(struct spi_device *spi)
{
    struct device *dev = &spi->dev;
    struct rt_drvdata *drvdata;
    int i;

    pr_info("rt_smp_drv_probe\n");

    drvdata = kzalloc(sizeof(*drvdata), GFP_KERNEL);
    if (!drvdata) {
        dev_err(dev, "kzalloc failed\n");
        return -1;
    }

    dev_set_drvdata(dev, drvdata);
    drvdata->spi = spi;
    drvdata->class = rt_smp_class;

    if (sample_init(&drvdata->sample, &drvdata->wave, rt_smp_class) < 0) {
        dev_err(dev, "sample_init failed\n");
        goto r_alloc;
    }

    if (sysfs_create_group(&drvdata->sample.device->kobj, &sample_attr_group) < 0) {
        dev_err(dev, "sysfs_create_group failed\n");
        goto r_sample;
    }

    if (wave_init(&drvdata->wave, &drvdata->sample, rt_smp_class) < 0) {
        dev_err(dev, "wave_init failed\n");
        goto r_attr;
    }

    if (freq_init(&drvdata->freq0, "0", &drvdata->sample, rt_smp_class) < 0) {
        dev_err(dev, "freq_init failed\n");
        goto r_wave;
    }

    if (freq_init(&drvdata->freq1, "1", &drvdata->sample, rt_smp_class) < 0) {
        dev_err(dev, "freq_init failed\n");
        goto r_freq0;
    }

    /*
    if (!gpio_is_valid(GPIO_CNVT_NUM)) {
        pr_err("gpio not valid: %d\n", GPIO_CNVT_NUM);
        goto r_attr;
    }

    if (gpio_request(GPIO_CNVT_NUM, "cnvt") < 0){
        pr_err("goio_request failed: %d\n", GPIO_CNVT_NUM);
        goto r_attr;
    }

    gpio_direction_output(GPIO_CNVT_NUM, 0);
    */

    // if (!gpio_is_valid(GPIO_FLAG_NUM)) {
    //     dev_err(dev, "gpio not valid: %d\n", GPIO_FLAG_NUM);
    //     goto r_freq1;
    // }

    // if (gpio_request(GPIO_FLAG_NUM, "flag") < 0){
    //     dev_err(dev, "goio_request failed: %d\n", GPIO_FLAG_NUM);
    //     goto r_freq1;
    // }

    // gpio_direction_output(GPIO_FLAG_NUM, 0);

    for (i = 0; i < 4; ++i) {
        gpio_request(gpio_cs[i], NULL);
        gpio_request(gpio_busy[i], NULL);
        gpio_direction_output(gpio_cs[i], 1);
        gpio_direction_input(gpio_busy[i]);
    }
    
    /*复位引脚保持低电平40-500ns*/
    gpio_request(GPIO_RST, "rst");
    gpio_direction_output(GPIO_RST, 0);
    ndelay(500);
    gpio_set_value(GPIO_RST, 1);

    if(of_find_property(of_find_node_by_name(NULL, "gpio_para"), "gpio_pin_51", NULL) == NULL){
        drvdata->isMGC2 = false;
        printk("[rt_smp]: the hardware is MGC1.9");
    }else{
        drvdata->isMGC2 = true;
        printk("[rt_smp]: the hardware is MGC2.0");
    }

    if(drvdata->isMGC2){
        for (i = 0; i < HW2_BI_CHAN; ++i) {
            gpio_request(hw2_gpio_bi[i], NULL);
            gpio_direction_input(hw2_gpio_bi[i]);
        }
        for (i = 0; i < HW2_BI_CS; ++i) {
            gpio_request(hw2_bi_cs[i], NULL);
            gpio_direction_output(hw2_bi_cs[i], 1);
        }
    }else{
        for (i = 0; i < HW1_BI_CHAN; ++i) {
            gpio_request(hw1_gpio_bi[i], NULL);
            gpio_direction_input(hw1_gpio_bi[i]);
        }
        for (i = 0; i < HW1_BI_CS; ++i) {
            gpio_request(hw1_bi_cs[i], NULL);
            gpio_direction_output(hw1_bi_cs[i], 1);
        }
    }

    gpio_request(GPIO_BS, "bs");
    gpio_direction_output(GPIO_BS, 0);

    /*
    drvdata.cnvt_base = ioremap(GPIO_CNVT_ADDR, 4);
    if (drvdata.cnvt_base == NULL) {
        pr_err("ioremap failed: %#x\n", GPIO_CNVT_ADDR);
        goto r_gpio;
    }

    if (GPIO_FLAG_ADDR == GPIO_CNVT_ADDR)
        drvdata.flag_base = drvdata.cnvt_base;
    else {
        drvdata.flag_base = ioremap(GPIO_FLAG_ADDR, 4);
        if (drvdata.flag_base == NULL) {
            pr_err("ioremap failed: %#x\n", GPIO_FLAG_ADDR);
            goto r_iomap1;
        }
    }
    */

    // drvdata->flag_base = ioremap(GPIO_FLAG_ADDR, 4);
    // if (drvdata->flag_base == NULL) {
    //     dev_err(dev, "ioremap failed: %#x\n", GPIO_FLAG_ADDR);
    //     goto r_gpio;
    // }

    drvdata->pwm_per = ioremap(PWM_PER_ADDR, 4);
    if (drvdata->pwm_per == NULL) {
        dev_err(dev, "ioremap failed: %#x\n", PWM_PER_ADDR);
        goto r_iomap1;
    }

    drvdata->pwm_pcr = ioremap(PWM_PCR_ADDR, 4);
    if (drvdata->pwm_pcr == NULL) {
        dev_err(dev, "ioremap failed: %#x\n", PWM_PCR_ADDR);
        goto r_iomap2;
    }

    drvdata->pwm_ppr = ioremap(PWM_PPR_ADDR, 4);
    if (drvdata->pwm_ppr == NULL) {
        dev_err(dev, "ioremap failed: %#x\n", PWM_PPR_ADDR);
        goto r_iomap3;
    }

    drvdata->pwm_pcntr = ioremap(PWM_PCNTR_ADDR, 4);
    if (drvdata->pwm_pcntr == NULL) {
        dev_err(dev, "ioremap failed: %#x\n", PWM_PCNTR_ADDR);
        goto r_iomap4;
    }

    // TODO: manually init drvdata
    // drvdata.pulse = 0;
    // drvdata.timeout = ktime_set(0, TIMEOUT_WAIT);
    // drvdata.timeout = TIMEOUT_DEFAULT;
    // drvdata.status = 123;
    drvdata->enable = 0;
    drvdata->pwm_entire_cycle = 0;
    drvdata->pwm_act_cycle = 0;
    drvdata->pulse_miss_cnt = 0;
    drvdata->pulse_all_cnt = 0;
    drvdata->queue_failed_cnt = 0;
    drvdata->spi_read_usec_min = 0xffffffff;
    drvdata->spi_read_usec_max = 0;

    for (i = 0; i < 4; ++i) {
        spi_message_init(&drvdata->spi_msg[i]);
        memset(&drvdata->spi_xfer[i], 0, sizeof(drvdata->spi_xfer[0]));
        drvdata->spi_xfer[i].len = 32;
        drvdata->spi_xfer[i].speed_hz = spi->max_speed_hz;
        drvdata->spi_xfer[i].bits_per_word = 8;
        drvdata->spi_xfer[i].rx_buf = &drvdata->buf[i][0];
    }

    /*
    drvdata->wq = alloc_workqueue("%s", WQ_UNBOUND | WQ_HIGHPRI, 1, "rt_sample");
    if (drvdata->wq == NULL) {
        dev_err(dev, "allock_workqueue failed\n");
        goto r_iomap5;
    }

    INIT_WORK(&drvdata->work, worker_callback);
    */
    mutex_init(&drvdata->mutex);

    hrtimer_init(&drvdata->timer, CLOCK_MONOTONIC, HRTIMER_MODE_REL);
    drvdata->timer.function = &timer_callback;
    hrtimer_start(&drvdata->timer, TIMEOUT_DEFAULT, HRTIMER_MODE_REL);

    return 0;

    hrtimer_cancel(&drvdata->timer);
r_iomap5:
    iounmap(drvdata->pwm_pcntr);
r_iomap4:
    iounmap(drvdata->pwm_ppr);
r_iomap3:
    iounmap(drvdata->pwm_pcr);
r_iomap2:
    iounmap(drvdata->pwm_per);
r_iomap1:
    iounmap(drvdata->flag_base);
/*
r_iomap1:
    iounmap(drvdata.cnvt_base);
*/
r_gpio:
    // gpio_free(GPIO_FLAG_NUM);
/*
  r_gpio:
  gpio_free(GPIO_CNVT_NUM);
*/
r_freq1:
    freq_exit(&drvdata->freq1);
r_freq0:
    freq_exit(&drvdata->freq0);
r_wave:
    wave_exit(&drvdata->wave);
r_attr:
    sysfs_remove_group(&drvdata->sample.device->kobj, &sample_attr_group);
r_sample:
    sample_exit(&drvdata->sample);
r_alloc:
	kfree(drvdata);

    return -1;
}

static int rt_smp_drv_remove(struct spi_device *spi)
{
    struct device *dev = &spi->dev;
    struct rt_drvdata *drvdata = dev_get_drvdata(dev);

	if (!drvdata)
		return -ENODEV;

    //stop the timer
    hrtimer_cancel(&drvdata->timer);
    iounmap(drvdata->pwm_pcntr);
    iounmap(drvdata->pwm_ppr);
    iounmap(drvdata->pwm_pcr);
    iounmap(drvdata->pwm_per);
    iounmap(drvdata->flag_base);
    //iounmap(drvdata.cnvt_base);
    // gpio_free(GPIO_FLAG_NUM);
    //gpio_free(GPIO_CNVT_NUM);
    freq_exit(&drvdata->freq1);
    freq_exit(&drvdata->freq0);
    wave_exit(&drvdata->wave);
    sysfs_remove_group(&drvdata->sample.device->kobj, &sample_attr_group);
    sample_exit(&drvdata->sample);
	kfree(drvdata);

    pr_info("Rt_SMP Driver Remove Done\n");
    return 0;
}

static const struct of_device_id rt_smp_of_match[] = {
	{ .compatible = "deri,rt_smp" },
	{},
};
MODULE_DEVICE_TABLE(of, rt_smp_of_match);

static struct spi_driver rt_smp_driver = {
	.driver = {
		.name = "rt_smp",
		.owner = THIS_MODULE,
		.of_match_table = of_match_ptr(rt_smp_of_match),
	},
	.probe = rt_smp_drv_probe,
	.remove = rt_smp_drv_remove,
};

static int __init rt_driver_init(void)
{
    int ret = -1;

    rt_smp_class = class_create(THIS_MODULE, DRIVER_NAME);
    if (rt_smp_class == NULL) {
        pr_err("class_create failed\n");
        return -1;
    }

    ret = spi_register_driver(&rt_smp_driver);

    pr_info("Rt_SMP Module Init: %d\n", ret);
    return ret;
}

static void __exit rt_driver_exit(void)
{
	spi_unregister_driver(&rt_smp_driver);
    class_destroy(rt_smp_class);

    pr_info("Rt_SMP Module Exit\n");
}

module_init(rt_driver_init);
module_exit(rt_driver_exit);


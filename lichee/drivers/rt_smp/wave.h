/*******************************************************************************
* COPYRIGHT (c) DERI. nanjing, china
*
*        FILE NAME: wave.h
*
* FILE DESCRIPTION: realtime wave header
*
* --DATE--    NAME        REVISION HISTORY
* 20180525    liuchenxin  Original Issue
* 20211119    liuchenxin  for A40I Linux SMP
*******************************************************************************/

#pragma once

#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/module.h>
#include <linux/kdev_t.h>
#include <linux/fs.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/hrtimer.h>

#include "sample_conf.h"
#include "sample.h"

#define RT_WAVE_AUX 2

#define RT_WAVE_ERR_AVA 0x01
#define RT_WAVE_ERR_REC 0x02
#define RT_WAVE_ERR_WRT 0x04
#define RT_WAVE_ERR_WAT 0x08

#define RT_CELL_STAT_NORMAL 0x0000  // chan available
#define RT_CELL_STAT_REC_RQ 0x0001  // record request, set by host
#define RT_CELL_STAT_REC_BEF_OK 0x0002  // record started, set by worker
#define RT_CELL_STAT_REC_AFT_OK 0x0004  // record ended, set by worker

#define RT_CELL_STAT_WRT_RQ 0x0100  // write request, set by host
#define RT_CELL_STAT_WRT_ST 0x0200  // write started, set by writer
// #define RT_CELL_STAT_WRT_OK 0x0400  // write ended, set by writer

#define RT_CELL_STAT_REC_MASK 0x00ff
#define RT_CELL_STAT_WRT_MASK 0xff00
#define RT_CELL_STAT_WRT_SHIFT 8

#define RT_WAVE_STAT_NORMAL 0
#define RT_WAVE_STAT_START  1
//#define RT_WAVE_STAT_UNINIT 0x5aa5

// moved points num every task period
#define RT_WAVE_WORKER_POINTS ((RT_SAMP_POINTS_PER_CYC) / 4)

struct Wave_Data {
    struct timespec trig_time;

    // wave data buffers
    int16_t buf_ad[RT_SAMP_AD_CHANS][RT_WAVE_POINTS];
    // float buf_ai[RT_SAMP_AI_CHANS][RT_WAVE_POINTS];
    uint8_t buf_bi[RT_SAMP_BI_CHANS][RT_WAVE_POINTS];
    //uint16_t buf_freq[RT_WAVE_POINTS];
};

struct Wave_Cell {
    // output
    uint32_t status;

    int begin_samp_cnt;
    int trig_samp_cnt;
    int remain_samp_cnt;

    struct Wave_Data data;
};

typedef int16_t (* Wave_Buffer)[RT_WAVE_POINTS];

struct Wave {
    dev_t devt;
    struct cdev cdev;
    struct device *device;
    struct class *class;

    struct mutex m_open;
    wait_queue_head_t wq_read;

    // symbol
    //drp::Symbol<int> max_trigger_num;

    // input
    struct Sample* sample;
    uint8_t trigger;
    //drp::SignalInArray<float, RT_SAMP_AI_CHANS> ai;
    //int32_t write_ch_done;

    // output
    //int32_t write_ch_ready;
    uint32_t status;             // managed by demon

    int cell_rec;                // recording cell (only one recording routine)
    //int cell_wrt;                // writing cell (multiple writing requests)

    int start_last;

    // wave cells
    struct Wave_Cell cell[RT_WAVE_AUX];

    uint32_t err;
    // int trigger_num;
    // int ai_num;
    // int bi_num;

};

int wave_init(struct Wave* self, struct Sample* sample, struct class *class);
void wave_exit(struct Wave* self);
void wave_run(struct Wave*);




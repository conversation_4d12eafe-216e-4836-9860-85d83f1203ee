/*******************************************************************************
* COPYRIGHT (c) DERI. nanjing, china
*
*        FILE NAME: freq_prot.c
*
* FILE DESCRIPTION: realtime frequency calculation
*
* --DATE--    NAME        REVISION HISTORY
* 20180625    liuchenxin  Original Issue
* 20211206    liuchenxin  for A40I Linux SMP
*******************************************************************************/

#include "freq.h"

static int freq_open(struct inode *inode, struct file *filp)
{
    struct FreqCycle *freq = container_of(inode->i_cdev, struct FreqCycle, cdev);

    if (mutex_trylock(&freq->m_open) == 0)
        return -EBUSY;

    filp->private_data = freq;

    return 0;
}

static int freq_release(struct inode *inode, struct file *filp)
{
    struct FreqCycle *freq = filp->private_data;

    mutex_unlock(&freq->m_open);
    return 0;
}

static ssize_t freq_read(struct file *filp,
                         char __user *buf, size_t len, loff_t *off)
{
    struct FreqCycle *freq = filp->private_data;

    if (len != sizeof(freq->cycle))
        return -EINVAL;

    if (copy_to_user(buf, &freq->cycle, sizeof(freq->cycle)))
        return -EFAULT;

    return sizeof(freq->cycle);
}

static struct file_operations fops = {
    .owner          = THIS_MODULE,
    .read           = freq_read,
    .open           = freq_open,
    .release        = freq_release,
};

static ssize_t status_show(struct device *dev,
                           struct device_attribute *attr, char *buf)
{
    struct FreqCycle *freq = dev_get_drvdata(dev);

    return sprintf(buf, "[rt_freq]\n"
                   "cycle: %d ns\n"
                   "rating: %d Hz\n"
                   "interval: %d ns\n"
                   "chanA: %d\n"
                   "chanB: %d\n"
                   "cycle_err: %#x\n",
                   freq->cycle,
                   freq->rating,
                   freq->ns_interval,
                   freq->chanA,
                   freq->chanB,
                   freq->cycle_err
        );
}

static DEVICE_ATTR(status, S_IRUSR, status_show, NULL);

static ssize_t rating_store(struct device *dev,
                            struct device_attribute *attr,
                            const char *buf, size_t count)
{
    struct FreqCycle *freq = dev_get_drvdata(dev);

	unsigned long cfg = 0;

	if (kstrtoul(buf, 10, &cfg))
		return -EINVAL;

    if (cfg != 50 && cfg != 60)
        return -EINVAL;

    freq->rating = cfg;
    freq->ns_rating = NSEC_PER_SEC / 2 / freq->rating;
    freq->ns_up = NSEC_PER_SEC / 2 / (freq->rating - 10);
    freq->ns_down = NSEC_PER_SEC / 2 / (freq->rating + 10);
    // freq->ns_interval = NSEC_PER_SEC / freq->rating / RT_SAMP_POINTS_PER_CYC;

    return count;
}
static DEVICE_ATTR(rating, S_IWUSR, NULL, rating_store);

static ssize_t interval_store(struct device *dev,
                                struct device_attribute *attr,
                                const char *buf, size_t count)
{
    struct FreqCycle *freq = dev_get_drvdata(dev);

	unsigned long cfg = 0;

	if (kstrtoul(buf, 10, &cfg))
		return -EINVAL;

    freq->ns_interval = cfg;

    return count;
}
static DEVICE_ATTR(interval, S_IWUSR, NULL, interval_store);

static ssize_t chanA_store(struct device *dev,
                           struct device_attribute *attr,
                           const char *buf, size_t count)
{
    struct FreqCycle *freq = dev_get_drvdata(dev);

	unsigned long cfg = 0;

	if (kstrtoul(buf, 10, &cfg))
		return -EINVAL;

    freq->chanA = cfg;
    freq->chan_Ua = &freq->sample->flex->buf_ad[freq->chanA][0];

    return count;
}
static DEVICE_ATTR(chanA, S_IWUSR, NULL, chanA_store);

static ssize_t chanB_store(struct device *dev,
                           struct device_attribute *attr,
                           const char *buf, size_t count)
{
    struct FreqCycle *freq = dev_get_drvdata(dev);

	unsigned long cfg = 0;

	if (kstrtoul(buf, 10, &cfg))
		return -EINVAL;

    freq->chanB = cfg;
    freq->chan_Ub = &freq->sample->flex->buf_ad[freq->chanB][0];

    return count;
}
static DEVICE_ATTR(chanB, S_IWUSR, NULL, chanB_store);

static struct attribute *attributes[] = {
    &dev_attr_status.attr,
    &dev_attr_rating.attr,
    &dev_attr_interval.attr,
    &dev_attr_chanA.attr,
    &dev_attr_chanB.attr,
    NULL
};

static const struct attribute_group freq_attr_group = {
    .attrs = attributes,
};

int freq_init(struct FreqCycle* self, const char* n, struct Sample* sample, struct class *class)
{
    self->class = class;

    if ((alloc_chrdev_region(&self->devt, 0, 1, "rt_freq")) < 0) {
        pr_err("freq alloc_chrdev_region failed\n");
        return -1;
    }
    pr_info("rt_freq: major=%d, minor=%d\n", MAJOR(self->devt), MINOR(self->devt));

    cdev_init(&self->cdev, &fops);

    if ((cdev_add(&self->cdev, self->devt, 1)) < 0) {
        pr_err("freq cdev_add failed\n");
        goto r_chrdev;
    }

    self->device = device_create(class, NULL, self->devt, self, "freq%s", n);
    if (self->device == NULL) {
        pr_err("freq device_create failed\n");
        goto r_cdev;
    }

    if (sysfs_create_group(&self->device->kobj, &freq_attr_group) < 0) {
        pr_err("freq sysfs_create_group failed\n");
        goto r_device;
    }

    mutex_init(&self->m_open);

    self->sample = sample;
    self->sample_cnt = &sample->flex->cnt;
    self->chanA = 0;
    self->chanB = 1;
    self->chan_Ua = &sample->flex->buf_ad[self->chanA][0];
    self->chan_Ub = &sample->flex->buf_ad[self->chanB][0];

    self->rating = 50;
    self->ns_rating = NSEC_PER_SEC / 2 / self->rating;
    self->ns_up = NSEC_PER_SEC / 2 / (self->rating - 10);
    self->ns_down = NSEC_PER_SEC / 2 / (self->rating + 10);
    self->ns_interval = NSEC_PER_SEC / self->rating / RT_SAMP_POINTS_PER_CYC;

    // self->t_last = ktime_set(0, 0);

    self->ad_Uab_last = 0;
    self->ad_Uab_now = 0;
    self->ad_ready = 0;

    // self->cycle_cnt = 0;
    // self->t_last = 0;
    self->dt = 0;

    for (int i = 0; i < FREQ_CYCLE_AVE_NUM; ++i)
        self->cycle_buff[i] = self->rating;
    self->cycle_buff_cnt = 0;

    self->cycle_now = 0;
    self->cycle_last = 0;
    self->cycle_ava = 0;
    self->cycle_ava_last = 0;
    self->cycle = 0;

    self->cycle_err = 1;
    self->ready = 0;
    self->ready_cnt = 0;
    self->cycle_overflow = FREQ_CYCLE_AVE_NUM;
    self->cycle_jump = 0;

    return 0;

r_attr:
    sysfs_remove_group(&self->device->kobj, &freq_attr_group);
r_device:
    device_destroy(self->class, self->devt);
r_cdev:
    cdev_del(&self->cdev);
r_chrdev:
    unregister_chrdev_region(self->devt, 1);

    return -1;
}
EXPORT_SYMBOL(freq_init);


void freq_exit(struct FreqCycle* self)
{
    sysfs_remove_group(&self->device->kobj, &freq_attr_group);
    device_destroy(self->class, self->devt);
    cdev_del(&self->cdev);
    unregister_chrdev_region(self->devt, 1);
}
EXPORT_SYMBOL(freq_exit);

void freq_run(struct FreqCycle* self)
{
    // uint64_t ts;
    // uint32_t tt;

    // XTime_GetTime(&ts);

    // ktime_t now = ktime_get();
    int ad_cnt = *self->sample_cnt;
    int16_t ad_Ua = self->chan_Ua[ad_cnt];
    int16_t ad_Ub = self->chan_Ub[ad_cnt];
    int32_t abs_val;
    int32_t Uab_last;
    int32_t Uab_now;
    uint32_t dt_adder;
    uint32_t tmp;

    // uint32_t ns_ps = ktime_to_ns(ktime_sub(*ts, self->t_last));
    // uint32_t ns_ps = ktime_to_ns(ktime_sub(now, self->t_last));

    self->ad_Uab_last = self->ad_Uab_now;
    self->ad_Uab_now = (int32_t)ad_Ua - (int32_t)ad_Ub;

    abs_val = (self->ad_Uab_now > 0) ? self->ad_Uab_now : -self->ad_Uab_now;
    if (abs_val > K_UAB_THRESHOLD_VALUE)
        self->ad_ready = RT_SAMP_POINTS_PER_CYC;
    else if (self->ad_ready > 0)
        --self->ad_ready;

    Uab_last = self->ad_Uab_last;
    Uab_now = self->ad_Uab_now;

    if (self->ad_ready) {
        if (self->ready_cnt >= FREQ_CYCLE_READY_DELAY_NUM)
            self->ready = 1;
        else
            ++self->ready_cnt;

        // ++self->cycle_cnt;
        // tt = (uint32_t)(ts - t_last);
        // t_last = ts;
        // dt += tt;

        self->dt += self->ns_interval;
        // self->dt += ns_ps;

        if ((Uab_last > 0 && Uab_now <= 0) || (Uab_last < 0 && Uab_now >= 0)) {

            dt_adder = 0;
            if (Uab_last > 0 && Uab_now <= 0) {
                if (Uab_now < 0) {
                    tmp = self->ns_interval * (-Uab_now) / (Uab_last - Uab_now);
                    // tmp = ns_ps * (-Uab_now) / (Uab_last - Uab_now);
                    self->dt -= tmp;
                    dt_adder = tmp;
                }
            } else if (Uab_last < 0 && Uab_now >= 0) {
                if (Uab_now > 0) {
                    tmp = self->ns_interval * Uab_now / (Uab_now - Uab_last);
                    // tmp = ns_ps * Uab_now / (Uab_now - Uab_last);
                    self->dt -= tmp;
                    dt_adder = tmp;
                }
            }

            if (self->dt < self->ns_down || self->dt > self->ns_up) {
                if (self->cycle_overflow <= FREQ_CYCLE_AVE_NUM)
                    ++self->cycle_overflow;
            } else {
                self->cycle_overflow = 0;
                //freq_now = TICKS_1HZ / (float)dt;
                self->cycle_now = self->dt;

                self->cycle_buff[self->cycle_buff_cnt++] = self->cycle_now;
                self->cycle_jump = 0;

                self->cycle_buff_cnt = self->cycle_buff_cnt % FREQ_CYCLE_AVE_NUM;

                // calculate freq
                tmp = 0;
                for (int i = 0; i < FREQ_CYCLE_AVE_NUM; ++i)
                    tmp += self->cycle_buff[i];
                self->cycle_ava_last = self->cycle_ava;
                self->cycle_ava = tmp / FREQ_CYCLE_AVE_NUM;
                self->cycle = self->cycle_ava;
                // freq.write();
            }

            if (self->cycle_overflow > FREQ_CYCLE_AVE_NUM || self->ready != 1) {
                self->cycle_err = 1;
                for (int i = 0; i < FREQ_CYCLE_AVE_NUM; ++i)
                    self->cycle_buff[i] = self->rating;
                self->cycle_buff_cnt = 0;
                self->cycle = 0.0;
                // freq.write();

                self->dt = 0;
            } else {
                self->cycle_err = 0;
                
                if (Uab_now != 0)
                    self->dt = dt_adder;
                else
                    self->dt = 0;
            }

        }
    } else {
        self->dt = 0;
        self->cycle_ava = 0;
        self->cycle_ava_last = 0;
        self->cycle = 0;
        // freq.write();
        self->cycle_now = 0;
        self->cycle_last = 00;

        for (int i = 0; i < FREQ_CYCLE_AVE_NUM; ++i)
            self->cycle_buff[i] = self->rating;
        self->cycle_buff_cnt = 0;

        self->ready = 0;
        self->ready_cnt = 0;
        self->cycle_overflow = FREQ_CYCLE_AVE_NUM;
        self->cycle_jump = 0;
        self->cycle_err = 1;
    }

    // self->t_last = *ts;
    // self->t_last = now;
}
EXPORT_SYMBOL(freq_run);


#include "stdio.h"
#include "unistd.h"
#include "fcntl.h"


int main(int argc, char *argv[])
{
	int fd;
	unsigned int databuf[2];
	float temp, humi;
	int ret = 0;

	fd = open("/dev/dht20", O_RDWR);
	if(fd < 0) {
		printf("can't open /dev/dht20\r\n");
		return -1;
	}

	while (1) {
		ret = read(fd, databuf, sizeof(databuf));
		if(ret == 0) { 			
			humi = databuf[0]; 	
            temp = databuf[1];
			printf("humi = %f, temp = %f\r\n", humi, temp);
		}
		sleep(2);
	}
	close(fd);	/* 关闭文件 */	
	return 0;
}


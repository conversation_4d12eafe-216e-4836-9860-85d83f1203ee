
#include <linux/kernel.h>
#include <linux/delay.h>
#include <linux/ide.h>
#include <linux/init.h>
#include <linux/module.h>
#include <linux/errno.h>
#include <linux/cdev.h>
#include <linux/i2c.h>

#define DHT20_CNT	1
#define DHT20_NAME	"dht20"
#define DHT20_ADDR  0X38	/* DHT20器件地址  */

struct dht20_dev {
	dev_t devid;			/* 设备号 	 */
	struct cdev cdev;		/* cdev 	*/
	struct class *class;	/* 类 		*/
	struct device *device;	/* 设备 	 */
	struct device_node	*nd; /* 设备节点 */
	int major;			/* 主设备号 */
	void *private_data;	/* 私有数据 */
	int temp, humi;		/* 温度、湿度数据 */
};

static struct dht20_dev dht20dev;


static int dht20_read_regs(struct dht20_dev *dev, void *buf, int len)
{
	struct i2c_msg msg;
	struct i2c_client *client = (struct i2c_client *)dev->private_data;

	msg.addr = client->addr;		/* dht20地址 */
	msg.flags = 1;			        /* 标记为读取数据*/
	msg.buf = buf;					/* 读取数据缓冲区 */
	msg.len = len;					/* 要读取的数据长度*/

	return i2c_transfer(client->adapter, &msg, 1);
}


static int dht20_write_regs(struct dht20_dev *dev, u8 *buf, u8 len)
{
	struct i2c_msg msg;
	struct i2c_client *client = (struct i2c_client *)dev->private_data;
		
	msg.addr = client->addr;	/* dht20地址 */
	msg.flags = 0;				/* 标记为写数据 */
	msg.buf = buf;				/* 要写入的数据缓冲区 */
	msg.len = len;			    /* 要写入的数据长度 */

	return i2c_transfer(client->adapter, &msg, 1);
}

uint8_t dht20_read_status(struct dht20_dev *dev)
{
    unsigned char writebuf[1] = {0x71};
    unsigned char readbuf[1];
    dht20_write_regs(dev, writebuf, 1);
    dht20_read_regs(dev, readbuf, 1);
    return readbuf[0];
}

void dht20_readdata(struct dht20_dev *dev)
{
    unsigned char writebuf[3] = {0xAC, 0x33, 0x00};
    unsigned char readbuf[6];
	dht20_write_regs(dev, writebuf, 3);
    msleep(80);
    while((dht20_read_status(dev)&0x80)==0x80){
        msleep(1);
    }

    dht20_read_regs(dev, readbuf, 6);
    dev->humi = ((((readbuf[1] << 16) + (readbuf[2] << 8) + readbuf[3]) >> 4) * 100) >> 20;
    dev->temp = (((((readbuf[3] << 16) + (readbuf[4] << 8) + readbuf[5]) & 0x0fffff) * 200) >> 20) - 50;	
}

static int dht20_open(struct inode *inode, struct file *filp)
{
	filp->private_data = &dht20dev;
	return 0;
}

static ssize_t dht20_read(struct file *filp, char __user *buf, size_t cnt, loff_t *off)
{
	int data[2];
	long err = 0;

	struct dht20_dev *dev = (struct dht20_dev *)filp->private_data;
	
	dht20_readdata(dev);

	data[0] = dev->humi;
	data[1] = dev->temp;
	err = copy_to_user(buf, data, sizeof(data));
	return 0;
}

static int dht20_release(struct inode *inode, struct file *filp)
{
	return 0;
}

/* dht20操作函数 */
static const struct file_operations dht20_ops = {
	.owner = THIS_MODULE,
	.open = dht20_open,
	.read = dht20_read,
	.release = dht20_release,
};

static ssize_t humi_show(struct device *dev, struct device_attribute *attr, char *buf)
{
    dht20_readdata(&dht20dev);
    return sprintf(buf, "humidty: %d\n", dht20dev.humi);
}
static DEVICE_ATTR(humi, S_IRUSR, humi_show, NULL);

static ssize_t temp_show(struct device *dev, struct device_attribute *attr, char *buf)
{
    dht20_readdata(&dht20dev);
    return sprintf(buf, "temperature: %d\n", dht20dev.temp);
}
static DEVICE_ATTR(temp, S_IRUSR, temp_show, NULL);

static struct attribute *attributes[] = {
    &dev_attr_humi.attr,
    &dev_attr_temp.attr,
    NULL
};

static const struct attribute_group dht20_attr_group = {
    .attrs = attributes,
};

static int dht20_probe(struct i2c_client *client, const struct i2c_device_id *id)
{
	/* 1、构建设备号 */
	if (dht20dev.major) {
		dht20dev.devid = MKDEV(dht20dev.major, 0);
		register_chrdev_region(dht20dev.devid, DHT20_CNT, DHT20_NAME);
	} else {
		alloc_chrdev_region(&dht20dev.devid, 0, DHT20_CNT, DHT20_NAME);
		dht20dev.major = MAJOR(dht20dev.devid);
	}

	/* 2、注册设备 */
	cdev_init(&dht20dev.cdev, &dht20_ops);
	cdev_add(&dht20dev.cdev, dht20dev.devid, DHT20_CNT);

	/* 3、创建类 */
	dht20dev.class = class_create(THIS_MODULE, DHT20_NAME);
	if (IS_ERR(dht20dev.class)) {
		return PTR_ERR(dht20dev.class);
	}

	/* 4、创建设备 */
	dht20dev.device = device_create(dht20dev.class, NULL, dht20dev.devid, NULL, DHT20_NAME);
	if (IS_ERR(dht20dev.device)) {
		return PTR_ERR(dht20dev.device);
	}

    if (sysfs_create_group(&dht20dev.device->kobj, &dht20_attr_group) < 0) {
        pr_err("dht20 sysfs_create_group failed\n");
        return -1;
    }

	dht20dev.private_data = client;

    msleep(500);

	return 0;
}

static int dht20_remove(struct i2c_client *client)
{
	/* 删除设备 */
	cdev_del(&dht20dev.cdev);
	unregister_chrdev_region(dht20dev.devid, DHT20_CNT);

    sysfs_remove_group(&dht20dev.device->kobj, &dht20_attr_group);

	/* 注销掉类和设备 */
	device_destroy(dht20dev.class, dht20dev.devid);
	class_destroy(dht20dev.class);
	return 0;
}

/* 传统匹配方式ID列表 */
static const struct i2c_device_id dht20_id[] = {
	{"deri,dht20", 0},  
	{}
};

/* 设备树匹配列表 */
static const struct of_device_id dht20_of_match[] = {
	{ .compatible = "deri,dht20" },
	{ /* Sentinel */ }
};

/* i2c驱动结构体 */	
static struct i2c_driver dht20_driver = {
	.probe = dht20_probe,
	.remove = dht20_remove,
	.driver = {
			.owner = THIS_MODULE,
		   	.name = "dht20",
		   	.of_match_table = dht20_of_match, 
		   },
	.id_table = dht20_id,
};
		   
static int __init dht20_init(void)
{
	int ret = 0;

	ret = i2c_add_driver(&dht20_driver);
	return ret;
}

static void __exit dht20_exit(void)
{
	i2c_del_driver(&dht20_driver);
}

module_init(dht20_init);
module_exit(dht20_exit);
MODULE_LICENSE("GPL");
MODULE_AUTHOR("xufei");




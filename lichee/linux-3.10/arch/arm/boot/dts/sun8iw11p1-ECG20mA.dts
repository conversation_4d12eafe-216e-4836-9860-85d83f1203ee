/*
 * Allwinner Technology CO., Ltd. sun8iw11p1 soc board.
 *
 */

/dts-v1/;

#include "sun8iw11p1.dtsi"
#include "IMGCB01.dtsi"
/{

	soc@01c00000 {
	};

    clocks {
        mcp251x_clock:mcp251x_clock{
            compatible = "fixed-clock";
            #clock-cells = <0>;
            clock-frequency = <8000000>;
        };
    };        
};

&spi0 {
    status = "okay";
    mcp2515_1:mcp2515@0 {
        #address-cells=<1>;
        #size-cells=<1>;
        compatible = "microchip,mcp2515";
        pinctrl-names = "default";
        reg=<0>;
        spi-max-frequency = <2000000>;
        clocks = <&mcp251x_clock>;
        interrupt-parent = <&pio>;
        interrupts = <27 0>;
    };
};

&spi2 {
    status = "okay";
    mcp2515_2:mcp2515@0 {
        #address-cells=<1>;
        #size-cells=<1>;
        compatible = "microchip,mcp2515";
        pinctrl-names = "default";
        reg=<0>;
        spi-max-frequency = <2000000>;
        clocks = <&mcp251x_clock>;
        interrupt-parent = <&pio>;
        interrupts = <22 0>;
    };
};

&spi1 {
    status = "okay";
    ad7091r2:ad7091r2@0 {
        #address-cells=<1>;
        #size-cells=<1>;
        compatible = "ecg,ad7091r2";
        pinctrl-names = "default";
        reg=<0>;
        spi-max-frequency = <2000000>;
        convst-gpio = <&pio PE 2 1 3 1 0>; 
    };
};

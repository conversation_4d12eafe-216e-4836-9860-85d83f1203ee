/*
 * Allwinner Technology CO., Ltd. sun8iw11p1 soc board.
 *
 */

/dts-v1/;

#include "sun8iw11p1.dtsi"
#include "ECGA03.dtsi"
/{

	soc@01c00000 {
        can@0x0{
 			#address-cells = <1>;
 			#size-cells = <0>;
 			compatible = "allwinner,sunxi-can";
 			device_type = "can0";
 			can-pin = <0>; /* 0->PA16 PA17 1->PH20 PH21 */
 			status = "okay";
 		};
 
	};
    clocks {
        mcp251x_clock:mcp251x_clock{
            compatible = "fixed-clock";
            #clock-cells = <0>;
            clock-frequency = <8000000>;
        };
    };
};

&spi1 {
    status = "okay";
    mcp2515:mcp2515@0 {
        #address-cells=<1>;
        #size-cells=<1>;
        compatible = "microchip,mcp2515";
        pinctrl-names = "default";
        reg=<0>;
        spi-max-frequency = <2000000>;
        clocks = <&mcp251x_clock>;
        interrupt-parent = <&pio>;
        interrupts = <24 0>;
    };
};

/*
 * Allwinner Technology CO., Ltd. sun8iw11p1 soc board.
 *
 */

/dts-v1/;

#include "sun8iw11p1.dtsi"
#include "MGCA02.dtsi"
/{
    clocks {
        mcp251x_clock:mcp251x_clock{
            compatible = "fixed-clock";
            #clock-cells = <0>;
            clock-frequency = <8000000>;
        };
    };
};

&spi0 {
    status = "okay";
    mcp2515_1:mcp2515@0 {
        #address-cells=<1>;
        #size-cells=<1>;
        compatible = "microchip,mcp2515";
        pinctrl-names = "default";
        reg=<0>;
        spi-max-frequency = <2000000>;
        clocks = <&mcp251x_clock>;
        interrupt-parent = <&pio>;
        interrupts = <27 0>;
    };
    mcp2515_2:mcp2515@1 {
        #address-cells=<1>;
        #size-cells=<1>;
        compatible = "microchip,mcp2515";
        pinctrl-names = "default";
        reg=<1>;
        spi-max-frequency = <2000000>;
        clocks = <&mcp251x_clock>;
        interrupt-parent = <&pio>;
        interrupts = <1 0>;
    };
};

&spi2 {
    status = "okay";
    mcp2515_3:mcp2515@0 {
        #address-cells=<1>;
        #size-cells=<1>;
        compatible = "microchip,mcp2515";
        pinctrl-names = "default";
        reg=<0>;
        spi-max-frequency = <2000000>;
        clocks = <&mcp251x_clock>;
        interrupt-parent = <&pio>;
        interrupts = <22 0>;
    };
    mcp2515_4:mcp2515@1 {
        #address-cells=<1>;
        #size-cells=<1>;
        compatible = "microchip,mcp2515";
        pinctrl-names = "default";
        reg=<1>;
        spi-max-frequency = <2000000>;
        clocks = <&mcp251x_clock>;
        interrupt-parent = <&pio>;
        interrupts = <12 0>;
    };
};

&spi1 {
	status = "okay";
    rt_smp:rt_smp@0 {
        #address-cells=<1>;
        #size-cells=<1>;
        compatible = "deri,rt_smp";
        pinctrl-names = "default";
        reg=<0>;
        spi-max-frequency = <25000000>;
    };
};

&pwm {
	pwm-number = <1>;
	pwm-base = <0x4>;
	pwms = <&pwm4>;
};

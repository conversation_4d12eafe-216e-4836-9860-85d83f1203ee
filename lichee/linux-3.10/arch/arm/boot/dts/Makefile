ifeq ($(CONFIG_OF),y)

# Keep at91 dtb files sorted alphabetically for each SoC
# rm9200
dtb-$(CONFIG_ARCH_AT91) += at91rm9200ek.dtb
dtb-$(CONFIG_ARCH_AT91) += mpa1600.dtb
# sam9260
dtb-$(CONFIG_ARCH_AT91) += animeo_ip.dtb
dtb-$(CONFIG_ARCH_AT91) += aks-cdu.dtb
dtb-$(CONFIG_ARCH_AT91) += ethernut5.dtb
dtb-$(CONFIG_ARCH_AT91) += evk-pro3.dtb
dtb-$(CONFIG_ARCH_AT91) += tny_a9260.dtb
dtb-$(CONFIG_ARCH_AT91) += usb_a9260.dtb
# sam9263
dtb-$(CONFIG_ARCH_AT91) += at91sam9263ek.dtb
dtb-$(CONFIG_ARCH_AT91) += tny_a9263.dtb
dtb-$(CONFIG_ARCH_AT91) += usb_a9263.dtb
# sam9g20
dtb-$(CONFIG_ARCH_AT91) += at91sam9g20ek.dtb
dtb-$(CONFIG_ARCH_AT91) += at91sam9g20ek_2mmc.dtb
dtb-$(CONFIG_ARCH_AT91) += kizbox.dtb
dtb-$(CONFIG_ARCH_AT91) += tny_a9g20.dtb
dtb-$(CONFIG_ARCH_AT91) += usb_a9g20.dtb
# sam9g45
dtb-$(CONFIG_ARCH_AT91) += at91sam9m10g45ek.dtb
dtb-$(CONFIG_ARCH_AT91) += pm9g45.dtb
# sam9n12
dtb-$(CONFIG_ARCH_AT91) += at91sam9n12ek.dtb
# sam9x5
dtb-$(CONFIG_ARCH_AT91) += at91-ariag25.dtb
dtb-$(CONFIG_ARCH_AT91) += at91sam9g15ek.dtb
dtb-$(CONFIG_ARCH_AT91) += at91sam9g25ek.dtb
dtb-$(CONFIG_ARCH_AT91) += at91sam9g35ek.dtb
dtb-$(CONFIG_ARCH_AT91) += at91sam9x25ek.dtb
dtb-$(CONFIG_ARCH_AT91) += at91sam9x35ek.dtb
# sama5d3
dtb-$(CONFIG_ARCH_AT91)	+= sama5d31ek.dtb
dtb-$(CONFIG_ARCH_AT91)	+= sama5d33ek.dtb
dtb-$(CONFIG_ARCH_AT91)	+= sama5d34ek.dtb
dtb-$(CONFIG_ARCH_AT91)	+= sama5d35ek.dtb

dtb-$(CONFIG_ARCH_BCM2835) += bcm2835-rpi-b.dtb
dtb-$(CONFIG_ARCH_BCM) += bcm11351-brt.dtb
dtb-$(CONFIG_ARCH_DAVINCI) += da850-enbw-cmc.dtb \
	da850-evm.dtb
dtb-$(CONFIG_ARCH_DOVE) += dove-cm-a510.dtb \
	dove-cubox.dtb \
	dove-dove-db.dtb
dtb-$(CONFIG_ARCH_EXYNOS) += exynos4210-origen.dtb \
	exynos4210-smdkv310.dtb \
	exynos4210-trats.dtb \
	exynos4210-universal_c210.dtb \
	exynos4412-odroidx.dtb \
	exynos4412-smdk4412.dtb \
	exynos4412-origen.dtb \
	exynos5250-arndale.dtb \
	exynos5440-sd5v1.dtb \
	exynos5250-smdk5250.dtb \
	exynos5250-snow.dtb \
	exynos5440-ssdk5440.dtb
dtb-$(CONFIG_ARCH_HIGHBANK) += highbank.dtb \
	ecx-2000.dtb
dtb-$(CONFIG_ARCH_INTEGRATOR) += integratorap.dtb \
	integratorcp.dtb
dtb-$(CONFIG_ARCH_LPC32XX) += ea3250.dtb phy3250.dtb
dtb-$(CONFIG_ARCH_KIRKWOOD) += kirkwood-cloudbox.dtb \
	kirkwood-dns320.dtb \
	kirkwood-dns325.dtb \
	kirkwood-dockstar.dtb \
	kirkwood-dreamplug.dtb \
	kirkwood-goflexnet.dtb \
	kirkwood-guruplug-server-plus.dtb \
	kirkwood-ib62x0.dtb \
	kirkwood-iconnect.dtb \
	kirkwood-iomega_ix2_200.dtb \
	kirkwood-is2.dtb \
	kirkwood-km_kirkwood.dtb \
	kirkwood-lschlv2.dtb \
	kirkwood-lsxhl.dtb \
	kirkwood-mplcec4.dtb \
	kirkwood-netgear_readynas_duo_v2.dtb \
	kirkwood-ns2.dtb \
	kirkwood-ns2lite.dtb \
	kirkwood-ns2max.dtb \
	kirkwood-ns2mini.dtb \
	kirkwood-nsa310.dtb \
	kirkwood-topkick.dtb \
	kirkwood-ts219-6281.dtb \
	kirkwood-ts219-6282.dtb \
	kirkwood-openblocks_a6.dtb
dtb-$(CONFIG_ARCH_MARCO) += marco-evb.dtb
dtb-$(CONFIG_ARCH_MSM) += msm8660-surf.dtb \
	msm8960-cdp.dtb
dtb-$(CONFIG_ARCH_MVEBU) += armada-370-db.dtb \
	armada-370-mirabox.dtb \
	armada-370-rd.dtb \
	armada-xp-db.dtb \
	armada-xp-gp.dtb \
	armada-xp-openblocks-ax3-4.dtb
dtb-$(CONFIG_ARCH_MXC) += \
	imx25-karo-tx25.dtb \
	imx25-pdk.dtb \
	imx27-apf27.dtb \
	imx27-apf27dev.dtb \
	imx27-pdk.dtb \
	imx27-phytec-phycore.dtb \
	imx31-bug.dtb \
	imx51-apf51.dtb \
	imx51-apf51dev.dtb \
	imx51-babbage.dtb \
	imx53-ard.dtb \
	imx53-evk.dtb \
	imx53-mba53.dtb \
	imx53-qsb.dtb \
	imx53-smd.dtb \
	imx6dl-sabreauto.dtb \
	imx6dl-sabresd.dtb \
	imx6dl-wandboard.dtb \
	imx6q-arm2.dtb \
	imx6q-sabreauto.dtb \
	imx6q-sabrelite.dtb \
	imx6q-sabresd.dtb \
	imx6q-sbc6x.dtb
dtb-$(CONFIG_ARCH_MXS) += imx23-evk.dtb \
	imx23-olinuxino.dtb \
	imx23-stmp378x_devb.dtb \
	imx28-apf28.dtb \
	imx28-apf28dev.dtb \
	imx28-apx4devkit.dtb \
	imx28-cfa10036.dtb \
	imx28-cfa10037.dtb \
	imx28-cfa10049.dtb \
	imx28-evk.dtb \
	imx28-m28evk.dtb \
	imx28-sps1.dtb \
	imx28-tx28.dtb
dtb-$(CONFIG_ARCH_NOMADIK) += ste-nomadik-s8815.dtb
dtb-$(CONFIG_ARCH_OMAP2PLUS) += omap2420-h4.dtb \
	omap3430-sdp.dtb \
	omap3-beagle.dtb \
	omap3-devkit8000.dtb \
	omap3-beagle-xm.dtb \
	omap3-evm.dtb \
	omap3-tobi.dtb \
	omap3-igep0020.dtb \
	omap3-igep0030.dtb \
	omap4-panda.dtb \
	omap4-panda-a4.dtb \
	omap4-panda-es.dtb \
	omap4-var-som.dtb \
	omap4-sdp.dtb \
	omap5-evm.dtb \
	am335x-evm.dtb \
	am335x-evmsk.dtb \
	am335x-bone.dtb
dtb-$(CONFIG_ARCH_ORION5X) += orion5x-lacie-ethernet-disk-mini-v2.dtb
dtb-$(CONFIG_ARCH_PRIMA2) += prima2-evb.dtb
dtb-$(CONFIG_ARCH_U8500) += snowball.dtb \
	hrefprev60.dtb \
	hrefv60plus.dtb \
	ccu9540.dtb
dtb-$(CONFIG_ARCH_SHMOBILE) += emev2-kzm9d.dtb \
	r8a7740-armadillo800eva.dtb \
	r8a7778-bockw.dtb \
	r8a7779-marzen-reference.dtb \
	r8a7790-lager.dtb \
	sh73a0-kzm9g.dtb \
	sh73a0-kzm9g-reference.dtb \
	r8a73a4-ape6evm.dtb \
	sh7372-mackerel.dtb
dtb-$(CONFIG_ARCH_SOCFPGA) += socfpga_cyclone5.dtb \
	socfpga_vt.dtb
dtb-$(CONFIG_ARCH_SPEAR13XX) += spear1310-evb.dtb \
	spear1340-evb.dtb
dtb-$(CONFIG_ARCH_SPEAR3XX)+= spear300-evb.dtb \
	spear310-evb.dtb \
	spear320-evb.dtb \
	spear320-hmi.dtb
dtb-$(CONFIG_ARCH_SPEAR6XX)+= spear600-evb.dtb
dtb-$(CONFIG_ARCH_SUN8IW10P1) += sun8iw10p1-fpga.dtb \
	sun8iw10p1-perf1_v1_0.dtb \
	sun8iw10p1-perf2_v1_0.dtb \
	sun8iw10p1-pro.dtb \
	sun8iw10p1-soc.dtb
dtb-$(CONFIG_ARCH_SUN8IW11P1) += sun8iw11p1-soc.dtb \
	sun8iw11p1-ECG.dtb \
	sun8iw11p1-ECG20mA.dtb \
	sun8iw11p1-ECGmeter.dtb \
	sun8iw11p1-ECG03.dtb \
	sun8iw11p1-MGC1.9.dtb \
	sun8iw11p1-MGC2.0.dtb
dtb-$(CONFIG_ARCH_SUN50IW1P1) += sun50iw1p1-soc.dtb \
	sun50iw1p1-perf1_v1_0.dtb
dtb-$(CONFIG_ARCH_TEGRA) += tegra20-harmony.dtb \
	tegra20-iris-512.dtb \
	tegra20-medcom-wide.dtb \
	tegra20-paz00.dtb \
	tegra20-plutux.dtb \
	tegra20-seaboard.dtb \
	tegra20-tec.dtb \
	tegra20-trimslice.dtb \
	tegra20-ventana.dtb \
	tegra20-whistler.dtb \
	tegra30-beaver.dtb \
	tegra30-cardhu-a02.dtb \
	tegra30-cardhu-a04.dtb \
	tegra114-dalmore.dtb \
	tegra114-pluto.dtb
dtb-$(CONFIG_ARCH_VERSATILE) += versatile-ab.dtb \
	versatile-pb.dtb
dtb-$(CONFIG_ARCH_VEXPRESS) += vexpress-v2p-ca5s.dtb \
	vexpress-v2p-ca9.dtb \
	vexpress-v2p-ca15-tc1.dtb \
	vexpress-v2p-ca15_a7.dtb \
	rtsm_ve-cortex_a9x2.dtb \
	rtsm_ve-cortex_a9x4.dtb \
	rtsm_ve-cortex_a15x1.dtb \
	rtsm_ve-cortex_a15x2.dtb \
	rtsm_ve-cortex_a15x4.dtb \
	rtsm_ve-v2p-ca15x1-ca7x1.dtb \
	rtsm_ve-v2p-ca15x4-ca7x4.dtb
dtb-$(CONFIG_ARCH_VIRT) += xenvm-4.2.dtb
dtb-$(CONFIG_ARCH_VT8500) += vt8500-bv07.dtb \
	wm8505-ref.dtb \
	wm8650-mid.dtb \
	wm8850-w70v2.dtb
dtb-$(CONFIG_ARCH_ZYNQ) += zynq-zc702.dtb

targets += dtbs
targets += $(dtb-y)
endif

# *.dtb used to be generated in the directory above. Clean out the
# old build results so people don't accidentally use them.
dtbs: $(addprefix $(obj)/, $(dtb-y))
	$(Q)rm -f $(obj)/../*.dtb

clean-files := *.dtb

/*
 * Allwinner sun8iw11p1 pin config info.
 */

/ {
	soc@01c00000{
		pio: pinctrl@01c20800 {
			compatible = "allwinner,sun8iw11p1-pinctrl";
			reg = <0x0 0x01c20800 0x0 0x400>;
			interrupts = <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>;
			device_type = "pio";
			clocks = <&clk_pio>;
			gpio-controller;
			interrupt-controller;
			#interrupt-cells = <2>;
			#size-cells = <0>;
			#gpio-cells = <6>;

			vdevice_pins_a: vdevice@0 {
				allwinner,pins = "PB0", "PB1";
				allwinner,function = "vdevice";
				allwinner,muxsel = <4>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
				allwinner,data = <1>;
			};

			ir0_pins_a: ir0@0 {
				allwinner,pins = "PB4";
				allwinner,function = "ir0";
				allwinner,muxsel = <2>;
				allwinner,drive = <2>;
				allwinner,pull = <1>;
			};

			ir1_pins_a: ir1@0 {
				allwinner,pins = "PB23";
				allwinner,function = "ir1";
				allwinner,muxsel = <3>;
				allwinner,drive = <2>;
				allwinner,pull = <1>;
			};

			uart0_pins_a: uart0@0 {
				allwinner,pins = "PB22", "PB23";
				allwinner,pname = "uart0_tx", "uart0_rx";
				allwinner,function = "uart0";
				allwinner,muxsel = <2>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			uart0_pins_b: uart0@1 {
				allwinner,pins = "PB22", "PB23";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			uart1_pins_a: uart1@0 {
				allwinner,pins = "PA10", "PA11", "PA12", "PA13", "PA14", "PA15", "PA16", "PA17";
				allwinner,pname = "uart1_tx", "uart1_rx", "uart1_rts", "uart1_cts", "uart1_dtr", "uart1_dsr", "uart1_dcd", "uart1_ring";
				allwinner,function = "uart1";
				allwinner,muxsel = <4>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			uart1_pins_b: uart1@1 {
				allwinner,pins = "PA10", "PA11", "PA12", "PA13", "PA14", "PA15", "PA16", "PA17";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			uart2_pins_a: uart2@0 {
				allwinner,pins = "PA2", "PA3", "PA0", "PA1";
				allwinner,pname = "uart2_tx", "uart2_rx", "uart2_rts", "uart2_cts";
				allwinner,function = "uart2";
				allwinner,muxsel = <4>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			uart2_pins_b: uart2@1 {
				allwinner,pins = "PA3", "PA2", "PA1", "PA0";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			uart3_pins_a: uart3@0 {
				allwinner,pins = "PG6", "PG7", "PG8", "PG9";
				allwinner,pname = "uart3_tx", "uart3_rx", "uart3_rts", "uart3_cts";
				allwinner,function = "uart3";
				allwinner,muxsel = <4>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			uart3_pins_b: uart3@1 {
				allwinner,pins = "PG6", "PG7", "PG8", "PG9";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			uart4_pins_a: uart4@0 {
				allwinner,pins = "PH4", "PH5";
				allwinner,pname = "uart4_tx", "uart4_rx";
				allwinner,function = "uart4";
				allwinner,muxsel = <4>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			uart4_pins_b: uart4@1 {
				allwinner,pins = "PH4", "PH5";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			uart5_pins_a: uart5@0 {
				allwinner,pins = "PH6", "PH7";
				allwinner,pname = "uart5_tx", "uart5_rx";
				allwinner,function = "uart5";
				allwinner,muxsel = <4>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			uart5_pins_b: uart5@1 {
				allwinner,pins = "PH6", "PH7";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <3>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			uart6_pins_a: uart6@0 {
				allwinner,pins = "PA12", "PA13";
				allwinner,pname = "uart6_tx", "uart6_rx";
				allwinner,function = "uart6";
				allwinner,muxsel = <3>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			uart6_pins_b: uart6@1 {
				allwinner,pins = "PA12", "PA13";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			uart7_pins_a: uart7@0 {
				allwinner,pins = "PA14", "PA15";
				allwinner,pname = "uart7_tx", "uart7_rx";
				allwinner,function = "uart7";
				allwinner,muxsel = <3>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			uart7_pins_b: uart7@1 {
				allwinner,pins = "PA14", "PA15";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			can0_pins_a: can0@0 {
				allwinner,pins = "PH20", "PH21";
				allwinner,pname = "can0_tx", "can0_rx";
				allwinner,function = "can0";
				allwinner,muxsel = <4>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			can0_pins_b: can0@1 {
				allwinner,pins = "PH20", "PH21";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			twi0_pins_a: twi0@0 {
				allwinner,pins = "PB0", "PB1";
				allwinner,pname = "twi0_scl", "twi0_sda";
				allwinner,function = "twi0";
				allwinner,muxsel = <2>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			twi0_pins_b: twi0@1 {
				allwinner,pins = "PB0", "PB1";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			twi1_pins_a: twi1@0 {
				allwinner,pins = "PB18", "PB19";
				allwinner,pname = "twi1_scl", "twi1_sda";
				allwinner,function = "twi1";
				allwinner,muxsel = <2>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			twi1_pins_b: twi1@1 {
				allwinner,pins = "PB18", "PB19";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			twi2_pins_a: twi2@0 {
				allwinner,pins = "PB20", "PB21";
				allwinner,pname = "twi2_scl", "twi2_sda";
				allwinner,function = "twi2";
				allwinner,muxsel = <2>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			twi2_pins_b: twi2@1 {
				allwinner,pins = "PB20", "PB21";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			twi3_pins_a: twi3@0 {
				allwinner,pins = "PI0", "PI1";
				allwinner,pname = "twi3_scl", "twi3_sda";
				allwinner,function = "twi3";
				allwinner,muxsel = <3>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			twi3_pins_b: twi3@1 {
				allwinner,pins = "PI0", "PI1";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			twi4_pins_a: twi4@0 {
				allwinner,pins = "PI2", "PI3";
				allwinner,pname = "twi4_scl", "twi4_sda";
				allwinner,function = "twi4";
				allwinner,muxsel = <3>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			twi4_pins_b: twi4@1 {
				allwinner,pins = "PI2", "PI3";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			spi0_pins_a: spi0@0 {
				allwinner,pins = "PC0", "PC1", "PC2";
				allwinner,pname = "spi0_mosi", "spi0_miso", "spi0_sclk";
				allwinner,function = "spi0";
				allwinner,muxsel = <3>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			spi0_pins_b: spi0@1 {
				allwinner,pins = "PC23";
				allwinner,pname = "spi0_cs0";
				allwinner,function = "spi0";
				allwinner,muxsel = <3>;
				allwinner,drive = <1>;
				allwinner,pull = <1>; 	// only CS should be pulled up
			};

			spi0_pins_c: spi0@2 {
				allwinner,pins = "PI14";
				allwinner,pname = "spi0_cs1";
				allwinner,function = "spi0";
				allwinner,muxsel = <2>;
				allwinner,drive = <1>;
				allwinner,pull = <1>; 	// only CS should be pulled up
			};



			spi0_pins_d: spi0@3 {
				allwinner,pins = "PC0", "PC1", "PC2", "PC23", "PI14";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			spi1_pins_a: spi1@0 {
				allwinner,pins = "PA2", "PA3", "PA1";
				allwinner,pname = "spi1_mosi", "spi1_miso", "spi1_sclk";
				allwinner,function = "spi1";
				allwinner,muxsel = <3>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			spi1_pins_b: spi1@1 {
				allwinner,pins = "PA0", "PA4";
				allwinner,pname = "spi1_cs0", "spi1_cs1";
				allwinner,function = "spi1";
				allwinner,muxsel = <3>;
				allwinner,drive = <1>;
				allwinner,pull = <1>; 	// only CS should be pulled up
			};

			spi1_pins_c: spi1@2 {
				allwinner,pins = "PA2", "PA3", "PA1", "PA0", "PA4";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			spi2_pins_a: spi2@0 {
				allwinner,pins = "PB16", "PB17", "PB15";
				allwinner,pname = "spi2_mosi", "spi2_miso", "spi2_sclk";
				allwinner,function = "spi2";
				allwinner,muxsel = <2>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			spi2_pins_b: spi2@1 {
				allwinner,pins = "PB14", "PB13";
				allwinner,pname = "spi2_cs0", "spi2_cs1";
				allwinner,function = "spi2";
				allwinner,muxsel = <2>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;   // only CS should be pulled up
			};

			spi2_pins_c: spi2@2 {
				allwinner,pins = "PB16", "PB17", "PB15", "PB14", "PB13";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			spi3_pins_a: spi3@0 {
				allwinner,pins = "PA7", "PA8", "PA6";
				allwinner,pname = "spi3_mosi", "spi3_miso", "spi3_sclk";
				allwinner,function = "spi3";
				allwinner,muxsel = <3>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			spi3_pins_b: spi3@1 {
				allwinner,pins = "PA5", "PA9";
				allwinner,pname = "spi3_cs0", "spi3_cs1";
				allwinner,function = "spi3";
				allwinner,muxsel = <3>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;   // only CS should be pulled up
			};

			spi3_pins_c: spi3@2 {
				allwinner,pins = "PA7", "PA8", "PA6", "PA5", "PA9";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			sdc0_pins_a: sdc0@0 {
				allwinner,pins = "PF0", "PF1", "PF2", "PF3","PF4","PF5";
				allwinner,function = "sdc0";
				allwinner,muxsel = <2>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			sdc0_pins_b: sdc0@1 {
				allwinner,pins = "PF0", "PF1", "PF2", "PF3","PF4","PF5";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			sdc1_pins_a: sdc1@0 {
				allwinner,pins = "PH22", "PH23", "PH24", "PH25","PH26","PH27";
				allwinner,function = "sdc1";
				allwinner,muxsel = <5>;
				allwinner,drive = <3>;
				allwinner,pull = <1>;
			};

			sdc1_pins_b: sdc1@1 {
				allwinner,pins = "PH22", "PH23", "PH24", "PH25","PH26","PH27";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			sdc2_pins_a: sdc2@0 {
				allwinner,pins = "PC5", "PC6","PC7", "PC8", "PC9","PC10","PC11","PC12","PC13","PC14","PC15","PC24";
				allwinner,function = "sdc2";
				allwinner,muxsel = <3>;
				allwinner,drive = <2>;
				allwinner,pull = <1>;
			};

			sdc2_pins_b: sdc2@1 {
				allwinner,pins = "PC5", "PC6","PC7", "PC8", "PC9","PC10","PC11","PC12","PC13","PC14","PC15","PC24";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			sdc3_pins_a: sdc3@0 {
				allwinner,pins = "PI4", "PI5","PI6", "PI7", "PI8", "PI9";
				allwinner,function = "sdc3";
				allwinner,muxsel = <2>;
				allwinner,drive = <2>;
				allwinner,pull = <1>;
			};

			sdc3_pins_b: sdc3@1 {
				allwinner,pins = "PI4", "PI5","PI6", "PI7", "PI8", "PI9";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			daudio0_pins_a: daudio0@0 {
				allwinner,pins = "PB6", "PB7", "PB8", "PB12";
				allwinner,function = "i2s0";
				allwinner,muxsel = <2>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};
			daudio0_pins_b: daudio0_sleep@0 {
				allwinner,pins = "PB6", "PB7", "PB8", "PB12";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};
			daudio1_pins_a: daudio1@0 {
				allwinner,pins = "PA14", "PA15", "PA16", "PA17";
				allwinner,function = "i2s1";
				allwinner,muxsel = <6>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};
			daudio1_pins_b: daudio1_sleep@0 {
				allwinner,pins = "PA14", "PA15", "PA16", "PA17";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};
			spdif_pins_a: spdif@0 {
				allwinner,pins = "PB12", "PB13";
				allwinner,function = "spdif0";
				allwinner,muxsel = <4>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};
			spdif_pins_b: spdif_sleep@0 {
				allwinner,pins = "PB12", "PB13";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			csi0_pins_a: csi0@0 {
				allwinner,pins = "PE0","PE2","PE3","PE4","PE5","PE6","PE7","PE8","PE9","PE10","PE11";
				allwinner,pname = "csi0_pck","csi0_hsync","csi0_vsync","csi0_d0","csi0_d1","csi0_d2",
								"csi0_d3","csi0_d4","csi0_d5","csi0_d6","csi0_d7";
				allwinner,function = "csi0";
				allwinner,muxsel = <3>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
				allwinner,data = <0>;
			};
			csi0_pins_b: csi0@1 {
				allwinner,pins = "PE0","PE2","PE3","PE4","PE5","PE6","PE7","PE8","PE9","PE10","PE11";
				allwinner,pname = "csi0_pck","csi0_hsync","csi0_vsync","csi0_d0","csi0_d1","csi0_d2",
								"csi0_d3","csi0_d4","csi0_d5","csi0_d6","csi0_d7";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
				allwinner,data = <0>;
			};

			csi1_pins_a: csi1@0 {
				allwinner,pins = "PG0","PG2","PG3","PG4","PG5","PG6","PG7","PG8","PG9","PG10","PG11";
				allwinner,pname = "csi1_pck","csi1_hsync","csi1_vsync","csi1_d0","csi1_d1","csi1_d2",
								"csi1_d3","csi1_d4","csi1_d5","csi1_d6","csi1_d7";
				allwinner,function = "csi1";
				allwinner,muxsel = <3>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
				allwinner,data = <0>;
			};
			csi1_pins_b: csi1@1 {
				allwinner,pins = "PG0","PG2","PG3","PG4","PG5","PG6","PG7","PG8","PG9","PG10","PG11";
				allwinner,pname = "csi1_pck","csi1_hsync","csi1_vsync","csi1_d0","csi1_d1","csi1_d2",
								"csi1_d3","csi1_d4","csi1_d5","csi1_d6","csi1_d7";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
				allwinner,data = <0>;
			};

			scr_pins_a: smartcard@0 {
				allwinner,pins = "PH13",  "PH16","PH18", "PH19";
				allwinner,pname = "smc_rst","smc_det", "smc_sck", "smc_sda";
				allwinner,function = "sim0";
				allwinner,muxsel = <5>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

			scr_pins_b: smartcard@1 {
				allwinner,pins = "PH13", "PH16","PH18", "PH19";
				allwinner,pname = "smc_rst", "smc_det","smc_sck", "smc_sda";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <0>;
				allwinner,pull = <0>;
			};

			nand0_pins_a: nand0@0 {
				allwinner,pins = "PC0",      "PC1",       "PC2",       "PC5",       "PC8",      "PC9",      "PC10",     "PC11",     "PC12",     "PC13",     "PC14",     "PC15",     "PC24";
				allwinner,pname= "nand0_we", "nand0_ale", "nand0_cle", "nand0_nre", "nand0_d0", "nand0_d1", "nand0_d2", "nand0_d3", "nand0_d4", "nand0_d5", "nand0_d6", "nand0_d7", "nand0_ndqs";
				allwinner,function = "nand0";
				allwinner,muxsel = <2>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			nand0_pins_b: nand0@1 {
				allwinner,pins = "PC3",       "PC4",       "PC6",       "PC7",       "PC16",      "PC17",      "PC18",      "PC19",      "PC20",      "PC21",      "PC22";
				allwinner,pname= "nand0_ce1", "nand0_ce0", "nand0_rb0", "nand0_rb1", "nand0_nwp", "nand0_ce2", "nand0_ce3", "nand0_ce4", "nand0_ce5", "nand0_ce6", "nand0_ce7";
				allwinner,function = "nand0";
				allwinner,muxsel = <2>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;// only RB&CE should be pulled up
			};

			nand0_pins_c: nand0@2 {
				allwinner,pins ="PC0", "PC1", "PC2", "PC3", "PC4", "PC5", "PC6", "PC7", "PC8", "PC9", "PC10", "PC11", "PC12", "PC13", "PC14", "PC15", "PC16", "PC17", "PC18", "PC19", "PC20", "PC21", "PC22", "PC24";
				allwinner,function = "io_disabled";
				allwinner,muxsel = <7>;
				allwinner,drive = <1>;
				allwinner,pull = <0>;
			};

			gmac_pins_a: gmac@0 {
				allwinner,pins = "PA0", "PA1", "PA2", "PA3",
						"PA4", "PA5", "PA6", "PA7",
						"PA8", "PA9", "PA10", "PA11",
						"PA11", "PA12", "PA13", "PA14",
						"PA15", "PA16", "PA17";
				allwinner,function = "gmac0";
				allwinner,muxsel = <5>;
				allwinner,drive = <3>;
				allwinner,pull = <0>;
			};

			emac_pins_a: emac@0 {
				allwinner,pins = "PA0", "PA1", "PA2", "PA3",
						"PA4", "PA5", "PA6", "PA7",
						"PA8", "PA9", "PA10", "PA11",
						"PA12", "PA13", "PA14", "PA15",
						"PA16", "PA17";
				allwinner,function = "emac0";
				allwinner,muxsel = <2>;
				allwinner,drive = <3>;
				allwinner,pull = <0>;
			};

			emac_pins_b: emac@1 {
				allwinner,pins = "PH8", "PH9", "PH10", "PH11",
						"PH14", "PH15", "PH16", "PH17",
						"PH18", "PH19", "PH20", "PH21",
						"PH22", "PH23", "PH24", "PH25",
						"PH26", "PH27";
				allwinner,function = "emac0";
				allwinner,muxsel = <2>;
				allwinner,drive = <3>;
				allwinner,pull = <0>;
			};

			ps20_pins_a: ps20@0 {
				allwinner,pins = "PI20", "PI21";
				allwinner,pname = "ps2_sck0", "ps2_sda0";
				allwinner,function = "ps20";
				allwinner,muxsel = <2>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};
			ps21_pins_a: ps21@0 {
				allwinner,pins = "PH12", "PH13";
				allwinner,pname = "ps2_sck1", "ps2_sda1";
				allwinner,function = "ps21";
				allwinner,muxsel = <4>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};
			ps21_pins_b: ps21@1 {
				allwinner,pins = "PI14", "PI15";
				allwinner,pname = "ps2_sck1", "ps2_sda1";
				allwinner,function = "ps21";
				allwinner,muxsel = <3>;
				allwinner,drive = <1>;
				allwinner,pull = <1>;
			};

		};
	};
};

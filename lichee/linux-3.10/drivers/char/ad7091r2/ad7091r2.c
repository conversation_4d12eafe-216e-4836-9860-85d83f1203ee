#include <linux/ide.h>
#include <linux/spi/spi.h>
#include <linux/module.h>
#include <linux/errno.h>
#include <linux/cdev.h>
#include <linux/of_gpio.h>
#include <linux/delay.h>
// #include <linux/types.h>
// #include <linux/kernel.h>
// #include <linux/init.h>
// #include <linux/gpio.h>
// #include <linux/device.h>
// #include <linux/semaphore.h>
// #include <linux/timer.h>
// #include <linux/i2c.h>
// #include <linux/of.h>
// #include <linux/of_address.h>
// #include <linux/platform_device.h>
// #include <asm/mach/map.h>
// #include <asm/uaccess.h>
// #include <asm/io.h>

#define	RESULT_REG		0x00
#define	CHANNEL_REG		0x01
#define	CONFIG_REG		0x02

#define AD7091R2_CNT	1
#define AD7091R2_NAME	"ad7091r2"

struct ad7091r2_dev {
	dev_t devid;				/* 设备号 	*/
	struct cdev cdev;			/* cdev 	*/
	struct class *class;		/* 类 		*/
	struct device *device;		/* 设备 	*/
	struct device_node	*nd; 	/* 设备节点 */
	int major;					/* 主设备号 */
	void *private_data;			/* 私有数据 */
	int convst_gpio;			/* CONVST所使用的GPIO编号   */
	unsigned short adcchan[2];	/* ADC原始值,共2通道        */
};

static struct ad7091r2_dev ad7091r2dev;

void ad7091r2_reginit(struct spi_device *spi)
{
    unsigned char writebuf[2];
    /* 写CONFIG寄存器，设置使用内部2.5V参考电压, 使能软件重启 */
    writebuf[0] = CONFIG_REG << 3 | 0x04 | 0x02;
    writebuf[1] = 0x01;
    spi_write(spi, writebuf, 2);
    /* 写CHANNEL寄存器，使能通道0、1 */
    writebuf[0] = CHANNEL_REG << 3 | 0x04;
    writebuf[1] = 0x03; 
    spi_write(spi, writebuf, 2);
}

/*
 * @description		: 打开设备
 * @param - inode 	: 传递给驱动的inode
 * @param - filp 	: 设备文件，filp结构体有个叫做private_data的成员变量
 * 					  一般在open的时候将private_data指向设备结构体。
 * @return 			: 0 成功;其他 失败
 */
static int ad7091r2_open(struct inode *inode, struct file *filp)
{
    filp->private_data = &ad7091r2dev; /* 设置私有数据 */
	return 0;
}

/*
 * @description		: 从设备读取数据 
 * @param - filp 	: 要打开的设备文件(文件描述符)
 * @param - buf 	: 返回给用户空间的数据缓冲区
 * @param - cnt 	: 要读取的数据长度
 * @param - offt 	: 相对于文件首地址的偏移
 * @return 			: 读取的字节数，如果为负值，表示读取失败
 */
static ssize_t ad7091r2_read(struct file *filp, char __user *buf, size_t cnt, loff_t *off)
{
    unsigned int data[2];
    unsigned char chan_id = 0;
    unsigned char readbuf[2];
	long err = 0;
	struct ad7091r2_dev *dev = (struct ad7091r2_dev *)filp->private_data;
    
    for(int k = 0; k < 2; k++){
        gpio_set_value(dev->convst_gpio, 0);				/* CONVST拉低，开始转换 */
        gpio_set_value(dev->convst_gpio, 1);
        spi_read(dev->private_data, readbuf, 2);
        printk("readbuf0=%d\r\n",readbuf[0]);
        printk("readbuf1=%d\r\n",readbuf[1]);
        chan_id = readbuf[0] >> 5;
        printk("chan_id=%d\r\n",chan_id);
        if((chan_id==0) || (chan_id==1)){
            dev->adcchan[chan_id] = (unsigned short)(((readbuf[0] & 0x0f) << 8) | readbuf[1]);
            printk("chan_adc=%d\r\n",dev->adcchan[chan_id]);
            data[chan_id] = dev->adcchan[chan_id];
        }
        msleep(1);
    }

	err = copy_to_user(buf, data, sizeof(data));
	return 0;
}

/*
 * @description		: 关闭/释放设备
 * @param - filp 	: 要关闭的设备文件(文件描述符)
 * @return 			: 0 成功;其他 失败
 */
static int ad7091r2_release(struct inode *inode, struct file *filp)
{
	return 0;
}

/* ad7091r2操作函数 */
static const struct file_operations ad7091r2_ops = {
	.owner = THIS_MODULE,
	.open = ad7091r2_open,
	.read = ad7091r2_read,
	.release = ad7091r2_release,
};

 /*
  * @description     : spi驱动的probe函数，当驱动与
  *                    设备匹配以后此函数就会执行
  * @param - client  : spi设备
  * @param - id      : spi设备ID
  * 
  */	
static int ad7091r2_probe(struct spi_device *spi)
{
    int ret = 0;

	/* 1、构建设备号 */
	if (ad7091r2dev.major) {
		ad7091r2dev.devid = MKDEV(ad7091r2dev.major, 0);
		register_chrdev_region(ad7091r2dev.devid, AD7091R2_CNT, AD7091R2_NAME);
	} else {
		alloc_chrdev_region(&ad7091r2dev.devid, 0, AD7091R2_CNT, AD7091R2_NAME);
		ad7091r2dev.major = MAJOR(ad7091r2dev.devid);
	}

	/* 2、注册设备 */
	cdev_init(&ad7091r2dev.cdev, &ad7091r2_ops);
	cdev_add(&ad7091r2dev.cdev, ad7091r2dev.devid, AD7091R2_CNT);

	/* 3、创建类 */
	ad7091r2dev.class = class_create(THIS_MODULE, AD7091R2_NAME);
	if (IS_ERR(ad7091r2dev.class)) {
		return PTR_ERR(ad7091r2dev.class);
	}

	/* 4、创建设备 */
	ad7091r2dev.device = device_create(ad7091r2dev.class, NULL, ad7091r2dev.devid, NULL, AD7091R2_NAME);
	if (IS_ERR(ad7091r2dev.device)) {
		return PTR_ERR(ad7091r2dev.device);
	}

	/* 获取设备树中CONVST信号 */
    ad7091r2dev.nd = of_find_node_by_name(NULL, "ad7091r2");
	if(ad7091r2dev.nd == NULL) {
		printk("spi1 node not find!\r\n");
		return -EINVAL;
	} 

	/* 获取设备树中的gpio属性，得到CONVST所使用的GPIO编号 */
	ad7091r2dev.convst_gpio = of_get_named_gpio(ad7091r2dev.nd, "convst-gpio", 0);
	if(ad7091r2dev.convst_gpio < 0) {
		printk("can't get convst gpio");
		return -EINVAL;
	}

	/* 设置CONVST为输出，并且输出高电平 */
	ret = gpio_direction_output(ad7091r2dev.convst_gpio, 1);
	if(ret < 0) {
		printk("can't set convst gpio!\r\n");
	}

	/*初始化spi_device */
	spi->mode = SPI_MODE_0;	/*MODE0，CPOL=0，CPHA=0*/
	spi_setup(spi);
	ad7091r2dev.private_data = spi; /* 设置私有数据 */

	/* 初始化AD7091R2内部寄存器 */
	ad7091r2_reginit(spi); 

	return 0;
}

/*
 * @description     : spi驱动的remove函数，移除spi驱动的时候此函数会执行
 * @param - client 	: spi设备
 * @return          : 0，成功;其他负值,失败
 */
static int ad7091r2_remove(struct spi_device *spi)
{
    gpio_direction_output(ad7091r2dev.convst_gpio, 0);
    /* 删除设备 */
	cdev_del(&ad7091r2dev.cdev);
	unregister_chrdev_region(ad7091r2dev.devid, AD7091R2_CNT);

	/* 注销掉类和设备 */
	device_destroy(ad7091r2dev.class, ad7091r2dev.devid);
	class_destroy(ad7091r2dev.class);
	return 0;
}

/* 传统匹配方式ID列表 */
static const struct spi_device_id ad7091r2_id[] = {
	{"ecg,ad7091r2", 0},  
	{}
};

/* 设备树匹配列表 */
static const struct of_device_id ad7091r2_of_match[] = {
	{ .compatible = "ecg,ad7091r2" },
	{ /* Sentinel */ }
};

/* SPI驱动结构体 */	
static struct spi_driver ad7091r2_driver = {
	.probe = ad7091r2_probe,
	.remove = ad7091r2_remove,
	.driver = {
			.owner = THIS_MODULE,
		   	.name = "ad7091r2",
		   	.of_match_table = ad7091r2_of_match, 
		   },
	.id_table = ad7091r2_id,
};
		   
/*
 * @description	: 驱动入口函数
 * @param 		: 无
 * @return 		: 无
 */
static int __init ad7091r2_init(void)
{
    return spi_register_driver(&ad7091r2_driver);
}

/*
 * @description	: 驱动出口函数
 * @param 		: 无
 * @return 		: 无
 */
static void __exit ad7091r2_exit(void)
{
    spi_unregister_driver(&ad7091r2_driver);
}

module_init(ad7091r2_init);
module_exit(ad7091r2_exit);
MODULE_LICENSE("GPL");
MODULE_AUTHOR("xufei");




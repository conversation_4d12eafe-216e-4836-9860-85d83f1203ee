#!/usr/bin/env bash

BUILD_ROOT_DIR=${BUILD_ROOT_DIR:-/build/buildroot}
BUILD_ROOT_OUTPUT=${BUILD_ROOT_OUTPUT:-${BUILD_ROOT_DIR}/output}
LICHEE_BUILD_DIR=${LICHEE_BUILD_DIR:-/build/lichee}
current_script_path=$(dirname "$(readlink -f ./prepare.sh)")
cd "${BUILD_ROOT_DIR}" || exit 1

# copy
cp -r "${current_script_path}"/lichee/* /build/lichee/
cp -r "${current_script_path}"/rootfs/* /build/a40i_platform/initramfs/* "${BUILD_ROOT_DIR}"

# ensure privileges of exec
cd "${BUILD_ROOT_DIR}"/ramfscustom && chmod +x ./init
cd "${BUILD_ROOT_DIR}"/rootfscustom && chmod +x ./etc/init.d/* ./* ./usr/bin/*
if [ -d "./usr/python3.8/bin" ];then
    chmod +x ./usr/python3.8/bin/*
fi

alias clr='find ${BUILD_ROOT_OUTPUT} -type f -name ".stamp_target_installed" -exec rm {} \; && rm -rf ${BUILD_ROOT_OUTPUT}/target'
alias mkrootfs='cd ${BUILD_ROOT_DIR} && make sun8iw11p1_hf_defconfig && make'
alias mkramfs='cd ${BUILD_ROOT_DIR} && make  initramfs_defconfig && make'
alias packimg='cp ${BUILD_ROOT_OUTPUT}/images/rootfs.cpio.gz ${LICHEE_BUILD_DIR}/packimg && chmod u+x ${LICHEE_BUILD_DIR}/packimg/* && ${LICHEE_BUILD_DIR}/packimg/packimg.sh'
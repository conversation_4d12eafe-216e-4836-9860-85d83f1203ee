use std::{collections::HashMap, io::{self, Write}, net::Ipv4Addr, vec};
// use std::process::Command;
use regex::Regex;
use anyhow::{anyhow, Result};
use ipnetwork::Ipv4Network;
use clap::{arg, value_parser, Arg, Command};

fn iface_get_uuid(iface:&str) -> Result<Vec<String>> {
    let output = std::process::Command::new("nmcli")
        .args(&["-g", "NAME,UUID", "connection"])
        .output()?;
    if !output.status.success() {
        return Err(anyhow!("Failed to execute nmcli command"));
    }
    let stdout = String::from_utf8(output.stdout)?;
    let mut uuids = vec![];
    for line in stdout.lines() {
        let parts: Vec<&str> = line.split(':').collect();
        if parts.len() != 2 {
            continue;
        }
        let con_name = parts[0].trim();
        let uuid = parts[1].trim();
        if con_name.contains(iface) {
            uuids.push(uuid.to_string());
        }
    }
    Ok(uuids)
}

fn del_con(ids:Vec<String>) -> Result<()> {
    let output = std::process::Command::new("nmcli")
        .args(&["-g", "NAME,UUID", "connection"])
        .output()?;
    if output.status.success() {
        let stdout = String::from_utf8(output.stdout)?;
        for id in ids {
            for line in stdout.lines() {
                if line.contains(&id) {
                    println!("Deleting old connection: {}", line);
                    let output = std::process::Command::new("nmcli")
                        .args(&["connection", "delete", line.split(':').collect::<Vec<&str>>()[1]])
                        .output()?;
                    if !output.status.success() {
                        return Err(anyhow!("Failed to delete connection: {}", line));
                    }
                }
            }
        }
    }
    Ok(())
}
fn main() -> Result<()> {
    let cmd = clap::Command::new("Network Manage helper")
        .version("0.1")
        .author("liuyuxuan <<EMAIL>>")
        .about("A helper for network management")
        // .arg_required_else_help(true)
        .subcommand(
    Command::new("set")
                .about("Set the network configuration")
                .hide(true)
                .arg(
                    Arg::new("interface")
                    .short('i')
                    .long("iface")
                    .help("The interface to set the configuration")
                    .action(clap::ArgAction::Set)
                    .required(true))
                .arg(arg!(-m --method <method> "<dhcp | static> The method to set the configuration")
                    .value_parser(["dhcp", "static"])
                    .default_value("static")
                    .required(true))
                .arg(arg!(-a --address <address> "The address dotted quad/mask")
                    // .default_value("")
                    .required_if_eq("method", "static"))
                .arg(arg!(--netmask <mask> "netmask in dotted quad, this will overwrite the prefix in <address> if provided")
                    .value_parser(value_parser!(std::net::Ipv4Addr)))
                .arg(arg!(-g --gateway <gateway> "The gateway address"))
                    // .default_value("")
                    // .required_if_eq("method", "static"))
                .arg(arg!(--metric <metric> "The metric for the route")
                    // .default_value("2000")
                    .allow_negative_numbers(true)
                    .value_parser(value_parser!(i64).range(-1..=4294967295)))
                .arg(arg!(--noauto "Set the interface not to auto up")
                    .value_parser(value_parser!(bool))
                    .action(clap::ArgAction::SetTrue))
        )
        // .subcommand(
        //     Command::new("modify")
        //     .about("modify configuration")
        //     .arg(arg!(-i --iface <ifname> "The interface to set the configuration")
        //         .required(true))
        // )
        .subcommand(
            Command::new("show")
                .about("Show the network configuration")
                .arg(arg!(interface: -i --iface <ifname> "The interface to get the configuration"))
        )
        .subcommand(
            Command::new("list")
                .about("List the network configuration")
        )
        .subcommand(
            Command::new("diag")
                .about("Diagnose the network")
        )
        .subcommand(
            Command::new("prio")
                .about("Choose a default via device")
                .arg(arg!(interface: -i --iface <ifname> "The interface to be first to use").hide(true))
                .arg(arg!(-s --show "Show the current default via device")
                    .action(clap::ArgAction::SetTrue))
        )
        .after_help("Longer explanation to appear after the options when \
        displaying the help information from --help or -h")
        .get_matches();
        // .get_matches_from(vec!["helper", "set", "-i", "eth0", "-m", "static", "-a", "***********01/24", "-g", "***********"]);

    match cmd.subcommand() {
        Some(("set", set_matches)) => {
            let iface:&String = set_matches.get_one("interface").expect("get interface failed");
            let method_arg_in:&String = set_matches.get_one("method").expect("get method failed");
            let con_name= format!("{}-{}", iface, method_arg_in);
            let address:Option<&String> = set_matches.get_one("address");
            let gateway:Option<&String> = set_matches.get_one("gateway");
            let method_arg_out= match method_arg_in.as_str() {
                "dhcp" => "auto",
                "static" => "manual",
                _ => "manual"
            };
            let noauto:bool = set_matches.get_flag("noauto");
            let metric:Option<&i64> = set_matches.get_one("metric");
            // println!("{:?}", address);
            // println!("{:?}", gateway);
            // println!("{:?}", metric);
            
            let uuids = iface_get_uuid(iface)?;

            let mut args: Vec<&str> = vec!["c", "add", "type", "ethernet", "con-name", &con_name, "ifname", iface, "ipv4.method", method_arg_out,
            "ipv6.method", "auto", "connection.autoconnect", if noauto {"no"} else {"yes"}, "connection.autoconnect-retries", "0"];
            let ip4addr:String;
            if let Some(mut addr) = address {
                if !addr.contains("/") && !addr.contains(",") {
                    if let Some(mask) = set_matches.get_one::<Ipv4Addr>("netmask") {
                        ip4addr = Ipv4Network::with_netmask(addr.parse::<Ipv4Addr>()?, *mask)?.to_string();
                        addr = &ip4addr;
                    } else {
                        return Err(anyhow!("Must provide netmask, either in <address> or <netmask>"));
                    }
                }
                // addr.parse::<Ipv4Network>()?;  // no need, checked by nmcli
                args.push("ipv4.addresses");
                args.push(addr);
            }
            if let Some(gw) = gateway {
                if gw.parse::<std::net::Ipv4Addr>().is_ok() {
                    args.push("ipv4.gateway");
                    args.push(gw);
                }
            }
            let m_string:String;
            if let Some(m) = metric {
                m_string = m.to_string();
                args.push("ipv4.route-metric");
                args.push(&m_string);
            }
            // println!("{:?}", args);
            let nmcli = std::process::Command::new("nmcli")
                .args(&args) 
                .output()?;
            if nmcli.status.success() {
                println!("Successfully set the connection: {}", String::from_utf8(nmcli.stdout)?);
                del_con(uuids)?;
                println!();
                println!("{}", String::from_utf8(
                    std::process::Command::new(std::env::current_exe()?)
                    .args(&["show", "-i", iface])
                    .output()?.stdout)?
                );
            } else {
                println!("{}", String::from_utf8(nmcli.stderr)?);
            }
            // println!("{:?}", nmcli);
        }
        Some(("diag", _)) => {
            let mut output = std::process::Command::new("nmcli")
                .args(&["-g", "all"])
                .output()?;
            io::stdout().write_all(&output.stdout)?;
            io::stderr().write_all(&output.stderr)?;

            output = std::process::Command::new("ip")
                .args(&["route", "show", "default"])
                .output()?;
            io::stdout().write_all(&output.stdout)?;
            io::stderr().write_all(&output.stderr)?;

            // output = std::process::Command::new("nslookup")
            //     .arg("baidu.com")
            //     .output()?;
            // std::io::stdout().write_all(&output.stdout).unwrap();
            // std::io::stderr().write_all(&output.stderr).unwrap();
            
            // output = std::process::Command::new("nslookup")
            //     .arg("jumpjk.teld.cn")
            //     .output()?;
            // std::io::stdout().write_all(&output.stdout).unwrap();
            // std::io::stderr().write_all(&output.stderr).unwrap();
            println!()
        }
        Some(("list", _)) => {
            let output = std::process::Command::new("nmcli")
                .arg("d")
                .output()?;
                std::io::stdout().write_all(&output.stdout)?;
                std::io::stderr().write_all(&output.stderr)?;
        }
        Some(("show", show_mathces)) => {
            let iface:Option<&String> = show_mathces.get_one("interface");
            if let Some(ifname) = iface {
                let output = std::process::Command::new("nmcli")
                    .args(&["-g", "NAME", "connection"])
                    .output()?;
                if output.status.success() {
                    let stdout = String::from_utf8(output.stdout)?;
                    if !stdout.contains(ifname) {
                        return Err(anyhow!("no configuration for {ifname} was found"))
                    }
                    for line in stdout.lines() {
                        if line.contains(ifname) {
                            pretty_show_connections(line)?;
                        }
                    }
                }
            } else {
                let output = std::process::Command::new("nmcli")
                    .args(&["-g", "all"])
                    .output()?;
                std::io::stdout().write_all(&output.stdout)?;
                std::io::stderr().write_all(&output.stderr)?;
            }
        }
        Some(("prio", prio_matches)) => {
            // some problem
            let iface:Option<&String> = prio_matches.get_one("interface");
            list_prio()?;
            let default_via = get_default_via_dev()?;
            println!("\n当前上网网口: {}\n", default_via);
            if prio_matches.get_flag("show") {
                return Ok(());
            }
            if let Some(ifname) = iface {
                set_prio(ifname)?;
            } else {
                set_prio(&choose_prio()?)?;
            }
        }
        None => {
            // wizzard mode
            wizard()?
        }
        _ => {
            println!("unknown subcommand")
        }
    }
    Ok(())
}

fn eths_in_ord () -> Result<Vec<String>> {
    let output = std::process::Command::new("nmcli")
        .args(&["-g", "DEVICE", "device"])
        .output()
        .expect("fail to execute nmcli");
    if !output.status.success() {
        return Err(anyhow!("Failed to execute nmcli command"));
    }
    let dev_names = String::from_utf8(output.stdout)?;
    let mut eth_devs: Vec<&str> = dev_names.lines().filter(|line| line.contains("eth")).collect();
    eth_devs.sort_unstable_by(|a, b| {
        let re = Regex::new(r"eth(\d+)").unwrap();
        let cap_a = re.captures(a).unwrap();
        let cap_b = re.captures(b).unwrap();
        let num_a: usize = cap_a[1].parse().unwrap();
        let num_b: usize = cap_b[1].parse().unwrap();
        num_a.cmp(&num_b)
    });
    Ok(eth_devs.iter().map(|s| s.to_string()).collect())
}

fn wizard() -> Result<()> {
    let eths:Vec<String> = eths_in_ord()?;
    let iface = prompt_for_input::<String>("请选择配置的网口: ", Some(&eths.iter().map(|s| s.as_str()).collect::<Vec<&str>>()), Some(DefaultVal::Index(1)))?;
    let method = prompt_for_input::<String>("请选择配置方式: ", Some(&vec!["dhcp", "static"]), Some(DefaultVal::Index(1)))?;
    let mut ip4 = None;
    let mut netmask = None;
    let mut gateway = None;

    if method == "static" {
        loop {
            // ip4 = Some(prompt_for_input::<Ipv4Network>("请输入IP地址: ", None, Some(DefaultVal::Str("*************")))?);
            let iplist = prompt_for_input::<String>("请输入IP地址: ", None, Some(DefaultVal::Str("*************")))?;
            let mut temp = Vec::new();
            for ip in iplist.split(',') {
                match ip.parse::<Ipv4Network>() {
                    Ok(_) => temp.push(ip),
                    Err(e) => {
                        println!("{}",e);
                        temp.clear();
                        break;
                    },
                };
            }
            ip4 = Some(temp.join(","));
            if !ip4.as_ref().unwrap().to_string().is_empty() {break}
        }

        if !ip4.as_ref().unwrap().contains("/") && !ip4.as_ref().unwrap().contains(",") {
            let first_byte = ip4.as_ref().unwrap().to_string().trim().split('.').next().unwrap().parse::<u8>()?;
            let dv:&str;
            if first_byte < 128 {
                dv = "*********";
            } else if first_byte < 192 {
                dv = "***********";
            } else {
                dv = "*************";
            }
            netmask = Some(prompt_for_input::<Ipv4Addr>("请输入子网掩码: ", None, Some(DefaultVal::Str(dv)))?);
        }
        gateway = Some(prompt_for_input::<Ipv4Addr>("请输入网关地址: ", None, None)?);
    }

    pretty_show_configuration(&iface, &method, &ip4, &netmask, &gateway);
    
    let mut default_via = None;
    if let Ok(dv) = get_default_via_dev() {
        if dv == iface {
            println!("当前配置的网口为默认上网网口，请重新选择优先上网网口");
            default_via = match choose_prio() {
                Ok(dv) => Some(dv),
                Err(e) => {
                    eprintln!("choose prio failed, {}", e);
                    None
                }
            }
        }
    }
    let confirm = prompt_for_input::<bool>("是否应用该配置？", Some(&vec!["yes", "no"]), Some(DefaultVal::Index(0)));
    if confirm.as_ref().unwrap() == "yes" {
        let mut args = vec!["set".to_string(), "-i".to_string(), iface, "-m".to_string(), method];
        if let Some(mask) = netmask {
            args.push("--netmask".to_string());
            args.push(mask);
        }
        if let Some(a) = ip4 {
            args.push("-a".to_string());
            args.push(a);
        }
        if let Some(gw) = gateway {
            args.push("-g".to_string());
            args.push(gw);
        }

        let mut output = std::process::Command::new(std::env::current_exe()?)
            .args(&args)
            .output()?;
        println!("{}", String::from_utf8(output.stdout)?);
        if let Some(dv) = default_via {
            set_prio(&dv)?;
        }
        output = std::process::Command::new(std::env::current_exe()?)
        .args(vec!["prio", "-s"])
        .output()?;
        println!("{}", String::from_utf8(output.stdout)?);
    }
    Ok(())
}
#[derive(Debug)]
enum DefaultVal<'a> {
    Index(usize),
    Str(&'a str),
}
impl <'a> std::fmt::Display for DefaultVal<'a> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DefaultVal::Index(n) => write!(f, "{}", n),
            DefaultVal::Str(s) => write!(f, "{}", s),
        }
    }
}
fn prompt_for_input<T>(prompt: &str, choices:Option<&[&str]>, default_val:Option<DefaultVal>) -> Result<String>
where 
    T: std::str::FromStr + std::fmt::Display,
{
    let mut buffer = String::new();
    loop {
        println!("{}", prompt);
        if let Some(choices) = choices {
            for (i, choice) in choices.iter().enumerate() {
                println!("  {}. {}", i + 1, choice);
            }
        }
        if let Some(dv) = default_val.as_ref() {
            print!("  (默认值: ");
            match dv {
                DefaultVal::Index(n) => print!("{})", n+1),
                DefaultVal::Str(s) => print!("{})", s),
            }
        } else {
            print!("(可选)");
        }
        print!(": ");
        io::stdout().flush()?;
        buffer.clear();

        if choices.is_some_and(|c| c.len() == 1) {
            let choice = choices.unwrap()[0];
            println!("{}", choice);
            return Ok(choice.to_string());
        }
        
        io::stdin().read_line(&mut buffer)?;
        // early return if empty
        if buffer.trim().is_empty() && default_val.is_some() {
            match default_val.unwrap() {
                DefaultVal::Index(n) => {
                    if choices.is_some() {
                        println!("  使用默认参数: {}. {}", n + 1, choices.unwrap().get(n).expect("default value out of range"));
                        return Ok(choices.unwrap()[n].to_string());
                    } else {
                        println!("  使用默认参数: {}", n);
                        return Ok(n.to_string());
                    }
                }
                DefaultVal::Str(s) => {
                    println!("  使用默认参数: {}", s);
                    return Ok(s.to_string());
                }
            }
        }
        if let Some(choices) = choices {
            match buffer.trim().parse::<usize>() {
                Ok(n) if n >= 1 && n <= choices.len() => return Ok(choices[n - 1].to_string()),
                _ => println!("无效的输入，请重新输入。")
            }
        } else {
            // if empty, return
            if buffer.trim().is_empty() {
                return Ok("".to_string());
            }
            match buffer.trim().parse::<T>() {
                Ok(_) => return Ok(buffer.trim().to_string()),
                Err(_) => println!("无效的输入，请重新输入。"),
            }
        }
    }
}
fn pretty_show_connections(name:&str) -> Result<()> {
    println!("Showing connection: {}", name);
    let output = std::process::Command::new("nmcli")
        .args(&["connection", "show", name])
        .output()?;
    if !output.status.success() {
        return Err(anyhow!("Failed to show connection: {}", name));
    }
    /* let stdout = String::from_utf8(output.stdout)?;
    for line in stdout.lines() {
        let re = Regex::new(r"[A-Z]").unwrap();
        if line.contains("ipv4") || re.is_match(line) {
            println!("{}", line);
        }
    } */
    let re = Regex::new(&(String::from(r"(connection.id|connection.uuid|connection.type|connection.interface-name|connection.autoconnect|connection.autoconnect-priority")
                                    +r"|ipv4.method|ipv4.addresses|ipv4.gateway"
                                +r"|GENERAL.STATE|IP4\..+|DHCP.+):\s*(.*)")).unwrap();
    let stdout = String::from_utf8(output.stdout)?;
    for line in stdout.lines() {
        if re.is_match(line) {
            println!("{}", line);
        }
    }
    Ok(())
}
fn pretty_show_configuration(iface:&String, method:&String, ip4:&Option<String>, netmask:&Option<String>, gateway:&Option<String>) {
    println!();
    println!("-------------------------------");
    println!("生成配置信息: ");
    println!("  配置网口： {iface}");
    println!("  配置方式： {method}");
    if let Some(ip) = ip4 {
        println!("  IP地址: {ip}");
    }
    if let Some(mask) = netmask {
        println!("  子网掩码: {mask}");
    }
    if let Some(gw) = gateway {
        println!("  网关地址: {gw}");
    }
    println!("-------------------------------")
}
fn choose_prio () -> Result<String> {
    let eths = eths_in_ord()?;
    let net_devs = [vec!["usb0"], eths.iter().map(|s| s.as_str()).collect::<Vec<&str>>()].concat();
    prompt_for_input::<bool>("选择优先上网网口: ", Some(&net_devs), Some(DefaultVal::Index(0)))
}
fn set_prio(iface:&str) ->Result<()> {
    let mut metrics = vec![];
    let metric;
    let eths = eths_in_ord()?;
    let output = std::process::Command::new("ip")
        .arg("route")
        .output()
        .expect("fail to execute ip route");
        if output.status.success() {
            let stdout = String::from_utf8(output.stdout)?;
            let re = Regex::new(r"metric (\d+)").unwrap();
            for line in stdout.lines() {
                if !line.contains(&iface){
                    if let Some(cap) = re.captures(line) {
                        metrics.push(cap[1].trim().parse::<u64>().expect(&format!("parse \"{}\" to u64 failed", cap[1].trim())));
                    }
                }
            }
        } else {
            return Err(anyhow!("excute ip route"));
        }
        let conf = std::fs::read_to_string("/etc/NetworkManager/conf.d/eth.conf")?;
        let re = Regex::new(r"ipv4\.route-metric=(\d+)").unwrap();
        for cap in re.captures_iter(&conf) {
            if let Ok(m) = cap[1].parse::<u64>() {
                metrics.push(m);
            }
        }
        metrics.sort_unstable();
        //println!("{:?}", metrics);
        metric = match metrics.get(0) {
            Some(m) => (m).checked_sub(1),
            _ => None
        };

        for eth in eths {
            for uuid in iface_get_uuid(&eth)? {

                let m = if eth == iface && metric.is_some() {
                    metric.unwrap().to_string()
                } else {
                    "-1".to_string()
                };
                let output = std::process::Command::new("nmcli")
                    .args(&["connection", "modify", &uuid, "ipv4.route-metric", &m])
                    .output()?;
                if !output.status.success() {
                    return Err(anyhow!("Failed to set metric for {}", eth));
                }
                std::process::Command::new("nmcli")
                    .args(&["c", "up", &uuid])
                    .output()?;
                if !output.status.success() {
                    return Err(anyhow!("{}:{}配置成功，未生效。{}", eth, uuid, String::from_utf8(output.stderr)?))
                }
            }
        }
    Ok(())
}
fn list_prio() -> Result<()> {
    let mut metrics = HashMap::<String, u64>::new();
    let re = Regex::new(r"\[connection-(.+?)\].+?ipv4\.route-metric=(\d+)").unwrap();
    let conf = std::fs::read_to_string("/etc/NetworkManager/conf.d/eth.conf")?.replace("\n", "");
    for cap in re.captures_iter(&conf) {
        // println!("{} {}", &cap[1], &cap[2]);
        if let Ok(m) = cap[2].parse::<u64>() {
            metrics.entry(cap[1].to_string()).or_insert(m);
        }
    }
    let eths = eths_in_ord()?;
    for eth in eths {
        for uuid in iface_get_uuid(&eth)? {
            let output = std::process::Command::new("nmcli")
                .args(&["-g", "ipv4.route-metric", "connection", "show", &uuid])
                .output()?;
            if output.status.success() {
                let metric: i32 = String::from_utf8(output.stdout)?.trim().parse()?;
                if metric != -1 {
                    metrics.entry(eth.clone()).and_modify(|m| *m = metric as u64);
                }
            }

        }
    }
    let mut list = metrics.iter().collect::<Vec<(&String, &u64)>>();
    list.sort_unstable_by(|a, b| a.1.cmp(b.1));
    println!("当前优先级顺序(高到低)：");
    println!("{:<10}{:<10}", "dev", "metric");
    println!("--------------------");
    for (eth, metric) in list {
        println!("{:<10}{:<10}", eth, metric);
    }

    Ok(())
}
fn get_default_via_dev() -> Result<String> 
{
    let output = std::process::Command::new("ip")
        .arg("route")
        .output();
    match output {
        Ok(o) => {
            if o.status.success() {
                let stdout = String::from_utf8(o.stdout)?;
                let re = Regex::new(r"default via .+ dev (\w+)")?;
                if let Some(cap) = re.captures(&stdout) {
                    return Ok(cap[1].trim().to_string());
                } else {
                    // return Err(anyhow!("Failed to parse default via device"));
                    return Ok("无".to_string())
                }
            } else {
                return Err(anyhow!("Failed to execute ip route"));
            }
        }
        Err(e) => Err(anyhow!("Failed to execute ip route: {}", e))
    }
}
#ifndef __QXDM_DEFAULT_CFG_H__
#define __QXDM_DEFAULT_CFG_H__

char qxdm_default_cfg_buf[] = {
    0x1D, 0x1C, 0x3B, 0x7E, 0x00, 0x78, 0xF0, 0x7E, 0x4B, 0x32, 0x06, 0x00, 0xBA, 0x4D, 0x7E, 0x7C, 
    0x93, 0x49, 0x7E, 0x1C, 0x95, 0x2A, 0x7E, 0x0C, 0x14, 0x3A, 0x7E, 0x63, 0xE5, 0xA1, 0x7E, 0x4B, 
    0x0F, 0x00, 0x00, 0xBB, 0x60, 0x7E, 0x4B, 0x09, 0x00, 0x00, 0x62, 0xB6, 0x7E, 0x4B, 0x08, 0x00, 
    0x00, 0xBE, 0xEC, 0x7E, 0x4B, 0x08, 0x01, 0x00, 0x66, 0xF5, 0x7E, 0x4B, 0x04, 0x00, 0x00, 0x1D, 
    0x49, 0x7E, 0x4B, 0x04, 0x0F, 0x00, 0xD5, 0xCA, 0x7E, 0x4B, 0x0F, 0x18, 0x00, 0x01, 0x9E, 0xA9, 
    0x7E, 0x4B, 0x0F, 0x18, 0x00, 0x02, 0x05, 0x9B, 0x7E, 0x4B, 0x0F, 0x2C, 0x00, 0x28, 0xEA, 0x7E, 
    0x4B, 0x12, 0x39, 0x00, 0xEB, 0x7B, 0x7E, 0x4B, 0x12, 0x3C, 0x00, 0x53, 0x05, 0x7E, 0x4B, 0x12, 
    0x37, 0x00, 0xFB, 0xE1, 0x7E, 0x4B, 0x12, 0x3B, 0x00, 0x5B, 0x48, 0x7E, 0x4B, 0x12, 0x35, 0x00, 
    0x4B, 0xD2, 0x7E, 0x4B, 0x12, 0x3A, 0x00, 0x83, 0x51, 0x7E, 0x4B, 0x12, 0x00, 0x08, 0x19, 0x96, 
    0x7E, 0x7D, 0x5D, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x74, 0x41, 0x7E, 0x73, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0xDA, 0x81, 0x7E, 0x60, 0x00, 0x12, 0x6A, 0x7E, 0x73, 0x00, 0x00, 
    0x00, 0x03, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0xD9, 0x0C, 0x00, 0x00, 0xF0, 0x01, 0xC8, 
    0x00, 0x00, 0x40, 0xC4, 0x00, 0x00, 0x33, 0x0F, 0xC0, 0x49, 0xF3, 0xC7, 0x5B, 0x7C, 0xF3, 0x0B, 
    0x01, 0x00, 0x00, 0x00, 0x20, 0xEC, 0x00, 0xCC, 0x83, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 
    0x00, 0x38, 0x00, 0x38, 0x00, 0x38, 0x00, 0x00, 0x01, 0x01, 0x00, 0x40, 0x18, 0xF0, 0x07, 0x0C, 
    0xF8, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x04, 0x00, 0xFF, 0xF7, 0x7F, 0xF0, 0xFC, 
    0xFF, 0xFF, 0xAD, 0xE0, 0x7F, 0x02, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x78, 0xE0, 0xFF, 0xFF, 0xFF, 0x40, 0x1C, 0x1E, 0x00, 0x00, 0x00, 0x00, 0xFB, 0xFF, 
    0xFF, 0xFF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x18, 0x00, 0x00, 0x80, 0x1B, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0xC1, 0x09, 0x41, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x01, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xCE, 0x00, 0x00, 0x01, 0x00, 
    0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x0C, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0xF8, 0x07, 
    0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0xD2, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 
    0x40, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x0A, 0x30, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x06, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x16, 
    0x07, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x40, 0x00, 0x00, 0x08, 0x88, 0x00, 0x04, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0xC0, 0x07, 0xC0, 0x3F, 0x40, 0x00, 
    0x00, 0x00, 0x00, 0x40, 0x08, 0x00, 0x04, 0x00, 0x01, 0xBC, 0xB7, 0x7E, 0x73, 0x00, 0x00, 0x00, 
    0x03, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x0B, 0x08, 0x00, 0x00, 0x35, 0x00, 0x09, 0x80, 
    0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xEF, 0xA2, 
    0xF8, 0x90, 0x3C, 0x1D, 0x60, 0x04, 0x00, 0x00, 0x4F, 0x03, 0xFE, 0x07, 0x43, 0x0B, 0x0A, 0x01, 
    0x00, 0x04, 0x87, 0xF4, 0xC7, 0x5F, 0xB8, 0x73, 0x00, 0x00, 0x00, 0x00, 0x86, 0x8A, 0x4D, 0xF8, 
    0x25, 0x14, 0x00, 0xCC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x82, 0xC3, 0x1E, 0x30, 
    0x7D, 0x5E, 0x00, 0x4E, 0x00, 0xFF, 0x03, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x07, 0x1C, 
    0x46, 0x7E, 0x73, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x85, 0x0D, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x9C, 
    0xFF, 0x3B, 0xFC, 0x03, 0x73, 0xD3, 0x0D, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD0, 0xFF, 0xD7, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0xF0, 0xFF, 0xFF, 0xFF, 0xBF, 0xFB, 0x4F, 0x7F, 0xBF, 0x00, 0xE0, 0xE3, 0x0F, 0xFF, 0x0D, 0x00, 
    0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x01, 0x00, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x90, 
    0x6F, 0x3F, 0xFC, 0x71, 0x02, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD0, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x18, 0xF8, 0xE7, 0xFF, 0xFF, 0xCF, 0x7F, 0xBF, 0x00, 0xE0, 0xE3, 0x0F, 0xFF, 0x0D, 0x00, 
    0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x10, 0xEA, 0xED, 0x7E, 0x73, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x07, 0x00, 
    0x00, 0x00, 0x57, 0x0B, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x7F, 0x00, 0x00, 
    0x7F, 0x02, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0x00, 0x06, 0x0A, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 
    0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 
    0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x77, 0x00, 0x00, 
    0x7F, 0x4A, 0x09, 0x7E, 0x73, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 
    0xC1, 0x09, 0x00, 0x00, 0x06, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x5F, 0x4C, 0x00, 0x00, 0xCE, 0x36, 0xCE, 0x00, 0x3F, 0x01, 0x3F, 0x00, 0xDF, 0xA4, 0x00, 0x00, 
    0x7F, 0xFC, 0x3C, 0x00, 0x00, 0x00, 0x3E, 0x28, 0x6E, 0x56, 0x15, 0x12, 0x71, 0xE0, 0x00, 0x08, 
    0xFF, 0xFF, 0xFF, 0xE2, 0xEF, 0xED, 0x7D, 0x5D, 0x59, 0x7F, 0x08, 0x01, 0x74, 0x42, 0xE0, 0x00, 
    0x97, 0x28, 0x84, 0x2C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xDF, 0x13, 0x00, 0xFE, 0x1D, 0x00, 0x00, 0x0F, 0x78, 0xC0, 
    0x01, 0x07, 0x03, 0x0F, 0x00, 0xFF, 0xFF, 0xCB, 0x78, 0xDB, 0x7D, 0x5D, 0x00, 0x00, 0xF1, 0xEF, 
    0x0F, 0x66, 0xA5, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x67, 0x02, 0xFF, 0xFF, 0xBF, 0xD2, 0x62, 0xCB, 0x05, 0x00, 0x08, 0x00, 0x00, 0x02, 0x01, 0x7C, 
    0x9C, 0x7E, 0x73, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x0D, 0x00, 0x00, 0x00, 0x39, 0x01, 
    0x00, 0x00, 0x79, 0xA6, 0xFF, 0xFF, 0xFF, 0xA1, 0x1F, 0x00, 0xDF, 0x01, 0x00, 0x00, 0x03, 0x40, 
    0x00, 0x00, 0x00, 0x9F, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x06, 0x00, 0x00, 0x00, 0x00, 0x5F, 0x00, 
    0x00, 0x00, 0xFF, 0x37, 0xFF, 0xBF, 0x1B, 0x05, 0x00, 0x01, 0x2B, 0x17, 0x7E, 0x60, 0x01, 0x9B, 
    0x7B, 0x7E, 0x82, 0x00, 0x00, 0x00, 0xCE, 0x0C, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0x00, 0x80, 0xFF, 0xFF, 0x7F, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0x7F, 0xFF, 0xFF, 0x07, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0F, 0xC0, 0xFF, 0xFF, 
    0xFF, 0xFF, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
    0xFF, 0x3F, 0x0C, 0x79, 0x7E, 0x7D, 0x5D, 0x04, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x4C, 0x06, 0x7E, 0x7D, 0x5D, 0x04, 
    0x05, 0x00, 0x05, 0x00, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0xCE, 0xA7, 0x7E, 0x7D, 0x5D, 0x04, 
    0x07, 0x00, 0x08, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xD0, 0x71, 
    0x7E, 0x7D, 0x5D, 0x04, 0x0B, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x7C, 0x68, 0x7E, 0x7D, 0x5D, 0x04, 0x0E, 0x00, 0x12, 0x00, 0x00, 0x00, 0xFF, 0x01, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1F, 0x00, 
    0x00, 0x00, 0x0E, 0x26, 0x7E, 0x7D, 0x5D, 0x04, 0x14, 0x00, 0x16, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0xED, 0x14, 0x7E, 0x7D, 0x5D, 0x04, 
    0x19, 0x00, 0x19, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x88, 0xD0, 0x7E, 0x7D, 0x5D, 0x04, 
    0x20, 0x00, 0x20, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xEA, 0xAA, 0x7E, 0x7D, 0x5D, 0x04, 
    0x27, 0x00, 0x28, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xFE, 0x01, 0x00, 0x00, 0x89, 0x11, 
    0x7E, 0x7D, 0x5D, 0x04, 0x2A, 0x00, 0x2B, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x01, 0x1A, 0x7E, 0x7D, 0x5D, 0x04, 0x32, 0x00, 0x33, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x3A, 0x20, 0x7E, 0x7D, 0x5D, 0x04, 0x36, 0x00, 0x36, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xA3, 0xD6, 0x7E, 0x7D, 0x5D, 0x04, 0x39, 0x00, 0x3A, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x65, 0xDF, 0x7E, 0x7D, 0x5D, 0x04, 
    0x3F, 0x00, 0x42, 0x00, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0xFE, 0xFF, 0x1F, 0x00, 0x1F, 0x00, 
    0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x9F, 0xAF, 0x7E, 0x7D, 0x5D, 0x04, 0x44, 0x00, 0x45, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x2E, 0x6C, 0x7E, 0x7D, 0x5D, 0x04, 
    0x48, 0x00, 0x4A, 0x00, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x4F, 0xF2, 0x7E, 0x7D, 0x5D, 0x04, 0x4C, 0x00, 0x4C, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0xF2, 0x66, 0x7E, 0x7D, 0x5D, 0x04, 0x4E, 0x00, 0x4E, 0x00, 0x00, 0x00, 0x07, 0x00, 
    0x00, 0x00, 0x11, 0x0F, 0x7E, 0x7D, 0x5D, 0x04, 0x53, 0x00, 0x53, 0x00, 0x00, 0x00, 0x10, 0x00, 
    0x00, 0x00, 0xF4, 0x5D, 0x7E, 0x7D, 0x5D, 0x04, 0x58, 0x00, 0x58, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x9A, 0x49, 0x7E, 0x7D, 0x5D, 0x04, 0x5A, 0x00, 0x5B, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x0E, 0x43, 0x7E, 0x7D, 0x5D, 0x04, 0x5D, 0x00, 0x5D, 0x00, 
    0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0xFB, 0x5E, 0x7E, 0x7D, 0x5D, 0x04, 0x63, 0x00, 0x63, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xD9, 0x60, 0x7E, 0x7D, 0x5D, 0x04, 0x70, 0x00, 0x70, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x4A, 0x17, 0x7E, 0x7D, 0x5D, 0x04, 0xEA, 0x03, 0xEA, 0x03, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xA9, 0x9E, 0x7E, 0x7D, 0x5D, 0x04, 0xEE, 0x03, 0xEF, 0x03, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x9B, 0x20, 0x7E, 0x7D, 0x5D, 0x04, 
    0xD0, 0x07, 0xD7, 0x07, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xDC, 0x10, 0x7E, 0x7D, 0x5D, 0x04, 0xB8, 0x0B, 0xC5, 0x0B, 
    0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0xFE, 0xFF, 0x7F, 0x00, 0x7F, 0x00, 0x00, 0x00, 0x1F, 0x00, 
    0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0xA9, 0x36, 0x7E, 0x7D, 0x5D, 0x04, 
    0xA0, 0x0F, 0xAA, 0x0F, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xFE, 0x01, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xFE, 0xFF, 
    0x01, 0x00, 0xFE, 0xFF, 0x07, 0x00, 0xFE, 0xFF, 0x01, 0x00, 0xFE, 0x07, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0xC3, 0xB9, 0x7E, 0x7D, 0x5D, 0x04, 0x01, 0x12, 0x01, 0x12, 0x00, 0x00, 0x1F, 0x00, 
    0x00, 0x00, 0x90, 0xE7, 0x7E, 0x7D, 0x5D, 0x04, 0x04, 0x12, 0x05, 0x12, 0x00, 0x00, 0x01, 0x00, 
    0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0xBD, 0x05, 0x7E, 0x7D, 0x5D, 0x04, 0x07, 0x12, 0x07, 0x12, 
    0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0xF3, 0x12, 0x7E, 0x7D, 0x5D, 0x04, 0x88, 0x13, 0xA8, 0x13, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xF4, 0x4C, 0x7E, 0x7D, 0x5D, 0x04, 0xAA, 0x13, 0xAA, 0x13, 
    0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0xC5, 0xFC, 0x7E, 0x7D, 0x5D, 0x04, 0x7F, 0x15, 0x7F, 0x15, 
    0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, 0x91, 0x4C, 0x7E, 0x7D, 0x5D, 0x04, 0x72, 0x17, 0x72, 0x17, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xCC, 0x20, 0x7E, 0x7D, 0x5D, 0x04, 0x74, 0x17, 0x74, 0x17, 
    0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x47, 0x46, 0x7E, 0x7D, 0x5D, 0x04, 0x93, 0x17, 0x93, 0x17, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x8F, 0xCA, 0x7E, 0x7D, 0x5D, 0x04, 0x97, 0x17, 0x97, 0x17, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xCD, 0x6C, 0x7E, 0x7D, 0x5D, 0x04, 0xA4, 0x17, 0xB7, 0x17, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x8D, 0xD1, 0x7E, 0x7D, 0x5D, 0x04, 0xC0, 0x17, 0xC0, 0x17, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x96, 0x89, 0x7E, 0x7D, 0x5D, 0x04, 0x34, 0x21, 0x34, 0x21, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x10, 0xC3, 0x7E, 0x7D, 0x5D, 0x04, 0x1C, 0x25, 0x25, 0x25, 0x00, 0x00, 0x1E, 0x00, 
    0x00, 0x00, 0x1E, 0xFF, 0x1F, 0x00, 0x7D, 0x5E, 0x00, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, 0x7D, 
    0x5E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xFE, 0x03, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 
    0x1E, 0x00, 0x00, 0x00, 0x3E, 0xD0, 0x00, 0x00, 0x90, 0xED, 0x7E, 0x7D, 0x5D, 0x04, 0x0B, 0x28, 
    0x0F, 0x28, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 
    0x1C, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x71, 0x86, 0x7E, 0x7D, 0x5D, 0x04, 0x6E, 0x28, 
    0x89, 0x28, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 
    0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 
    0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 
    0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 
    0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 
    0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 
    0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 
    0x1E, 0x00, 0x00, 0x00, 0x52, 0x90, 0x7E, 0x4B, 0x44, 0x01, 0x90, 0x26, 0x00, 0x00, 0x00, 0x01, 
    0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x23, 0x00, 0x00, 0x00, 0x09, 0x00, 0x1E, 0x00, 0x0B, 
    0x00, 0x03, 0x00, 0x12, 0x00, 0x01, 0x00, 0x2A, 0x00, 0x07, 0x00, 0x2D, 0x00, 0x03, 0x00, 0x2F, 
    0x00, 0x07, 0x00, 0x34, 0x00, 0x0F, 0x00, 0x35, 0x00, 0x1F, 0x00, 0x36, 0x00, 0x03, 0x00, 0x3D, 
    0x00, 0x03, 0x00, 0x40, 0x00, 0x03, 0x00, 0x49, 0x00, 0x0F, 0x00, 0x4C, 0x00, 0x7F, 0x00, 0x4E, 
    0x00, 0x02, 0x00, 0x50, 0x00, 0x03, 0x00, 0x53, 0x00, 0x3F, 0x00, 0x54, 0x00, 0x0F, 0x00, 0x55, 
    0x00, 0x0F, 0x00, 0x56, 0x00, 0x03, 0x00, 0x57, 0x00, 0x03, 0x00, 0x58, 0x00, 0x01, 0x00, 0x59, 
    0x00, 0x0F, 0x00, 0x5E, 0x00, 0x01, 0x00, 0x60, 0x00, 0x07, 0x00, 0x61, 0x00, 0x03, 0x00, 0x62, 
    0x00, 0x0F, 0x00, 0x63, 0x00, 0x03, 0x00, 0x64, 0x00, 0x1F, 0x00, 0x65, 0x00, 0x03, 0x00, 0x68, 
    0x00, 0x03, 0x00, 0x69, 0x00, 0x0F, 0x00, 0x6B, 0x00, 0x03, 0x00, 0x6C, 0x00, 0x03, 0x00, 0x6D, 
    0x00, 0x03, 0x00, 0x6E, 0x00, 0x03, 0x00, 0x64, 0x79, 0x7E, 
};

#endif

#------------------------------------------------------------------------------#
# This makefile was generated by 'cbp2make' tool rev.147                       #
#------------------------------------------------------------------------------#

WINDRES = windres

INC = -I./inc
CFLAGS = -Wall -fexceptions
RESINC = 
LIBDIR = 
LIB = 
LDFLAGS = -pthread

INC_DEBUG = $(INC)
CFLAGS_DEBUG = $(CFLAGS)
RESINC_DEBUG = $(RESINC)
RCFLAGS_DEBUG = $(RCFLAGS)
LIBDIR_DEBUG = $(LIBDIR)
LIB_DEBUG = $(LIB)
LDFLAGS_DEBUG = $(LDFLAGS)
OBJDIR = obj/
OUTDIR = lib/
DEP_DEBUG = 
OUT_DEBUG = $(OUTDIR)libxlog.a

SRCDIR = ./src/
CPP_SRC_FILES = $(wildcard $(SRCDIR)*.cpp)
C_SRC_FILES = $(wildcard $(SRCDIR)*.c)  
#OBJ_CPP_DEBUG = $(patsubst %.cpp,%.o,$(CPP_SRC_FILES))
OBJDIR_DEBUG = $(OBJDIR)
OBJ_CPP_DEBUG = $(notdir $(patsubst %.cpp, %.o , $(CPP_SRC_FILES)))
OBJ_CPP_DEBUG_PREFIX = $(addprefix $(OBJDIR),$(OBJ_CPP_DEBUG))
OBJ_C_DEBUG = $(notdir $(patsubst %.c, %.o , $(C_SRC_FILES)))
OBJ_C_DEBUG_PREFIX = $(addprefix $(OBJDIR),$(OBJ_C_DEBUG))

all: debug

clean: clean_debug clean_release

before_debug: 
	test -d $(OBJDIR_DEBUG) || mkdir -p $(OBJDIR_DEBUG)
	test -d $(OUTDIR) || mkdir -p $(OUTDIR)

after_debug: 

debug: before_debug out_debug after_debug

out_debug: before_debug $(OBJ_CPP_DEBUG) $(OBJ_C_DEBUG) $(DEP_DEBUG)
	$(AR) rc $(LIBDIR_DEBUG) $(OUT_DEBUG) $(OBJ_CPP_DEBUG_PREFIX) $(OBJ_C_DEBUG_PREFIX) $(LIB_DEBUG)

$(OBJ_CPP_DEBUG) : %.o : $(SRCDIR)%.cpp
	$(CXX) -c $< -o $(OBJDIR)$@ $(CFLAGS) $(INC_DEBUG)

$(OBJ_C_DEBUG) : %.o : $(SRCDIR)%.c
	$(CC) -c $< -o $(OBJDIR)$@ $(CFLAGS) $(INC_DEBUG)

clean_debug: 
	rm -rf $(OBJDIR_DEBUG)

.PHONY: before_debug after_debug clean_debug before_release after_release clean_release


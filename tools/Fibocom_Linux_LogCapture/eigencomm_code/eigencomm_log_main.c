#include<stdio.h>
#include<stdlib.h>
#include<string.h>
#include<sys/types.h>
#include<sys/stat.h>
#include<fcntl.h>
#include<unistd.h>
#include<termios.h>
#include<string.h>
#include<signal.h>
#include "fifo.h"
#include "misc_usb.h"
#include "eigencomm_devices_list.h"
#include "log_control.h"


#define LOG_BUFFER_SIZE     (32*1024) // 32K
#define LOG_WRITE_BUFFER_SIZE (128*1024)
#define FIFO_BUFFER_SIZE    (512*1024)

static uint8_t s_readbuf[LOG_BUFFER_SIZE] = {0};
static uint8_t s_writebuf[LOG_WRITE_BUFFER_SIZE] = {0};

static volatile int s_log_process = 0;


static char s_logpath[FIBO_BUF_SIZE+EXTEND] = ".";
static int single_logfile_size = 50*1024*1024;
static struct fifo *plog_fifo = NULL;
static int s_max_file_num = 0;

#define MAX_BUFF_SIZE 128*1024
char buf_read[MAX_BUFF_SIZE];

fibo_usbdev_t *eigencomm_find_devices_in_table(int idvendor, int idproduct)
{
    int i, size = 0;

    size = sizeof(eigencomm_devices_table)/sizeof(eigencomm_devices_table[0]);
    for (i=0; i<size; i++)
    {
        fibo_usbdev_t *udev = &eigencomm_devices_table[i];

        if ((udev->idVendor == idvendor) && (udev->idProduct == idproduct)) {
            return udev;
        }
    }

    return NULL;
}

static void *thread_save_log_to_file(void *arg)
{
    uint32_t get_fifo_len = 0;
    int file_size = 0;
    FILE *p_logfile = NULL;
    char log_filename[MAX_PATH_LEN+2*EXTEND] = { 0 };
    arg = NULL;

    while (s_log_process)
    {
        if (p_logfile == NULL) {
            time_t t = time(NULL);
            struct tm *tm = localtime(&t);

            sprintf(log_filename, "%s%02d_%02d%02d_%02d%02d%02d.bin", s_logpath,
                1900+tm->tm_year, tm->tm_mon+1, tm->tm_mday, tm->tm_hour, tm->tm_min, tm->tm_sec);
            LogInfo("%s: filename: %s\n", __func__, log_filename);

            p_logfile = fopen(log_filename, "wb");
            if (p_logfile == NULL) {
                LogInfo("create log file failed, errno:%d(%s)\n", errno, strerror(errno));
                break;
            }

            log_storage_control(log_filename, s_max_file_num, 1);
        }
        else
        {
            if (file_size > single_logfile_size) {
                fclose(p_logfile);
                p_logfile = NULL;
                file_size = 0;
                continue;
            }
        }
        get_fifo_len = __fifo_len(plog_fifo);
        if (get_fifo_len > 0)
        {
            get_fifo_len = __fifo_get(plog_fifo, s_writebuf, LOG_WRITE_BUFFER_SIZE);
            fwrite(s_writebuf, get_fifo_len, 1, p_logfile);
            file_size += get_fifo_len;
        }
        else
        {
            usleep(1000);
        }
    }

    get_fifo_len = __fifo_len(plog_fifo);
    if (get_fifo_len > 0)
    {
        get_fifo_len = __fifo_get(plog_fifo, s_writebuf, LOG_WRITE_BUFFER_SIZE);
        fwrite(s_writebuf, get_fifo_len, 1, p_logfile);
    }

    if (p_logfile) {
        fclose(p_logfile);
        p_logfile = NULL;
    }

    return NULL;
}

static void fibo_exit_function(int msg)
{
    LogInfo("\n%s: %d\n", __func__, msg);
    log_storage_control(NULL, 0, 0);
    s_log_process = 0;

    sleep(1);
    signal(SIGINT, SIG_DFL); //Enable Ctrl+C to exit
}

int set_opt(int fd,int nSpeed, int nBits, char nEvent, int nStop)
{
    struct termios newtio,oldtio;
    if( tcgetattr( fd,&oldtio)  !=  0) {
        perror("tcgetattr error");
        return -1;
    }
    bzero( &newtio, sizeof( newtio ) );
    newtio.c_cflag  |=  CLOCAL | CREAD;
    newtio.c_cflag &= ~CSIZE;

    switch( nBits )
    {
        case 7:
            newtio.c_cflag |= CS7;
            break;
        case 8:
            newtio.c_cflag |= CS8;
            break;
    }

    switch( nEvent )
    {
        case 'O':
            newtio.c_cflag |= PARENB;
            newtio.c_cflag |= PARODD;
            newtio.c_iflag |= (INPCK | ISTRIP);
            break;
        case 'E':
                newtio.c_iflag |= (INPCK | ISTRIP);
                newtio.c_cflag |= PARENB;
                newtio.c_cflag &= ~PARODD;
                break;
        case 'N':
            newtio.c_cflag &= ~PARENB;
            break;
    }

    switch( nSpeed )
    {
        case 2400:
            cfsetispeed(&newtio, B2400);
            cfsetospeed(&newtio, B2400);
            break;
        case 4800:
            cfsetispeed(&newtio, B4800);
            cfsetospeed(&newtio, B4800);
            break;
        case 9600:
            cfsetispeed(&newtio, B9600);
            cfsetospeed(&newtio, B9600);
            break;
        case 115200:
            cfsetispeed(&newtio, B115200);
            cfsetospeed(&newtio, B115200);
            break;
        case 460800:
            cfsetispeed(&newtio, B460800);
            cfsetospeed(&newtio, B460800);
            break;
        default:
            cfsetispeed(&newtio, B9600);
            cfsetospeed(&newtio, B9600);
            break;
    }

    if( nStop == 1){
        newtio.c_cflag &=  ~CSTOPB;
    }else if ( nStop == 2 ){
        newtio.c_cflag |=  CSTOPB;
    }
    newtio.c_cc[VTIME]  = 0;
    newtio.c_cc[VMIN] = 0;
    tcflush(fd,TCIFLUSH);
    if((tcsetattr(fd,TCSANOW,&newtio))!=0)
    {
        LogInfo("set error");
        return -1;
    }
    return 0;
}

static void usage(char *arg)
{
    LogInfo("========================================\n");
    LogInfo("Usage:\n");
    LogInfo("%s <-d [diag port]> <-s [log save dir]> <-m [single logfile size(MB)]> <-n [max_log_filenum]>\n", arg);
    LogInfo("example: %s\n", arg);
    LogInfo("========================================\n");
}

int eigencomm_log_main(int argc, char **argv)
{
    int ret = -1, opt = -1;
    fibo_usbdev_t *pdev = NULL;
    char portname[FIBO_BUF_SIZE] = {0};
    char log_dir[FIBO_BUF_SIZE] = ".";
    pthread_t thread2;

    LogInfo("[%s] start\n", __func__);

    optind = 1;//must set to 1
    while ((opt = getopt(argc, argv, "d:s:m:n:h")) != -1)
    {
        switch (opt)
        {
            case 'd':
                strcpy(portname, optarg);
                break;
            case 's':
                strcpy(log_dir, optarg);
                break;
            case 'm':
                single_logfile_size = atoi(optarg)*1024*1024;
                break;
            case 'n':
                s_max_file_num = atoi(optarg);
                break;
            case 'h':
                usage(argv[0]);
                return 0;
            default:;
        }
    }

    {
        int i;
        time_t t = time(NULL);
        struct tm *tm = localtime(&t);

        sprintf(s_logpath, "%s/fibolog_%02d%02d%02d%02d%02d%02d/", log_dir,
            1900+tm->tm_year, tm->tm_mon+1, tm->tm_mday, tm->tm_hour, tm->tm_min, tm->tm_sec);
        LogInfo("%s: s_logpath: %s\n", __func__, s_logpath);

        for (i=1; i<sizeof(s_logpath) && s_logpath[i] != 0; i++)
        {
            if (s_logpath[i] == '\\'  || s_logpath[i] == '/' )
            {
                char str_dir[FIBO_BUF_SIZE] = {0};
                strcpy(str_dir, s_logpath);
                str_dir[i] = '\0';
                if (access(str_dir, 0)) {
                    mkdir(str_dir, 0777);
                    LogInfo("[%s] mkdir:%s\n", __func__, str_dir);
                }
            }
        }

    }

    pdev = fibo_get_fibocom_device(eigencomm_find_devices_in_table, portname, 0);
    if (pdev == NULL)
    {
        goto END;
    }

    if (fibo_usb_open(pdev, 0, 1)) {
        goto END;
    }

    ret = set_opt(pdev->ttyfd[0], 460800, 8, 'N', 1);
    if (ret == -1)
    {
    	LogInfo("Set %s failed! Exit!\n",pdev->portname);
    	goto END;
    }

    plog_fifo = fifo_alloc(FIFO_BUFFER_SIZE);
    if(plog_fifo == NULL)
    {
        LogInfo("fifo_alloc failed.\n");
        goto END;
    }
    printf("FIFO size is %d\r\n", plog_fifo->size);

    s_log_process = 1;

    if (pthread_create(&thread2, NULL, thread_save_log_to_file, NULL) < 0) {
        LogInfo("savelog pthread_create failed!\n");
        goto END;
    }

    signal(SIGINT, fibo_exit_function); //Ctrl+C
    signal(SIGTERM, fibo_exit_function); //Kill

    while (s_log_process)
    {
        int readlen = pdev->read(pdev, s_readbuf, LOG_BUFFER_SIZE, 0);
        if (readlen > 0)
        {
            if (readlen > __fifo_put(plog_fifo, s_readbuf, readlen))
            {
                LogInfo("warning, fifo buf is full now.\n");
            }

            /* print current status */
            {
                uint32_t cur_tick = 0, diff_tick = 0;
                static uint64_t read_bytes = 0, start_tick = 0;

                read_bytes += readlen;
                if (start_tick == 0) {
                    start_tick = time(NULL);
                }
                else
                {
                    cur_tick = time(NULL);
                    diff_tick = cur_tick - start_tick;
                    if (diff_tick >= 5)
                    {
                        LogInfo("read_bytes(%lu), tick(%u), %luB/s\n", read_bytes, diff_tick, read_bytes/diff_tick);
                        start_tick = cur_tick;
                        read_bytes = 0;
                    }
                }
            }
        }
        usleep(2000);
    }

    ret = 0;
END:
    fibo_usb_close(pdev, 0);
    fifo_free(plog_fifo);

    return ret;
}

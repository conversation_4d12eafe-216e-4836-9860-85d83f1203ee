
use std::path::PathBuf;

use anyhow::{anyhow, Ok, Result};
use clap::{value_parser, Arg, Command};

const FILE_PATH: &str = "/usr/lib/app-init.d";
const LINK_PATH: &str = "/etc/app-init.d";

fn main() -> Result<()> {
    let m = Command::new("app")
        .subcommand(Command::new("list").about("list all init scripts"))
        .subcommand(Command::new("enable").about("enable init script")
            .arg(Arg::new("name").help("name of the init script, 'all' for all scripts").required(true).index(1)
            .value_parser(value_parser!(String)))
        )
        .subcommand(Command::new("disable").about("disable init script")
            .arg(Arg::new("name").help("name of the init script, 'all' for all scripts").required(true).index(1)
            .value_parser(value_parser!(String)))
        )
        .subcommand(Command::new("start").about("start init script")
            .arg(Arg::new("name").help("name of the init script, 'all' for all scripts").required(true).index(1)
            .value_parser(value_parser!(String)))
        )
        .subcommand(Command::new("stop").about("stop init script")
            .arg(Arg::new("name").help("name of the init script, 'all' for all scripts").required(true).index(1)
            .value_parser(value_parser!(String)))
        )
        .arg_required_else_help(true)
        .get_matches();

        match m.subcommand() {
            Some(("list", _)) => {
                println!("{:<20}{:<10}{}","Appname", "Stage", "Status");
                let re = regex::Regex::new(r"(S\d+)(.*)").unwrap();
                let entries = entries_in_order(FILE_PATH)?;
                for entry in entries {
                    // let entry = entry.unwrap();
                    // println!("{:?}", entry.file_name());
                    // println!()
                    // let name = entry.file_name().into_string().unwrap();
                    let name = entry.file_name().unwrap().to_string_lossy();
                    let status = if std::path::Path::new(&format!("{}/{}", LINK_PATH, name)).exists() {
                        "enabled"
                    } else {
                        "disabled"
                    };
                    if let Some(cap) = re.captures(&name) {
                        println!("{:<20}{:<10}{}", &cap[2], &cap[1], status);
                    } else {
                        println!("invalid filename: {}", name);
                    }
                }

            },
            Some(("enable", args)) => {
                let name: &String = args.get_one("name").unwrap();
                if name == "all" {
                    for entry in std::fs::read_dir(FILE_PATH)? {
                        let entry = entry.unwrap();
                        let name = entry.file_name().into_string().unwrap();
                        let _ = std::os::unix::fs::symlink(&entry.path(), format!("{}/{}", LINK_PATH, name));
                    }
                } else {
                    let file = find_file(name, FILE_PATH)?;
                    let filename = file.file_name().unwrap().to_string_lossy();
                    std::os::unix::fs::symlink(&file, format!("{}/{}", LINK_PATH, filename))?;
                }
            },
            Some(("disable", args)) => {
                let name: &String = args.get_one("name").unwrap();
                if name == "all" {
                    for entry in std::fs::read_dir(LINK_PATH)? {
                        let entry = entry.unwrap();
                        std::fs::remove_file(entry.path())?;
                    }
                } else {
                    let file = find_file(name, LINK_PATH)?;
                    std::fs::remove_file(&file)?;
                }
            },
            Some(("start", args)) => {
                let name: &String = args.get_one("name").unwrap();
                if name == "all" {
                    let entries = entries_in_order(LINK_PATH)?;
                    for entry in entries {
                        // let entry = entry.unwrap();
                        std::process::Command::new(&entry).arg("start").status()?;
                    }
                } else {
                    let file = find_file(name, FILE_PATH)?;
                    std::process::Command::new(&file).arg("start").status()?;
                }
            },
            Some(("stop", args)) => {
                let name: &String = args.get_one("name").unwrap();
                if name == "all" {
                    let mut entries = entries_in_order(LINK_PATH)?;
                    entries.reverse();
                    for entry in entries {
                        // let entry = entry.unwrap();
                        std::process::Command::new(&entry).arg("stop").status()?;
                    }
                } else {
                    let file = find_file(name, FILE_PATH)?;
                    std::process::Command::new(&file).arg("stop").status()?;
                }
            },
            _ => {
                println!("No subcommand was used");
            },
        }
        Ok(())
}

fn find_file(name: &str, path: &str) -> Result<PathBuf> {
    let mut files:Vec<PathBuf> = vec![];
    let re = regex::Regex::new(r"(S\d+)(.*)").unwrap();
    for entry in std::fs::read_dir(path).unwrap() {
        let entry = entry.unwrap();
        // if entry.path().to_string_lossy().contains(name) {
        // let cap = re.captures(entry.)
        //     files.push(entry.path());
        // }
        let filename = entry.file_name().to_string_lossy().to_string();

        if filename.eq(name) {
            files.push(entry.path());
            continue;
        }
        if let Some(cap) = re.captures(&filename) {
            if cap.get(2).expect(&format!("invalid filename:{}", &cap[0])).as_str().eq(name) {
                files.push(entry.path());
            }
        }

    }
    match files.len() {
        0 => Err(anyhow!("no such file")),
        1 => Ok(files[0].clone()),
        _ => Err(anyhow!("multiple files found: {:?}", files)),
    }
} 

fn entries_in_order<P>(path:P) -> Result<Vec<PathBuf>>
where
    P: AsRef<std::path::Path>,
{
    let mut entries = std::fs::read_dir(path)?
        .map(|res| res.map(|e| e.path()))
        .collect::<Result<Vec<_>, std::io::Error>>()?;
    entries.sort();
    Ok(entries)
}
BR2_arm=y
BR2_cortex_a7=y
BR2_SVN="svn"
BR2_TOOLCHAIN_EXTERNAL=y
BR2_TOOLCHAIN_EXTERNAL_CUSTOM=y
BR2_TOOLCHAIN_EXTERNAL_PATH="/opt/gcc-arm"
BR2_TOOLCHAIN_EXTERNAL_CUSTOM_PREFIX="$(ARCH)-none-linux-gnueabihf"
BR2_TOOLCHAIN_EXTERNAL_HEADERS_4_20=y
BR2_TOOLCHAIN_EXTERNAL_CUSTOM_GLIBC=y
BR2_TOOLCHAIN_EXTERNAL_CXX=y
BR2_TOOLCHAIN_EXTERNAL_GDB_SERVER_COPY=y
BR2_TOOLCHAIN_EXTRA_LIBS="libstdc++"
BR2_TARGET_GENERIC_HOSTNAME="mgc"
BR2_TARGET_GENERIC_ISSUE="Welcome to MicroGrid Controller of DERI Co.,Ltd."
BR2_ROOTFS_DEVICE_CREATION_DYNAMIC_EUDEV=y
BR2_TARGET_GENERIC_ROOT_PASSWD="deri"
BR2_SYSTEM_BIN_SH_BASH=y
BR2_TARGET_GENERIC_GETTY_PORT="ttyS0"
BR2_TARGET_GENERIC_GETTY_BAUDRATE_115200=y
BR2_SYSTEM_ENABLE_NLS=y
BR2_TARGET_LOCALTIME="Asia/Shanghai"
BR2_ROOTFS_OVERLAY="./rootfscustom"
BR2_PACKAGE_BUSYBOX_SHOW_OTHERS=y
BR2_PACKAGE_ALSA_UTILS=y
BR2_PACKAGE_ALSA_UTILS_ALSAUCM=y
BR2_PACKAGE_ALSA_UTILS_AMIXER=y
BR2_PACKAGE_ALSA_UTILS_APLAY=y
BR2_PACKAGE_BZIP2=y
BR2_PACKAGE_GZIP=y
BR2_PACKAGE_P7ZIP=y
BR2_PACKAGE_UNZIP=y
BR2_PACKAGE_ZIP=y
BR2_PACKAGE_GDB=y
BR2_PACKAGE_GDB_DEBUGGER=y
BR2_PACKAGE_GDB_TUI=y
BR2_PACKAGE_STRACE=y
BR2_PACKAGE_STRESS=y
BR2_PACKAGE_STRESS_NG=y
BR2_PACKAGE_LIBTOOL=y
BR2_PACKAGE_MAKE=y
BR2_PACKAGE_CPIO=y
BR2_PACKAGE_DOSFSTOOLS=y
BR2_PACKAGE_DOSFSTOOLS_FATLABEL=y
BR2_PACKAGE_DOSFSTOOLS_FSCK_FAT=y
BR2_PACKAGE_DOSFSTOOLS_MKFS_FAT=y
BR2_PACKAGE_E2FSPROGS_RESIZE2FS=y
BR2_PACKAGE_E2TOOLS=y
BR2_PACKAGE_MMC_UTILS=y
BR2_PACKAGE_SUNXI_TOOLS=y
BR2_PACKAGE_DEJAVU=y
BR2_PACKAGE_WQY_ZENHEI=y
BR2_PACKAGE_MESA3D=y
BR2_PACKAGE_MESA3D_GALLIUM_DRIVER_KMSRO=y
BR2_PACKAGE_MESA3D_GALLIUM_DRIVER_LIMA=y
BR2_PACKAGE_MESA3D_OPENGL_GLX=y
BR2_PACKAGE_MESA3D_OPENGL_EGL=y
BR2_PACKAGE_MESA3D_OPENGL_ES=y
BR2_PACKAGE_QT5=y
BR2_PACKAGE_QT53D=y
BR2_PACKAGE_QT5BASE_CUSTOM_CONF_OPTS="-no-feature-getentropy"
BR2_PACKAGE_QT5BASE_MYSQL=y
BR2_PACKAGE_QT5BASE_SQLITE_QT=y
BR2_PACKAGE_QT5BASE_SYSLOG=y
BR2_PACKAGE_QT5BASE_ICU=y
BR2_PACKAGE_QT5BASE_TSLIB=y
BR2_PACKAGE_QT5CHARTS=y
BR2_PACKAGE_QT5COAP=y
BR2_PACKAGE_QT5CONNECTIVITY=y
BR2_PACKAGE_QT5ENGINIO=y
BR2_PACKAGE_QT5LOCATION=y
BR2_PACKAGE_QT5LOTTIE=y
BR2_PACKAGE_QT5MQTT=y
BR2_PACKAGE_QT5REMOTEOBJECTS=y
BR2_PACKAGE_QT5SCRIPT=y
BR2_PACKAGE_QT5SENSORS=y
BR2_PACKAGE_QT5SERIALBUS=y
BR2_PACKAGE_QT5TOOLS=y
BR2_PACKAGE_QT5WEBSOCKETS=y
BR2_PACKAGE_QT5XMLPATTERNS=y
BR2_PACKAGE_QJSON=y
BR2_PACKAGE_QUAZIP=y
BR2_PACKAGE_WESTON=y
BR2_PACKAGE_WESTON_XWAYLAND=y
BR2_PACKAGE_XORG7=y
BR2_PACKAGE_XSERVER_XORG_SERVER=y
BR2_PACKAGE_XAPP_APPRES=y
BR2_PACKAGE_XAPP_BDFTOPCF=y
BR2_PACKAGE_XAPP_BEFORELIGHT=y
BR2_PACKAGE_XAPP_BITMAP=y
BR2_PACKAGE_XAPP_EDITRES=y
BR2_PACKAGE_XAPP_FONTTOSFNT=y
BR2_PACKAGE_XAPP_FSLSFONTS=y
BR2_PACKAGE_XAPP_FSTOBDF=y
BR2_PACKAGE_XAPP_ICEAUTH=y
BR2_PACKAGE_XAPP_ICO=y
BR2_PACKAGE_XAPP_LISTRES=y
BR2_PACKAGE_XAPP_LUIT=y
BR2_PACKAGE_XAPP_MKFONTSCALE=y
BR2_PACKAGE_XAPP_OCLOCK=y
BR2_PACKAGE_XAPP_RGB=y
BR2_PACKAGE_XAPP_RSTART=y
BR2_PACKAGE_XAPP_SCRIPTS=y
BR2_PACKAGE_XAPP_SESSREG=y
BR2_PACKAGE_XAPP_SETXKBMAP=y
BR2_PACKAGE_XAPP_SHOWFONT=y
BR2_PACKAGE_XAPP_SMPROXY=y
BR2_PACKAGE_XAPP_TWM=y
BR2_PACKAGE_XAPP_VIEWRES=y
BR2_PACKAGE_XAPP_X11PERF=y
BR2_PACKAGE_XAPP_XAUTH=y
BR2_PACKAGE_XAPP_XBACKLIGHT=y
BR2_PACKAGE_XAPP_XBIFF=y
BR2_PACKAGE_XAPP_XCALC=y
BR2_PACKAGE_XAPP_XCLIPBOARD=y
BR2_PACKAGE_XAPP_XCLOCK=y
BR2_PACKAGE_XAPP_XCMSDB=y
BR2_PACKAGE_XAPP_XCOMPMGR=y
BR2_PACKAGE_XAPP_XCONSOLE=y
BR2_PACKAGE_XAPP_XCURSORGEN=y
BR2_PACKAGE_XAPP_XDBEDIZZY=y
BR2_PACKAGE_XAPP_XDITVIEW=y
BR2_PACKAGE_XAPP_XDPYINFO=y
BR2_PACKAGE_XAPP_XEDIT=y
BR2_PACKAGE_XAPP_XEV=y
BR2_PACKAGE_XAPP_XEYES=y
BR2_PACKAGE_XAPP_XF86DGA=y
BR2_PACKAGE_XAPP_XFD=y
BR2_PACKAGE_XAPP_XFINDPROXY=y
BR2_PACKAGE_XAPP_XFONTSEL=y
BR2_PACKAGE_XAPP_XFS=y
BR2_PACKAGE_XAPP_XFSINFO=y
BR2_PACKAGE_XAPP_XGAMMA=y
BR2_PACKAGE_XAPP_XGC=y
BR2_PACKAGE_XAPP_XHOST=y
BR2_PACKAGE_XAPP_XINPUT=y
BR2_PACKAGE_XAPP_XINPUT_CALIBRATOR=y
BR2_PACKAGE_XAPP_XKBEVD=y
BR2_PACKAGE_XAPP_XKBPRINT=y
BR2_PACKAGE_XAPP_XKBUTILS=y
BR2_PACKAGE_XAPP_XKILL=y
BR2_PACKAGE_XAPP_XLOAD=y
BR2_PACKAGE_XAPP_XLOGO=y
BR2_PACKAGE_XAPP_XLSATOMS=y
BR2_PACKAGE_XAPP_XLSCLIENTS=y
BR2_PACKAGE_XAPP_XLSFONTS=y
BR2_PACKAGE_XAPP_XMAG=y
BR2_PACKAGE_XAPP_XMAN=y
BR2_PACKAGE_XAPP_XMESSAGE=y
BR2_PACKAGE_XAPP_XMH=y
BR2_PACKAGE_XAPP_XMODMAP=y
BR2_PACKAGE_XAPP_XMORE=y
BR2_PACKAGE_XAPP_XPR=y
BR2_PACKAGE_XAPP_XPROP=y
BR2_PACKAGE_XAPP_XRANDR=y
BR2_PACKAGE_XAPP_XRDB=y
BR2_PACKAGE_XAPP_XREFRESH=y
BR2_PACKAGE_XAPP_XSET=y
BR2_PACKAGE_XAPP_XSETMODE=y
BR2_PACKAGE_XAPP_XSETPOINTER=y
BR2_PACKAGE_XAPP_XSETROOT=y
BR2_PACKAGE_XAPP_XSM=y
BR2_PACKAGE_XAPP_XSTDCMAP=y
BR2_PACKAGE_XAPP_XVIDTUNE=y
BR2_PACKAGE_XAPP_XVINFO=y
BR2_PACKAGE_XAPP_XWD=y
BR2_PACKAGE_XAPP_XWININFO=y
BR2_PACKAGE_XAPP_XWUD=y
BR2_PACKAGE_XDRIVER_XF86_INPUT_EVDEV=y
BR2_PACKAGE_XDRIVER_XF86_INPUT_LIBINPUT=y
BR2_PACKAGE_XDRIVER_XF86_VIDEO_FBDEV=y
BR2_PACKAGE_XDRIVER_XF86_VIDEO_FBTURBO=y
BR2_PACKAGE_DBUS_CPP=y
BR2_PACKAGE_DBUS_GLIB=y
BR2_PACKAGE_EUDEV_RULES_GEN=y
BR2_PACKAGE_IOSTAT=y
BR2_PACKAGE_MEMTESTER=y
BR2_PACKAGE_PYTHON3=y
BR2_PACKAGE_PYTHON_CACHED_PROPERTY=y
BR2_PACKAGE_PYTHON_DOCKER=y
BR2_PACKAGE_PYTHON_DOCKERPTY=y
BR2_PACKAGE_PYTHON_DOCOPT=y
BR2_PACKAGE_PYTHON_JSONSCHEMA=y
BR2_PACKAGE_PYTHON_MELD3=y
BR2_PACKAGE_PYTHON_PARAMIKO=y
BR2_PACKAGE_PYTHON_PYYAML=y
BR2_PACKAGE_PYTHON_TEXTTABLE=y
# BR2_PACKAGE_ALSA_LIB_ALOAD is not set
# BR2_PACKAGE_ALSA_LIB_RAWMIDI is not set
# BR2_PACKAGE_ALSA_LIB_HWDEP is not set
# BR2_PACKAGE_ALSA_LIB_ALISP is not set
# BR2_PACKAGE_ALSA_LIB_OLD_SYMBOLS is not set
BR2_PACKAGE_OPUS=y
BR2_PACKAGE_SPEEXDSP=y
BR2_PACKAGE_LIBZIP=y
BR2_PACKAGE_LZ4=y
BR2_PACKAGE_LZO=y
BR2_PACKAGE_GNUTLS_OPENSSL=y
BR2_PACKAGE_LIBGCRYPT=y
BR2_PACKAGE_LIBNSS=y
# BR2_PACKAGE_LIBSODIUM_FULL is not set
BR2_PACKAGE_LIBOPENSSL_BIN=y
BR2_PACKAGE_SQLITE_STAT4=y
BR2_PACKAGE_SQLITE_ENABLE_COLUMN_METADATA=y
BR2_PACKAGE_SQLITE_ENABLE_FTS3=y
BR2_PACKAGE_SQLITE_ENABLE_JSON1=y
BR2_PACKAGE_SQLITE_ENABLE_UNLOCK_NOTIFY=y
BR2_PACKAGE_SQLITE_SECURE_DELETE=y
BR2_PACKAGE_SQLITE_NO_SYNC=y
BR2_PACKAGE_AT_SPI2_ATK=y
BR2_PACKAGE_GRAPHITE2=y
BR2_PACKAGE_GTKMM3=y
BR2_PACKAGE_GTKSOURCEVIEW=y
BR2_PACKAGE_LIBEXIF=y
BR2_PACKAGE_LIBGTK3=y
# BR2_PACKAGE_LIBGTK3_BROADWAY is not set
BR2_PACKAGE_WEBP=y
BR2_PACKAGE_WEBP_DEMUX=y
BR2_PACKAGE_DTC=y
BR2_PACKAGE_DTC_PROGRAMS=y
BR2_PACKAGE_LIBGUDEV=y
BR2_PACKAGE_LIBHID=y
BR2_PACKAGE_LIBIIO=y
BR2_PACKAGE_LIBOGG=y
BR2_PACKAGE_LIBCURL=y
BR2_PACKAGE_LIBCURL_CURL=y
BR2_PACKAGE_LIBIDN=y
BR2_PACKAGE_LIBKRB5=y
BR2_PACKAGE_LIBNDP=y
BR2_PACKAGE_LIBSOCKETCAN=y
BR2_PACKAGE_LIBSOUP=y
BR2_PACKAGE_LIBSOUP_SSL=y
BR2_PACKAGE_GTEST=y
BR2_PACKAGE_LIBDAEMON=y
BR2_PACKAGE_LIBUBOX=y
BR2_PACKAGE_MPDECIMAL=y
BR2_PACKAGE_PCRE_16=y
BR2_PACKAGE_PCRE_32=y
BR2_PACKAGE_BLUEZ5_UTILS=y
BR2_PACKAGE_BLUEZ5_UTILS_OBEX=y
BR2_PACKAGE_BLUEZ5_UTILS_CLIENT=y
BR2_PACKAGE_BLUEZ5_UTILS_DEPRECATED=y
BR2_PACKAGE_CAN_UTILS=y
BR2_PACKAGE_CUPS=y
BR2_PACKAGE_DHCPCD=y
BR2_PACKAGE_DNSMASQ=y
BR2_PACKAGE_DNSMASQ_DNSSEC=y
BR2_PACKAGE_DNSMASQ_IDN=y
BR2_PACKAGE_ETHTOOL=y
BR2_PACKAGE_FPING=y
BR2_PACKAGE_HOSTAPD=y
BR2_PACKAGE_HOSTAPD_EAP=y
BR2_PACKAGE_HOSTAPD_WPS=y
BR2_PACKAGE_HTTPING=y
BR2_PACKAGE_HTTPING_TFO=y
BR2_PACKAGE_IPERF=y
BR2_PACKAGE_IPERF3=y
BR2_PACKAGE_IPROUTE2=y
BR2_PACKAGE_IPTABLES=y
BR2_PACKAGE_IW=y
BR2_PACKAGE_LRZSZ=y
BR2_PACKAGE_MOSQUITTO=y
BR2_PACKAGE_NTP=y
BR2_PACKAGE_NTP_NTP_KEYGEN=y
BR2_PACKAGE_NTP_NTP_SHM_CLK=y
BR2_PACKAGE_NTP_NTPD_ATOM_PPS=y
BR2_PACKAGE_NTP_NTPDATE=y
BR2_PACKAGE_NTP_NTPDC=y
BR2_PACKAGE_NTP_NTPQ=y
BR2_PACKAGE_NTP_NTPTIME=y
BR2_PACKAGE_OPENSSH=y
BR2_PACKAGE_PPPD=y
BR2_PACKAGE_PPPD_FILTER=y
BR2_PACKAGE_PPPD_RADIUS=y
BR2_PACKAGE_PPTP_LINUX=y
BR2_PACKAGE_SAMBA4=y
BR2_PACKAGE_SAMBA4_ADS=y
BR2_PACKAGE_SAMBA4_SMBTORTURE=y
BR2_PACKAGE_TCPDUMP=y
BR2_PACKAGE_TCPDUMP_SMB=y
BR2_PACKAGE_TCPING=y
BR2_PACKAGE_TCPREPLAY=y
BR2_PACKAGE_VSFTPD=y
BR2_PACKAGE_VSFTPD_UTMPX=y
BR2_PACKAGE_WGET=y
BR2_PACKAGE_WIRELESS_TOOLS=y
BR2_PACKAGE_WIRELESS_TOOLS_LIB=y
BR2_PACKAGE_WPA_SUPPLICANT=y
BR2_PACKAGE_WPA_SUPPLICANT_AP_SUPPORT=y
BR2_PACKAGE_WPA_SUPPLICANT_WIFI_DISPLAY=y
BR2_PACKAGE_WPA_SUPPLICANT_AUTOSCAN=y
BR2_PACKAGE_WPA_SUPPLICANT_EAP=y
BR2_PACKAGE_WPA_SUPPLICANT_DEBUG_SYSLOG=y
BR2_PACKAGE_WPA_SUPPLICANT_WPS=y
BR2_PACKAGE_WPA_SUPPLICANT_CLI=y
BR2_PACKAGE_WPA_SUPPLICANT_PASSPHRASE=y
BR2_PACKAGE_OPKG=y
BR2_PACKAGE_OPKG_GPG_SIGN=y
BR2_PACKAGE_LOGROTATE=y
BR2_PACKAGE_ATTR=y
BR2_PACKAGE_CGROUPFS_MOUNT=y
BR2_PACKAGE_COREUTILS=y
BR2_PACKAGE_COREUTILS_INDIVIDUAL_BINARIES=y
BR2_PACKAGE_DAEMON=y
BR2_PACKAGE_HTOP=y
BR2_PACKAGE_LIBOSTREE=y
BR2_PACKAGE_MONIT=y
BR2_PACKAGE_PROCPS_NG=y
BR2_PACKAGE_RSYSLOG=y
BR2_PACKAGE_START_STOP_DAEMON=y
BR2_PACKAGE_TAR=y
BR2_PACKAGE_VIM=y
BR2_TARGET_ROOTFS_EXT2=y
BR2_TARGET_ROOTFS_EXT2_4=y
BR2_TARGET_ROOTFS_EXT2_LABEL=""
BR2_TARGET_ROOTFS_EXT2_SIZE="1G"
# BR2_TARGET_ROOTFS_TAR is not set

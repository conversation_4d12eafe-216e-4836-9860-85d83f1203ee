V2.1 20240429
buildroot新增：gdb、tar、strace、ostree、libsoup、NetworkManager、ModemManager、attr、coreutils、start_top_daemon、7zip、monit、rsyslog
         移除：sntp、dropbear
杂项：
  增加：
    coredump转储，格式为core.{可执行文件名}；
    4g模块支持nl668
    netmodule：4g模块管理工具；
    设备树增加硬件信息
    hwinfo：读取硬件信息；
    sn：SN码工具；
    netconfig：网络配置工具；
    logtool：广和通fibocom抓log工具；
    命令行提示符显示sn码；
  移除：
    /etc/openvpn/mgc.ovpn
    /etc/syslog.conf
  修改：
    app-init方式：app-init.sh整合到rc.local, 在/etc/app-init.d下管理启动项
V2.0.1 20230512
设置网口默认静态IP；设置POWER_LOST引脚下降沿中断
V2.0 20230315
兼容A40i和T3；更新4G脚本
v1.9 20221128
增加rt_smp.ko,ade7953.ko,dht20.ko驱动文件；修改启动文件rc.local,根据硬件默认加载相应驱动
v1.8 20220929
增加中文显示支持
v1.7 20220829
增加温湿度传感器适配；增加计量芯片适配；增加蜂鸣器适配
v1.6 20220622
增加日志错误过滤脚本；增加系统状态查询脚本；增加版本查询脚本
v1.5 20220615
更新uboot，适配去除加密芯片的核心板；更新4G脚本为v1.3版本
v1.4 20220421
更新脚本与日志
v1.3 20220401
使用chipID设置mac地址；兼容ECG和MGC的特定GPIO初始化; 更新4G和VPN相关脚本
v1.2 20211012
更新python版本；更新看门狗脚本；去除wireshark和qt软件包，减少文件系统占用空间
v1.1 20210819:
增加装置安全运行相关文件与配置
v1.0 20210810:
初次发布

#!/bin/sh -e
#
# rc.local
#
# This script is executed at the end of each multiuser runlevel.
# Make sure that the script will "exit 0" on success or any other
# value on error.
#
# In order to enable or disable this script just change the execution
# bits.
#
# By default this script does nothing.
# 20220420    xufei       v1.1
# 20221128    xufei       v1.2
# 20240227    liuyuxuan   v1.3

/rootfssize.sh

#insmod /lib/modules/3.10.65/mali.ko

if [ -d /sys/class/pwm/pwmchip4 ]; 
then
    insmod /lib/modules/3.10.65/rt_smp.ko
else
    insmod /lib/modules/3.10.65/dht20.ko
    insmod /lib/modules/3.10.65/ade7953.ko	
fi

#enable VCC2(ECG)
if [ -e "/sys/class/gpio_sw/PI5" ]; then
    echo 1 > /sys/class/gpio_sw/PI5/data
fi

#enable DO_OUT_EN(MGC)
if [ -e "/sys/class/gpio_sw/PE8" ]; then
    echo 0 > /sys/class/gpio_sw/PE8/data
fi

#enable QD_5V(MGC)
if [ -e "/sys/class/gpio_sw/PB12" ]; then
    echo 1 > /sys/class/gpio_sw/PB12/data
fi

#unlink softlink and create resolv.conf
if [ -L "/etc/resolv.conf" ]; then
    unlink /etc/resolv.conf
fi

. /usr/bin/set_mac.sh
. /usr/bin/set_powerlost_interrupt.sh

mgc-watchdog enable &
# detect 4g module and start
chmod +x /usr/bin/netmodule && netmodule start noquit &
edge-vpn &

export PATH=$PATH:/usr/local/bin:/usr/local/sbin
export LD_LIBRARY_PATH=/deri/mgc:/usr/local/lib
echo "Starting app-init"
for i in /etc/app-init.d/S??*; do

    # Ignore dangling symlinks (if any).
    [ ! -f "$i" ] && continue

     case "$i" in
	*.sh)
	    # Source shell script for speed.
	    (
		trap - INT QUIT TSTP
		set start
		. $i
	    )
	    ;;
	*)
	    # No sh extension, so fork subprocess.
        $i start
	    ;;
    esac
done
unset i

exit 0

#!/bin/bash

###########################################
# COPYRIGHT (c) DERI. Nanjing, China
#
#        FILE NAME: nl668.sh
#
# FILE DESCRIPTION: 4G module NL668 network connect script
#
# --DATE--    NAME        REVISION HISTORY
# 20250117    liuyuxuan   v1.0
###########################################

log_file=/deri/log/net-module.log
log_file_0=/deri/log/net-module.log.0
daemon_file=/var/run/net-module-daemon.pid
version=v1.0
# rev_whitelist=(
# )

source /opt/net_module/4g_module_common.sh
function usage()
{
    echo "this script is used to connect network with 4G module ec200m"
    echo "eg: ec200m.sh &"
}
# function set_GPS()
# {
#     #enable gps
#     echo -e "AT+GTGPSPOWER?\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep "1" > /dev/null 2>&1 || retry_AT "AT+GTGPSPOWER=1" 90
#     echo -e "AT+GTGPSPOWER?\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep "1" > /dev/null 2>&1
#     if [ $? -ne 0 ]; then
#         log "enable gps failed"
#         return 1
#     else
#         log "enable gps success"
#         return 0
#     fi
# }
# function get_location()
# {
#     local res
#     res=$(echo -e "AT+GTGPS=\"RMC\"\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep RMC, | head -1 | tr -d '\r')
#     echo $res | awk -v output="${OUTPUT_PATH}" -F ',' '{print $4 $5> output "/latitude";print $6 $7> output "/longitude"}'
# }
function log_csq()
{
    local cesq
    local rsrp
    # local rsrq
    echo -e "AT+CSQ\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep CSQ: | tr -d '\r' > ${OUTPUT_PATH}/rssi
    log "$(cat ${OUTPUT_PATH}/rssi)"
    [ -z "$(cat ${OUTPUT_PATH}/reg)" ] && return 0
    cesq=$(echo -e "AT+CESQ\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep CESQ: | sed 's/\r//g')

    rsrp=$(echo $cesq | awk -F ',' '{print $6}' | sed '/^\s*$/d;s/\r//g')
    if [ "x$rsrp" != "x" -a "x$rsrp" != "x255" ];then
        rsrp=`expr $rsrp - 141`
        echo $rsrp > ${OUTPUT_PATH}/rsrp
        log "rsrp: $rsrp"
    else
        log "get rsrp failed"
    fi

    local snr=$(echo -e "AT+QENG="servingcell"\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep -A 1 "LTE" | awk -F',' '{print $17}')
    if [ "x$snr" != "x" -a "x$snr" != "x255" ];then
        echo $snr > ${OUTPUT_PATH}/sinr
        log "snr: $snr"
    else
        log "get snr failed"
    fi
}
function check_hotplug()
{
    local hp
    hp=$(echo -e "AT+QSIMSTAT?\r\n" | microcom -t 1000 /dev/ttyUSB1 | awk '/\+QSIMSTAT:/ {print $2}' | awk -F',' '{print $2}')
    if [ x"$hp" != "x0" ];then
        echo "$(date +"%Y-%m-%d %H:%M:%S"): hotplug: $hp, set to 0" | tee -a $log_file
        retry_AT "AT+QSIMSTAT=0" 5
        return 1
    else
        return 0
    fi
}

function check_connection()
{
    local rc=$(echo -e "AT+QNETDEVCTL?\r\n" | microcom -t 1000 /dev/ttyUSB1 | awk -F',' '{print $4}' | sed '/^\s*$/d')
    if [ -z "$rc" ]; then
        # log "RNDIS ip not exist"
        log "网络离线，拨号失败"
        return 1;
    fi
    echo -e "AT+CPIN?\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep "READY" > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo 1 > ${OUTPUT_PATH}/cd
    else
        echo 0 > ${OUTPUT_PATH}/cd
        return 1
    fi
    check_connection_common
    return $?
}
function check_rev()
{
    local imei=$(echo -e "AT+CGSN\r\n" | microcom -t 1000 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1)
    if [ -n "$imei" ];then
        log "imei: $imei"
    else
        log "获取imei失败"
    fi
    local rev=""
    local cnt=0
    while [ "x$rev" = "x" ]; do
        rev=$(echo -e "ATI\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep "Revision:" | sed 's/Revision://;s/[ \r]//g')
        ((cnt++))
        [ $cnt -gt 100 ] && log "获取固件版本失败" && return 1
        # sleep 1
    done
    log "固件版本: $rev"
    
}
function close_echo()
{
    while true; do
        retry_AT "ATE0" 5
        if [ $? -eq 0 ];then
            log "关闭回显成功"
            break
        else
            # echo "$(date +"%Y-%m-%d %H:%M:%S"): ATE0 fail" | tee -a $log_file
            echo "$(date +"%Y-%m-%d %H:%M:%S"): 关闭回显失败" | tee -a $log_file
        fi
        sleep 5
    done
}
function dial_up()
{
    local cid
    local pdp_type
    local apn
    local rc
    test -r /etc/default/PDPContext && . /etc/default/PDPContext
    cid=${CID:-1}
    pdp_type=${PDP_TYPE:-IP}
    apn=${APN:-internet}
    retry_AT "AT+CGDCONT=$cid,\"$pdp_type\",\"$apn\"" 5
    if [ $? -ne 0 ]; then
        # echo "$(date +"%Y-%m-%d %H:%M:%S"): set apn failed, needs to hard reset 4G module" | tee -a $log_file
        echo "$(date +"%Y-%m-%d %H:%M:%S"): 设置apn失败" | tee -a $log_file
    fi

    retry_AT "AT+QNETDEVCTL=1,$cid" 5
    rc=$(echo -e "AT+QNETDEVCTL?\r\n" | microcom -t 1000 /dev/ttyUSB1 | awk -F',' '{print $4}' | sed '/^\s*$/d' | tr -d '"')
    if [ -n "$rc" ]; then
        # echo "$(date +"%Y-%m-%d %H:%M:%S"): ECM dail up OK" | tee -a $log_file
        echo "$(date +"%Y-%m-%d %H:%M:%S"): ECM拨号成功" | tee -a $log_file
        get_oper
        return 0
    else
        # echo "$(date +"%Y-%m-%d %H:%M:%S"): WARN: ECM dail up fail, needs to hard reset 4G module" | tee -a $log_file
        echo "$(date +"%Y-%m-%d %H:%M:%S"): WARN: ECM拨号失败" | tee -a $log_file
        return 1
    fi
}
function module_setup()
{
    # repower
    # CFUN=15, reset module
    # reset_module 15 $reset_step

    check_ttyUSB1
    if [ $? -ne 0 ]; then
        return 1
    fi

    check_atcmd
    if [ $? -ne 0 ]; then
        return 2
    fi

    close_echo
    [ $? -ne 0 ] && return 3
    
    check_rev

    retry_AT "AT+CMEE=2" 3

    check_hotplug
    local hp=$?
    # check_usbmode
    if [ $? -ne 0 -o $hp -ne 0 ]; then
        return 4
    fi

    # set_GPS
    # if [ $? -ne 0 ]; then
    #     return 9
    # fi

    check_cfun
    if [ $? -ne 0 ]; then
        return 4
    fi

    check_sim
    if [ $? -ne 0 ]; then
        return 5
    fi

    #check network state
    # log "$(echo -e "AT+ZPAS?\r\n" | microcom -t 400 /dev/ttyUSB1 | sed '/^\s*$/d;s/\r//g' | head -1)"
    # echo -e "AT+ZPAS?\r\n" | microcom -t 400 /dev/ttyUSB1 | grep "Service\|ERROR" > /dev/null 2>&1
    # if [ $? -eq 0 ]; then
    #     echo "$(date +"%Y-%m-%d %H:%M:%S"): WARN: 无服务" | tee -a $log_file
    #     return 4 
    # fi
    check_csq
    [ $? -ne 0 ] && return 6
    check_reg_state
    [ $? -ne 0 ] && return 7
    
    dial_up
    if [ $? -ne 0 ]; then
        return 8
    fi
    #4G module needs a while to connect the network after dail up
    sleep 10
    check_sim_directional
    return $?
}


if [ $# -gt 0 ]; then
    case "$1" in
        -v)
            echo $version
            exit 0 
            ;;
        -h) 
            usage
            exit 0
            ;;
        *)
            echo "wrong argument"
            exit 1
            ;;
    esac
fi

mkdir -p "/deri/log"
if [ ! -f $log_file ]; then
    echo "" > ${log_file}
fi

if [ -f "${daemon_file}" ]; then
    rec_pid=$(cat "${daemon_file}")
    if [ -d "/proc/${rec_pid}" ]; then
        self_status=$(cat "/proc/${rec_pid}/stat" | awk '{print $3}')
        if [ "${self_status}" != "Z" ]; then
            date=$(date +"%Y-%m-%d %H:%M:%S")
            echo "${date} ec200m[$$]: WARN: ec200m.sh already running" >> "${log_file}"
            exit 0
        else 
            kill -9 $rec_pid
        fi
    fi
fi
echo "$$" > ${daemon_file}

#main loop
first_time=0
reset_step=0
CFUN_RESET=1,1
while true; do
    if [ $first_time -eq 0 ]; then
        # first_time=1
        # log "first time to connect 4G network"
        log "this module is $(echo -e "AT+CGMM\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep -v "CGMM" | sed '/^\s*$/d' | head -1)"
        do_reset $CFUN_RESET
    fi
    check_connection
    if [ $? -ne 0 ]; then
        # dial_up
        # if [ $? -ne 0 ]; then
            do_reset $CFUN_RESET
        # fi
    fi

    log_csq
    # get_location
    check_logsize
    sleep 60
done




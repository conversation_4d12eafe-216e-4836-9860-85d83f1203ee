#!/bin/bash

OUTPUT_PATH="/var/run/net_module"
[ -d $OUTPUT_PATH ] || mkdir -p $OUTPUT_PATH
output_list=(
    "sim"
    "sim_type"
    "rssi"
    "rsrp"
    "sinr"
    # "rsrq"
    "iccid"
    "imsi"
    "oper"
    "rev"
    "sn"
    "mode"
    "cd"
    "reg"
    "latitude"
    "longitude"
)
for file in ${output_list[@]}; do
    touch "${OUTPUT_PATH}/${file}"
done

ping_list=(
    119.29.29.29
    223.5.5.5
    180.76.76.76
)

function log()
{
    echo "$(date +"%Y-%m-%d %H:%M:%S"): $@" >> $log_file
}
function check_logsize()
{
    logsize=$(ls -l $log_file | awk '{print $5}')
    maxsize=$((10*1024*1024))
    if [ $logsize -gt $maxsize ]; then
        mv $log_file $log_file_0
        echo "" > $log_file
    fi
}
function retry_AT()
{
    local failed_attempts=0
    local at_command=$1
    local max_attempts=$2

    while [ "$failed_attempts" -lt "$max_attempts" ]; do
        # 发送AT指令并获取输出
        echo -e "$at_command\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep OK > /dev/null 2>&1

        # 检查输出是否包含"OK"
        if [ $? -eq 0 ]; then
            # echo "AT command successful. Exiting loop."
            return 0
        else
            # echo "AT command failed. Retrying..."
            ((failed_attempts++))
        fi

        # 添加一些延迟，避免频繁尝试
        sleep 1
    done
    return 1
}
function soft_pwroff()
{
    log "AT指令关闭模块"
    if [ "x$(cat ${OUTPUT_PATH}/model)" = "xME3630" ];then
        retry_AT "AT+ZTURNOFF" 2
        return $?
    fi
    if [ "x$(cat ${OUTPUT_PATH}/model)" = "xNL668" ];then
        retry_AT "AT+CPWROFF" 2
        return $?
    fi
    if [ "x$(cat ${OUTPUT_PATH}/model)" = "xEC200M" ];then
        echo -e "AT+QPOWD=1\r\n" | microcom -t 60000 /dev/ttyUSB1 | grep "POWERED DOWN" &> /dev/null
        return $?
    fi
    retry_AT "AT+ZTURNOFF" 2
    retry_AT "AT+CPWROFF" 2
    retry_AT "AT+QPOWD=1" 1
}
function repower()
{
    log "WARN: 模块重新上电..."
    soft_pwroff
    echo 0 > /sys/class/gpio_sw/PI6/data    #cut power
    sleep 15
    echo 1 > /sys/class/gpio_sw/PI6/data    #enable power
    # echo -e "AT+CPWROFF\r\n" | microcom -t 400 /dev/ttyUSB1
}
function soft_reset()
{
    [ -c /dev/ttyUSB1 ] || return 1;
    local output="CFUN=${1}重启模块"
    retry_AT "AT+CFUN=$1" 5
    if [ $? -eq 0 ];then
        log "${output}成功"
        return 0
    else
        log "${output}失败"
        return 1
    fi
}
function part_repower()
{
    log "最小模式断电"
    retry_AT "AT+CFUN=0" 5
    sleep 3
    retry_AT "AT+CFUN=1" 5
}
function reset_module()
{
    # [ $first_time -eq 0 ] && first_time=1 && return 0
    local cnt=0
    log "重启步骤: $2"
    case $2 in
        0) 
            part_repower
            ;;
        1)
            soft_reset $1
            # if [ $? -ne 0 ];then
            #     repower
            # else
                # sleep 10
            while [ -c /dev/ttyUSB1 ];do
                [ $cnt -gt 20 ] && break
                ((cnt++))
                sleep 1;
            done
            # fi
            ;;
        2|*)
            repower
            ;;
    esac
}
function do_reset()
{
    while true; do
        if [ $first_time -eq 0 ]; then
            log "first time to connect 4G network"
            first_time=1
            echo 1 > /sys/class/gpio_sw/PI6/data
        else
            reset_module $1 $reset_step
        fi
        module_setup
        if [ $? -eq 0 ];then
            reset_step=0
            break
        fi
        ((reset_step++))
        reset_step=$(expr $reset_step % 3)
    done
}
function ping_test()
{
    # local res
    while true; do
        for ip in ${ping_list[@]}; do
            ping -I usb0 -c 4 -s 24 $ip > /dev/null 2>&1
            if [ $? -eq 0 ];then
                log "ping test: $ip OK"
                return 0
            fi
        done
        if [ $cnt -gt 5 ]; then
            log "WARN: 4G network connection fail"
            return 1
        fi

        cnt=$((cnt+1))
        sleep 5
    done
}
function check_sim_directional()
{
    ping_test
    [ $? -ne 0 ] && return 1
    ping -I usb0 -c 4 -s 24 jumpjk.teld.cn > /dev/null 2>&1
    local teld_res=$?
    ping -I usb0 -c 4 -s 24 baidu.com > /dev/null 2>&1
    local baidu_res=$?
    case "$teld_res$baidu_res" in
        00)
            log "非定向卡"
            return 0
            ;;
        01)
            log "定向卡"
            return 0
            ;;
        10)
            log "WARN: 未ping通jumpjk.teld.cn"
            return 0
            ;;
        11)
            log "网络或DNS故障"
            return 1
            ;;
    esac
}
function check_ttyUSB1()
{
    cnt=0
    while true; do
        if [ -c /dev/ttyUSB1 ]; then
            echo "$(date +"%Y-%m-%d %H:%M:%S"): virtual serial port /dev/ttyUSB1 is ready" | tee -a $log_file
            return 0
        fi
        if [ $cnt -gt 300 ]; then
            echo "$(date +"%Y-%m-%d %H:%M:%S"): ERROR: generate virtual serial port /dev/ttyUSB1 timeout, please check the hardware!" | tee -a $log_file
            return 1
        fi
        cnt=$((cnt+1))
        sleep 1
    done
}
function check_atcmd()
{
    cnt=0
    while true; do
        echo -e "AT\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep "OK" > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo "$(date +"%Y-%m-%d %H:%M:%S"): AT cmd ready" | tee -a $log_file
            return 0
        fi
        if [ $cnt -gt 300 ]; then
            echo "$(date +"%Y-%m-%d %H:%M:%S"): WARN: AT cmd doesnot work, needs to hard reset 4G module" | tee -a $log_file
            return 1
        fi
        cnt=$((cnt+1))
        # sleep 1
    done
}

function get_sim_id()
{
    #ICCID
    echo -e "AT+CCID\r\n" | microcom -t 1000 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1 > ${OUTPUT_PATH}/iccid
    log "$(cat ${OUTPUT_PATH}/iccid | sed 's/\r//g')"
    #network operator ID
    echo -e "AT+CIMI\r\n" | microcom -t 1000 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1 > ${OUTPUT_PATH}/imsi

}
function check_sim()
{
    cnt=0
    while true; do
        echo -e "AT+CPIN?\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep "READY" > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo "$(date +"%Y-%m-%d %H:%M:%S"): SIM card is ready" | tee -a $log_file
            #SIM
            echo -e "AT+CPIN?\r\n" | microcom -t 1000 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1 > ${OUTPUT_PATH}/sim
            #SIM ID
            get_sim_id

            echo 1 > ${OUTPUT_PATH}/cd
            return 0
        fi
        if [[ "$cnt" -gt 300 ]]; then
            echo "$(date +"%Y-%m-%d %H:%M:%S"): WARN: SIM card doesnot work, needs to check the SIM card" | tee -a $log_file
            log "CPIN指令返回: $(echo -e "AT+CPIN?\r\n" | microcom -t 1000 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1)"
            echo 0 > ${OUTPUT_PATH}/cd
            return 1
        fi
        cnt=$((cnt+1))
        # sleep 1
    done
}
function get_oper()
{
    echo -e "AT+COPS=3,2\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep 'OK' > /dev/null 2>&1 &&\
    echo -e "AT+COPS?\r\n" | microcom -t 1000 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1 | awk -F, '{print $3}' | tr -d '"' > ${OUTPUT_PATH}/oper
}
function check_cfun()
{
    local cnt=0
    local cfun
    while true; do
        cfun=$(echo -e "AT+CFUN?\r\n" | microcom -t 1000 /dev/ttyUSB1 |  awk -F'[ ,\r]' '/\+CFUN:/ {print $2}')
        if [ "x$cfun" != "x1" ]; then
            log "CFUN=${cfun}, set CFUN=1"
            echo -e "AT+CFUN=1\r\n" | microcom -t 1000 /dev/ttyUSB1
            ((cnt++))
            [ $cnt -gt 5 ] && log "WARN: set CFUN=1 failed" && return 1
        else
            log "CFUN=${cfun} OK"
            return 0
        fi
    done
}
function check_csq()
{
    local temp
    local rssi
    local cnt=0
    while true; do
        temp=$(echo -e "AT+CSQ\r\n" | microcom -t 1000 /dev/ttyUSB1 | tr -d '\r')
        rssi=$(echo -e $temp | awk -F '[ ,]' '/\+CSQ:/ {print $2}')
        if [ "x$rssi" = "x" -o "x$rssi" = "x99" ];then
            ((cnt++))
            [ $cnt -gt 8 ] && log "$temp 无信号" && return 1
        else
            return 0
        fi
        sleep 10
    done
}
function check_reg_common()
{
    local temp
    local rc
    local cnt=0
    while true; do    
        temp=$(echo -e "$1\r\n" | microcom -t 1000 /dev/ttyUSB1 | tr -d '\r')
        rc=$(echo $temp | awk -F'[ ,]' '{print $3}')
        if [ "x$rc" = "x2" ];then
            ((cnt++))
            if [ $cnt -gt 8 ];then
                return 1
            fi
            sleep 10
            continue
        fi
        if [ "x$rc" != "x1" -a "x$rc" != "x5" ];then
            log "注网失败, 指令返回: $temp"
            return 2
        fi
        return 0
    done
}
function check_reg_state()
{
    local g_rc
    local e_rc
    # check_reg_common "AT+CREG?"
    # case "$?" in
    #     0)
    #         log "注册cs域成功"
    #         ;;
    #     1)
    #         log "超过90秒未注册到cs域"
    #         return 1
    #         ;;
    #     2)
    #         return 1
    #         ;;
    # esac
    check_reg_common "AT+CGREG?"
    g_rc=$?
    check_reg_common "AT+CEREG?"
    e_rc=$?
    case "${g_rc}${e_rc}" in
        00)
            log "注册到EPS"
            echo "EPS" > ${OUTPUT_PATH}/reg
            ;;
        01|02)
            log "注册到GPRS"
            echo "GPRS" > ${OUTPUT_PATH}/reg
            ;;
        10|20)
            log "*注册到EPS"
            ;;
        *)
            log "注册数据域失败"
            # return 1
            ;;
    esac
    return 0
}
function check_connection_common()
{
    local model=$(cat ${OUTPUT_PATH}/model)
    local res
    case "$model" in 
        ME3630)
            res=$(echo -e "AT+ZPAS?\r\n" | microcom -t 1000 /dev/ttyUSB1 | awk -F'"' '{print $2}' | tr -d '\r\n')
            ;;
        NL668)
            res=$(echo -e "AT+GTCCINFO?\r\n" | microcom -t 1000 /dev/ttyUSB1 | awk '/service cell/ {print $1}')
            ;;
        EC200M)
            res=$(echo -e "AT+QENG="servingcell"\r\n" | microcom -t 1000 /dev/ttyUSB1 | awk -F ',' '/\+QENG: "servingcell"/ {print $3}' | tr -d '"')
            ;;
        *)
            res=""
            ;;
    esac
    log "网络制式: ${res:-未知}"
    [ -n "$res" ] && echo $res > "${OUTPUT_PATH}/mode" 

    local ereg
    local greg
    ereg=$(echo -e "AT+CEREG?\r\n" | microcom -t 1000 /dev/ttyUSB1 | awk -F '[ ,]' '/\+CEREG:/ {print $3}' | tr -d '\r')
    if [ "x$ereg" != "x1" -a "x$ereg" != "x5" ];then
        greg=$(echo -e "AT+CGREG?\r\n" | microcom -t 1000 /dev/ttyUSB1 | awk -F '[ ,]' '/\+CGREG:/ {print $3}' | tr -d '\r')
        if [ "x$greg" != "x1" -a "x$greg" != "x5" ];then
            log "未注册到数据域"
            echo -n > ${OUTPUT_PATH}/reg
            # return 1
        else
            [ "x$(cat ${OUTPUT_PATH}/reg)" != "xGPRS" ] && echo "GPRS" > ${OUTPUT_PATH}/reg && log "网络切换到GPRS"
        fi
    else
        [ "x$(cat ${OUTPUT_PATH}/reg)" != "xEPS" ] && echo "EPS" > ${OUTPUT_PATH}/reg && log "网络切换到EPS"
    fi

    ping_test
    return $?
}
#!/bin/sh

###########################################
# COPYRIGHT (c) DERI. Nanjing, China
#
#        FILE NAME: me3630.sh
#
# FILE DESCRIPTION: 4G module ME3630 network connect script
#
# --DATE--    NAME        REVISION HISTORY
# 20210812    xufei       v1.0
# 20220325    xufei       v1.1
# 20220421    xufei       v1.2
# 20220519    xufei       v1.3
# 20221212    xufei       v1.4
# 20230302    xufei       v1.5
# 20240112    liuyuxuan   v1.6
# 20240419    liuyuxuan   v1.7
# 20240612    liuyuxuan   v1.8
# 20250721    liuyuxuan   v1.9
###########################################

log_file=/deri/log/net-module.log
log_file_0=/deri/log/net-module.log.0
daemon_file=/var/run/net-module-daemon.pid
version=v1.9

# touch /var/run/me3630sim
# touch /var/run/me3630rssi
# touch /var/run/me3630iccid
# touch /var/run/me3630imsi
# touch /var/run/me3630rsrp      #信号强度
# touch /var/run/me3630sinr      #信噪比
# touch /var/run/me3630sim_type  #卡类型，无卡时为NO INSERT SIM
# touch /var/run/me3630oper      #运营商
# touch /var/run/me3630rev       #固件版本
# touch /var/run/me3630mode      #网络制式
# touch /var/run/me3630cd        #card detect 0-no card; 1-have card;

source /opt/net_module/4g_module_common.sh
function usage()
{
    echo "this script is used to connect network with 4G module ME3630"
    echo "eg: me3630.sh &"
}
function set_GPS()
{
    #init GPS
    echo -e "AT+ZGINIT\r\n" | microcom -t 1000 /dev/ttyUSB1 > /dev/null 2>&1
    #enable GPS messages transport
    echo -e "AT+ZGPSR=1\r\n" | microcom -t 1000 /dev/ttyUSB1 > /dev/null 2>&1
    #GPS messages transport by MODEM port, aka ttyUSB2
    retry_AT AT+ZGPORT=2 5
    echo -e "AT+ZGPORT?\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep 2 > /dev/null 2>&1
    if [ $? -eq 0 ]; then 
        #set GPS as tracing mode
        retry_AT AT+ZGRUN=2 5
    else
        retry_AT AT+ZGRUN=0 5
    fi
}
function get_location()
{
    stty -F /dev/ttyUSB2 flusho
    echo -n | microcom -t 500 /dev/ttyUSB2 | grep "+ZGPSR:" | head -1 | awk -v output="${OUTPUT_PATH}" -F ',' '{print $2 > output "/latitude"; print $3 > output "/longitude"}' 
    echo -n | microcom -t 500 /dev/ttyUSB2 | grep "+ZGPSR:" | head -1 | awk -v output="${OUTPUT_PATH}" -F ',' '{print $2 > output "/latitude"; print $3 > output "/longitude"}' 
}
function log_csq()
{
    echo -e "AT+CSQ\r\n" | microcom -t 100 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1 > ${OUTPUT_PATH}/rssi
    log "$(cat ${OUTPUT_PATH}/rssi)"
    if [ $(cat ${OUTPUT_PATH}/cd) -eq 1 ];then
        echo -e "AT+ZSRVRSP?\r\n" | microcom -t 100 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1 | awk -v output="${OUTPUT_PATH}" -F'[:,]' '{gsub(/"/, "", $2); print $2 > output "/rsrp"; print $NF > output "/sinr"}'
        log "rsrp $(cat ${OUTPUT_PATH}/rsrp), sinr $(cat ${OUTPUT_PATH}/sinr)"
    else
        echo -n > ${OUTPUT_PATH}/rsrp
        echo -n > ${OUTPUT_PATH}/sinr
    fi
}
function clear_output()
{
    echo -n > ${OUTPUT_PATH}/sim
    echo -n > ${OUTPUT_PATH}/rssi
    echo -n > ${OUTPUT_PATH}/iccid
    echo -n > ${OUTPUT_PATH}/imsi
    echo -n > ${OUTPUT_PATH}/rsrp
    echo -n > ${OUTPUT_PATH}/sinr
    echo -n > ${OUTPUT_PATH}/sim_type
    echo -n > ${OUTPUT_PATH}/oper
    echo -n > ${OUTPUT_PATH}/rev
    echo -n > ${OUTPUT_PATH}/mode
}
function check_connection()
{
    cnt=0
    echo -e "AT+ZSIMCARD\r\n" | microcom -t 100 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1 | grep "INSERT" > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        log "NO SIM DETECTED"
        echo 0 > ${OUTPUT_PATH}/cd
        clear_output
        return 1
    fi
    check_connection_common
    return $?
}
function get_sim_id() {
    #ICCID
    echo -e "AT+ZGETICCID\r\n" | microcom -t 100 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1 | awk -F':' '{print $2}' | tr -d ' ' > ${OUTPUT_PATH}/iccid
    log "+CCID: $(cat ${OUTPUT_PATH}/iccid | sed 's/\r//g')"
    #network operator ID
    echo -e "AT+CIMI\r\n" | microcom -t 100 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1 > ${OUTPUT_PATH}/imsi        
}
function check_sim()
{
    local rc
    cnt=0
    while true; do
        rc=$(echo -e "AT+CPIN?\r\n" | microcom -t 100 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1)
        echo $rc | grep "READY" > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo "$(date +"%Y-%m-%d %H:%M:%S"): SIM card is ready" | tee -a $log_file
            echo 1 > ${OUTPUT_PATH}/cd
            #SIM
            echo -e "AT+CPIN?\r\n" | microcom -t 100 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1 > ${OUTPUT_PATH}/sim
            echo -e "AT+ZSIMCARD\r\n" | microcom -t 100 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1 > ${OUTPUT_PATH}/sim_type
            get_sim_id
            #revision
            echo -e "AT+GMR\r\n" | microcom -t 100 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1 > ${OUTPUT_PATH}/rev
            return 0
        fi
        if [[ "$cnt" -gt 300 ]]; then
            echo "$(date +"%Y-%m-%d %H:%M:%S"): WARN: SIM card doesnot work, needs to check the SIM card" | tee -a $log_file
            log "CPIN return: $rc"
            log "SIM_TYPE: $(echo -e "AT+ZSIMCARD\r\n" | microcom -t 100 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1)"
            echo 0 > ${OUTPUT_PATH}/cd
            clear_output
            return 1
        fi
        cnt=$((cnt+1))
        sleep 1
    done
}
function get_mode()
{
    local rc
    rc=$(echo -e "AT+ZPAS?\r\n" | microcom -t 100 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1)
    echo $rc | grep 'SERVICE' > /dev/null 2>&1
    if [ $? -eq 0 ];then
        echo 'NOSERVIC/NONE' > ${OUTPUT_PATH}/mode
    else
        echo $rc | awk -F'"' '{print $2}' > ${OUTPUT_PATH}/mode
    fi
}
function dial_up()
{
    cnt=0
    while true; do
        echo -e "AT+ZECMCALL=1\r\n" | microcom -t 100 /dev/ttyUSB1 | grep "CONNECT" > /dev/null 2>&1
        echo -e "AT+ZECMCALL=1\r\n" | microcom -t 100 /dev/ttyUSB1 | grep "CONNECT" > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo "$(date +"%Y-%m-%d %H:%M:%S"): ECM dail up OK" | tee -a $log_file
            log_csq
            #mode
            get_mode
            #current operator
            get_oper
            return 0
        else
            echo -e "AT+ZECMCALL=0\r\n" | microcom -t 100 /dev/ttyUSB1 > /dev/null 2>&1
        fi
        if [[ "$cnt" -gt 5 ]]; then
            echo "$(date +"%Y-%m-%d %H:%M:%S"): WARN: ECM dail up fail, needs to hard reset 4G module" | tee -a $log_file
            return 1
        fi
        cnt=$((cnt+1))
        sleep 5
    done
}
function check_net_state()
{
    local res
    local reg
    local cnt=0
    while true; do
        res=$(echo -e "AT+ZPAS?\r\n" | microcom -t 100 /dev/ttyUSB1 | sed '/^\s*$/d;s/\r//g' | head -1)
        reg=$(echo $res | awk -F',' '/\+ZPAS:/ {print $2}' | tr -d '"')
        echo $res | grep "Service\|ERROR\|SERVICE" > /dev/null 2>&1
        if [ "x$res" = "x" -o $? -eq 0 -o "x$reg" = "x" ]; then
            ((cnt++)) 
            [ $cnt -gt 8 ] && log "WARN: no network service, needs to reset 4G module" && return 1
            sleep 10
        else
            log "$res"
            return 0
        fi
    done
}
function module_setup()
{
    # repower

    check_ttyUSB1
    if [ $? -ne 0 ]; then
        return 1
    fi

    check_atcmd
    if [ $? -ne 0 ]; then
        return 2
    fi

    set_GPS

    clear_output

    check_cfun
    if [ $? -ne 0 ]; then
        return 6
    fi

    check_sim
    if [ $? -ne 0 ]; then
        return 3
    fi

    check_csq
    if [ $? -ne 0 ];then
        return 7
    fi

    #check network state
    check_net_state
    if [ $? -ne 0 ]; then
        return 4 
    fi
    check_reg_state
    [ $? -ne 0 ] && return 4
    
    dial_up
    if [ $? -ne 0 ]; then
        return 5
    fi
    #4G module needs a while to connect the network after dail up
    sleep 10
    check_sim_directional
    return $?
}

if [ $# -gt 0 ]; then
    case "$1" in
        -v)
            echo $version
            exit 0 
            ;;
        -h) 
            usage
            exit 0
            ;;
        *)
            echo "wrong argument"
            exit 1
            ;;
    esac
fi

mkdir -p "/deri/log"
if [ ! -f $log_file ]; then
    echo "" > ${log_file}
fi

if [ -f "${daemon_file}" ]; then
    rec_pid=$(cat "${daemon_file}")
    if [ -d "/proc/${rec_pid}" ]; then
        self_status=$(cat "/proc/${rec_pid}/stat" | awk '{print $3}')
        if [ "${self_status}" != "Z" ]; then
            date=$(date +"%Y-%m-%d %H:%M:%S")
            echo "${date} me3630[$$]: WARN: me3630.sh already running" >> "${log_file}"
            exit 0
        else 
            kill -9 $rec_pid
        fi
    fi
fi
echo "$$" > ${daemon_file}

#main loop
first_time=0
reset_step=0
CFUN_RESET=6
while true; do
    if [ $first_time -eq 0 ]; then
        # first_time=1
        # log "first time to connect 4G network"
        log "this module is ME3630"
        do_reset $CFUN_RESET
    fi
    check_connection
    if [ $? -ne 0 ]; then
        # dial_up
        # if [ $? -ne 0 ]; then
            do_reset $CFUN_RESET
        # fi
    fi

    get_sim_id
    get_oper
    log_csq
    get_location
    check_logsize
    sleep 60
done




# -*- coding: utf-8 -*-
# Copyright 2009-2020 <PERSON>. All Rights Reserved.
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

"""Define bidict package metadata."""


# _version.py is generated by setuptools_scm (via its `write_to` param, see setup.py)
try:
    from ._version import version
except (ImportError, ValueError, SystemError):  # pragma: no cover
    try:
        import pkg_resources
    except ImportError:
        __version__ = '0.0.0.VERSION_NOT_FOUND'
    else:
        try:
            __version__ = pkg_resources.get_distribution('bidict').version
        except pkg_resources.DistributionNotFound:
            __version__ = '0.0.0.VERSION_NOT_FOUND'
else:  # pragma: no cover
    __version__ = version

try:
    __version_info__ = tuple(int(p) if i < 3 else p for (i, p) in enumerate(__version__.split('.')))
except Exception:  # pragma: no cover
    __vesion_info__ = (0, 0, 0, f'PARSE FAILURE: __version__={__version__!r}')

__author__ = 'Joshua <PERSON>ronson'
__maintainer__ = 'Joshua Bronson'
__copyright__ = 'Copyright 2009-2020 Joshua Bronson'
__email__ = '<EMAIL>'

# See: ../docs/thanks.rst
__credits__ = [i.strip() for i in """
Joshua Bronson, Michael Arntzenius, Francis Carr, Gregory Ewing, Raymond Hettinger, Jozef Knaperek,
Daniel Pope, Terry Reedy, David Turner, Tom Viner, Richard Sanger, Zeyi Wang
""".split(',')]

__description__ = 'The bidirectional mapping library for Python.'
__keywords__ = 'dict dictionary mapping datastructure bimap bijection bijective ' \
    'injective inverse reverse bidirectional two-way 2-way'

__license__ = 'MPL 2.0'
__status__ = 'Beta'
__url__ = 'https://bidict.readthedocs.io'

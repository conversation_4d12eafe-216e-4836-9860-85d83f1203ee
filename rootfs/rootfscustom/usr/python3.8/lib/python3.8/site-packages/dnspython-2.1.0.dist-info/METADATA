Metadata-Version: 2.1
Name: dnspython
Version: 2.1.0
Summary: DNS toolkit
Home-page: http://www.dnspython.org
Author: <PERSON>
Author-email: <EMAIL>
License: ISC
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: ISC License (ISCL)
Classifier: Operating System :: POSIX
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: Python
Classifier: Topic :: Internet :: Name Service (DNS)
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Provides: dns
Requires-Python: >=3.6
Provides-Extra: dnssec
Requires-Dist: cryptography (>=2.6) ; extra == 'dnssec'
Provides-Extra: doh
Requires-Dist: requests ; extra == 'doh'
Requires-Dist: requests-toolbelt ; extra == 'doh'
Provides-Extra: idna
Requires-Dist: idna (>=2.1) ; extra == 'idna'
Provides-Extra: curio
Requires-Dist: curio (>=1.2) ; extra == 'curio'
Requires-Dist: sniffio (>=1.1) ; extra == 'curio'
Provides-Extra: trio
Requires-Dist: trio (>=0.14.0) ; extra == 'trio'
Requires-Dist: sniffio (>=1.1) ; extra == 'trio'

dnspython is a DNS toolkit for Python. It supports almost all
record types. It can be used for queries, zone transfers, and dynamic
updates.  It supports TSIG authenticated messages and EDNS0.

dnspython provides both high and low level access to DNS. The high
level classes perform queries for data of a given name, type, and
class, and return an answer set.  The low level classes allow
direct manipulation of DNS zones, messages, names, and records.


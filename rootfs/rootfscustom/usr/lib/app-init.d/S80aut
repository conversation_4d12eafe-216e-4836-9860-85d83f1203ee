#!/bin/bash

DAEMON=aut-bin
PIDFILE="/var/run/$DAEMON.pid"
EXECUTE=/usr/bin/aut-bin
VERSION_CMD="/usr/bin/aut-bin -V"

start() {
        printf "Starting $DAEMON: "
        start-stop-daemon -S -b -q -m -p $PIDFILE -x $EXECUTE
        local stat=$?
        if [ $stat = 0 ]; then
                echo "OK"
        else
                echo "FAIL"
        fi
        return $stat
}

stop() {
        printf "Stopping $DAEMON: "
        start-stop-daemon -K -q -p $PIDFILE
        local stat=$?
        if [ $stat = 0 ]; then
                echo "OK"
        else
                echo "FAIL"
        fi
        return $stat
}

version() {
        local ver=$($VERSION_CMD)
        echo $ver
}

restart() {
        stop
        sleep 1
        start
}

case "$1" in
start | stop | restart | version)
        "$1"
        ;;
reload)
        # Restart, since there is no true "reload" feature.
        restart
        ;;
*)
        start
        ;;
esac

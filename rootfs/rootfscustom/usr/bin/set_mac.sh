#!/bin/sh

# COPYRIGHT (c) DERI. Nanjing, China
#
#        FILE NAME: set_mac.sh
#
# FILE DESCRIPTION: set mac address with unique chipid
#
# --DATE--    NAME        REVISION HISTORY
# 20220315    xufei       V1.0
# 20220506    xufei       V1.1

echo "set mac address with chip id"
# use 36:C9:00 to identify eth0
# use 36:C9:01 to identify eth1
# first 6 bits of chip id as the rest mac address
chipid=$(head -3 /sys/class/sunxi_info/sys_info | tail -1 | tr -cd "[0-9]")
# echo $chipid
ifconfig -a |grep eth0 |grep -v grep > /dev/null
if [ $? -eq 0 ]
then 
	ifconfig eth0 down
	ifconfig eth0 hw ether 36:C9:00:${chipid:0:2}:${chipid:2:2}:${chipid:4:2}
	ifconfig eth0 up
fi
ifconfig -a |grep eth1 |grep -v grep > /dev/null
if [ $? -eq 0 ]
then 
	ifconfig eth1 down
	ifconfig eth1 hw ether 36:C9:01:${chipid:0:2}:${chipid:2:2}:${chipid:4:2}
	ifconfig eth1 up
fi

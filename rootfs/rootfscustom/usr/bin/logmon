#!/bin/bash

# COPYRIGHT (C) DERI. Nanjing, China
#
#       FILE NAME: logmon
#
# FILE DESCRIPTION: view the error log
#
# --DATE--      NAME        REVISION HISTORY
# 20220528   wangzhiruo     v1.0

version=v1.0

Folder_a="/deri/log"
Folder_b="/deri/log/drp"

#The default time is 30 days
date_now=`date "+%Y-%m-%d %H:%M:%S"`
date_now_seconds=`date +%s`
period=`expr 60 \* 60 \* 24 \* 30 `
diff_date=`expr $date_now_seconds - $period`  
date_ago=`date -d @$diff_date "+%Y-%m-%d %H:%M:%S"` 


#function script
function script()
{
    if [ -d ${Folder_a} ];then
        for file_a in ${Folder_a}/*
        do
            awk -F": " '$1>="'"$date_ago"'" && $1<="'"$date_now"'"' $file_a | grep -iE 'error' 

        done 
    fi
}

#function app
function app()
{
    if [ -d ${Folder_b} ];then
        for file_b in ${Folder_b}/*.log
        do
            awk -F": " '$1>="'"[$date_ago]"'" && $1<="'"[$date_now]"'"' $file_b | grep -iE 'error'
        done 
    fi
}

# No parameters
if [ $# -eq 0 ];then
    script
    app
fi

usage="

Usage: logmon [OPTIONS]

Options:
    -t,--time:      Specify a period of time
    -s,--script:    Displays the error log of the script
    -a,--app:       Displays the error log of the application
    -w,--warn:      Displays the warning log
    -v,--version:   Version
    -h,--help:      Usage

Examples:
    1.By default, error logs for scripts and applications from the last 30 days are displayed
      $ logmon   
    2.Displays the error log of the script in the last 5 days
      $ logmon --time -d5 --script mgc-watchdog  
    3.Displays error and warning logs for the comm process from June 4, 2022 to June 6, 2022
      $ logmon --time \"2022-06-04,2022-06-06\" --app comm* --warn
    4.Displays the error log of mgc-watchdog from May 4, 2022 at 19:00:00 to June 6, 2022 at 18:00:00
      $ logmon --time \"2022-05-04 19:00:00,2022-06-06 18:00:00\" --script mgc-watchdog 

"




# parse the command-line
getopt_cmd=$(getopt -o t:savhw --long time:,script,app,version,help,warn -n $(basename $0) -- "$@" )
[ $? -ne 0 ] && exit 1
eval set -- "$getopt_cmd"  

cmd=$@

# Parse other arguments 
while [ -n "$1" ] 
do
    case "$1" in
    -t|--time)
        case "$2" in
        -d*)
            day=${2##*d}
            period=`expr 60 \* 60 \* 24 \* $day`
            date_now=`date "+%Y-%m-%d %H:%M:%S"`
            date_now_seconds=`date +%s`
            diff_date=`expr $date_now_seconds - $period`  
            date_ago=`date -d @$diff_date "+%Y-%m-%d %H:%M:%S"` 
            shift 2

            ;;

        *)
            date_ago=${2%%,*}
            date_now=${2##*,}

            shift 2
            ;;
        esac
        ;;
    -s|--script) 
                
        shift     
        ;;
    -a|--app)

        shift 
        ;;
    -w|--warn)
        shift
        ;;
 
    -v|--version)
        echo $version
        shift
        ;;
    -h|--help)
        echo -e "$usage"
        exit 1
        ;;
    --) shift
        break
        ;;
    *) echo "$1 is not an option"
        exit 1 
        ;;
    esac
   
done

if [[ $cmd =~ "-s" ]];then 
    if [[ $cmd =~ "-w" ]];then
        for param in "$@"
        do
            for param_a in /deri/log/$param*
            do
                if [ "$param" = "all" ];then
                    if [ -d ${Folder_a} ];then
                        for file_a in ${Folder_a}/*
                        do
                            awk -F": " '$1>="'"$date_ago"'" && $1<="'"$date_now"'"' $file_a | grep -iE 'error|warn' 

                        done 
                    fi  
                else
                        awk  -F": " '$1>="'"$date_ago"'" && $1<="'"$date_now"'"'  $param_a |grep -iE 'error|warn'
                fi
            done
        done 

    else
        for param in "$@"
        do
            for param_a in /deri/log/$param*
            do
                if [ "$param" = "all" ];then
                    script   
                else
                    awk  -F": " '$1>="'"$date_ago"'" && $1<="'"$date_now"'"'  $param_a |grep -iE 'error'
                fi
            done
        done 
    fi
fi

if [[ $cmd =~ "-a" ]];then 
    if [[ $cmd =~ "-w" ]];then
        for param in "$@"
        do
            for param_b in /deri/log/drp/$param*.log
            do
                if [ "$param" = "all" ];then
                    if [ -d ${Folder_b} ];then
                        for file_b in ${Folder_b}/*.log
                        do
                            awk -F": " '$1>="'"[$date_ago]"'" && $1<="'"[$date_now]"'"' $file_b | grep -iE 'error|warn'
                        done 
                    fi
                else
                    awk -F": " '$1>="'"[$date_ago]"'" && $1<="'"[$date_now]"'"'  $param_b | grep -iE 'error|warn'
                fi
            done
        done
    else
        for param in "$@"
        do
            for param_b in /deri/log/drp/$param*.log
            do             
                if [ "$param" = "all" ];then
                        app
                else
                        awk -F": " '$1>="'"[$date_ago]"'" && $1<="'"[$date_now]"'"'  $param_b | grep -iE 'error'
                fi                 
            done
        done
    fi
fi















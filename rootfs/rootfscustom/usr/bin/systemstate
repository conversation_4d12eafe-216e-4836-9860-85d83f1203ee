#!/bin/bash

# COPYRIGHT (C) DERI. Nanjing, China
#
#       FILE NAME: systemstate
#
# FILE DESCRIPTION: Outputs system status information
# 
# --DATE--     NAME      REVISION HISTORY
# 20220606  wangzhiruo   v1.0

version=v1.0

usage="

Usage: systemstate [OPTIONS]

Options:
    -y,--system:    Displays the status of the system
    -s,--script:    Displays the status of the script
    -a,--app:       Displays the status of the application
    -v,--version:   Version
    -h,--help:      Usage

Examples:
    1.$ systemstate
    2.$ systemstate --system
    3.$ systemstste -a
"

function system()
{
echo "------------------------------system state--------------------------------------"
echo "Disk Usage:"
df -hP | grep -vE 'Filesystem|tmpfs|none|cgroup' | awk '{print $1 "  Used:" $3 "  Capacity:" $5 "  Mount:" $6}'
capacity1=`df -hP | grep -vE 'Filesystem|tmpfs|none|cgroup' | awk '{print $5 }'|awk -F"%" '{print $1}'|head -n1`
capacity2=`df -hP | grep -vE 'Filesystem|tmpfs|none|cgroup' | awk '{print $5 }'|awk -F"%" '{print $1}'|tail -n1`
if [ $capacity1 -gt 80 ];then
    echo "WARN:Disk Usage is too high"
fi
if [ $capacity2 -gt 80 ];then
    echo "WARN:Disk Usage is too high"
fi
echo -e "Mem Usage:\n"`free -m | grep 'Mem' | awk '{print " total:"$2"MB  used:"$3"MB  free:"$4"MB"}'`
mem_free=`free -m | grep 'Mem' | awk '{print $4}'`
if [ $mem_free -lt 100 ];then
    echo "WARN: MEM usage is too high"
fi
echo -e "CPU Usage:\n"`top -n1 |fgrep "CPU" | head -n1 |awk -F":" '{print $2}'|awk '{print $1,$2,$3,$4,$7,$8}'`
cpu_idle=`printf '%1.0f' $(top -n1 |fgrep "CPU" | head -n1 |awk '{print $8}'|awk -F"%" '{print $1}')`
# c=`printf '%1.0f' $cpu_idle`

if [ $cpu_idle -lt 20 ];then
    echo "WARN:CPU Usage is too high"
fi
echo -e "Uptime:\n"`uptime`
}

function app()
{
echo "------------------------------app    state---------------------------------------"

if [ -f /var/run/mgc-master.pid ]; then
        pid=$(pidof master)
        if [ -z "$pid" ] || [ ! -d "/proc/${pid}" ]; then
            echo "ERROR:master is dead: $pid"
        else
            status=$(cat "/proc/${pid}/stat" | awk '{print $3}')
            if [ "$status" == "Z" ]; then
                echo "ERROR:master is zombie: $pid"
            else
                echo "master[$pid] is running"
            fi
        fi
        if [ -f /var/run/mgc-slaves.pid ]; then
            lines=$(cat /var/run/mgc-slaves.pid)
            for line in ${lines}; do
                pid=$(echo ${line} | awk -F, '{print $1}')
                name=$(echo ${line} | awk -F, '{print $2}')
                opt=$(echo ${line} | awk -F, '{print $3}')

                if [ "${opt}" == "true" ]; then
                    if [ ! -d "/proc/${pid}" ]; then
                        echo "ERROR:$name is dead: $pid"
                    else
                        status=$(cat "/proc/${pid}/stat" | awk '{print $3}')
                        if [ "$status" == "Z" ]; then
                            echo "ERROR:$name is zombie: $pid"
                        else 
                            echo "$name[$pid] is running"
                        fi
                    fi
                fi
            done
        else
            echo "ERROR:slaves-pid file missing"
        fi
else
    echo "WARN:master is not running"
fi

}

function script()
{
echo "------------------------------script state---------------------------------------"
if [ -f /usr/bin/me3630.sh ];then
    echo -e "me3630:"
    if [ -f /var/run/me3630-daemon.pid ]; then
        pid=$(cat /var/run/me3630-daemon.pid)
        if [ -d "/proc/${pid}" ]; then
            status=$(cat "/proc/${pid}/stat" | awk '{print $3}')
            if [ "${status}" != "Z" ]; then
                echo -e "\tPID:[$pid]\n\tme3630 is running" 
                lsusb | grep 2514 > /dev/null 2>&1
                if [ $? -ne 0 ];then
                    echo -e "\tWARN:2514 chip not found"
                else 
                    lsusb |grep 1476 > /dev/null 2>&1
                    if [ $? -ne 0 ];then
                    echo -e "\tWARN:4G module not found"
                    else
                        ping -c 2 *************** > /dev/null 2>&1
                        if [ $? -ne 0 ]; then
                                echo -e "\tWARN:4G network disconnected" 
                        else 
                                echo -e "\t4G network connection OK" 
                        fi
                    fi
                fi
                

            else 
                echo -e "\tPID:[$pid]\n\tme3630 is zombie"
            fi
        fi
    else 
        echo -e "\tWARN:me3630 is not running"   
    fi
fi




echo -e "edge-vpn:"
#echo "Device_id:"

if [ -f /var/run/edge-vpn-daemon.pid ]; then
    pid=$(cat /var/run/edge-vpn-daemon.pid)
    if [ -d "/proc/${pid}" ]; then
        status=$(cat "/proc/${pid}/stat" | awk '{print $3}')
        if [ "${status}" != "Z" ]; then
            echo -e "\tPID:[$pid]\n\tedge-vpn is running" 
        else 
            echo -e "\tPID:[$pid]\n\tedge-vpn is zombie"
        fi
    fi
else 
    echo -e "\tWARN:edge-vpn is not running"    
   
fi
if [ ! -f /etc/openvpn/edge.ovpn ]; then
        echo -e "\tERROR: no edge.ovpn file"
fi

echo -e "mgc-watchdog:"
if [ -f /var/run/mgc-watchdog.enable ]; then
    state=$(cat /var/run/mgc-watchdog.enable)
    if [ "$state" == "true" ];then
        echo -e "\tState:enable"
    else
        echo -e "\tState:disable"
    fi 
fi 
if [ -f /var/run/mgc-watchdog.pid ]; then
    pid=$(cat /var/run/mgc-watchdog.pid)
    if [ -d "/proc/${pid}" ]; then
        status=$(cat "/proc/${pid}/stat" | awk '{print $3}')
        if [ "${status}" != "Z" ]; then
            echo -e "\tPID:[$pid]\n\tmgc-watchdog is running" 
        else 
            echo -e "\tPID:[$pid]\n\tmgc-watchdog is zombie"
        fi
    fi
else 
    echo -e "\tWARN:mgc-watchdog is not running"    
   
fi

echo -e "mgc-daemon:"

if [ -f /var/run/mgc-daemon.enable ]; then
    state=$(cat /var/run/mgc-daemon.enable)
    if [ "$state" == "true" ];then
        echo -e "\tState:enable"
    else
        echo -e "\tState:disable"
    fi 
fi 
if [ -f /var/run/mgc-daemon.pid ]; then
    pid=$(cat /var/run/mgc-daemon.pid)
    if [ -d "/proc/${pid}" ]; then
        status=$(cat "/proc/${pid}/stat" | awk '{print $3}')
        if [ "${status}" != "Z" ]; then
            echo -e "\tPID[$pid]\n\tmgc-daemon is running" 
        else 
            echo -e "\tPID:[$pid]\n\tmgc-daemon is zombie"
        fi
    fi
else 
    echo -e "\tWARN:mgc-daemon is not running"    

fi


}

if [ $# -gt 0 ];then
    case "$1" in
        -v|--version)
            echo $version
            exit 0
            ;;
        -h|--help)
            echo -e "$usage"
            exit 0
            ;;
        -y|--system)
            system
            exit 0
            ;;
        -s|--script)
            script
            exit 0
            ;;
        -a|--app)
            app
            exit 0
            ;;
        *)
            echo "wrong argument"
            exit 1
            ;;
        esac
else
    system
    app
    script
    exit 0
fi

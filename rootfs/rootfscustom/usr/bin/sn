#!/bin/bash

# COPYRIGHT (c) DERI. Nanjing, China
#
#        FILE NAME: sn
#
# FILE DESCRIPTION: set or get serial number
#
# --DATE--    NAME        REVISION HISTORY
# 20240410    liuyuxuan   Original


version="v1.0"

SN_FILE="/etc/sn"
# HW_INFO_PATH="/proc/device-tree/hw_info"

function usage() {
    echo "Usage: $0 {get|set <serial number>}"
    exit 1
}

if [ $# -eq 0 ]; then
    usage
fi

case "$1" in
    get)
        # if [ "$2" == "-r" ]; then
        #     if [ ! -d $HW_INFO_PATH ];then
        #         echo "No hardware information found."
        #         exit 1
        #     fi
        #     echo "board: $(cat ${HW_INFO_PATH}/board | tr -d '\0')"
        #     echo "core: $(cat ${HW_INFO_PATH}/core | tr -d '\0')"
        # else
        if [ ! -f $SN_FILE ]; then
            echo "No serial number found."
            exit 1
        fi
        cat $SN_FILE
        # fi
        ;;
    set)
        if [ -z "$2" ]; then
            echo "Please provide a serial number."
            exit 1
        fi
        [ -f $SN_FILE ] || touch $SN_FILE
        chattr -i $SN_FILE
        echo "$2" > $SN_FILE
        chattr +i $SN_FILE
        ;;
    *)
        echo $version
        usage
        ;;
esac

exit 0



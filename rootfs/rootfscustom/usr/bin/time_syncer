#!/bin/sh

# COPYRIGHT (c) DERI. Nanjing, China
#
#        FILE NAME: time_syncer
#
# FILE DESCRIPTION: sync time frome core1 to core2
#
# --DATE--    NAME        REVISION HISTORY
# 20180912 liuchenxin  Original Original

if [ -f /etc/container_name ]; then exit 1; fi

# wait eth ready
sleep 20

while true 
do
    # ntpd may fail to write date or rtc
    ntpd -n -q
    hwclock --systohc --utc
    # write into c2
    rt_running=""
    if [ -f "/var/run/mgc-slaves.pid" ]
    then    
        rt_running=$(cat "/var/run/mgc-slaves.pid" | grep "rt")
    fi

    if [ ! -z ${rt_running} ]; then
        sync_core2_time
    fi
    sleep 180
done


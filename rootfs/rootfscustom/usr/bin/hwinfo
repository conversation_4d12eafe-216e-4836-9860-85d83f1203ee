#!/bin/bash

# COPYRIGHT (c) DERI. Nanjing, China
#
#        FILE NAME: hwinfo
#
# FILE DESCRIPTION: get hardware info
#
# --DATE--    NAME        REVISION HISTORY
# 20240410    liuyuxuan   Original

CPU_INFO="/proc/cpuinfo"
HW_INFO_PATH="/proc/device-tree/hw_info"
function enum_device {
    __HW_BOARD=unknown
    __HW_CORE=unknown
    grep Zynq /proc/cpuinfo &> /dev/null
    if [ $? -eq 0 ]; then
        __HW_BOARD=MGCA
        __HW_CORE=Zynq
        return
    fi
    if [ -d /sys/class/pwm/pwmchip4 ]; then
        __HW_BOARD=MGCA
        grep sun8i /proc/cpuinfo &> /dev/null
        if [ $? -eq 0 ]; then
            __HW_CORE=OKA40i
        fi
    else
        local io_num=$(ls /sys/class/gpio_sw | wc -l)
        if [ "x$io_num" == "x15" ]; then
            __HW_BOARD=AT01B
            __HW_CORE=OKT3
        elif [ "x$io_num" == "x22" ]; then
            __HW_BOARD=IMGCB01
            __HW_CORE=OKA40i
        fi
    fi
}

if [ ! -d $HW_INFO_PATH ];then
    # echo "No hardware information found." >&2
    # exit 1
    enum_device
fi
if [ -f $CPU_INFO ];then
    echo "cpu_serial: $(cat $CPU_INFO | awk -F ':' '/Serial/ {print $2}' | tr -d ' ')"
fi
if [ ! -d $HW_INFO_PATH ];then
    echo "board: $__HW_BOARD"
    echo "core: $__HW_CORE"
    unset __HW_BOARD
    unset __HW_CORE
else
    echo "board: $(cat ${HW_INFO_PATH}/board | tr -d '\0')"
    echo "core: $(cat ${HW_INFO_PATH}/core | tr -d '\0')"
fi
#!/bin/sh

# COPYRIGHT (c) DERI. Nanjing, China
#
#        FILE NAME: pdpcontext
#
# FILE DESCRIPTION: set or get settings of 4G pdpcontext
#
# --DATE--    NAME        REVISION HISTORY
# 20240122    liuyuxuan   Original

#!/bin/bash

version="v1.0"
context_path="/etc/default/PDPContext"

function usage() {
    echo "This script is used to set or get pdpcontext"
    echo "Usage:"
    echo "  pdpcontext set [--cid <cid>] [--type <TYPE>] [--apn <APN>]"
    echo "    cid:(1-24); TPYE:(IP, IPV6, IPV4V6); APN:(name_of_apn)"
    echo "    eg. pdpcontext set --cid 1 --type IP --apn \"cmnet\""
    echo "    eg. pdpcontext set --apn \"\"    # can be set to empty"
    echo "  pdpcontext get <all | --cid | --type | --apn>"
    echo "    eg. pdpcontext get all"
    echo "    eg. pdpcontext get --cid"
}
function set_context() {
    local cid
    local type
    local apn
    while [[ $# -ge 2 ]]; do
        case $1 in
            --cid)
                cid=$2
                sed -i "s/^CID.*$/CID=\"$cid\"/" "$context_path"   # can be set to empty
                shift 2
                ;;
            --type)
                type=$2
                sed -i "s/^PDP_TYPE.*$/PDP_TYPE=\"$type\"/" "$context_path"
                shift 2
                ;;
            --apn)
                apn=$2
                sed -i "s/^APN.*$/APN=\"$apn\"/" "$context_path"
                shift 2
                ;;
            *)
                echo "Invalid argument: $1"
                exit 1
                ;;
        esac
    done

    # [ -n "$cid" ] && sed -i "s/^CID.*$/CID=\"$cid\"/" "$context_path" #&& grep CID "$context_path"
    # [ -n "$type" ] && sed -i "s/^PDP_TYPE.*$/PDP_TYPE=\"$type\"/" "$context_path" #&& grep PDP_TYPE "$context_path"
    # [ -n "$apn" ] && sed -i "s/^APN.*$/APN=\"$apn\"/" "$context_path" #&& grep APN "$context_path"
}
function get_context() {
    local context_path="/etc/default/PDPContext"

    if [ -r "$context_path" ]; then
        if [ "$1" == "all" ]; then
            cat "$context_path"
        elif [ "$1" == "--cid" ]; then
            grep CID "$context_path" | cut -d '=' -f2
        elif [ "$1" == "--type" ]; then
            grep PDP_TYPE "$context_path" | cut -d '=' -f2
        elif [ "$1" == "--apn" ]; then
            grep APN "$context_path" | cut -d '=' -f2
        else
            echo "Invalid argument: $1"
            exit 1
        fi
    else
        echo "PDPContext file not found."
    fi
}

if [ $# -gt 0 ]; then
    case "$1" in
        -v)
            echo $version
            exit 0 
            ;;
        -h) 
            usage
            exit 0
            ;;
        set)
            shift
            set_context "$@"
            ;;
        get)
            shift
            get_context "$@"
            ;;
        *)
            echo "Invalid command: $1"
            exit 1
            ;;
    esac
else
    usage
fi


#!/bin/sh

# COPYRIGHT (c) DERI. Nanjing, China
#
#        FILE NAME: edge-vpn
#
# FILE DESCRIPTION: edge device vpn
#
# --DATE--    NAME        REVISION HISTORY
# 20210723    liuchenxin  Original Original
# 20220406    xufei       v1.1
# 20220420    xufei       v1.2
# 20250520    liuyuxuan   v1.3

log_file="/deri/log/edge-vpn.log"
log_file_0="/deri/log/edge-vpn.log.0"
pid_file="/var/run/edge-vpn.pid"
daemon_file="/var/run/edge-vpn-daemon.pid"

version=v1.3
function usage()
{
    echo "this script is used to connect vpn"
    echo "eg: edge-vpn &"
}
function log()
{
    echo "$(date) edge-vpn[$$]: $@" >> "${log_file}"
}
function check_mtime()
{
    local pid=$1
    local vpn_uptime=$(ps -e -o pid,lstart | grep $pid | awk '{$1=""; print substr($0, 2)}')
    local epoch=$(date -d "$vpn_uptime" +%s)
    local mtime=$(find /etc/ssl/edsc/ -type f -exec stat -c %Z {} \; | sort -nr | head -1)
    if [ "x$epoch" != "x" -a "x$mtime" != "x" ]; then
        if [ $mtime -gt $epoch ]; then
            return 0
        fi
    fi
    return 1
}
if [ $# -gt 0 ]; then
    case "$1" in
        -v)
            echo $version
            exit 0 
            ;;
        -h) 
            usage
            exit 0
            ;;
        *)
            echo "wrong argument, this script needs no argument"
            exit 1
            ;;
    esac
fi

mkdir -p "/deri/log"

if [ ! -f $log_file ]; then
    echo "" > $log_file
fi

if [ -f "${daemon_file}" ]; then
    rec_pid=$(cat "${daemon_file}")
    if [ -d "/proc/${rec_pid}" ]; then
        self_status=$(cat "/proc/${rec_pid}/stat" | awk '{print $3}')
        if [ "${self_status}" != "Z" ]; then
            date=$(date +"%Y-%m-%d %H:%M:%S")
            log "edge-vpn already running"
            exit 0
        else
            kill -9 $rec_pid
        fi
    fi
fi

echo "$$" > ${daemon_file}

# wait eth ready
sleep 20

pid=-1
if [ -f "${pid_file}" ]; then
    pid=$(cat "${pid_file}")
fi

while true
do
    if [ ! -f "/etc/openvpn/edge.ovpn" ]; then
        date=$(date +"%Y-%m-%d %H:%M:%S")
        log "ERROR: no edge.ovpn file"
    fi
    
    # openvpn will exit if connection was rejected
    is_running="false"
    if [ -d "/proc/${pid}" ]; then
        status=$(cat "/proc/${pid}/stat" | awk '{print $3}')
        if [ "${status}" != "Z" ]; then
            is_running="true"
        fi
    fi

    if [ "${is_running}" == "false" ]
    then
        openvpn --cd /etc/openvpn --config "edge.ovpn" --log-append $log_file > /dev/null 2>&1 &
        pid=$!
        echo "${pid}" > ${pid_file}
    else
        check_mtime $pid 2>/dev/null
        if [ $? -eq 0 ]; then
            log "INFO: config file changed, restart openvpn"
            killall openvpn
            continue
        fi
    fi

    #check log size
    logsize=$(ls -l $log_file | awk '{print $5}')
    maxsize=$((10*1024*1024))
    if [ $logsize -gt $maxsize ]; then
        mv $log_file $log_file_0
        echo "" > $log_file
    fi

    sleep 10
done



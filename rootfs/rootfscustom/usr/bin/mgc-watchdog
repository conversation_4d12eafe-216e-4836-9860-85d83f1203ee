#!/bin/sh

# COPYRIGHT (c) DERI. Nanjing, China
#
#        FILE NAME: mgc-watchdog
#
# FILE DESCRIPTION: mgc-watchdog
#
# --DATE--    NAME        REVISION HISTORY
# 20210130 	  xufei  	  v1.0
# 20210421 	  xufei  	  v1.1

name=mgc-watchdog
opt_file=/var/run/${name}.enable
pid_file=/var/run/${name}.pid
log_file=/deri/log/mgc-watchdog.log

version=v1.1
function usage()
{
    echo "this script is used to manage watchdog, make sure it's enable after debugging"
    echo "enable example: mgc-watchdog enable &"
    echo "disable example: mgc-watchdog disable &"
    echo "state check example: mgc-watchdog state"
}
if [ $# -gt 0 ]; then
    case "$1" in
        -v)
            echo $version
            exit 0 
            ;;
        -h) 
            usage
            exit 0
            ;;
        enable) 
            echo "true" > ${opt_file}
            ;;
        disable) 
            echo "false" > ${opt_file}
            ;;
        state) 
            echo $(cat ${opt_file})
            exit 0
            ;;
        *)
            echo "wrong way to use the script"
            usage
            exit 1
            ;;
    esac
else
    echo "wrong way to use the script"
    usage
    exit 1
fi

mkdir -p "/deri/log"

if [ ! -f $log_file ]; then
    echo "" > $log_file
fi

opt=$(cat ${opt_file})
echo "$(date +"%Y-%m-%d %H:%M:%S"): mgc-watchdog state is $opt" >> $log_file

# check if watchdog started
started="false"
if [ -f "${pid_file}" ]; then
    pid=$(cat "${pid_file}")
    if [ -d "/proc/${pid}" ]; then
        status=$(cat "/proc/${pid}/stat" | awk '{print $3}')
        if [ "${status}" != "Z" ]; then
            started="true"
        fi
    fi
fi

if [ "${started}" == "true" ]; then
	exit 0;
fi

# record mgc-watchdog pid
echo "$$" > "${pid_file}"

function run_ecg_watchdog()
{
    if [ ! -d "/sys/class/gpio/gpio139/" ]; then
        echo 139 > /sys/class/gpio/export
    fi

    while true; do
        if [ "${opt}" == "false" ]; then
            echo in > /sys/class/gpio/gpio139/direction
            sleep 1
        else
            echo out > /sys/class/gpio/gpio139/direction
            echo 0 > /sys/class/gpio/gpio139/value
            usleep 100000
            echo 1 > /sys/class/gpio/gpio139/value
            usleep 100000
        fi
    done
}

function run_mgc_watchdog()
{
    if [ ! -d "/sys/class/gpio/gpio263/" ]; then
        echo 263 > /sys/class/gpio/export
    fi

    while true; do
        if [ "${opt}" == "false" ]; then
            echo in > /sys/class/gpio/gpio263/direction
            sleep 1
        else
            echo out > /sys/class/gpio/gpio263/direction
            echo 0 > /sys/class/gpio/gpio263/value
            usleep 100000
            echo 1 > /sys/class/gpio/gpio263/value
            usleep 100000
        fi
    done
}

if [ -d /sys/class/pwm/pwmchip4 ]; then
    run_mgc_watchdog
else
    run_ecg_watchdog
fi




#!/bin/bash

OUTPUT_PATH="/var/run/net_module"
[ -d $OUTPUT_PATH ] || mkdir -p ${OUTPUT_PATH}
MODEL="${OUTPUT_PATH}/model"
[ -f $MODEL ] || touch ${MODEL}
file_list=$(ls ${OUTPUT_PATH})
daemon_file=/var/run/net-module-daemon.pid

version="v0.2"

rst_pin=/sys/class/gpio_sw/PB8/data
rst_pin_file=/opt/net_module/rst_pin
function usage()
{
    echo "Usage: $0 {detect|start|info|state|log|cmd|stop|shutdown}"
    exit 1
}
function rst_toggle()
{
    local rst=$(cat ${rst_pin})
    if [ "$rst" -eq 1 ]; then
        echo 0 > ${rst_pin}
    else
        echo 1 > ${rst_pin}
    fi
}
function detect_rst_pin()
{
    echo $(cat ${rst_pin_file}) > ${rst_pin}
    while true; do
        sleep 30
        if [ -c /dev/ttyUSB1 ]; then
            echo $(cat ${rst_pin}) > ${rst_pin_file}
            return 0
        fi
        rst_toggle
    done
    
}
function net_module_detect()
{
    echo 1 > /sys/class/gpio_sw/PI6/data
    echo -e "ATI\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep EC200M &> /dev/null && echo EC200M | tee ${MODEL} && return 0
    local res=$(echo -e "ATI\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep Model)
    [ -z "$res" ] && echo "模块未准备好, 稍后重试" >&2 && return 1
    echo $res
    echo $res | grep ME3630 &> /dev/null && echo ME3630 > ${MODEL} && return 0
    echo $res | grep NL668 &> /dev/null && echo NL668 > ${MODEL} && return 0
}

function net_module_start()
{
    echo 1 > /sys/class/gpio_sw/PI6/data
    cnt=0
    while [ ! -c /dev/ttyUSB1 ]; do
        if [ -c /dev/ttyUSB1 ]; then
            break
        fi
        if [ $cnt -gt 300 ]; then
            echo "$(date +"%Y-%m-%d %H:%M:%S"): 找不到模块ttyUSB串口" >&2
            if [ $# -gt 1 ]; then
                cnt=0
            else
                exit 1
            fi
        fi
        cnt=$((cnt+1))
        sleep 1
    done
    if [ -f $daemon_file ]; then
        local proc=/proc/$(cat $daemon_file)
        [ -d $proc ] && grep -E "me3630|nl668|ec200" $proc/stat | grep -v Z &> /dev/null
        if [ $? -eq 0 ]; then
            echo "4G脚本已经在运行, pid: $(cat $daemon_file)" >&2
            exit 1
        fi
    fi

    while true; do
        echo -e "ATI\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep NL668 > /dev/null 2>&1
        if [ $? -eq 0 ];then
            chmod +x /opt/net_module/nl668.sh && /opt/net_module/nl668.sh &>/dev/null &
            echo NL668 > ${MODEL}
            exit 0
        fi
        echo -e "ATI\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep ME3630 > /dev/null 2>&1
        if [ $? -eq 0 ];then
            chmod +x /opt/net_module/me3630.sh && /opt/net_module/me3630.sh &>/dev/null &
            echo ME3630 > ${MODEL}
            exit 0
        fi
        echo -e "ATI\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep EC200M > /dev/null 2>&1
        if [ $? -eq 0 ];then
            chmod +x /opt/net_module/ec200m.sh && /opt/net_module/ec200m.sh &>/dev/null &
            echo EC200M > ${MODEL}
            exit 0
        fi
        sleep 5
    done
}
function net_module_stop()
{
    local rec_pid=$(cat /var/run/net-module-daemon.pid)
    kill -9 $rec_pid
    if [ $? -ne 0 ]; then
        echo "kill pid $rec_pid 失败" >&2
    else
        echo "kill pid $rec_pid 成功"
    fi
}
function net_module_shutdown()
{
    ps -ef | grep /opt/net_module | grep -v grep &> /dev/null \
    && net_module_stop
    case "$(cat ${MODEL})" in
        ME3630)
            echo -e "AT+ZTURNOFF\r\n" | microcom -t 1000 /dev/ttyUSB1
            ;;
        NL668)
            echo -e "AT+CPWROFF\r\n" | microcom -t 1000 /dev/ttyUSB1
            ;;
        EC200M)
            echo -e "AT+QPOWD=1\r\n" | microcom -t 1000 /dev/ttyUSB1
            ;;
        *)
            echo "no module detected"
            exit 1
            ;;
    esac
    while [ -c /dev/ttyUSB1 ]; do
        sleep 0.2
    done
    echo 0 > /sys/class/gpio_sw/PI6/data
    echo "模块已断电"
}

function net_module_info()
{
    echo "power: $(cat /sys/class/gpio_sw/PI6/data)"
    echo -n "active: "
    if [ -c /dev/ttyUSB1 ]; then
        echo 1
    else
        echo 0
    fi
    for file in ${file_list[@]}; do
        echo "${file}: $(cat ${OUTPUT_PATH}/${file} | sed 's/^.*:\s//;s/\r//g')"
    done
}
function AT_parse()
{
    local cmd=$1
    local deli=$2
    local index=$3
    local res=$(echo -e "AT+$cmd\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep : | tr -d '\r')
    local rc=$(echo "$res" | awk -v i=$index -F"$deli" '{print $ i}')
    
    echo -n "  "
    case "$cmd" in
        CFUN?)
            echo -n "功能模式："
            case "$rc" in
                0)
                    echo -n "最小功能"
                    ;;
                1)
                    echo -n "全功能"
                    ;;
                4)
                    echo -n "飞行模式"
                    ;;
                5)
                    echo -n "工厂测试模式"
                    ;;
                *)
                    echo -n "未知"
                    ;;
            esac
            ;;
        CPIN?)
            echo -n "SIM卡状态: "
            echo $rc | grep READY > /dev/null 2>&1
            if [ $? -eq 0 ]; then
                echo -n "SIM卡已准备好"
            else
                echo -n "SIM卡未准备好"
            fi
            ;;
        CSQ)
            echo -n "信号强度: "
            if [[ $rc -eq 0 && $rc -eq 99 ]]; then
                echo -n "无信号"
            elif [[ $rc -ge 1 && $rc -le 5 ]]; then
                echo -n "信号极差"
            elif [[ $rc -ge 6 && $rc -le 17 ]]; then
                echo -n "信号较差"
            elif [[ $rc -ge 18 && $rc -le 21 ]]; then
                echo -n "信号一般"
            elif [[ $rc -ge 22 && $rc -le 24 ]]; then
                echo -n "信号较好"
            elif [[ $rc -ge 25 && $rc -le 31 ]]; then
                echo -n "信号极好"
            else
                echo -n "未知"
            fi
            ;;
        CREG?|CGREG?|CEREG?)
            [ "$cmd" = "CREG?" ] && echo -n "CS注册状态: "
            [ "$cmd" = "CGREG?" ] && echo -n "GPRS注册状态: "
            [ "$cmd" = "CEREG?" ] && echo -n "EPS注册状态: "
            case "$rc" in
                0)
                    echo -n "未注册"
                    ;;
                1)
                    echo -n "已注册"
                    ;;
                2)
                    echo -n "正在注册"
                    ;;
                3)
                    echo -n "注册被拒绝"
                    ;;
                4)
                    echo -n "未知"
                    ;;
                5)
                    echo -n "已注册漫游"
                    ;;
                *)
                    echo -n "未知"
                    ;;
            esac
            ;;
        *)
            echo "AT command not supported"
            ;;
    esac
    echo -en "($res)\r\n"
}
function net_module_state()
{
    echo "设备状态："
    case "$1" in 
        ME3630|NL668|EC200M)
            echo "  模组型号: $1"
            AT_parse "CFUN?" "[ ,]" "2"
            AT_parse "CPIN?" ' ' "0"
            AT_parse "CSQ" "[ ,]" "2"
            AT_parse "CREG?" "," "2"
            AT_parse "CGREG?" "," "2"
            AT_parse "CEREG?" "," "2"
            if [ "$1" = "ME3630" ]; then
                local res=$(echo -e "AT+ZPAS?\r\n" | microcom -t 1000 /dev/ttyUSB1 | awk -F'"' '{print $2}' | tr -d '\r\n')
                case "$res" in
                    LTE)
                        echo "  网络制式: LTE"
                        echo -e "AT+ZSRVRSP\r\n?" | microcom -t 1000 /dev/ttyUSB1 | sed '/^\s*$/d' | head -1 | awk -F'[:,]' '{gsub(/"/, "", $2);gsub(/\r/,"",$NF);print "  rsrp: "$2"dBm" ",sinr: "$NF"dB"}'
                        ;;
                    *)
                        echo "  网络制式: ${res:-未知}"
                        echo "  rsrp, sinr未知"
                        ;;
                esac
            fi
            if [ "$1" = "NL668" ]; then
                local rsrp
                local snr
                local res=$(echo -e "AT+GTCCINFO?\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep -A 1 "service cell")
                local mode=$(echo $res | awk '/service cell/ {print $1}')
                echo "  网络制式: ${mode:-未知}"
                if [ "x$mode" = "xLTE" ]; then
                    snr=$(echo $res | awk -F',' '{print $11}')
                    rsrp=$(echo $res | awk -F',' '{print $13}')
                    snr=$(expr $snr / 2)
                    rsrp=$(expr $rsrp - 141)
                    echo "  rsrp: $rsrp dBm, snr: $snr dB"
                else
                    echo "  rsrp, snr未知"
                fi
            fi
            if [ "$1" = "EC200M" ]; then
                local rsrp
                local snr
                local res=$(echo -e "AT+QENG=servingcell\r\n" | microcom -t 1000 /dev/ttyUSB1 | grep QENG)
                local mode=$(echo $res | awk -F',' '{print $3}' | tr -d '"')
                echo "  网络制式: ${mode:-未知}"
                if [ "x$mode" = "xLTE" ]; then
                    snr=$(echo $res | awk -F',' '{print $17}')
                    rsrp=$(echo $res | awk -F',' '{print $14}')
                    echo "  rsrp: $rsrp dBm, snr: $snr dB"
                else
                    echo "  rsrp, snr未知"
                fi
            fi
            ;;
        *)
            echo "no module detected, please check module and run netmodule detect" >&2
            ;;
    esac
}
function netmodule_log()
{
    less /deri/log/net-module.log
}
function netmodule_AT()
{
    echo -e "AT$1\r\n" | microcom -t 1000 /dev/ttyUSB1
}
case "$1" in
    detect)
        detect_rst_pin &> /dev/null &
        net_module_detect
        ;;
    start)
        detect_rst_pin &> /dev/null &
        net_module_start $@
        ;;
    info)
        net_module_info
        ;;
    state)
        net_module_state $(cat ${MODEL})
        ;;
    log)
        netmodule_log
        ;;
    cmd)
        netmodule_AT $2
        ;;
    stop)
        net_module_stop
        ;;
    shutdown)
        net_module_shutdown
        ;;
    *)
        echo $version
        usage
        ;;
esac

exit 0
#/bin/sh

# COPYRIGHT (c) DERI. Nanjing, China
#
#        FILE NAME: edge-update-raw
#
# FILE DESCRIPTION: 
#
# --DATE--    NAME        REVISION HISTORY
# 20210803    liuchenxin  Original Original

if [ -z "$1" ]; then exit 1; fi

if [ ! -f "$1" ]; then
    echo "not exist: $1"
    exit 1;
fi

unzip -t $1 > /dev/null 2>&1
if [ ! "$?" -eq 0 ]; then
    echo "not zip file: $1"
    exit 1;
fi

name=$(basename "$1")
path="/tmp/update/$name"

if [ -e $path ]; then
    rm -rf $path
fi

mkdir -p $path

unzip $1 -d $path > /dev/null 2>&1
if [ ! "$?" -eq 0 ]; then
    echo "failed to unzip: $1"
    exit 1
fi

if [ ! -f "$path/config" ]; then
    echo "not found: $path/config"
    exit 1
fi

update() {
    raw=$(declare -p $1)
    declare -A it=${raw#*=}

    if [ ! -z "${it[pre_cmd]}" ]; then
        eval "${it[pre_cmd]}"
        if [ ! "$?" -eq 0 ]; then
            echo "failed eval: ${it[pre_cmd]}"
            exit 1;
        fi
    fi

    mkdir -p "${it[dest]}"
    if [ ! "$?" -eq 0 ]; then
        echo "not dir: ${it[des]}"
        exit 1;
    fi

    eval "mv $path/${it[src]} ${it[dest]} --backup=numbered"
    if [ ! "$?" -eq 0 ]; then
        echo "mv failed"
        exit 1;
    fi

    if [ ! -z "${it[post_cmd]}" ]; then
        eval "${it[post_cmd]}"
        if [ ! "$?" -eq 0 ]; then
            echo "failed eval: ${it[post_cmd]}"
            exit 1;
        fi
    fi
}

source $path/config


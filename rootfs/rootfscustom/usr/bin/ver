#!/bin/bash

# COPYRIGHT (C) DERI. Nanjing, China
#
#       FILE NAME: ver
#
# FILE DESCRIPTION: show version
# 
# --DATE--     NAME      REVISION HISTORY
# 20220527  wangzhiruo   v1.0

version=v1.0

usage="

Usage: ver [OPTIONS]

Options:
    -y,--system:    Displays version of the system
    -s,--script:    Displays version of the script
    -a,--app:       Displays version of the application
    -v,--version:   Version
    -h,--help:      Usage

Examples:
    1.$ ver
    2.$ ver --script
    3.$ ver -a
"
function system()
{
    echo -e "----------------------------System Version---------------------------------"
    echo -e "Kernal version:\n"`uname -a`
    if [ -f /etc/rootfs.version ];then 
        echo -e "rootfs version:"
        cat /etc/rootfs.version
    fi
}
function script()
{
    echo -e "----------------------------Script Version---------------------------------"
    echo -e "ver:\t\t"`ver --version`
    echo -e "start:\t\t"`start -v`
    echo -e "stop:\t\t"`stop -v`
    echo -e "mgc-watchdog:\t"`mgc-watchdog -v`
    echo -e "mgc-daemon:\t"`mgc-daemon -v`
    if [ -f /usr/bin/me3630.sh ];then 
        echo -e "me3630:\t\t"`me3630.sh -v`
    fi
    if [ -f /usr/bin/GUI-daemon ];then 
        echo -e "GUI-daemon:\t"`GUI-daemon -v`
    fi
    echo -e "edge-vpn:\t"`edge-vpn -v`
}
function app()
{
    echo -e "----------------------------App    Version---------------------------------"
    export LD_LIBRARY_PATH=/deri/mgc
    echo -e `cd /deri/mgc && chmod +x * && ./master version | grep -iv error`
}
if [ $# -gt 0 ]; then
    case "$1" in
        -v|--version)
            echo $version
            exit 0
            ;;
        -h|--help)
            echo -e "$usage"
            exit 0
            ;;
        -y|--system)
            system
            exit 0
            ;;
        -s|--script)
            script
            exit 0
            ;;
        -a|--app)
            app
            exit 0
            ;;
        *)
            echo "wrong argument"
            exit 1
            ;;
    esac
else
    system
    script
    app
    exit 0
fi

